# Admin Device Revocation System

This feature provides administrators with the ability to remotely revoke device access for users, ensuring immediate session termination while maintaining a smooth user experience.

## Features Overview

### ✅ **Implemented Features**

1. **Immediate Session Invalidation**
   - Instant token invalidation in database when device is revoked
   - All authentication tokens for the device are marked as revoked
   - Device session is marked as inactive with admin audit trail

2. **Graceful Logout Detection**
   - Periodic session validation detects revocation (every 5 minutes)
   - API requests automatically detect invalid sessions
   - Offline-aware detection when device reconnects

3. **User-Friendly Experience**
   - Non-intrusive notification before logout
   - Clear admin revocation message with support contact info
   - Graceful handling that doesn't interrupt critical actions
   - Professional logout dialog with appropriate messaging

4. **Best Practices Implementation**
   - Leverages existing single-session authentication system
   - Comprehensive offline handling and reconnection logic
   - Robust error handling and fallback mechanisms
   - Audit logging for all admin actions

5. **Admin Dashboard Integration**
   - Complete device management interface
   - Real-time device status monitoring
   - Confirmation dialogs before revocation
   - Comprehensive audit trail and logging

## Architecture

### Backend Components

1. **Database Schema** (`admin/database/device_management_schema.sql`)
   - `admin_action_logs`: Audit trail for all admin actions
   - `device_sessions`: Track active device sessions
   - Enhanced `api_tokens`: Device tracking and revocation status
   - Views for active device monitoring

2. **API Endpoints**
   - `admin/api/revoke_device.php`: Device revocation endpoint
   - `admin/api/get_user_devices.php`: Device information retrieval
   - Enhanced `admin/api/session_validate.php`: Admin revocation detection

3. **Admin Dashboard** (`admin/device_management.php`)
   - Real-time device monitoring interface
   - Device revocation controls with confirmation
   - Statistics and activity monitoring
   - Responsive design with professional UI

### Flutter Integration

1. **Enhanced Session Service** (`lib/services/single_session_auth_service.dart`)
   - Admin revocation event type
   - Graceful logout handling
   - Enhanced session validation with revocation detection

2. **Session Manager** (`lib/services/session_manager.dart`)
   - Admin revocation event handling
   - User-friendly notification system
   - Graceful logout coordination

3. **User Interface**
   - Enhanced logout dialogs with admin-specific messaging
   - Non-intrusive notification system
   - Smooth transition to login screen

## Usage Guide

### Admin Dashboard Access

1. **Navigate to Device Management**
   ```
   Admin Panel → Device Management
   ```

2. **View Active Devices**
   - See all active user devices
   - Monitor last activity and token counts
   - View device information and user details

3. **Revoke Device Access**
   - Click "Revoke" button next to target device
   - Enter reason for revocation
   - Confirm action in dialog
   - Device is immediately invalidated

### API Usage

#### Revoke Device Access
```php
POST /admin/api/revoke_device.php
Content-Type: application/json

{
    "user_id": 123,
    "device_id": "device_abc123",
    "reason": "Security policy violation"
}
```

#### Get Device Information
```php
GET /admin/api/get_user_devices.php?user_id=123&include_revoked=false
```

### Flutter Integration

#### Listen for Admin Revocation Events
```dart
SessionManager().singleSessionService.sessionEventStream.listen((event) {
  if (event.type == SessionEventType.adminRevocation) {
    print('Admin revocation: ${event.message}');
    // Handle graceful logout
  }
});
```

#### Manual Session Validation
```dart
final result = await SessionManager().validateSession();
if (result == SessionValidationResult.invalidated) {
  // Session was invalidated (possibly by admin)
}
```

## User Experience Flow

### Normal Device Revocation Flow

1. **Admin Action**
   - Admin selects device in dashboard
   - Enters revocation reason
   - Confirms action

2. **Immediate Backend Changes**
   - All tokens for device are invalidated
   - Device session marked as revoked
   - Admin action logged for audit

3. **User Device Detection**
   - Next API request detects invalid session
   - OR periodic validation (5 minutes) detects revocation
   - Special admin revocation handling triggered

4. **Graceful User Experience**
   - Non-intrusive notification shown first
   - 3-second delay before logout dialog
   - Clear message about admin termination
   - Smooth transition to login screen

### Offline Device Handling

1. **Device Offline During Revocation**
   - Revocation processed on server
   - Device tokens invalidated immediately

2. **Device Comes Online**
   - First API request detects invalid session
   - Admin revocation message displayed
   - Graceful logout process initiated

## Security Features

### Audit Trail
- All admin actions logged with timestamp
- IP address and user agent tracking
- Reason for revocation stored
- Complete audit trail for compliance

### Token Security
- Immediate token invalidation
- Device-specific token tracking
- Revocation status in database
- No token reuse after revocation

### Session Protection
- Device ID validation on every request
- Session integrity checks
- Offline-aware security
- Graceful degradation

## Database Schema

### Admin Action Logs
```sql
CREATE TABLE admin_action_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT NOT NULL,
    admin_username VARCHAR(50) NOT NULL,
    action_type ENUM('device_revoke', 'user_deactivate', ...),
    target_user_id INT NOT NULL,
    target_device_id VARCHAR(255),
    reason TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Device Sessions
```sql
CREATE TABLE device_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    revoked_by_admin INT DEFAULT NULL,
    revoked_at TIMESTAMP NULL,
    revocation_reason TEXT,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Configuration

### Session Validation Interval
```dart
// In SingleSessionAuthService
static const Duration _validationInterval = Duration(minutes: 5);
```

### Graceful Logout Delay
```dart
// In SessionManager
Future.delayed(const Duration(seconds: 3), () {
  _showAdminRevocationDialog(message, revocationInfo);
});
```

## Monitoring and Logging

### Admin Actions
- All device revocations logged
- Reason and timestamp recorded
- Admin user identification
- IP address tracking

### User Experience
- Session validation attempts
- Revocation detection events
- Graceful logout completions
- Error handling events

### Performance Metrics
- Session validation response times
- Device revocation processing time
- User logout completion rates
- Error rates and recovery

## Testing

### Unit Tests
```bash
flutter test test/admin_device_revocation_test.dart
```

### Integration Testing
1. **Admin Dashboard**: Test device revocation flow
2. **Flutter App**: Test graceful logout experience
3. **API Endpoints**: Test revocation and validation
4. **Offline Scenarios**: Test offline device handling

### Manual Testing Scenarios

1. **Basic Revocation**
   - Login user on Device A
   - Revoke Device A from admin dashboard
   - Verify Device A shows admin revocation message

2. **Offline Revocation**
   - Login user on Device A
   - Disconnect Device A from internet
   - Revoke Device A from admin dashboard
   - Reconnect Device A
   - Verify graceful logout with admin message

3. **Multiple Devices**
   - Login user on multiple devices
   - Revoke specific device
   - Verify only target device is affected

## Troubleshooting

### Common Issues

1. **Session Not Invalidating**
   - Check database token revocation status
   - Verify session validation endpoint
   - Check network connectivity

2. **User Not Seeing Logout Dialog**
   - Verify app is in foreground
   - Check notification permissions
   - Verify session manager context

3. **Admin Dashboard Not Loading Devices**
   - Check admin authentication
   - Verify database views are created
   - Check API endpoint responses

### Debug Logging

Enable debug logging to monitor the revocation process:
```dart
// Flutter logs
debugPrint('🚨 Admin revocation detected: $message');

// Backend logs
error_log("ADMIN ACTION: Device revoked by {$adminUsername}");
```

## Future Enhancements

Potential improvements:
1. **Real-time Notifications**: Push notifications for immediate logout
2. **Bulk Operations**: Revoke multiple devices simultaneously
3. **Scheduled Revocations**: Time-based device access control
4. **Device Fingerprinting**: Enhanced device identification
5. **User Notifications**: Email/SMS alerts for device revocations
