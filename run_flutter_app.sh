#!/bin/bash
echo "Starting Flutter app with local IP..."

# Local IP address
LOCAL_IP="***************"
WEB_PORT="9001"

# Check Flutter installation
echo "Checking Flutter installation..."
FLUTTER_PATH=$(which flutter)
if [ -z "$FLUTTER_PATH" ]; then
  echo "Flutter not found in PATH. Trying direct path..."
  FLUTTER_PATH="/Users/<USER>/flutter/bin/flutter"
fi

# Check available devices
echo "Checking available devices..."
$FLUTTER_PATH devices

# Run the app with local IP
echo "Running the Flutter app on $LOCAL_IP:$WEB_PORT..."
$FLUTTER_PATH run --web-renderer html --web-hostname $LOCAL_IP --web-port $WEB_PORT

echo "If the app doesn't start, try running one of these commands manually:"
echo "flutter run --web-renderer html --web-hostname $LOCAL_IP --web-port $WEB_PORT"
echo "flutter run -d chrome"
echo "flutter run -d android"
echo "flutter run -d ios"
