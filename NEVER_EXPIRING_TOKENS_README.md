# Never-Expiring Tokens Implementation

This implementation provides **never-expiring access tokens** that work seamlessly with the admin-only logout system. Users stay logged in indefinitely until an administrator explicitly revokes their access.

## Key Features

### ✅ **Never-Expiring Access**
- Tokens have no expiration date (`exp` field omitted from JWT)
- Users stay logged in indefinitely
- No automatic logout due to token expiration
- Seamless user experience

### ✅ **Admin-Only Revocation**
- Only administrators can force logout
- Complete control over user sessions
- Immediate access termination when needed
- Comprehensive audit trail

### ✅ **Enhanced Security**
- Device fingerprinting support
- Device ID validation
- Admin revocation detection
- Secure token storage

## Technical Implementation

### Backend Changes

#### 1. Login Endpoint (`admin/api/login.php`)

**Never-Expiring Access Token:**
```php
// **Never-expiring access token** - omit 'exp' entirely for unlimited access
$accessTokenPayload = [
    'user_id'       => $user['id'],
    'phone_number'  => $user['phone_number'],
    'name'          => $user['name'],
    'type'          => 'access',
    'iat'           => time(),
    // omit 'exp' entirely for unlimited access
    'extended'      => $extendedSession || $rememberMe,
    'device_id'     => $deviceId
];
$accessToken = generate_jwt($accessTokenPayload, APP_SECRET);
```

**Never-Expiring Refresh Token:**
```php
// **Never-expiring refresh token** - omit 'exp' entirely
$refreshTokenPayload = [
    'user_id'  => $user['id'],
    'type'     => 'refresh',
    'iat'      => time(),
    // omit 'exp' entirely for unlimited access
    'device_id' => $deviceId
];
$refreshToken = generate_jwt($refreshTokenPayload, APP_SECRET . '_refresh');
```

**Database Storage with NULL Expiry:**
```php
// Store refresh token with NULL expiry in database
$stmt = $conn->prepare("
    INSERT INTO api_tokens 
      (user_id, token, device_id, device_fingerprint, expires_at, is_revoked)
    VALUES 
      (?, ?, ?, ?, NULL, 0)
    ON DUPLICATE KEY UPDATE 
      token = VALUES(token),
      device_id = VALUES(device_id),
      device_fingerprint = VALUES(device_fingerprint),
      expires_at = VALUES(expires_at),
      is_revoked = 0,
      last_used_at = NOW()
");
```

#### 2. Session Validation (`admin/api/session_validate.php`)

**Never-Expiring Token Validation:**
```php
// For never-expiring tokens, expires_at will be NULL
// Only check expiry if it's not NULL (for backward compatibility)
if ($tokenInfo['expires_at'] !== null && strtotime($tokenInfo['expires_at']) < time()) {
    returnError('Token has expired', 401);
}
```

**JWT Validation for Never-Expiring Tokens:**
```php
// For never-expiring tokens, don't check 'exp' field
// Only check if 'exp' exists and is valid
if (isset($payload['exp']) && $payload['exp'] < time()) {
    return null; // Token has expired
}
```

**Database Query for Never-Expiring Tokens:**
```php
WHERE t.token = ? AND u.is_active = 1 AND (t.is_revoked = 0 OR t.is_revoked IS NULL)
AND (t.expires_at IS NULL OR t.expires_at > NOW())
```

### Flutter Changes

#### Single Session Auth Service

**Never-Expiring Token Detection:**
```dart
// Check if this is a never-expiring token
final tokenType = responseBody['token_type'];
final expiresAt = responseBody['expires_at'];

if (tokenType == 'never_expiring' || expiresAt == null) {
  debugPrint('🔓 Never-expiring token detected - no expiry validation needed');
} else {
  debugPrint('⏰ Expiring token detected - expiry: $expiresAt');
}
```

## API Response Format

### Login Response
```json
{
  "success": true,
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expires_at": null,
  "extended_session": true,
  "forced_logout": false,
  "session_info": {
    "device_id": "device_abc123",
    "device_fingerprint": "fp_xyz789",
    "login_time": "2024-01-15 10:30:00",
    "session_type": "same_device",
    "previous_device_logout": false,
    "token_expiry": "never"
  },
  "user": {
    "id": 123,
    "name": "John Doe",
    "username": "johndoe",
    "email": "<EMAIL>",
    "phone_number": "+1234567890"
  }
}
```

### Session Validation Response
```json
{
  "success": true,
  "message": "Session is valid",
  "user_id": 123,
  "device_id": "device_abc123",
  "session_id": "session_xyz789",
  "validated_at": "2024-01-15 10:35:00",
  "expires_at": null,
  "token_type": "never_expiring"
}
```

## Database Schema

### api_tokens Table
```sql
CREATE TABLE api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(500) NOT NULL,
    device_id VARCHAR(255),
    device_fingerprint VARCHAR(255),
    expires_at TIMESTAMP NULL, -- NULL for never-expiring tokens
    is_revoked BOOLEAN DEFAULT FALSE,
    revoked_by_admin INT DEFAULT NULL,
    revoked_at TIMESTAMP NULL,
    revocation_reason TEXT,
    last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    INDEX idx_user_token (user_id, token),
    INDEX idx_device_id (device_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_revoked (is_revoked)
);
```

## Benefits

### For Users
- ✅ **Permanent Login**: Never need to re-login due to token expiration
- ✅ **Seamless Experience**: Continuous access without interruptions
- ✅ **No Session Timeouts**: App stays logged in indefinitely
- ✅ **Better UX**: No frustrating re-authentication prompts

### For Administrators
- ✅ **Full Control**: Can revoke access at any time
- ✅ **Immediate Action**: Instant logout when needed
- ✅ **Audit Trail**: Complete logging of all actions
- ✅ **Security**: Maintains security while improving UX

### For System
- ✅ **Reduced Load**: No token refresh operations needed
- ✅ **Simplified Logic**: No expiry calculations required
- ✅ **Better Performance**: Fewer database queries
- ✅ **Maintained Security**: Admin revocation still works

## Security Considerations

### Token Security
- JWT tokens are signed with secure secret keys
- Device ID validation prevents unauthorized access
- Admin revocation provides immediate access termination
- Device fingerprinting adds additional security layer

### Admin Control
- Only administrators can revoke tokens
- Complete audit trail of all revocation actions
- Immediate database updates for revoked tokens
- Clear user notification of admin actions

### Device Management
- Device ID binding prevents multi-device abuse
- Device fingerprinting for additional verification
- Admin can revoke specific device access
- Comprehensive device activity logging

## Migration from Expiring Tokens

### Backward Compatibility
- System supports both expiring and never-expiring tokens
- Existing expiring tokens continue to work
- Gradual migration possible
- No breaking changes for existing users

### Database Migration
```sql
-- Update existing tokens to never-expire (optional)
UPDATE api_tokens SET expires_at = NULL WHERE expires_at IS NOT NULL;

-- Add new columns for enhanced features
ALTER TABLE api_tokens 
ADD COLUMN device_fingerprint VARCHAR(255) AFTER device_id,
ADD COLUMN is_revoked BOOLEAN DEFAULT FALSE AFTER expires_at,
ADD COLUMN revoked_by_admin INT DEFAULT NULL AFTER is_revoked,
ADD COLUMN revoked_at TIMESTAMP NULL AFTER revoked_by_admin,
ADD COLUMN revocation_reason TEXT AFTER revoked_at;
```

## Testing

### Never-Expiring Token Tests
```dart
test('Should maintain session with never-expiring token', () async {
  // Login and get never-expiring token
  final loginResponse = await loginUser();
  final token = loginResponse['access_token'];
  
  // Validate session multiple times
  for (int i = 0; i < 10; i++) {
    final result = await validateSession(token);
    expect(result, equals(SessionValidationResult.valid));
  }
});
```

### Admin Revocation Tests
```dart
test('Should logout only on admin revocation', () async {
  // Login with never-expiring token
  final token = await getNeverExpiringToken();
  
  // Admin revokes access
  await adminRevokeDevice(userId, deviceId);
  
  // Next validation should trigger logout
  final result = await validateSession(token);
  expect(result, equals(SessionValidationResult.invalidated));
});
```

## Configuration

### Environment Variables
```php
// In config.php
define('APP_SECRET', 'your-secure-secret-key');
define('JWT_ALGORITHM', 'HS256');
define('DEV_MODE', true); // For development logging
```

### Flutter Configuration
```dart
// In app_config.dart
class AppConfig {
  static const String defaultApiBaseUrl = 'https://your-api.com/admin/api';
  static const Duration sessionValidationInterval = Duration(minutes: 5);
  static const bool enableNeverExpiringTokens = true;
}
```

## Monitoring and Logging

### Token Generation Logs
```
=== NEVER-EXPIRING TOKEN GENERATION ===
User ID: 123
User Name: John Doe
Current time: 1705312200 (2024-01-15 10:30:00)
Token type: NEVER-EXPIRING (no expiry set)
Device ID: device_abc123
Device Fingerprint: fp_xyz789
Extended Session: true
Remember Me: true
=== END NEVER-EXPIRING TOKEN GENERATION ===
```

### Session Validation Logs
```
✅ Session validated successfully
🔓 Never-expiring token detected - no expiry validation needed
```

### Admin Revocation Logs
```
🚨 Admin revocation detected (403), invalidating session
```

## Future Enhancements

1. **Multi-Device Support**: Allow limited concurrent sessions per user
2. **Token Rotation**: Periodic token refresh for enhanced security
3. **Advanced Analytics**: Track session patterns and usage statistics
4. **Push Notifications**: Real-time admin action notifications
5. **Device Management UI**: User interface for device management

## Conclusion

The never-expiring token system provides an excellent user experience while maintaining full administrative control. Users enjoy seamless, uninterrupted access while administrators retain complete control over user sessions through the admin-only logout system. 