#!/bin/bash

# Script to create a complete backup of the KFT Fitness application
# This script creates backups of both the backend code and the database

# Set variables
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"
BACKEND_DIR="./admin"
BACKEND_BACKUP_FILENAME="kft_backend_${TIMESTAMP}.zip"
DB_BACKUP_FILENAME="kft_fitness_db_${TIMESTAMP}.sql"
ALL_IN_ONE_BACKUP="kft_complete_backup_${TIMESTAMP}.zip"

# Database credentials from config.php
DB_HOST="localhost"
DB_USER="root"
DB_PASS=""
DB_NAME="kft_fitness"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "=== Starting KFT Fitness Complete Backup Process ==="
echo "Timestamp: $(date)"
echo "----------------------------------------"

# Step 1: Backup the backend code
echo "Step 1: Backing up backend code..."

# Check if the backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
    echo "Error: Backend directory '$BACKEND_DIR' not found!"
    exit 1
fi

# Create zip archive of the backend code
echo "Creating zip archive of backend code..."
zip -r "$BACKUP_DIR/$BACKEND_BACKUP_FILENAME" "$BACKEND_DIR" -x "*/node_modules/*" -x "*/vendor/*" -x "*/.git/*" -x "*/tmp/*" -x "*/cache/*" -x "*/logs/*"

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Backend code backup completed successfully!"
    echo "Backup saved to: $BACKUP_DIR/$BACKEND_BACKUP_FILENAME"
else
    echo "Error: Failed to create backend code backup!"
    exit 1
fi

echo "----------------------------------------"

# Step 2: Backup the database
echo "Step 2: Backing up database..."

# Check if mysqldump is available
if ! command -v mysqldump &> /dev/null; then
    echo "Error: mysqldump command not found. Please make sure MySQL is installed."
    exit 1
fi

# Create database backup
echo "Creating database backup..."
if [ -z "$DB_PASS" ]; then
    # If password is empty
    mysqldump -h "$DB_HOST" -u "$DB_USER" "$DB_NAME" > "$BACKUP_DIR/$DB_BACKUP_FILENAME"
else
    # If password is empty
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/$DB_BACKUP_FILENAME"
fi

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Database backup completed successfully!"
    echo "Backup saved to: $BACKUP_DIR/$DB_BACKUP_FILENAME"
else
    echo "Error: Failed to create database backup!"
    exit 1
fi

echo "----------------------------------------"

# Step 3: Create an all-in-one backup
echo "Step 3: Creating all-in-one backup..."

# Create a README file with backup information
README_FILE="$BACKUP_DIR/README.txt"
echo "KFT Fitness Complete Backup" > "$README_FILE"
echo "Created on: $(date)" >> "$README_FILE"
echo "" >> "$README_FILE"
echo "This backup contains:" >> "$README_FILE"
echo "1. Backend code backup: $BACKEND_BACKUP_FILENAME" >> "$README_FILE"
echo "2. Database backup: $DB_BACKUP_FILENAME" >> "$README_FILE"
echo "" >> "$README_FILE"
echo "To restore the database:" >> "$README_FILE"
echo "mysql -u [username] -p [database_name] < $DB_BACKUP_FILENAME" >> "$README_FILE"
echo "" >> "$README_FILE"
echo "To restore the backend code:" >> "$README_FILE"
echo "Unzip $BACKEND_BACKUP_FILENAME to your web server directory" >> "$README_FILE"

# Create the all-in-one backup
cd "$BACKUP_DIR" && zip -r "$ALL_IN_ONE_BACKUP" "$BACKEND_BACKUP_FILENAME" "$DB_BACKUP_FILENAME" "README.txt"

# Check if the all-in-one backup was successful
if [ $? -eq 0 ]; then
    echo "All-in-one backup completed successfully!"
    echo "Backup saved to: $BACKUP_DIR/$ALL_IN_ONE_BACKUP"
    
    # Clean up individual backup files if all-in-one was successful
    echo "Cleaning up individual backup files..."
    rm "$README_FILE"
else
    echo "Error: Failed to create all-in-one backup!"
    exit 1
fi

echo "----------------------------------------"
echo "=== KFT Fitness Complete Backup Process Completed ==="
echo "All-in-one backup: $BACKUP_DIR/$ALL_IN_ONE_BACKUP"
echo "Backup completed on: $(date)"
