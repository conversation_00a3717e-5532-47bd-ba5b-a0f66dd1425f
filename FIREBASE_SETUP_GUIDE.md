# Firebase Cloud Messaging Setup Guide for KFT Fitness App

This guide will help you set up Firebase Cloud Messaging (FCM) for reliable push notifications in the KFT Fitness Flutter app.

## Prerequisites

- Google account
- Flutter development environment
- Android Studio (for Android)
- Xcode (for iOS)
- Access to the KFT Fitness app codebase

## Step 1: Create Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Click "Create a project" or "Add project"
3. Enter project name: `kft-fitness-app` (or your preferred name)
4. Enable Google Analytics (recommended)
5. Choose or create a Google Analytics account
6. Click "Create project"

## Step 2: Add Android App to Firebase

1. In Firebase Console, click "Add app" and select Android
2. Enter the following details:
   - **Android package name**: `com.example.kft`
   - **App nickname**: `KFT Fitness Android`
   - **Debug signing certificate SHA-1**: (optional for development)

3. Click "Register app"
4. Download `google-services.json`
5. Place the file in `android/app/` directory
6. The Firebase SDK dependencies are already added to the project

## Step 3: Add iOS App to Firebase

1. In Firebase Console, click "Add app" and select iOS
2. Enter the following details:
   - **iOS bundle ID**: `com.example.kft`
   - **App nickname**: `KFT Fitness iOS`
   - **App Store ID**: (leave empty for now)

3. Click "Register app"
4. Download `GoogleService-Info.plist`
5. Open `ios/Runner.xcworkspace` in Xcode
6. Right-click on `Runner` folder and select "Add Files to Runner"
7. Select the downloaded `GoogleService-Info.plist` file
8. Make sure "Copy items if needed" is checked
9. Select the `Runner` target and click "Add"

## Step 4: Generate Firebase Configuration

1. Install Firebase CLI:
   ```bash
   npm install -g firebase-tools
   ```

2. Login to Firebase:
   ```bash
   firebase login
   ```

3. Install FlutterFire CLI:
   ```bash
   dart pub global activate flutterfire_cli
   ```

4. Configure Firebase for Flutter:
   ```bash
   cd /path/to/kft-fitness-app
   flutterfire configure
   ```

5. Select your Firebase project
6. Select platforms (Android and iOS)
7. This will generate `lib/firebase_options.dart` with your project configuration

## Step 5: Update Configuration Files

Replace the template values in the generated `lib/firebase_options.dart` with your actual Firebase project values.

### Android Configuration

The `android/app/google-services.json` file should contain your actual project configuration.

### iOS Configuration

The `ios/Runner/GoogleService-Info.plist` file should contain your actual project configuration.

## Step 6: Enable Cloud Messaging

1. In Firebase Console, go to "Cloud Messaging"
2. No additional setup required - FCM is enabled by default

## Step 7: Server-Side Setup

### Database Setup

1. Run the FCM database schema:
   ```sql
   mysql -u your_username -p your_database < server/database/fcm_schema.sql
   ```

### Environment Variables

Add the following to your server environment variables:

```env
FCM_SERVER_KEY=your_fcm_server_key
FIREBASE_PROJECT_ID=your_project_id
```

To get the FCM Server Key:
1. Go to Firebase Console → Project Settings
2. Click on "Cloud Messaging" tab
3. Copy the "Server key" (legacy)

### PHP Dependencies

Install required PHP packages:
```bash
composer require google/auth guzzlehttp/guzzle
```

## Step 8: Test the Implementation

### Test FCM Token Registration

1. Run the app on a device/emulator
2. Check the console logs for FCM token
3. Verify token is stored in the database

### Test Notifications

1. Use the notification settings page in the app
2. Enable workout reminders
3. Use the test notification feature
4. Check if notifications are received

### Test Server-Side Scheduling

1. Set up a cron job to run the scheduler:
   ```bash
   # Add to crontab (crontab -e)
   # Run every minute for testing
   * * * * * php /path/to/server/fcm_notification_scheduler.php?endpoint=schedule-workout
   
   # Run every hour for water reminders
   0 9,12,15,18 * * * php /path/to/server/fcm_notification_scheduler.php?endpoint=schedule-water
   ```

## Step 9: Production Considerations

### Security

1. **Never commit** `google-services.json` or `GoogleService-Info.plist` to version control
2. Use environment variables for sensitive configuration
3. Implement proper authentication for server endpoints
4. Validate all input data on the server

### Performance

1. Implement token cleanup for inactive devices
2. Use topic subscriptions for broadcast messages
3. Monitor notification delivery rates
4. Implement retry logic for failed notifications

### Monitoring

1. Enable Firebase Analytics for notification tracking
2. Monitor FCM quotas and usage
3. Set up alerts for notification failures
4. Track user engagement with notifications

## Troubleshooting

### Common Issues

1. **Notifications not received on Android**:
   - Check if battery optimization is disabled
   - Verify notification permissions are granted
   - Ensure the app is not in doze mode

2. **iOS notifications not working**:
   - Verify APNs certificates are configured
   - Check iOS notification permissions
   - Ensure the app is properly signed

3. **Token registration fails**:
   - Check internet connectivity
   - Verify Firebase configuration
   - Check console logs for errors

4. **Server-side errors**:
   - Verify FCM server key is correct
   - Check database connectivity
   - Validate JSON payload format

### Debug Commands

```bash
# Check FCM token in app logs
flutter logs | grep "FCM:"

# Test server endpoint
curl -X POST "your-server.com/fcm_notification_scheduler.php?endpoint=send-test" \
  -H "Content-Type: application/json" \
  -d '{"token":"your_fcm_token","title":"Test","body":"Test message"}'

# Check database for tokens
mysql -u username -p -e "SELECT * FROM fcm_tokens LIMIT 5;" your_database
```

## Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [FlutterFire Documentation](https://firebase.flutter.dev/)
- [FCM HTTP v1 API](https://firebase.google.com/docs/reference/fcm/rest/v1/projects.messages)
- [Android Notification Channels](https://developer.android.com/training/notify-user/channels)
- [iOS Push Notifications](https://developer.apple.com/documentation/usernotifications)

## Support

If you encounter issues during setup:

1. Check the Firebase Console for error messages
2. Review the Flutter and native platform logs
3. Verify all configuration files are properly placed
4. Test with a simple notification first before implementing complex scheduling

The hybrid notification system will automatically fall back to local notifications if FCM is not available, ensuring your users always receive important reminders.
