#!/bin/bash

# <PERSON>ript to create a backup of the KFT Fitness backend code
# This script creates a zip archive of the backend code

# Set variables
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"
BACKEND_DIR="./admin"
BACKUP_FILENAME="kft_backend_${TIMESTAMP}.zip"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "Starting backend code backup..."

# Check if the backend directory exists
if [ ! -d "$BACKEND_DIR" ]; then
    echo "Error: Backend directory '$BACKEND_DIR' not found!"
    exit 1
fi

# Create zip archive of the backend code
echo "Creating zip archive of backend code..."
zip -r "$BACKUP_DIR/$BACKUP_FILENAME" "$BACKEND_DIR" -x "*/node_modules/*" -x "*/vendor/*" -x "*/.git/*" -x "*/tmp/*" -x "*/cache/*" -x "*/logs/*"

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Backend code backup completed successfully!"
    echo "Backup saved to: $BACKUP_DIR/$BACK<PERSON>_FILENAME"
else
    echo "Error: Failed to create backend code backup!"
    exit 1
fi

echo "Backend code backup process completed."
