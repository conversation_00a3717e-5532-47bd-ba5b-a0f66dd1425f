-- Water Reminders Table Migration
-- This script creates or updates the water_reminders table with admin management features

-- Create water_reminders table if it doesn't exist
CREATE TABLE IF NOT EXISTS `water_reminders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `daily_target` int(11) NOT NULL DEFAULT 2000,
  `reminder_interval` int(11) NOT NULL DEFAULT 120,
  `start_time` time NOT NULL DEFAULT '08:00:00',
  `end_time` time NOT NULL DEFAULT '22:00:00',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `admin_managed` tinyint(1) NOT NULL DEFAULT 0,
  `created_by_admin` int(11) NULL,
  `updated_by_admin` int(11) NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  <PERSON><PERSON><PERSON><PERSON> KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`),
  <PERSON><PERSON>Y `idx_admin_managed` (`admin_managed`),
  KEY `idx_is_enabled` (`is_enabled`),
  CONSTRAINT `fk_water_reminders_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add admin management columns if they don't exist
ALTER TABLE `water_reminders` 
ADD COLUMN IF NOT EXISTS `admin_managed` tinyint(1) NOT NULL DEFAULT 0 AFTER `is_enabled`,
ADD COLUMN IF NOT EXISTS `created_by_admin` int(11) NULL AFTER `admin_managed`,
ADD COLUMN IF NOT EXISTS `updated_by_admin` int(11) NULL AFTER `created_by_admin`;

-- Add indexes for better performance
ALTER TABLE `water_reminders` 
ADD INDEX IF NOT EXISTS `idx_admin_managed` (`admin_managed`),
ADD INDEX IF NOT EXISTS `idx_is_enabled` (`is_enabled`);

-- Create water_intake_logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS `water_intake_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `amount` int(11) NOT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `date` date NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_date` (`user_id`, `date`),
  KEY `idx_timestamp` (`timestamp`),
  CONSTRAINT `fk_water_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Sample data for testing (optional)
-- INSERT IGNORE INTO `water_reminders` (`user_id`, `daily_target`, `reminder_interval`, `start_time`, `end_time`, `is_enabled`, `admin_managed`, `created_by_admin`) 
-- VALUES 
-- (1, 2500, 90, '07:00:00', '23:00:00', 1, 1, 1),
-- (2, 2000, 120, '08:00:00', '22:00:00', 1, 1, 1);

-- Update existing records to set admin_managed = 0 if NULL
UPDATE `water_reminders` SET `admin_managed` = 0 WHERE `admin_managed` IS NULL;

-- Ensure proper data types and constraints
ALTER TABLE `water_reminders` 
MODIFY COLUMN `daily_target` int(11) NOT NULL DEFAULT 2000,
MODIFY COLUMN `reminder_interval` int(11) NOT NULL DEFAULT 120,
MODIFY COLUMN `is_enabled` tinyint(1) NOT NULL DEFAULT 1,
MODIFY COLUMN `admin_managed` tinyint(1) NOT NULL DEFAULT 0;
