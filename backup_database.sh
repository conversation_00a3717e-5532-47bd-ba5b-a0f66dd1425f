#!/bin/bash

# Script to create a backup of the KFT Fitness database
# This script creates a SQL dump of the database

# Set variables
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_DIR="./backups"
BACKUP_FILENAME="kft_fitness_db_${TIMESTAMP}.sql"

# Database credentials from config.php
DB_HOST="localhost"
DB_USER="root"
DB_PASS=""
DB_NAME="kft_fitness"

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

echo "Starting database backup..."

# Check if mysqldump is available
if ! command -v mysqldump &> /dev/null; then
    echo "Error: mysqldump command not found. Please make sure MySQL is installed."
    exit 1
fi

# Create database backup
echo "Creating database backup..."
if [ -z "$DB_PASS" ]; then
    # If password is empty
    mysqldump -h "$DB_HOST" -u "$DB_USER" "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILENAME"
else
    # If password is set
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILENAME"
fi

# Check if the backup was successful
if [ $? -eq 0 ]; then
    echo "Database backup completed successfully!"
    echo "Backup saved to: $BACKUP_DIR/$BACKUP_FILENAME"
    
    # Create a compressed version of the backup
    echo "Compressing database backup..."
    gzip -c "$BACKUP_DIR/$BACKUP_FILENAME" > "$BACKUP_DIR/$BACKUP_FILENAME.gz"
    
    if [ $? -eq 0 ]; then
        echo "Compressed backup saved to: $BACKUP_DIR/$BACKUP_FILENAME.gz"
    else
        echo "Warning: Failed to compress database backup."
    fi
else
    echo "Error: Failed to create database backup!"
    exit 1
fi

echo "Database backup process completed."
