<?php
session_start();
require_once 'includes/auth.php';
require_once 'includes/database.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit;
}

// Check if user is admin
$db = new Database();
$conn = $db->getConnection();

// For development mode, assume the user is an admin
if (defined('DEV_MODE') && DEV_MODE === true) {
    $isAdmin = true;
} else {
    // Try to get from admin_users table first
    $stmt = $conn->prepare("SELECT role FROM admin_users WHERE id = ?");
    $stmt->bind_param("i", $_SESSION['user_id']);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $isAdmin = ($user['role'] === 'admin');
    } else {
        // If not in admin_users, check if user has admin role in session
        $isAdmin = isset($_SESSION['role']) && $_SESSION['role'] === 'admin';
    }
}

if (!$isAdmin) {
    header('Location: index.php');
    exit;
}

// Check if quote ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: quotes.php');
    exit;
}

$quoteId = (int)$_GET['id'];

// Get quote data
$stmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
$stmt->bind_param("i", $quoteId);
$stmt->execute();
$result = $stmt->get_result();
$quote = $result->fetch_assoc();
$stmt->close();

if (!$quote) {
    header('Location: quotes.php');
    exit;
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $quoteText = isset($_POST['quote']) ? trim($_POST['quote']) : '';
    $author = isset($_POST['author']) ? trim($_POST['author']) : '';
    $category = isset($_POST['category']) ? trim($_POST['category']) : '';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate input
    $errors = [];

    if (empty($quoteText)) {
        $errors[] = "Quote text is required.";
    }

    if (empty($errors)) {
        // Update the quote
        $stmt = $conn->prepare("UPDATE motivational_quotes SET quote = ?, author = ?, category = ?, is_active = ? WHERE id = ?");
        $stmt->bind_param("sssii", $quoteText, $author, $category, $isActive, $quoteId);

        if ($stmt->execute()) {
            $successMessage = "Quote updated successfully.";

            // Refresh quote data
            $stmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
            $stmt->bind_param("i", $quoteId);
            $stmt->execute();
            $result = $stmt->get_result();
            $quote = $result->fetch_assoc();
        } else {
            $errors[] = "Failed to update quote: " . $stmt->error;
        }

        $stmt->close();
    }
}

// Get all categories for dropdown
$categoriesStmt = $conn->prepare("SELECT DISTINCT category FROM motivational_quotes WHERE category IS NOT NULL AND category != '' ORDER BY category");
$categoriesStmt->execute();
$categoriesResult = $categoriesStmt->get_result();
$categories = [];

while ($row = $categoriesResult->fetch_assoc()) {
    $categories[] = $row['category'];
}

$categoriesStmt->close();

// Include header
$pageTitle = "Edit Quote";
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Edit Quote</h1>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Quote Details</h6>
                    <div>
                        <a href="quotes.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Quotes
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (isset($errors) && !empty($errors)): ?>
                        <div class="alert alert-danger">
                            <ul class="mb-0">
                                <?php foreach ($errors as $error): ?>
                                    <li><?php echo $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <?php if (isset($successMessage)): ?>
                        <div class="alert alert-success"><?php echo $successMessage; ?></div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="form-group">
                            <label for="quote">Quote Text <span class="text-danger">*</span></label>
                            <textarea name="quote" id="quote" class="form-control" rows="4" required><?php echo htmlspecialchars($quote['quote']); ?></textarea>
                        </div>

                        <div class="form-group">
                            <label for="author">Author</label>
                            <input type="text" name="author" id="author" class="form-control" value="<?php echo htmlspecialchars($quote['author'] ?: ''); ?>">
                            <small class="form-text text-muted">Leave blank for "Unknown" or "Anonymous"</small>
                        </div>

                        <div class="form-group">
                            <label for="category">Category</label>
                            <div class="input-group">
                                <input type="text" name="category" id="category" class="form-control" list="category-list" value="<?php echo htmlspecialchars($quote['category'] ?: ''); ?>">
                                <div class="input-group-append">
                                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Select</button>
                                    <div class="dropdown-menu">
                                        <?php foreach ($categories as $cat): ?>
                                            <a class="dropdown-item" href="#" onclick="document.getElementById('category').value='<?php echo htmlspecialchars($cat); ?>'; return false;">
                                                <?php echo htmlspecialchars(ucfirst($cat)); ?>
                                            </a>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <datalist id="category-list">
                                <?php foreach ($categories as $cat): ?>
                                    <option value="<?php echo htmlspecialchars($cat); ?>">
                                <?php endforeach; ?>
                            </datalist>
                            <small class="form-text text-muted">Enter a new category or select an existing one</small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" <?php echo $quote['is_active'] ? 'checked' : ''; ?>>
                                <label class="custom-control-label" for="is_active">Active</label>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Update Quote</button>
                            <a href="quotes.php" class="btn btn-secondary">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quote Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>ID:</strong> <?php echo $quote['id']; ?>
                    </div>

                    <div class="mb-3">
                        <strong>Created:</strong> <?php echo date('Y-m-d H:i:s', strtotime($quote['created_at'])); ?>
                    </div>

                    <div class="mb-3">
                        <strong>Last Updated:</strong> <?php echo date('Y-m-d H:i:s', strtotime($quote['updated_at'])); ?>
                    </div>

                    <div class="mb-3">
                        <strong>AI Generated:</strong> <?php echo $quote['is_ai_generated'] ? 'Yes' : 'No'; ?>
                    </div>

                    <div class="mb-3">
                        <strong>Status:</strong>
                        <span class="badge badge-<?php echo $quote['is_active'] ? 'success' : 'danger'; ?>">
                            <?php echo $quote['is_active'] ? 'Active' : 'Inactive'; ?>
                        </span>
                    </div>

                    <hr>

                    <div class="text-center">
                        <a href="quotes.php?toggle=<?php echo $quote['id']; ?>" class="btn btn-<?php echo $quote['is_active'] ? 'warning' : 'success'; ?> btn-sm">
                            <i class="fas fa-<?php echo $quote['is_active'] ? 'times' : 'check'; ?>"></i>
                            <?php echo $quote['is_active'] ? 'Deactivate' : 'Activate'; ?>
                        </a>
                        <a href="quotes.php?delete=<?php echo $quote['id']; ?>" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure you want to delete this quote?');">
                            <i class="fas fa-trash"></i> Delete
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
