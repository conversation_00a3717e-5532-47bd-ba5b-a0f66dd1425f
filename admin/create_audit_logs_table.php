<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to create database tables.');
    Utilities::redirect('index.php');
    exit;
}

// Create audit_logs table
$createTableQuery = "
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    staff_id BIGINT UNSIGNED NOT NULL,
    action_type VARCHAR(50) NOT NULL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    affected_user_id BIGINT UNSIGNED NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent VARCHAR(255) NULL,
    FOREIGN KEY (staff_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    FOREIGN KEY (affected_user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
";

// Execute the query
if ($conn->query($createTableQuery)) {
    Utilities::setFlashMessage('success', 'Audit logs table created successfully.');
} else {
    Utilities::setFlashMessage('error', 'Failed to create audit logs table: ' . $conn->error);
}

// Create index for faster queries
$createIndexQuery = "
CREATE INDEX idx_audit_logs_action_type ON audit_logs(action_type);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
";

// Execute the index creation query
if ($conn->multi_query($createIndexQuery)) {
    // Need to process all results to avoid "Commands out of sync" error
    do {
        if ($result = $conn->store_result()) {
            $result->free();
        }
    } while ($conn->more_results() && $conn->next_result());
}

// Redirect back to audit logs page
Utilities::redirect('audit_logs.php');
?>
