<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Test direct video access without authentication
$vimeoId = $_GET['video_id'] ?? '1087487482';

echo json_encode([
    'success' => true,
    'message' => 'Testing video access',
    'video_id' => $vimeoId,
    'test_urls' => [
        'basic_embed' => "https://player.vimeo.com/video/{$vimeoId}",
        'privacy_anywhere' => "https://player.vimeo.com/video/{$vimeoId}?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1",
        'with_domain' => "https://player.vimeo.com/video/{$vimeoId}?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&domain=com.kft.fitness",
        'with_referrer' => "https://player.vimeo.com/video/{$vimeoId}?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&referrer=" . urlencode('https://e7f0-2409-40c0-1028-c574-198e-55f1-fe67-96e.ngrok-free.app')
    ],
    'vimeo_api_test' => testVimeoAPI($vimeoId),
    'recommendations' => [
        'step1' => 'Check if video exists and is accessible',
        'step2' => 'Verify Vimeo privacy settings',
        'step3' => 'Add domain to Vimeo whitelist if needed',
        'step4' => 'Test with different embed parameters'
    ]
]);

function testVimeoAPI($vimeoId) {
    $accessToken = 'e181f678c92844bd2be36504f316fabd';
    
    if (!$accessToken) {
        return [
            'success' => false,
            'message' => 'No Vimeo access token configured'
        ];
    }
    
    $url = "https://api.vimeo.com/videos/{$vimeoId}";
    $headers = [
        "Authorization: Bearer {$accessToken}",
        "Content-Type: application/json"
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'message' => 'CURL error: ' . $error
        ];
    }
    
    if ($httpCode === 200 && $response) {
        $data = json_decode($response, true);
        return [
            'success' => true,
            'http_code' => $httpCode,
            'video_info' => [
                'title' => $data['name'] ?? 'Unknown',
                'privacy_view' => $data['privacy']['view'] ?? 'unknown',
                'privacy_embed' => $data['privacy']['embed'] ?? 'unknown',
                'embed_domains' => $data['privacy']['embed_domains'] ?? [],
                'status' => $data['status'] ?? 'unknown',
                'duration' => $data['duration'] ?? 0,
                'owner' => $data['user']['name'] ?? 'unknown'
            ]
        ];
    } else {
        return [
            'success' => false,
            'http_code' => $httpCode,
            'message' => 'API request failed',
            'response' => $response ? json_decode($response, true) : null
        ];
    }
}
?>
