<?php
/**
 * Disable Development Mode
 *
 * This script disables development mode in the database settings.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/settings.php';

// Initialize settings manager
$settingsManager = Settings::getInstance();

// Update the setting in the database
$result = $settingsManager->set('is_dev_mode', 'false');

// Display the result
echo "<h1>Development Mode Status</h1>";

if ($result) {
    echo "<p style='color:green'>✓ Development mode has been disabled successfully.</p>";
} else {
    echo "<p style='color:red'>✗ Failed to disable development mode.</p>";
}

// Show current settings
echo "<h2>Current Settings</h2>";
echo "<p>DEV_MODE constant: " . (defined('DEV_MODE') && DEV_MODE ? "true" : "false") . "</p>";
echo "<p>is_dev_mode database setting: " . ($settingsManager->isEnabled('is_dev_mode') ? "true" : "false") . "</p>";

// Show instructions
echo "<h2>Next Steps</h2>";
echo "<p>The development mode has been disabled. You should now:</p>";
echo "<ol>";
echo "<li>Verify that the development mode banner no longer appears in the admin panel</li>";
echo "<li>Check that the default credentials message no longer appears on the login page</li>";
echo "<li>Delete this file (disable_dev_mode.php) for security reasons</li>";
echo "</ol>";

// Add a link to go back to the admin panel
echo "<p><a href='index.php'>Return to Admin Panel</a></p>";
?>
