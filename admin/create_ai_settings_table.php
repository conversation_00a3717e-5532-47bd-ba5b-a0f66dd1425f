<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Create ai_settings table
$createAiSettingsTable = "
CREATE TABLE IF NOT EXISTS `ai_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the query
try {
    if ($conn->query($createAiSettingsTable)) {
        echo "Table 'ai_settings' created successfully.<br>";
        
        // Insert default settings
        $insertDefaultSettings = "
        INSERT INTO `ai_settings` (`setting_key`, `setting_value`) VALUES
        ('openai_api_key', ''),
        ('openai_model', 'gpt-4-vision-preview'),
        ('calorie_analysis_enabled', '1'),
        ('calorie_analysis_prompt', 'Analyze this food image and provide the following information in JSON format: {\"food_name\": \"name of the food\", \"calories\": estimated calories per serving, \"protein\": estimated protein in grams, \"carbs\": estimated carbs in grams, \"fat\": estimated fat in grams, \"serving_size\": \"standard serving size\"}. Be as accurate as possible with your estimates.')
        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value);
        ";

        if ($conn->query($insertDefaultSettings)) {
            echo "Default AI settings inserted successfully.<br>";
        } else {
            echo "Error inserting default AI settings: " . $conn->error . "<br>";
        }
        
        echo "AI settings table setup completed.";
    } else {
        echo "Error creating table 'ai_settings': " . $conn->error . "<br>";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
?>
