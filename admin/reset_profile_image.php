<?php
header("Content-Type: application/json");
require_once "includes/config.php";
require_once "includes/database.php";

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user ID is provided
if (!isset($_GET["id"]) || !is_numeric($_GET["id"])) {
    echo json_encode(["success" => false, "error" => "Invalid user ID"]);
    exit;
}

$userId = (int)$_GET["id"];

// Update user's profile image URL to NULL
$updateQuery = "UPDATE users SET profile_image_url = NULL WHERE id = ?";
$stmt = $conn->prepare($updateQuery);
$stmt->bind_param("i", $userId);

if ($stmt->execute()) {
    echo json_encode(["success" => true]);
} else {
    echo json_encode(["success" => false, "error" => "Database error: " . $stmt->error]);
}
?>