<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';
require_once 'includes/audit_logger.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    Utilities::setFlashMessage('error', 'You must be logged in to access this page.');
    Utilities::redirect('login.php');
    exit;
}

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isStaff = $auth->hasRole('staff');
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');

// Check if user has permission to enroll users in courses
if (!$isAdmin && !$isStaff && !$auth->hasPermission('manage_courses')) {
    Utilities::setFlashMessage('error', 'You do not have permission to enroll users in courses.');
    Utilities::redirect('index.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;
    $startDate = isset($_POST['start_date']) ? Utilities::sanitizeInput($_POST['start_date']) : date('Y-m-d');
    $status = isset($_POST['status']) ? Utilities::sanitizeInput($_POST['status']) : 'active';
    $expiryDate = isset($_POST['expiry_date']) ? Utilities::sanitizeInput($_POST['expiry_date']) : null;

    $errors = [];

    // Validate user ID
    if ($userId <= 0) {
        $errors[] = 'Invalid user ID.';
    }

    // Validate course ID
    if ($courseId <= 0) {
        $errors[] = 'Invalid course ID.';
    }

    // Validate start date
    if (!Utilities::validateDate($startDate)) {
        $errors[] = 'Invalid start date.';
    }

    // Validate status
    if (!in_array($status, ['active', 'pending', 'completed', 'cancelled'])) {
        $errors[] = 'Invalid status.';
    }

    if (empty($errors)) {
        // Check if user exists
        $userQuery = "SELECT id, name FROM users WHERE id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();

        // Check if course exists
        $courseQuery = "SELECT id, title, duration_weeks FROM courses WHERE id = ?";
        $courseStmt = $conn->prepare($courseQuery);
        $courseStmt->bind_param("i", $courseId);
        $courseStmt->execute();
        $courseResult = $courseStmt->get_result();

        if ($userResult->num_rows === 0) {
            $errors[] = 'User not found.';
        } elseif ($courseResult->num_rows === 0) {
            $errors[] = 'Course not found.';
        } else {
            $user = $userResult->fetch_assoc();
            $course = $courseResult->fetch_assoc();

            // Check if user is already enrolled in this course
            $checkQuery = "SELECT id FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bind_param("ii", $userId, $courseId);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();

            if ($checkResult->num_rows > 0) {
                $errors[] = 'User is already enrolled in this course.';
            } else {
                // Calculate end date based on course duration
                $courseDuration = $course['duration_weeks'];
                $endDate = $expiryDate && Utilities::validateDate($expiryDate) ? 
                          $expiryDate : 
                          date('Y-m-d', strtotime($startDate . " +{$courseDuration} weeks"));

                // Enroll user in course
                $insertQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                                VALUES (?, ?, CURDATE(), ?, ?, ?)";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param("iisss", $userId, $courseId, $startDate, $endDate, $status);

                if ($insertStmt->execute()) {
                    // Unlock first week videos
                    $videoQuery = "SELECT id FROM course_videos WHERE course_id = ? AND week_number = 1";
                    $videoStmt = $conn->prepare($videoQuery);
                    $videoStmt->bind_param("i", $courseId);
                    $videoStmt->execute();
                    $videoResult = $videoStmt->get_result();

                    while ($video = $videoResult->fetch_assoc()) {
                        $unlockQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                                       VALUES (?, ?, 1, CURDATE())";
                        $unlockStmt = $conn->prepare($unlockQuery);
                        $unlockStmt->bind_param("ii", $userId, $video['id']);
                        $unlockStmt->execute();
                    }

                    // Log the activity in audit logs
                    $details = "Enrolled user {$user['name']} in course {$course['title']} starting {$startDate} until {$endDate}";
                    logStaffActivity($conn, $currentAdminId, 'course_enroll', $userId, $details);

                    Utilities::setFlashMessage('success', 'User has been enrolled in the course successfully.');
                } else {
                    $errors[] = 'Failed to enroll user in course: ' . $conn->error;
                }
            }
        }
    }

    if (!empty($errors)) {
        foreach ($errors as $error) {
            Utilities::setFlashMessage('error', $error);
        }
    }

    // Redirect back to user edit page
    Utilities::redirect('user_edit.php?id=' . $userId);
    exit;
} else {
    // If not a POST request, redirect to users page
    Utilities::redirect('users.php');
    exit;
}
