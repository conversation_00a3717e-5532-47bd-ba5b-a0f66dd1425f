-- Device Management Schema for Admin Dashboard
-- This file adds tables for device management and admin action logging

USE kft_fitness;

-- Add device_id column to users table if it doesn't exist
-- Check if device_id column exists, if not add it
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'users'
     AND COLUMN_NAME = 'device_id') = 0,
    'ALTER TABLE users ADD COLUMN device_id VARCHAR(255) DEFAULT NULL',
    'SELECT "device_id column already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add index for device_id if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'users'
     AND INDEX_NAME = 'idx_device_id') = 0,
    'ALTER TABLE users ADD INDEX idx_device_id (device_id)',
    'SELECT "idx_device_id index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Admin action logs table for audit trail
CREATE TABLE IF NOT EXISTS admin_action_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_user_id INT NOT NULL,
    admin_username VARCHAR(50) NOT NULL,
    action_type ENUM('device_revoke', 'user_deactivate', 'user_activate', 'token_invalidate', 'session_terminate') NOT NULL,
    target_user_id INT NOT NULL,
    target_username VARCHAR(50),
    target_device_id VARCHAR(255),
    action_details JSON,
    reason TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_user (admin_user_id),
    INDEX idx_target_user (target_user_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (admin_user_id) REFERENCES admin_users(id) ON DELETE CASCADE,
    FOREIGN KEY (target_user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Device sessions table to track active device sessions
CREATE TABLE IF NOT EXISTS device_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    device_info JSON,
    first_login TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    revoked_by_admin INT DEFAULT NULL,
    revoked_at TIMESTAMP NULL,
    revocation_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_device (user_id, device_id),
    INDEX idx_user_id (user_id),
    INDEX idx_device_id (device_id),
    INDEX idx_is_active (is_active),
    INDEX idx_last_activity (last_activity),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (revoked_by_admin) REFERENCES admin_users(id) ON DELETE SET NULL
);

-- Enhanced API tokens table with device tracking
-- Add device_id column to api_tokens if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND COLUMN_NAME = 'device_id') = 0,
    'ALTER TABLE api_tokens ADD COLUMN device_id VARCHAR(255) DEFAULT NULL',
    'SELECT "device_id column already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add last_used column to api_tokens if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND COLUMN_NAME = 'last_used') = 0,
    'ALTER TABLE api_tokens ADD COLUMN last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT "last_used column already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add is_revoked column to api_tokens if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND COLUMN_NAME = 'is_revoked') = 0,
    'ALTER TABLE api_tokens ADD COLUMN is_revoked BOOLEAN DEFAULT FALSE',
    'SELECT "is_revoked column already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add revoked_by_admin column to api_tokens if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND COLUMN_NAME = 'revoked_by_admin') = 0,
    'ALTER TABLE api_tokens ADD COLUMN revoked_by_admin INT DEFAULT NULL',
    'SELECT "revoked_by_admin column already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add revoked_at column to api_tokens if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND COLUMN_NAME = 'revoked_at') = 0,
    'ALTER TABLE api_tokens ADD COLUMN revoked_at TIMESTAMP NULL',
    'SELECT "revoked_at column already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for api_tokens
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND INDEX_NAME = 'idx_device_id') = 0,
    'ALTER TABLE api_tokens ADD INDEX idx_device_id (device_id)',
    'SELECT "idx_device_id index already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND INDEX_NAME = 'idx_is_revoked') = 0,
    'ALTER TABLE api_tokens ADD INDEX idx_is_revoked (is_revoked)',
    'SELECT "idx_is_revoked index already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND INDEX_NAME = 'idx_last_used') = 0,
    'ALTER TABLE api_tokens ADD INDEX idx_last_used (last_used)',
    'SELECT "idx_last_used index already exists in api_tokens"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add foreign key for revoked_by_admin if it doesn't exist
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
     WHERE TABLE_SCHEMA = 'kft_fitness' 
     AND TABLE_NAME = 'api_tokens' 
     AND CONSTRAINT_NAME = 'fk_api_tokens_revoked_by_admin') = 0,
    'ALTER TABLE api_tokens ADD CONSTRAINT fk_api_tokens_revoked_by_admin FOREIGN KEY (revoked_by_admin) REFERENCES admin_users(id) ON DELETE SET NULL',
    'SELECT "Foreign key already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create view for active device sessions
CREATE OR REPLACE VIEW active_device_sessions AS
SELECT 
    ds.id,
    ds.user_id,
    u.name as user_name,
    u.username,
    u.phone_number,
    ds.device_id,
    ds.device_info,
    ds.first_login,
    ds.last_activity,
    ds.is_active,
    ds.revoked_by_admin,
    ds.revoked_at,
    ds.revocation_reason,
    au.username as revoked_by_username,
    COUNT(at.id) as active_tokens
FROM device_sessions ds
JOIN users u ON ds.user_id = u.id
LEFT JOIN admin_users au ON ds.revoked_by_admin = au.id
LEFT JOIN api_tokens at ON ds.user_id = at.user_id AND ds.device_id = at.device_id AND at.is_revoked = FALSE
WHERE ds.is_active = TRUE
GROUP BY ds.id, ds.user_id, u.name, u.username, u.phone_number, ds.device_id, ds.device_info, 
         ds.first_login, ds.last_activity, ds.is_active, ds.revoked_by_admin, ds.revoked_at, 
         ds.revocation_reason, au.username;

-- Insert existing user devices into device_sessions table
INSERT IGNORE INTO device_sessions (user_id, device_id, device_info, first_login, last_activity)
SELECT 
    id as user_id,
    device_id,
    JSON_OBJECT('platform', 'unknown', 'first_seen', created_at) as device_info,
    COALESCE(last_login, created_at) as first_login,
    COALESCE(last_login, created_at) as last_activity
FROM users 
WHERE device_id IS NOT NULL AND device_id != '';

-- Update api_tokens with device_id from users table
UPDATE api_tokens at
JOIN users u ON at.user_id = u.id
SET at.device_id = u.device_id
WHERE at.device_id IS NULL AND u.device_id IS NOT NULL;

-- Create indexes for better performance
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'users'
     AND INDEX_NAME = 'idx_users_device_id_active') = 0,
    'CREATE INDEX idx_users_device_id_active ON users(device_id, is_active)',
    'SELECT "idx_users_device_id_active index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'api_tokens'
     AND INDEX_NAME = 'idx_api_tokens_user_device') = 0,
    'CREATE INDEX idx_api_tokens_user_device ON api_tokens(user_id, device_id, is_revoked)',
    'SELECT "idx_api_tokens_user_device index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE TABLE_SCHEMA = 'kft_fitness'
     AND TABLE_NAME = 'device_sessions'
     AND INDEX_NAME = 'idx_device_sessions_user_active') = 0,
    'CREATE INDEX idx_device_sessions_user_active ON device_sessions(user_id, is_active)',
    'SELECT "idx_device_sessions_user_active index already exists"'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
