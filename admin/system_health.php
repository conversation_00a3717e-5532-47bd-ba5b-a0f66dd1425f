<?php
require_once 'includes/header.php';

// Check if user has appropriate permissions
if (!$auth->hasRole('super_admin') && !$auth->hasRole('admin') && !hasPermission('view_system_health')) {
    Utilities::setFlashMessage('error', 'You do not have permission to view system health.');
    Utilities::redirect('index.php');
    exit;
}

// Get server information
$serverInfo = [
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'server_os' => PHP_OS,
    'memory_limit' => ini_get('memory_limit'),
    'max_execution_time' => ini_get('max_execution_time') . ' seconds',
    'upload_max_filesize' => ini_get('upload_max_filesize'),
    'post_max_size' => ini_get('post_max_size')
];

// Get database information
$db = new Database();
$conn = $db->getConnection();
$dbInfo = [];

if ($conn) {
    $dbInfo['server_version'] = $conn->server_info;
    $dbInfo['client_version'] = $conn->client_info;
    $dbInfo['host_info'] = $conn->host_info;
    $dbInfo['protocol_version'] = $conn->protocol_version;
    
    // Get database size
    $query = "SELECT table_schema AS 'database', 
              ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS 'size_mb' 
              FROM information_schema.tables 
              WHERE table_schema = '" . DB_NAME . "'
              GROUP BY table_schema";
    $result = $conn->query($query);
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $dbInfo['size'] = $row['size_mb'] . ' MB';
    } else {
        $dbInfo['size'] = 'Unknown';
    }
    
    // Get table count
    $query = "SELECT COUNT(*) AS table_count FROM information_schema.tables WHERE table_schema = '" . DB_NAME . "'";
    $result = $conn->query($query);
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $dbInfo['table_count'] = $row['table_count'];
    } else {
        $dbInfo['table_count'] = 'Unknown';
    }
}

// Simulate system health metrics
$systemHealth = [
    'cpu_usage' => rand(10, 80),
    'memory_usage' => rand(30, 90),
    'disk_usage' => rand(40, 85),
    'network_traffic' => rand(5, 60),
    'response_time' => rand(50, 500) / 1000, // in seconds
    'uptime' => rand(1, 30) . ' days ' . rand(1, 23) . ' hours ' . rand(1, 59) . ' minutes'
];

// Simulate recent errors
$recentErrors = [
    [
        'timestamp' => date('Y-m-d H:i:s', strtotime('-2 hours')),
        'type' => 'PHP Warning',
        'message' => 'Division by zero in /var/www/html/admin/reports.php on line 156',
        'count' => 3
    ],
    [
        'timestamp' => date('Y-m-d H:i:s', strtotime('-5 hours')),
        'type' => 'Database Error',
        'message' => 'MySQL server has gone away',
        'count' => 1
    ],
    [
        'timestamp' => date('Y-m-d H:i:s', strtotime('-1 day')),
        'type' => 'PHP Notice',
        'message' => 'Undefined variable: user in /var/www/html/admin/user_view.php on line 78',
        'count' => 12
    ]
];

// Function to determine status color based on value
function getStatusColor($value, $thresholds = [70, 90]) {
    if ($value < $thresholds[0]) {
        return 'success';
    } elseif ($value < $thresholds[1]) {
        return 'warning';
    } else {
        return 'danger';
    }
}
?>

<style>
.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <h1 class="h3 mb-0 text-gray-800">System Health</h1>
    <div>
        <button class="btn btn-sm btn-success dashboard-green-btn" id="refreshHealth">
            <i class="fas fa-sync me-2"></i> Refresh Data
        </button>
        <button class="btn btn-sm btn-success dashboard-green-btn ms-2" id="exportHealth">
            <i class="fas fa-download me-2"></i> Export Report
        </button>
    </div>
</div>

<!-- System Health Overview -->
<div class="row">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow-sm h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">CPU Usage</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $systemHealth['cpu_usage']; ?>%</div>
                        <div class="progress progress-sm mt-2">
                            <div class="progress-bar bg-<?php echo getStatusColor($systemHealth['cpu_usage']); ?>" role="progressbar" style="width: <?php echo $systemHealth['cpu_usage']; ?>%" aria-valuenow="<?php echo $systemHealth['cpu_usage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-microchip fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow-sm h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Memory Usage</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $systemHealth['memory_usage']; ?>%</div>
                        <div class="progress progress-sm mt-2">
                            <div class="progress-bar bg-<?php echo getStatusColor($systemHealth['memory_usage']); ?>" role="progressbar" style="width: <?php echo $systemHealth['memory_usage']; ?>%" aria-valuenow="<?php echo $systemHealth['memory_usage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-memory fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow-sm h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Disk Usage</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $systemHealth['disk_usage']; ?>%</div>
                        <div class="progress progress-sm mt-2">
                            <div class="progress-bar bg-<?php echo getStatusColor($systemHealth['disk_usage']); ?>" role="progressbar" style="width: <?php echo $systemHealth['disk_usage']; ?>%" aria-valuenow="<?php echo $systemHealth['disk_usage']; ?>" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hdd fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow-sm h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Response Time</div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo $systemHealth['response_time']; ?> sec</div>
                        <div class="small text-muted mt-2">System Uptime: <?php echo $systemHealth['uptime']; ?></div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tachometer-alt fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Server Information -->
    <div class="col-lg-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Server Information</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="serverInfoDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="serverInfoDropdown">
                        <a class="dropdown-item" href="#">Refresh</a>
                        <a class="dropdown-item" href="#">Export</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th>PHP Version</th>
                                <td><?php echo $serverInfo['php_version']; ?></td>
                            </tr>
                            <tr>
                                <th>Server Software</th>
                                <td><?php echo $serverInfo['server_software']; ?></td>
                            </tr>
                            <tr>
                                <th>Operating System</th>
                                <td><?php echo $serverInfo['server_os']; ?></td>
                            </tr>
                            <tr>
                                <th>Memory Limit</th>
                                <td><?php echo $serverInfo['memory_limit']; ?></td>
                            </tr>
                            <tr>
                                <th>Max Execution Time</th>
                                <td><?php echo $serverInfo['max_execution_time']; ?></td>
                            </tr>
                            <tr>
                                <th>Upload Max Filesize</th>
                                <td><?php echo $serverInfo['upload_max_filesize']; ?></td>
                            </tr>
                            <tr>
                                <th>Post Max Size</th>
                                <td><?php echo $serverInfo['post_max_size']; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Database Information -->
    <div class="col-lg-6">
        <div class="card shadow-sm mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Database Information</h6>
                <div class="dropdown no-arrow">
                    <a class="dropdown-toggle" href="#" role="button" id="dbInfoDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dbInfoDropdown">
                        <a class="dropdown-item" href="#">Refresh</a>
                        <a class="dropdown-item" href="#">Export</a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                            <tr>
                                <th>Database Name</th>
                                <td><?php echo DB_NAME; ?></td>
                            </tr>
                            <tr>
                                <th>Server Version</th>
                                <td><?php echo $dbInfo['server_version'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <th>Client Version</th>
                                <td><?php echo $dbInfo['client_version'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <th>Host Info</th>
                                <td><?php echo $dbInfo['host_info'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <th>Protocol Version</th>
                                <td><?php echo $dbInfo['protocol_version'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <th>Database Size</th>
                                <td><?php echo $dbInfo['size'] ?? 'Unknown'; ?></td>
                            </tr>
                            <tr>
                                <th>Table Count</th>
                                <td><?php echo $dbInfo['table_count'] ?? 'Unknown'; ?></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Errors -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Errors</h6>
            </div>
            <div class="card-body">
                <?php if (!empty($recentErrors)): ?>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Type</th>
                                    <th>Message</th>
                                    <th>Count</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recentErrors as $error): ?>
                                    <tr>
                                        <td><?php echo $error['timestamp']; ?></td>
                                        <td><span class="badge bg-danger"><?php echo $error['type']; ?></span></td>
                                        <td><?php echo $error['message']; ?></td>
                                        <td><?php echo $error['count']; ?></td>
                                        <td>
                                            <button class="btn btn-sm btn-success dashboard-green-btn">View Details</button>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                        <p class="mb-0">No errors have been recorded recently.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Refresh health data
    document.getElementById('refreshHealth').addEventListener('click', function() {
        // Simulate refresh with a loading indicator
        this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
        this.disabled = true;
        
        // Simulate API call delay
        setTimeout(() => {
            location.reload();
        }, 1500);
    });
    
    // Export health report
    document.getElementById('exportHealth').addEventListener('click', function() {
        alert('System health report export functionality will be implemented here.');
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
