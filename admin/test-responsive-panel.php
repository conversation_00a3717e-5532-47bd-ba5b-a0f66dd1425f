<?php
/**
 * Test page for the responsive admin panel
 * This page demonstrates the new responsive admin panel functionality
 */

// Include authentication and configuration
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize authentication
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Set page title
$pageTitle = 'Responsive Admin Panel Test';

// Include header
require_once 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Responsive Admin Panel Test
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Testing Instructions</h6>
                    <ul class="mb-0">
                        <li><strong>Desktop (992px+):</strong> Sidebar should be visible by default</li>
                        <li><strong>Tablet (768px-991px):</strong> Sidebar hidden by default, toggle with hamburger menu</li>
                        <li><strong>Mobile (< 768px):</strong> Sidebar hidden by default, full-width overlay when opened</li>
                        <li><strong>Keyboard:</strong> Press Alt+S to toggle sidebar, ESC to close on mobile</li>
                    </ul>
                </div>

                <div class="row g-4">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-desktop me-2"></i>
                                    Desktop Features
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Always visible sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Collapsible to icon-only mode</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Smooth transitions</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Content adjusts automatically</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-tablet-alt me-2"></i>
                                    Mobile & Tablet Features
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Hidden by default</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Overlay when opened</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Touch-friendly navigation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-close after navigation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Navigation Options Available:</h6>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-tachometer-alt me-3 text-primary"></i>
                                <span>Overview Dashboard</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-users-cog me-3 text-primary"></i>
                                <span>Staff Management</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-cogs me-3 text-primary"></i>
                                <span>Platform Settings</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-graduation-cap me-3 text-primary"></i>
                                <span>All Courses</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-user-plus me-3 text-primary"></i>
                                <span>Assign Courses</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-users me-3 text-primary"></i>
                                <span>User Management</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-plus-circle me-3 text-primary"></i>
                                <span>Create Course</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex align-items-center p-2 border rounded">
                                <i class="fas fa-sign-out-alt me-3 text-danger"></i>
                                <span>Sign Out</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 p-3 bg-success bg-opacity-10 border border-success rounded">
                    <h6 class="text-success">
                        <i class="fas fa-clock me-2"></i>
                        Session Timeout Updated
                    </h6>
                    <p class="mb-0">Dashboard inactive sign out time has been updated to <strong>5 hours</strong> (previously 24 hours).</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test script to demonstrate panel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Add some test functionality
    console.log('Responsive Admin Panel Test Page Loaded');
    
    // Log current screen size
    function logScreenSize() {
        const width = window.innerWidth;
        let device = 'Desktop';
        if (width <= 767.98) device = 'Mobile';
        else if (width <= 991.98) device = 'Tablet';
        
        console.log(`Current device: ${device} (${width}px)`);
    }
    
    logScreenSize();
    window.addEventListener('resize', logScreenSize);
});
</script>

<?php require_once 'includes/footer.php'; ?>
