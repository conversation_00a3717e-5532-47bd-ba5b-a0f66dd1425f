<?php
// Run this script once to add the 'category' column to the 'courses' table if it does not exist
require_once __DIR__ . '/../api/config.php'; // Uses getDbConnection()

$conn = getDbConnection();

// Check if 'category' column exists
$checkColumn = $conn->query("SHOW COLUMNS FROM courses LIKE 'category'");
if ($checkColumn && $checkColumn->num_rows === 0) {
    $alter = $conn->query("ALTER TABLE courses ADD COLUMN category VARCHAR(255) DEFAULT NULL");
    if ($alter) {
        echo "Column 'category' added successfully.\n";
    } else {
        echo "Error adding column: " . $conn->error . "\n";
    }
} else {
    echo "Column 'category' already exists.\n";
}

$conn->close(); 