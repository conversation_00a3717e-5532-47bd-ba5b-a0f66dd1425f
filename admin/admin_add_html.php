<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="mt-4 mb-1 display-6 fw-bold">Add Staff Member</h1>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item"><a href="staff_management.php">Staff Management</a></li>
                <li class="breadcrumb-item active">Add Staff Member</li>
            </ol>
        </div>
        <div class="d-none d-md-block">
            <img src="assets/img/staff-add-illustration.svg" alt="Add Staff" class="img-fluid" style="height: 100px;" onerror="this.style.display='none'">
        </div>
    </div>

    <?php if (!$isActiveColumnExists): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <div class="d-flex">
            <div class="me-3">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
            </div>
            <div>
                <strong>Database Update Required!</strong> The <code>is_active</code> column is missing from the admin_users table.
                <a href="alter_admin_users_add_is_active.php" class="alert-link">Click here to add it</a>.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="admin-form-container">
        <div class="admin-form-card mb-4">
            <div class="admin-form-header">
                <h5 class="card-title"><i class="fas fa-user-plus"></i> Create New Staff Account</h5>
            </div>
            <div class="card-body p-4">
                <form method="post" action="" id="staffForm" class="needs-validation" novalidate>
                <!-- Basic Information Section -->
                <div class="admin-form-section mb-4">
                    <div class="admin-form-section-header">
                        <i class="fas fa-id-card"></i>
                        <h5>Basic Information</h5>
                    </div>
                    <div class="admin-form-section-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="username" class="admin-form-label">Username <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input class="form-control admin-form-control" id="username" name="username" type="text" placeholder="Enter username" required value="<?php echo isset($username) ? htmlspecialchars($username) : ''; ?>" />
                                    <div class="invalid-feedback">Please enter a username.</div>
                                </div>
                                <div class="admin-form-hint">
                                    <i class="fas fa-info-circle me-1"></i> Must be at least 4 characters long and cannot contain spaces
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="email" class="admin-form-label">Email Address <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input class="form-control admin-form-control" id="email" name="email" type="email" placeholder="Enter email" required value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" />
                                    <div class="invalid-feedback">Please enter a valid email address.</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="name" class="admin-form-label">Full Name <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-id-badge"></i></span>
                                    <input class="form-control admin-form-control" id="name" name="name" type="text" placeholder="Enter full name" required value="<?php echo isset($name) ? htmlspecialchars($name) : ''; ?>" />
                                    <div class="invalid-feedback">Please enter the full name.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="phone" class="admin-form-label">Phone Number <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                    <input class="form-control admin-form-control" id="phone" name="phone" type="text" placeholder="Enter phone number" required value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>" />
                                    <div class="invalid-feedback">Please enter a phone number.</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Account Settings Section -->
                <div class="admin-form-section mb-4">
                    <div class="admin-form-section-header">
                        <i class="fas fa-user-shield"></i>
                        <h5>Account Settings</h5>
                    </div>
                    <div class="admin-form-section-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="role" class="admin-form-label">Role <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-user-tag"></i></span>
                                    <select class="form-select admin-form-control" id="role" name="role" required>
                                        <option value="staff" selected>Staff</option>
                                        <option value="manager">Manager</option>
                                        <?php if ($auth->hasRole('super_admin')): ?>
                                        <option value="super_admin">Super Admin</option>
                                        <?php endif; ?>
                                    </select>
                                    <div class="invalid-feedback">Please select a role.</div>
                                </div>
                                <div class="admin-form-hint">
                                    <i class="fas fa-info-circle me-1"></i>
                                    <span class="admin-tooltip">
                                        Different roles have different access levels
                                        <span class="admin-tooltip-text">
                                            Staff: Basic user management<br>
                                            Manager: Staff oversight<br>
                                            Super Admin: Full system access
                                        </span>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="parent-admin-container">
                                    <label for="parent_admin_id" class="admin-form-label">Parent Admin <span class="text-danger staff-required">*</span></label>
                                    <div class="input-group mb-2">
                                        <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
                                        <select class="form-select admin-form-control" id="parent_admin_id" name="parent_admin_id">
                                            <option value="">Select Parent Admin</option>
                                            <?php foreach ($superAdmins as $admin): ?>
                                            <option value="<?php echo $admin['id']; ?>" <?php echo (isset($parentAdminId) && $parentAdminId == $admin['id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($admin['name'] . ' (' . $admin['username'] . ')'); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a parent admin.</div>
                                    </div>
                                    <div class="admin-form-hint">
                                        <i class="fas fa-info-circle me-1"></i> Required for staff members only
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="password" class="admin-form-label">Password <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input class="form-control admin-form-control" id="password" name="password" type="password" placeholder="Enter password" required />
                                    <button class="btn btn-outline-secondary toggle-password" type="button" tabindex="-1">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="invalid-feedback">Please enter a password.</div>
                                </div>
                                <div class="admin-form-hint">
                                    <i class="fas fa-info-circle me-1"></i> Must be at least 8 characters with uppercase and number
                                </div>
                                <div class="password-strength mt-2 d-none">
                                    <div class="progress" style="height: 5px;">
                                        <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                    </div>
                                    <small class="password-strength-text mt-1 d-block"></small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="confirm_password" class="admin-form-label">Confirm Password <span class="text-danger">*</span></label>
                                <div class="input-group mb-2">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input class="form-control admin-form-control" id="confirm_password" name="confirm_password" type="password" placeholder="Confirm password" required />
                                    <button class="btn btn-outline-secondary toggle-password" type="button" tabindex="-1">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="invalid-feedback" id="password-match-feedback">Passwords do not match</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Permissions Section -->
                <div class="admin-form-section mb-4">
                    <div class="admin-form-section-header">
                        <i class="fas fa-key"></i>
                        <h5>Permissions</h5>
                    </div>
                    <div class="admin-form-section-body">
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <div class="d-flex align-items-center">
                                <div class="form-check form-switch me-3">
                                    <input class="form-check-input" type="checkbox" id="showAllPermissions">
                                    <label class="form-check-label" for="showAllPermissions">Show all permissions</label>
                                </div>
                                <div class="admin-form-hint d-inline-block">
                                    <i class="fas fa-info-circle"></i> Toggle to show/hide detailed permissions
                                </div>
                            </div>
                            <div class="permission-actions">
                                <button type="button" class="btn btn-sm btn-success me-2" id="selectBasicPermissions">
                                    <i class="fas fa-check-circle me-1"></i> Basic Staff Permissions
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllPermissions">
                                    <i class="fas fa-check-double me-1"></i> Select All
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="deselectAllPermissions">
                                    <i class="fas fa-times me-1"></i> Deselect All
                                </button>
                            </div>
                        </div>

                        <div id="basicPermissionsAlert" class="alert alert-success mb-4" style="display: none;">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="fas fa-check-circle fa-2x"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">Basic permissions pre-selected!</h5>
                                    <p class="mb-1">These essential permissions allow staff to manage users from the Flutter app:</p>
                                    <div class="mt-2">
                                        <span class="badge bg-primary me-1">Manage Users</span>
                                        <span class="badge bg-primary me-1">Manage Workouts</span>
                                        <span class="badge bg-primary">View Reports</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="permissions-container">
                            <div class="accordion custom-accordion" id="permissionsAccordion">
                                <?php foreach ($permissionCategories as $categoryKey => $category): ?>
                                    <?php if (!empty($category['permissions'])): ?>
                                    <div class="accordion-item border-0 mb-3">
                                        <h2 class="accordion-header" id="heading<?php echo ucfirst($categoryKey); ?>">
                                            <button class="accordion-button rounded-3 shadow-sm" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo ucfirst($categoryKey); ?>" aria-expanded="true" aria-controls="collapse<?php echo ucfirst($categoryKey); ?>">
                                                <i class="fas fa-folder me-2"></i> <?php echo $category['title']; ?>
                                                <span class="ms-auto badge bg-primary permission-count"><?php echo count($category['permissions']); ?></span>
                                            </button>
                                        </h2>
                                        <div id="collapse<?php echo ucfirst($categoryKey); ?>" class="accordion-collapse collapse show" aria-labelledby="heading<?php echo ucfirst($categoryKey); ?>">
                                            <div class="accordion-body pt-4">
                                                <div class="row">
                                                    <?php foreach ($category['permissions'] as $permission): ?>
                                                    <div class="col-md-4 mb-3">
                                                        <div class="permission-item">
                                                            <div class="form-check">
                                                                <input class="form-check-input permission-checkbox" type="checkbox" id="permission_<?php echo $permission['id']; ?>" name="permissions[]" value="<?php echo $permission['id']; ?>" <?php echo (isset($selectedPermissions) && in_array($permission['id'], $selectedPermissions)) ? 'checked' : ''; ?> />
                                                                <label class="form-check-label" for="permission_<?php echo $permission['id']; ?>">
                                                                    <?php echo htmlspecialchars($permission['name']); ?>
                                                                    <small class="d-block text-muted"><?php echo htmlspecialchars($permission['description']); ?></small>
                                                                </label>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="admin-form-section mb-4">
                    <div class="admin-form-section-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="sendWelcomeEmail" name="send_welcome_email" checked>
                                    <label class="form-check-label" for="sendWelcomeEmail">
                                        Send welcome email with login details
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 text-md-end">
                                <div class="admin-form-hint mb-3">
                                    <i class="fas fa-info-circle me-1"></i> All fields marked with <span class="text-danger">*</span> are required
                                </div>
                            </div>
                        </div>

                        <div class="d-flex flex-column flex-md-row justify-content-between gap-3">
                            <a href="staff_management.php" class="btn btn-lg btn-outline-secondary admin-btn">
                                <i class="fas fa-arrow-left me-2"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-lg btn-primary admin-btn admin-btn-primary">
                                <i class="fas fa-user-plus me-2"></i> Add Staff Member
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Progress indicator for form submission -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 5">
    <div id="submissionToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-primary text-white">
            <i class="fas fa-spinner fa-spin me-2"></i>
            <strong class="me-auto">Processing</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            Creating staff account. Please wait...
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Password visibility toggle
    const togglePasswordButtons = document.querySelectorAll('.toggle-password');
    togglePasswordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
            input.setAttribute('type', type);

            // Toggle icon
            const icon = this.querySelector('i');
            if (type === 'text') {
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Password strength meter
    const passwordInput = document.getElementById('password');
    const passwordStrength = document.querySelector('.password-strength');
    const passwordStrengthBar = passwordStrength.querySelector('.progress-bar');
    const passwordStrengthText = passwordStrength.querySelector('.password-strength-text');

    passwordInput.addEventListener('input', function() {
        const value = this.value;
        if (value.length > 0) {
            passwordStrength.classList.remove('d-none');

            // Calculate strength
            let strength = 0;

            // Length check
            if (value.length >= 8) strength += 25;

            // Uppercase check
            if (/[A-Z]/.test(value)) strength += 25;

            // Lowercase check
            if (/[a-z]/.test(value)) strength += 25;

            // Number check
            if (/[0-9]/.test(value)) strength += 25;

            // Update UI
            passwordStrengthBar.style.width = strength + '%';

            // Set color based on strength
            if (strength < 50) {
                passwordStrengthBar.className = 'progress-bar bg-danger';
                passwordStrengthText.textContent = 'Weak password';
            } else if (strength < 75) {
                passwordStrengthBar.className = 'progress-bar bg-warning';
                passwordStrengthText.textContent = 'Moderate password';
            } else {
                passwordStrengthBar.className = 'progress-bar bg-success';
                passwordStrengthText.textContent = 'Strong password';
            }
        } else {
            passwordStrength.classList.add('d-none');
        }
    });

    // Username validation - prevent spaces
    const usernameInput = document.getElementById('username');
    const usernameInvalidFeedback = usernameInput.nextElementSibling;

    // Function to show inline alert messages
    function showInlineAlert(id, message, type = 'danger') {
        // Remove any existing alert with the same ID
        hideInlineAlert(id);

        // Create the alert element
        const alertDiv = document.createElement('div');
        alertDiv.id = id;
        alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-2`;
        alertDiv.role = 'alert';

        // Add the alert content
        alertDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <div class="me-2">
                    <i class="fas ${type === 'danger' ? 'fa-exclamation-circle' : type === 'success' ? 'fa-check-circle' : 'fa-info-circle'}"></i>
                </div>
                <div>${message}</div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // Find the parent element to insert the alert after
        const inputGroup = usernameInput.closest('.input-group');
        const hintElement = inputGroup.nextElementSibling;

        // Insert the alert after the hint
        hintElement.parentNode.insertBefore(alertDiv, hintElement.nextSibling);

        // Auto-hide after 5 seconds for success messages
        if (type === 'success') {
            setTimeout(() => {
                hideInlineAlert(id);
            }, 5000);
        }
    }

    // Function to hide inline alert messages
    function hideInlineAlert(id) {
        const existingAlert = document.getElementById(id);
        if (existingAlert) {
            // Use Bootstrap's dismiss method if available
            const bsAlert = bootstrap.Alert.getInstance(existingAlert);
            if (bsAlert) {
                bsAlert.close();
            } else {
                existingAlert.remove();
            }
        }
    }

    function validateUsername() {
        const value = usernameInput.value;
        if (value.includes(' ')) {
            usernameInput.setCustomValidity('Username cannot contain spaces');
            usernameInput.classList.add('is-invalid');
            usernameInput.classList.remove('is-valid');
            usernameInvalidFeedback.textContent = 'Username cannot contain spaces';
            showInlineAlert('username-alert', 'Username cannot contain spaces', 'danger');
            return false;
        } else if (value.trim() === '') {
            usernameInput.setCustomValidity('Username is required');
            usernameInput.classList.add('is-invalid');
            usernameInput.classList.remove('is-valid');
            usernameInvalidFeedback.textContent = 'Please enter a username';
            showInlineAlert('username-alert', 'Username is required', 'danger');
            return false;
        } else if (value.length < 4) {
            usernameInput.setCustomValidity('Username must be at least 4 characters long');
            usernameInput.classList.add('is-invalid');
            usernameInput.classList.remove('is-valid');
            usernameInvalidFeedback.textContent = 'Username must be at least 4 characters long';
            showInlineAlert('username-alert', 'Username must be at least 4 characters long', 'danger');
            return false;
        } else {
            usernameInput.setCustomValidity('');
            usernameInput.classList.remove('is-invalid');
            usernameInput.classList.add('is-valid');
            usernameInvalidFeedback.textContent = 'Please enter a username';
            hideInlineAlert('username-alert');
            return true;
        }
    }

    // Prevent spaces from being entered in the username field
    usernameInput.addEventListener('input', function(e) {
        if (e.data === ' ') {
            // Remove spaces immediately
            this.value = this.value.replace(/\s/g, '');

            // Show temporary error message
            usernameInput.classList.add('is-invalid');
            usernameInvalidFeedback.textContent = 'Spaces are not allowed in username';

            // Create and show an inline alert
            showInlineAlert('username-alert', 'Spaces are not allowed in username', 'danger');

            // Remove error after 3 seconds
            setTimeout(() => {
                if (!this.value.includes(' ')) {
                    usernameInput.classList.remove('is-invalid');
                    hideInlineAlert('username-alert');
                }
            }, 3000);
        }
        validateUsername();
    });

    usernameInput.addEventListener('blur', validateUsername);

    // Password matching validation
    const confirmPassword = document.getElementById('confirm_password');
    const passwordMatchFeedback = document.getElementById('password-match-feedback');

    function validatePasswordMatch() {
        if (passwordInput.value && confirmPassword.value) {
            if (passwordInput.value !== confirmPassword.value) {
                confirmPassword.setCustomValidity('Passwords do not match');
                confirmPassword.classList.add('is-invalid');
                return false;
            } else {
                confirmPassword.setCustomValidity('');
                confirmPassword.classList.remove('is-invalid');
                return true;
            }
        }
        return true;
    }

    passwordInput.addEventListener('input', validatePasswordMatch);
    confirmPassword.addEventListener('input', validatePasswordMatch);

    // Role-based parent admin requirement
    const roleSelect = document.getElementById('role');
    const parentAdminContainer = document.getElementById('parent-admin-container');
    const parentAdminSelect = document.getElementById('parent_admin_id');
    const staffRequiredMarkers = document.querySelectorAll('.staff-required');

    roleSelect.addEventListener('change', function() {
        if (this.value === 'staff') {
            parentAdminContainer.classList.add('required-field');
            parentAdminSelect.setAttribute('required', 'required');
            staffRequiredMarkers.forEach(marker => marker.style.display = 'inline');

            // Pre-select basic permissions when role is changed to staff
            preSelectBasicPermissions();
        } else {
            parentAdminContainer.classList.remove('required-field');
            parentAdminSelect.removeAttribute('required');
            staffRequiredMarkers.forEach(marker => marker.style.display = 'none');
        }
    });

    // Trigger the change event to set initial state
    roleSelect.dispatchEvent(new Event('change'));

    // Permissions handling
    const selectAllBtn = document.getElementById('selectAllPermissions');
    const deselectAllBtn = document.getElementById('deselectAllPermissions');
    const selectBasicBtn = document.getElementById('selectBasicPermissions');
    const permissionCheckboxes = document.querySelectorAll('.permission-checkbox');
    const showAllPermissionsToggle = document.getElementById('showAllPermissions');
    const permissionsContainer = document.querySelector('.permissions-container');

    // Basic permissions for staff to manage users from Flutter app
    const basicPermissionIds = [1, 3, 5]; // manage_users, manage_workouts, view_reports

    // Function to pre-select basic permissions
    function preSelectBasicPermissions() {
        permissionCheckboxes.forEach(checkbox => {
            const permissionId = parseInt(checkbox.value);
            checkbox.checked = basicPermissionIds.includes(permissionId);
        });

        // Show the alert
        document.getElementById('basicPermissionsAlert').style.display = 'block';

        // Update accordion headers with selected count
        updatePermissionCountBadges();
    }

    // Function to update permission count badges
    function updatePermissionCountBadges() {
        const categories = document.querySelectorAll('.accordion-item');
        categories.forEach(category => {
            const checkboxes = category.querySelectorAll('.permission-checkbox');
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            const totalCount = checkboxes.length;
            const badge = category.querySelector('.permission-count');

            badge.textContent = checkedCount + '/' + totalCount;

            if (checkedCount === 0) {
                badge.className = 'ms-auto badge bg-secondary permission-count';
            } else if (checkedCount === totalCount) {
                badge.className = 'ms-auto badge bg-success permission-count';
            } else {
                badge.className = 'ms-auto badge bg-primary permission-count';
            }
        });
    }

    // Toggle detailed permissions view
    showAllPermissionsToggle.addEventListener('change', function() {
        if (this.checked) {
            permissionsContainer.classList.add('show-all-permissions');
        } else {
            permissionsContainer.classList.remove('show-all-permissions');
        }
    });

    // Select all permissions
    selectAllBtn.addEventListener('click', function() {
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        document.getElementById('basicPermissionsAlert').style.display = 'none';
        updatePermissionCountBadges();
    });

    // Deselect all permissions
    deselectAllBtn.addEventListener('click', function() {
        permissionCheckboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        document.getElementById('basicPermissionsAlert').style.display = 'none';
        updatePermissionCountBadges();
    });

    // Select basic permissions
    selectBasicBtn.addEventListener('click', function() {
        preSelectBasicPermissions();
    });

    // Update badges when any permission is changed
    permissionCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updatePermissionCountBadges);
    });

    // Initial update of permission badges
    updatePermissionCountBadges();

    // Form submission handling
    const form = document.getElementById('staffForm');
    const submissionToast = new bootstrap.Toast(document.getElementById('submissionToast'));

    // Create a validation status container at the top of the form
    const validationStatusContainer = document.createElement('div');
    validationStatusContainer.id = 'validation-status-container';
    validationStatusContainer.className = 'mb-4';
    form.prepend(validationStatusContainer);

    form.addEventListener('submit', function(event) {
        // Validate username and password match before submission
        const isUsernameValid = validateUsername();
        const isPasswordValid = validatePasswordMatch();

        // Clear previous validation messages
        validationStatusContainer.innerHTML = '';

        if (!form.checkValidity() || !isUsernameValid || !isPasswordValid) {
            event.preventDefault();
            event.stopPropagation();

            // Show validation error message at the top of the form
            const errorAlert = document.createElement('div');
            errorAlert.className = 'alert alert-danger';
            errorAlert.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-exclamation-circle fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading mb-1">Please fix the following errors:</h5>
                        <ul class="mb-0 ps-3">
                            ${!isUsernameValid ? '<li>Username has validation errors</li>' : ''}
                            ${!isPasswordValid ? '<li>Passwords do not match</li>' : ''}
                            ${form.querySelectorAll(':invalid').length > 0 ? '<li>Some required fields are missing or invalid</li>' : ''}
                        </ul>
                    </div>
                </div>
            `;
            validationStatusContainer.appendChild(errorAlert);

            // Find the first invalid input and scroll to it
            const firstInvalid = form.querySelector(':invalid');
            if (firstInvalid) {
                firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstInvalid.focus();
            } else {
                // If no invalid inputs found but validation failed, scroll to the top
                validationStatusContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            // Show processing toast
            submissionToast.show();

            // Show success message
            const successAlert = document.createElement('div');
            successAlert.className = 'alert alert-success';
            successAlert.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <div>
                        <h5 class="alert-heading mb-1">Form is valid!</h5>
                        <p class="mb-0">Creating new staff member...</p>
                    </div>
                </div>
            `;
            validationStatusContainer.appendChild(successAlert);
        }

        form.classList.add('was-validated');
    });
});
</script>
