<?php
require_once 'includes/header.php';

// Check if user has admin privileges
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to access device management.');
    Utilities::redirect('index.php');
}

$db = new Database();
$conn = $db->getConnection();

// Handle AJAX requests
if (isset($_GET['action']) && $_GET['action'] === 'get_devices') {
    header('Content-Type: application/json');
    
    try {
        $query = "
            SELECT 
                ads.id,
                ads.user_id,
                ads.user_name,
                ads.username,
                ads.phone_number,
                ads.device_id,
                ads.device_info,
                ads.first_login,
                ads.last_activity,
                ads.is_active,
                ads.revoked_by_admin,
                ads.revoked_at,
                ads.revocation_reason,
                ads.revoked_by_username,
                ads.active_tokens,
                u.is_active as user_active
            FROM active_device_sessions ads
            JOIN users u ON ads.user_id = u.id
            ORDER BY ads.last_activity DESC
        ";
        
        $result = $conn->query($query);
        $devices = [];
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $devices[] = $row;
            }
        }
        
        echo json_encode(['success' => true, 'devices' => $devices]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
    exit;
}

// Get device statistics
$stats = [];
try {
    // Total active devices
    $activeDevicesQuery = "SELECT COUNT(*) as count FROM active_device_sessions WHERE is_active = 1";
    $result = $conn->query($activeDevicesQuery);
    $stats['active_devices'] = $result->fetch_assoc()['count'];
    
    // Total revoked devices
    $revokedDevicesQuery = "SELECT COUNT(*) as count FROM device_sessions WHERE is_active = 0 AND revoked_by_admin IS NOT NULL";
    $result = $conn->query($revokedDevicesQuery);
    $stats['revoked_devices'] = $result->fetch_assoc()['count'];
    
    // Devices active in last 24 hours
    $recentDevicesQuery = "SELECT COUNT(*) as count FROM active_device_sessions WHERE last_activity >= DATE_SUB(NOW(), INTERVAL 24 HOUR)";
    $result = $conn->query($recentDevicesQuery);
    $stats['recent_devices'] = $result->fetch_assoc()['count'];
    
    // Total unique users with devices
    $uniqueUsersQuery = "SELECT COUNT(DISTINCT user_id) as count FROM active_device_sessions WHERE is_active = 1";
    $result = $conn->query($uniqueUsersQuery);
    $stats['unique_users'] = $result->fetch_assoc()['count'];
    
} catch (Exception $e) {
    $stats = ['active_devices' => 0, 'revoked_devices' => 0, 'recent_devices' => 0, 'unique_users' => 0];
}
?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 font-weight-bold text-dark">Device Management</h1>
            <p class="text-muted">Monitor and manage user device access</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshDevices()">
                <i class="fas fa-sync-alt me-2"></i>Refresh
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-mobile-alt text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Active Devices</div>
                            <div class="h4 mb-0 font-weight-bold"><?php echo $stats['active_devices']; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-ban text-danger"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Revoked Devices</div>
                            <div class="h4 mb-0 font-weight-bold"><?php echo $stats['revoked_devices']; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-clock text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Active (24h)</div>
                            <div class="h4 mb-0 font-weight-bold"><?php echo $stats['recent_devices']; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                                <i class="fas fa-users text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="text-muted small">Unique Users</div>
                            <div class="h4 mb-0 font-weight-bold"><?php echo $stats['unique_users']; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Device List -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">Active Device Sessions</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="devicesTable">
                    <thead class="table-light">
                        <tr>
                            <th>User</th>
                            <th>Device ID</th>
                            <th>First Login</th>
                            <th>Last Activity</th>
                            <th>Active Tokens</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="devicesTableBody">
                        <tr>
                            <td colspan="7" class="text-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                Loading devices...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Device Revocation Modal -->
<div class="modal fade" id="revokeDeviceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Revoke Device Access</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action will immediately log out the user from this device and invalidate all their authentication tokens.
                </div>
                
                <div class="mb-3">
                    <label class="form-label">User:</label>
                    <div id="revokeUserInfo" class="fw-bold"></div>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Device ID:</label>
                    <div id="revokeDeviceInfo" class="font-monospace text-muted"></div>
                </div>
                
                <div class="mb-3">
                    <label for="revocationReason" class="form-label">Reason for revocation:</label>
                    <textarea class="form-control" id="revocationReason" rows="3" 
                              placeholder="Enter the reason for revoking device access...">Device access revoked by administrator</textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmRevokeDevice()">
                    <i class="fas fa-ban me-2"></i>Revoke Access
                </button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>

<script>
let currentRevokeData = null;

// Load devices on page load
document.addEventListener('DOMContentLoaded', function() {
    loadDevices();
});

// Load devices from server
function loadDevices() {
    fetch('device_management.php?action=get_devices')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderDevicesTable(data.devices);
            } else {
                showError('Failed to load devices: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            showError('Network error: ' + error.message);
        });
}

// Render devices table
function renderDevicesTable(devices) {
    const tbody = document.getElementById('devicesTableBody');
    
    if (devices.length === 0) {
        tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">No active devices found</td></tr>';
        return;
    }
    
    tbody.innerHTML = devices.map(device => {
        const lastActivity = new Date(device.last_activity);
        const firstLogin = new Date(device.first_login);
        const isRecent = (Date.now() - lastActivity.getTime()) < (24 * 60 * 60 * 1000);
        
        return `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div>
                            <div class="fw-bold">${escapeHtml(device.user_name)}</div>
                            <div class="text-muted small">${escapeHtml(device.username || device.phone_number || 'N/A')}</div>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="font-monospace text-muted">${escapeHtml(device.device_id.substring(0, 16))}...</span>
                </td>
                <td>
                    <div class="small">${firstLogin.toLocaleDateString()}</div>
                    <div class="text-muted smaller">${firstLogin.toLocaleTimeString()}</div>
                </td>
                <td>
                    <div class="small">${lastActivity.toLocaleDateString()}</div>
                    <div class="text-muted smaller">${lastActivity.toLocaleTimeString()}</div>
                    ${isRecent ? '<span class="badge bg-success ms-1">Recent</span>' : ''}
                </td>
                <td>
                    <span class="badge bg-primary">${device.active_tokens}</span>
                </td>
                <td>
                    ${device.is_active ? 
                        '<span class="badge bg-success">Active</span>' : 
                        '<span class="badge bg-danger">Revoked</span>'
                    }
                    ${!device.user_active ? '<span class="badge bg-warning ms-1">User Inactive</span>' : ''}
                </td>
                <td>
                    ${device.is_active && device.user_active ? 
                        `<button class="btn btn-sm btn-outline-danger" onclick="showRevokeModal(${device.user_id}, '${device.device_id}', '${escapeHtml(device.user_name)}')">
                            <i class="fas fa-ban me-1"></i>Revoke
                        </button>` : 
                        '<span class="text-muted">No actions</span>'
                    }
                </td>
            </tr>
        `;
    }).join('');
}

// Show revoke modal
function showRevokeModal(userId, deviceId, userName) {
    currentRevokeData = { userId, deviceId, userName };
    
    document.getElementById('revokeUserInfo').textContent = userName;
    document.getElementById('revokeDeviceInfo').textContent = deviceId;
    document.getElementById('revocationReason').value = 'Device access revoked by administrator';
    
    new bootstrap.Modal(document.getElementById('revokeDeviceModal')).show();
}

// Confirm device revocation
function confirmRevokeDevice() {
    if (!currentRevokeData) return;
    
    const reason = document.getElementById('revocationReason').value.trim();
    if (!reason) {
        showError('Please provide a reason for revocation');
        return;
    }
    
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Revoking...';
    button.disabled = true;
    
    fetch('api/revoke_device.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            user_id: currentRevokeData.userId,
            device_id: currentRevokeData.deviceId,
            reason: reason
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Device access revoked successfully');
            bootstrap.Modal.getInstance(document.getElementById('revokeDeviceModal')).hide();
            loadDevices(); // Refresh the table
        } else {
            showError('Failed to revoke device: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        showError('Network error: ' + error.message);
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Refresh devices
function refreshDevices() {
    loadDevices();
}

// Utility functions
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function showSuccess(message) {
    // You can implement your preferred notification system here
    alert('Success: ' + message);
}

function showError(message) {
    // You can implement your preferred notification system here
    alert('Error: ' + message);
}
</script>

<style>
.smaller {
    font-size: 0.75rem;
}

.font-monospace {
    font-family: 'Courier New', monospace;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.badge {
    font-size: 0.75rem;
}

.card {
    border-radius: 0.5rem;
}

.modal-content {
    border-radius: 0.5rem;
}
</style>
