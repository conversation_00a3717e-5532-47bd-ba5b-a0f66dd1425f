<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isStaff = $auth->hasRole('staff');
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');

// Check if course ID is provided
if (!isset($_GET['course_id']) || !is_numeric($_GET['course_id'])) {
    Utilities::setFlashMessage('error', 'Invalid course ID.');
    Utilities::redirect('courses.php');
}

$courseId = $_GET['course_id'];

// Fetch course data
$courseQuery = "SELECT * FROM courses WHERE id = ?";
$courseStmt = $conn->prepare($courseQuery);
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$courseResult = $courseStmt->get_result();

if ($courseResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Course not found.');
    Utilities::redirect('courses.php');
}

$course = $courseResult->fetch_assoc();

// Handle enrollment deletion
if (isset($_GET['delete']) && is_numeric($_GET['delete'])) {
    $enrollmentId = $_GET['delete'];

    // Delete the enrollment
    $deleteQuery = "DELETE FROM user_course_enrollments WHERE id = ? AND course_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("ii", $enrollmentId, $courseId);

    if ($stmt->execute()) {
        // Also delete related video progress
        $deleteProgressQuery = "DELETE uvp FROM user_video_progress uvp
                               JOIN course_videos cv ON uvp.video_id = cv.id
                               WHERE cv.course_id = ?";
        $progressStmt = $conn->prepare($deleteProgressQuery);
        $progressStmt->bind_param("i", $courseId);
        $progressStmt->execute();

        Utilities::setFlashMessage('success', 'Enrollment deleted successfully.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to delete enrollment.');
    }

    Utilities::redirect('course_enrollments.php?course_id=' . $courseId);
}

// Handle form submission for adding a new enrollment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $userId = intval($_POST['user_id'] ?? 0);
    $startDate = Utilities::sanitizeInput($_POST['start_date'] ?? '');
    $status = Utilities::sanitizeInput($_POST['status'] ?? 'active');

    // Calculate end date based on course duration
    $endDate = date('Y-m-d', strtotime($startDate . ' + ' . $course['duration_weeks'] . ' weeks'));

    // Validate inputs
    $errors = [];

    if ($userId <= 0) {
        $errors[] = 'Please select a user.';
    }

    if (empty($startDate)) {
        $errors[] = 'Start date is required.';
    }

    // Check if user is already enrolled in this course
    $checkQuery = "SELECT id FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
    $checkStmt = $conn->prepare($checkQuery);
    $checkStmt->bind_param("ii", $userId, $courseId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();

    if ($checkResult->num_rows > 0) {
        $errors[] = 'This user is already enrolled in this course.';
    }

    // If no errors, insert the enrollment
    if (empty($errors)) {
        $insertQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                        VALUES (?, ?, CURDATE(), ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("iisss", $userId, $courseId, $startDate, $endDate, $status);

        if ($stmt->execute()) {
            // Get all videos for this course
            $videosQuery = "SELECT id, week_number FROM course_videos WHERE course_id = ? ORDER BY week_number, sequence_number";
            $videosStmt = $conn->prepare($videosQuery);
            $videosStmt->bind_param("i", $courseId);
            $videosStmt->execute();
            $videosResult = $videosStmt->get_result();

            // Initialize video progress for each video
            while ($video = $videosResult->fetch_assoc()) {
                $isUnlocked = ($video['week_number'] == 1) ? 1 : 0; // Only unlock first week videos
                
                // Calculate unlock date based on week number
                if ($video['week_number'] == 1) {
                    $unlockDate = $startDate;
                } else {
                    // For week 2 and beyond, add exactly 7 days for each week after week 1
                    $daysToAdd = ($video['week_number'] - 1) * 7;
                    $unlockDate = date('Y-m-d', strtotime($startDate . ' + ' . $daysToAdd . ' days'));
                }

                $progressQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                                 VALUES (?, ?, ?, ?)";
                $progressStmt = $conn->prepare($progressQuery);
                $progressStmt->bind_param("iiis", $userId, $video['id'], $isUnlocked, $unlockDate);
                $progressStmt->execute();
            }

            Utilities::setFlashMessage('success', 'User enrolled successfully.');
            Utilities::redirect('course_enrollments.php?course_id=' . $courseId);
        } else {
            $errors[] = 'Failed to enroll user: ' . $conn->error;
        }
    }
}

// Get all enrollments for this course
$query = "SELECT e.*, u.name as user_name, u.username, u.assigned_staff_id
          FROM user_course_enrollments e
          JOIN users u ON e.user_id = u.id
          WHERE e.course_id = ?";

// For staff members, only show their assigned users
if ($isStaff) {
    $query .= " AND u.assigned_staff_id = ?";
}

$query .= " ORDER BY e.start_date DESC";
$stmt = $conn->prepare($query);

if ($isStaff) {
    $stmt->bind_param("ii", $courseId, $currentAdminId);
} else {
    $stmt->bind_param("i", $courseId);
}

$stmt->execute();
$result = $stmt->get_result();

// Get users for the dropdown
$usersQuery = "SELECT id, name, username FROM users WHERE is_active = 1";

// For staff members, only show their assigned users in the dropdown
if ($isStaff) {
    $usersQuery .= " AND assigned_staff_id = " . $currentAdminId;
}

$usersQuery .= " ORDER BY name";
$usersResult = $conn->query($usersQuery);
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Course Enrollments: <?php echo htmlspecialchars($course['title']); ?></h1>
    <a href="courses.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Courses
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Enrolled Users</h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Start Date</th>
                                <th>End Date</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($result && $result->num_rows > 0): ?>
                                <?php while ($enrollment = $result->fetch_assoc()): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($enrollment['user_name']); ?> (<?php echo htmlspecialchars($enrollment['username']); ?>)</td>
                                        <td><?php echo date('M d, Y', strtotime($enrollment['start_date'])); ?></td>
                                        <td><?php echo $enrollment['end_date'] ? date('M d, Y', strtotime($enrollment['end_date'])) : 'N/A'; ?></td>
                                        <td>
                                            <?php if ($enrollment['status'] === 'active'): ?>
                                                <span class="badge bg-dark">Active</span>
                                            <?php elseif ($enrollment['status'] === 'completed'): ?>
                                                <span class="badge bg-light">Completed</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary" style="background:#eee; color:#111;">Cancelled</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="user_progress.php?user_id=<?php echo $enrollment['user_id']; ?>&course_id=<?php echo $courseId; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-tasks"></i> Progress
                                                </a>
                                                <a href="course_enrollments.php?course_id=<?php echo $courseId; ?>&delete=<?php echo $enrollment['id']; ?>" class="btn btn-sm btn-outline-danger" onclick="return confirm('Are you sure you want to delete this enrollment? This will also remove all progress data.')">
                                                    <i class="fas fa-trash"></i> Delete
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">No enrollments found for this course</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Enroll User</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    <div class="mb-3">
                        <label for="user_id" class="form-label">Select User</label>
                        <select class="form-select user-select" id="user_id" name="user_id" required>
                            <option value="">-- Select User --</option>
                            <?php if ($usersResult && $usersResult->num_rows > 0): ?>
                                <?php while ($user = $usersResult->fetch_assoc()): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['name']); ?> (<?php echo htmlspecialchars($user['username']); ?>)
                                    </option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo date('Y-m-d'); ?>" required>
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i> Enroll User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
