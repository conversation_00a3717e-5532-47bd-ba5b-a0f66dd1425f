<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('error', 'Invalid purchase ID.');
    Utilities::redirect('course_purchases.php');
}

$purchaseId = $_GET['id'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $purchaseDate = Utilities::sanitizeInput($_POST['purchase_date'] ?? date('Y-m-d H:i:s'));
    $amountPaid = floatval($_POST['amount_paid'] ?? 0);
    $paymentMethod = Utilities::sanitizeInput($_POST['payment_method'] ?? 'credit_card');
    $transactionId = Utilities::sanitizeInput($_POST['transaction_id'] ?? '');
    $status = Utilities::sanitizeInput($_POST['status'] ?? 'completed');

    // Validate inputs
    $errors = [];

    if (empty($purchaseDate)) {
        $errors[] = 'Purchase date is required.';
    }

    if ($amountPaid < 0) {
        $errors[] = 'Amount paid cannot be negative.';
    }

    // If no errors, update the purchase
    if (empty($errors)) {
        $updateQuery = "UPDATE course_purchases
                        SET purchase_date = ?, amount_paid = ?, payment_method = ?, transaction_id = ?, status = ?
                        WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("sdsssi", $purchaseDate, $amountPaid, $paymentMethod, $transactionId, $status, $purchaseId);

        if ($stmt->execute()) {
            // Get purchase details to check if we need to update enrollment
            $purchaseQuery = "SELECT user_id, course_id, status FROM course_purchases WHERE id = ?";
            $purchaseStmt = $conn->prepare($purchaseQuery);
            $purchaseStmt->bind_param("i", $purchaseId);
            $purchaseStmt->execute();
            $purchaseResult = $purchaseStmt->get_result();
            $purchase = $purchaseResult->fetch_assoc();

            // If status changed to completed, create enrollment if it doesn't exist
            if ($status === 'completed') {
                // Check if enrollment exists
                $checkEnrollmentQuery = "SELECT id FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
                $checkEnrollmentStmt = $conn->prepare($checkEnrollmentQuery);
                $checkEnrollmentStmt->bind_param("ii", $purchase['user_id'], $purchase['course_id']);
                $checkEnrollmentStmt->execute();
                $checkEnrollmentResult = $checkEnrollmentStmt->get_result();

                if ($checkEnrollmentResult->num_rows === 0) {
                    // Create enrollment
                    $enrollmentQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                                       SELECT ?, ?, ?, ?, DATE_ADD(?, INTERVAL c.duration_weeks WEEK), 'active'
                                       FROM courses c WHERE c.id = ?";
                    $enrollmentStmt = $conn->prepare($enrollmentQuery);
                    $today = date('Y-m-d');
                    $enrollmentStmt->bind_param("iisssi", $purchase['user_id'], $purchase['course_id'], $today, $today, $today, $purchase['course_id']);
                    $enrollmentStmt->execute();

                    // Initialize video progress for first week videos
                    $videosQuery = "SELECT id, week_number FROM course_videos WHERE course_id = ? AND week_number = 1 ORDER BY sequence_number";
                    $videosStmt = $conn->prepare($videosQuery);
                    $videosStmt->bind_param("i", $purchase['course_id']);
                    $videosStmt->execute();
                    $videosResult = $videosStmt->get_result();

                    while ($video = $videosResult->fetch_assoc()) {
                        $progressQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                                         VALUES (?, ?, 1, ?)";
                        $progressStmt = $conn->prepare($progressQuery);
                        $progressStmt->bind_param("iis", $purchase['user_id'], $video['id'], $today);
                        $progressStmt->execute();
                    }
                }
            } else if ($status === 'refunded' || $status === 'failed') {
                // If status changed to refunded or failed, update enrollment to cancelled
                $updateEnrollmentQuery = "UPDATE user_course_enrollments SET status = 'cancelled' WHERE user_id = ? AND course_id = ?";
                $updateEnrollmentStmt = $conn->prepare($updateEnrollmentQuery);
                $updateEnrollmentStmt->bind_param("ii", $purchase['user_id'], $purchase['course_id']);
                $updateEnrollmentStmt->execute();
            }

            Utilities::setFlashMessage('success', 'Purchase updated successfully.');
            Utilities::redirect('course_purchases.php');
        } else {
            $errors[] = 'Failed to update purchase: ' . $conn->error;
        }
    }
} else {
    // Fetch purchase data
    $query = "SELECT p.*, u.name as user_name, u.username, c.title as course_title
              FROM course_purchases p
              JOIN users u ON p.user_id = u.id
              JOIN courses c ON p.course_id = c.id
              WHERE p.id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $purchaseId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        Utilities::setFlashMessage('error', 'Purchase not found.');
        Utilities::redirect('course_purchases.php');
    }

    $purchase = $result->fetch_assoc();

    // Set variables for form
    $userId = $purchase['user_id'];
    $userName = $purchase['user_name'] . ' (' . $purchase['username'] . ')';
    $courseId = $purchase['course_id'];
    $courseTitle = $purchase['course_title'];
    $purchaseDate = date('Y-m-d\TH:i', strtotime($purchase['purchase_date']));
    $amountPaid = $purchase['amount_paid'];
    $paymentMethod = $purchase['payment_method'];
    $transactionId = $purchase['transaction_id'];
    $status = $purchase['status'];
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Purchase</h1>
    <a href="course_purchases.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Purchases
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <form method="post">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">User</label>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($userName); ?>" readonly>
                </div>

                <div class="col-md-6 mb-3">
                    <label class="form-label">Course</label>
                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($courseTitle); ?>" readonly>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="purchase_date" class="form-label">Purchase Date</label>
                    <input type="datetime-local" class="form-control" id="purchase_date" name="purchase_date" value="<?php echo $purchaseDate; ?>" required>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="amount_paid" class="form-label">Amount Paid (₹)</label>
                    <input type="number" class="form-control" id="amount_paid" name="amount_paid" value="<?php echo $amountPaid; ?>" min="0" step="0.01" required>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6 mb-3">
                    <label for="payment_method" class="form-label">Payment Method</label>
                    <select class="form-select" id="payment_method" name="payment_method">
                        <option value="credit_card" <?php echo $paymentMethod === 'credit_card' ? 'selected' : ''; ?>>Credit Card</option>
                        <option value="paypal" <?php echo $paymentMethod === 'paypal' ? 'selected' : ''; ?>>PayPal</option>
                        <option value="bank_transfer" <?php echo $paymentMethod === 'bank_transfer' ? 'selected' : ''; ?>>Bank Transfer</option>
                        <option value="cash" <?php echo $paymentMethod === 'cash' ? 'selected' : ''; ?>>Cash</option>
                        <option value="other" <?php echo $paymentMethod === 'other' ? 'selected' : ''; ?>>Other</option>
                    </select>
                </div>

                <div class="col-md-6 mb-3">
                    <label for="transaction_id" class="form-label">Transaction ID</label>
                    <input type="text" class="form-control" id="transaction_id" name="transaction_id" value="<?php echo htmlspecialchars($transactionId); ?>" placeholder="Optional">
                </div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="completed" <?php echo $status === 'completed' ? 'selected' : ''; ?>>Completed</option>
                    <option value="pending" <?php echo $status === 'pending' ? 'selected' : ''; ?>>Pending</option>
                    <option value="refunded" <?php echo $status === 'refunded' ? 'selected' : ''; ?>>Refunded</option>
                    <option value="failed" <?php echo $status === 'failed' ? 'selected' : ''; ?>>Failed</option>
                </select>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Update Purchase
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
