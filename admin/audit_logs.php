<?php
require_once 'includes/header.php';
require_once 'includes/audit_logger.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to view audit logs.');
    Utilities::redirect('index.php');
    exit;
}

// Check if audit_logs table exists
$tableExistsQuery = "SHOW TABLES LIKE 'audit_logs'";
$tableExistsResult = $conn->query($tableExistsQuery);
$tableExists = ($tableExistsResult && $tableExistsResult->num_rows > 0);

if (!$tableExists) {
    // Table doesn't exist, show a message to create it
    Utilities::setFlashMessage('warning', 'The audit_logs table does not exist yet. Please create it first.');
}

// Initialize variables
$staffMembers = [];
$actionTypes = [];
$logs = [];
$totalLogs = 0;
$totalPages = 1;

if ($tableExists) {
    // Get filter parameters
    $staffId = isset($_GET['staff_id']) ? (int)$_GET['staff_id'] : '';
    $actionType = isset($_GET['action_type']) ? Utilities::sanitizeInput($_GET['action_type']) : '';
    $affectedUserId = isset($_GET['affected_user_id']) ? (int)$_GET['affected_user_id'] : '';
    $dateFrom = isset($_GET['date_from']) ? Utilities::sanitizeInput($_GET['date_from']) : '';
    $dateTo = isset($_GET['date_to']) ? Utilities::sanitizeInput($_GET['date_to']) : '';
    $search = isset($_GET['search']) ? Utilities::sanitizeInput($_GET['search']) : '';

    // Set default date range if not provided (last 7 days)
    if (empty($dateFrom)) {
        $dateFrom = date('Y-m-d', strtotime('-7 days'));
    }
    if (empty($dateTo)) {
        $dateTo = date('Y-m-d');
    }

    // Create filters array
    $filters = [
        'staff_id' => $staffId,
        'action_type' => $actionType,
        'affected_user_id' => $affectedUserId,
        'date_from' => $dateFrom,
        'date_to' => $dateTo,
        'search' => $search
    ];

    // Pagination
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 50;
    $offset = ($page - 1) * $limit;

    // Get total count for pagination
    $totalLogs = countAuditLogs($conn, $filters);
    $totalPages = ceil($totalLogs / $limit);

    // Get logs with pagination
    $logs = getAuditLogs($conn, $filters, $limit, $offset);

    // Get all staff members for the filter dropdown
    $staffQuery = "SELECT id, name, username FROM admin_users WHERE role = 'staff' ORDER BY name";
    $staffResult = $conn->query($staffQuery);
    $staffMembers = [];
    if ($staffResult && $staffResult->num_rows > 0) {
        while ($staff = $staffResult->fetch_assoc()) {
            $staffMembers[] = $staff;
        }
    }

    // Get all action types for the filter dropdown
    $actionTypes = getActionTypes($conn);
} else {
    // Default values if table doesn't exist
    $staffId = '';
    $actionType = '';
    $affectedUserId = '';
    $dateFrom = date('Y-m-d', strtotime('-7 days'));
    $dateTo = date('Y-m-d');
    $search = '';
    $page = 1;
}

// Build the pagination URL
$paginationUrl = "?page=%d";
if (!empty($staffId)) $paginationUrl .= "&staff_id=" . urlencode($staffId);
if (!empty($actionType)) $paginationUrl .= "&action_type=" . urlencode($actionType);
if (!empty($affectedUserId)) $paginationUrl .= "&affected_user_id=" . urlencode($affectedUserId);
if (!empty($dateFrom)) $paginationUrl .= "&date_from=" . urlencode($dateFrom);
if (!empty($dateTo)) $paginationUrl .= "&date_to=" . urlencode($dateTo);
if (!empty($search)) $paginationUrl .= "&search=" . urlencode($search);
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Audit Logs</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Audit Logs</li>
    </ol>

    <?php Utilities::displayFlashMessages(); ?>

    <?php if (!$tableExists): ?>
    <!-- Table doesn't exist message -->
    <div class="alert alert-warning">
        <div class="d-flex">
            <div class="me-3">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
            </div>
            <div>
                <h5 class="alert-heading">Audit Logs Table Not Found</h5>
                <p>The audit_logs table does not exist in the database yet. You need to create it before you can use this feature.</p>
                <a href="create_audit_logs_table.php" class="btn btn-primary">
                    <i class="fas fa-database me-2"></i> Create Audit Logs Table
                </a>
            </div>
        </div>
    </div>
    <?php else: ?>

    <!-- Filters -->
    <div class="card mb-4 shadow-sm">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">Filters</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="staff_id" class="form-label">Staff Member</label>
                    <select class="form-select" id="staff_id" name="staff_id">
                        <option value="">All Staff</option>
                        <?php foreach ($staffMembers as $staff): ?>
                            <option value="<?php echo $staff['id']; ?>" <?php echo $staffId == $staff['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="action_type" class="form-label">Action Type</label>
                    <select class="form-select" id="action_type" name="action_type">
                        <option value="">All Actions</option>
                        <?php foreach ($actionTypes as $type): ?>
                            <option value="<?php echo $type; ?>" <?php echo $actionType == $type ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars(ucwords(str_replace('_', ' ', $type))); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">Date From</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $dateFrom; ?>">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">Date To</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $dateTo; ?>">
                </div>
                <div class="col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search in details, staff or user names..." value="<?php echo htmlspecialchars($search); ?>">
                </div>
                <div class="col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">Apply Filters</button>
                    <a href="audit_logs.php" class="btn btn-outline-secondary">Reset</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Audit Logs Table -->
    <div class="card shadow-sm">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Audit Logs</h5>
            <span class="badge bg-primary"><?php echo number_format($totalLogs); ?> logs found</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>ID</th>
                            <th>Timestamp</th>
                            <th>Staff Member</th>
                            <th>Action Type</th>
                            <th>Affected User</th>
                            <th>Details</th>
                            <th>IP Address</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if (empty($logs)): ?>
                            <tr>
                                <td colspan="7" class="text-center py-4">No audit logs found matching your criteria</td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($logs as $log): ?>
                                <tr>
                                    <td><?php echo $log['id']; ?></td>
                                    <td><?php echo date('Y-m-d H:i:s', strtotime($log['timestamp'])); ?></td>
                                    <td>
                                        <?php if (!empty($log['staff_name'])): ?>
                                            <a href="audit_logs.php?staff_id=<?php echo $log['staff_id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($log['staff_name']); ?>
                                                <small class="text-muted d-block">@<?php echo htmlspecialchars($log['staff_username']); ?></small>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">Unknown (ID: <?php echo $log['staff_id']; ?>)</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <a href="audit_logs.php?action_type=<?php echo urlencode($log['action_type']); ?>" class="badge bg-secondary text-decoration-none">
                                            <?php echo htmlspecialchars(ucwords(str_replace('_', ' ', $log['action_type']))); ?>
                                        </a>
                                    </td>
                                    <td>
                                        <?php if (!empty($log['affected_user_id'])): ?>
                                            <a href="user_view.php?id=<?php echo $log['affected_user_id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($log['affected_user_name'] ?? 'Unknown'); ?>
                                                <?php if (!empty($log['affected_user_username'])): ?>
                                                    <small class="text-muted d-block">@<?php echo htmlspecialchars($log['affected_user_username']); ?></small>
                                                <?php endif; ?>
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                            <?php echo htmlspecialchars($log['details'] ?? 'No details'); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted"><?php echo htmlspecialchars($log['ip_address'] ?? 'Unknown'); ?></small>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <?php if ($totalPages > 1): ?>
    <nav aria-label="Page navigation" class="mt-4">
        <ul class="pagination justify-content-center">
            <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                <a class="page-link" href="<?php echo sprintf($paginationUrl, $page - 1); ?>" aria-label="Previous">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>

            <?php
            // Calculate range of pages to show
            $startPage = max(1, $page - 2);
            $endPage = min($totalPages, $page + 2);

            // Always show first page
            if ($startPage > 1) {
                echo '<li class="page-item"><a class="page-link" href="' . sprintf($paginationUrl, 1) . '">1</a></li>';
                if ($startPage > 2) {
                    echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
            }

            // Show page links
            for ($i = $startPage; $i <= $endPage; $i++) {
                echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="' . sprintf($paginationUrl, $i) . '">' . $i . '</a></li>';
            }

            // Always show last page
            if ($endPage < $totalPages) {
                if ($endPage < $totalPages - 1) {
                    echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                }
                echo '<li class="page-item"><a class="page-link" href="' . sprintf($paginationUrl, $totalPages) . '">' . $totalPages . '</a></li>';
            }
            ?>

            <li class="page-item <?php echo ($page >= $totalPages) ? 'disabled' : ''; ?>">
                <a class="page-link" href="<?php echo sprintf($paginationUrl, $page + 1); ?>" aria-label="Next">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        </ul>
    </nav>
    <?php endif; ?>
</div>
<?php endif; ?> <!-- Close the if ($tableExists) condition -->

<?php require_once 'includes/footer.php'; ?>
