<?php
require_once 'includes/header.php';

// Only admins can access
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('warning', 'You do not have permission to access Quote Settings.');
    Utilities::redirect('index.php');
}

$db = new Database();
$conn = $db->getConnection();
$settingsManager = Settings::getInstance();
$deepSeekEnabled = $settingsManager->isEnabled('deepseek_enabled');
$deepSeekApiKey = $settingsManager->get('deepseek_api_key') ?? '';
$fallbackSource = $settingsManager->get('quote_fallback_source') ?? 'local';
?>
<div class="container mt-4">
    <h1 class="mb-4"><i class="fas fa-quote-right me-2"></i>Quote Settings</h1>
    <form method="post" class="needs-validation" novalidate onsubmit="return false;">
        <!-- DeepSeek AI Integration -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-white d-flex align-items-center">
                <i class="fas fa-robot fa-lg me-2 text-primary"></i>
                <h5 class="card-title mb-0">DeepSeek AI Integration</h5>
            </div>
            <div class="card-body">
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="deepseek_enabled" value="1" <?php if ($deepSeekEnabled) echo 'checked'; ?>>
                    <label class="form-check-label" for="deepseek_enabled">
                        Enable DeepSeek AI Quotes for all users
                    </label>
                </div>
                <div class="mb-3">
                    <label for="deepseek_api_key" class="form-label">DeepSeek API Key</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="deepseek_api_key" value="<?php echo htmlspecialchars($deepSeekApiKey); ?>" placeholder="Enter your DeepSeek API key">
                        <button type="button" class="btn btn-primary" id="saveApiKeyBtn">
                            <i class="fas fa-save me-1"></i> Save
                        </button>
                    </div>
                    <div class="form-text">Required for AI-generated quotes. <a href="https://deepseek.com/" target="_blank">Get an API key</a>.</div>
                </div>
                <button type="button" class="btn btn-outline-info btn-sm" onclick="alert('Test not implemented.');">
                    <i class="fas fa-vial me-1"></i> Test API Key
                </button>
            </div>
        </div>

        <!-- Fallback Source -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-white d-flex align-items-center">
                <i class="fas fa-database fa-lg me-2 text-secondary"></i>
                <h5 class="card-title mb-0">Fallback Quote Source</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="fallback_source" class="form-label">If DeepSeek is disabled/unavailable, use:</label>
                    <select class="form-select" id="fallback_source">
                        <option value="local" <?php if ($fallbackSource == 'local') echo 'selected'; ?>>Local Database</option>
                        <option value="external_api" <?php if ($fallbackSource == 'external_api') echo 'selected'; ?>>External API</option>
                    </select>
                </div>
            </div>
        </div>

        <!-- Moderation/Advanced (Optional) -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-white d-flex align-items-center">
                <i class="fas fa-sliders-h fa-lg me-2 text-success"></i>
                <h5 class="card-title mb-0">Advanced Options</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="quote_categories" class="form-label">Allowed Categories (comma-separated)</label>
                    <input type="text" class="form-control" id="quote_categories" value="<?php echo htmlspecialchars($settingsManager->get('quote_categories') ?? ''); ?>" placeholder="e.g. fitness, motivation, health">
                    <div class="form-text">Restrict AI quotes to these categories.</div>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="moderate_quotes" value="1" <?php if ($settingsManager->isEnabled('moderate_quotes')) echo 'checked'; ?>>
                    <label class="form-check-label" for="moderate_quotes">
                        Require admin approval for new AI quotes
                    </label>
                </div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    function showToast(msg) {
        let toast = document.createElement('div');
        toast.className = 'toast align-items-center text-bg-success border-0 show position-fixed bottom-0 end-0 m-4';
        toast.role = 'alert';
        toast.innerHTML = `<div class="d-flex"><div class="toast-body">${msg}</div><button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button></div>`;
        document.body.appendChild(toast);
        setTimeout(() => toast.remove(), 2000);
    }
    function autoApplySetting(key, value) {
        fetch('/admin/api/update_quote_setting.php', {
            method: 'POST',
            headers: {'Content-Type': 'application/x-www-form-urlencoded'},
            body: `key=${encodeURIComponent(key)}&value=${encodeURIComponent(value)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Setting saved!');
            } else {
                showToast('Failed to save setting!');
            }
        })
        .catch(() => showToast('Network error!'));
    }
    document.getElementById('deepseek_enabled').addEventListener('change', function() {
        autoApplySetting('deepseek_enabled', this.checked ? 1 : 0);
    });
    document.getElementById('saveApiKeyBtn').addEventListener('click', function() {
        const apiKey = document.getElementById('deepseek_api_key').value;
        autoApplySetting('deepseek_api_key', apiKey);
    });
    document.getElementById('fallback_source').addEventListener('change', function() {
        autoApplySetting('quote_fallback_source', this.value);
    });
    document.getElementById('quote_categories').addEventListener('blur', function() {
        autoApplySetting('quote_categories', this.value);
    });
    document.getElementById('moderate_quotes').addEventListener('change', function() {
        autoApplySetting('moderate_quotes', this.checked ? 1 : 0);
    });
});
</script>
<?php require_once 'includes/footer.php'; ?> 