<?php
// Simple test script for Vimeo proxy

$vimeoId = '1087487482'; // Test video ID
$proxyUrl = "https://mycloudforge.com/admin/api/vimeo-proxy/{$vimeoId}?autoplay=1&title=0&byline=0&portrait=0";

?>
<!DOCTYPE html>
<html>
<head>
    <title>Vimeo Proxy Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .video-container { width: 100%; max-width: 800px; margin: 20px 0; }
        iframe { width: 100%; height: 450px; border: none; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow: auto; }
    </style>
</head>
<body>
    <h1>Vimeo Proxy Test</h1>
    
    <h2>Direct Vimeo Embed</h2>
    <div class="video-container">
        <iframe src="https://player.vimeo.com/video/<?php echo $vimeoId; ?>?autoplay=0&title=0&byline=0&portrait=0" 
                allow="autoplay; fullscreen" allowfullscreen></iframe>
    </div>
    
    <h2>Proxied Vimeo Embed</h2>
    <div class="video-container">
        <iframe src="<?php echo $proxyUrl; ?>"
                allow="autoplay; fullscreen" allowfullscreen></iframe>
    </div>
    
    <h2>Proxy Details</h2>
    <p>Proxy URL: <code><?php echo htmlspecialchars($proxyUrl); ?></code></p>
    
    <h3>Headers Test</h3>
    <pre><?php 
        $headers = get_headers($proxyUrl, 1);
        print_r($headers);
    ?></pre>
</body>
</html> 