<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Use Auth class for permission check
$auth = new Auth();
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

$settingsManager = Settings::getInstance();
if ($settingsManager->get('maintenance_mode', 0)) {
    if (!$auth->hasRole('super_admin')) {
        echo '<div style="max-width:600px;margin:100px auto;text-align:center;">
                <h2>Maintenance Mode</h2>
                <p>The platform is currently undergoing maintenance. Please check back later.</p>
              </div>';
        exit;
    }
}

// Set active page for sidebar
$activePage = 'profile.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();
$adminId = $_SESSION['user_id'];

// Fetch admin details
$stmt = $conn->prepare('SELECT name, email, phone, username, role, last_login FROM admin_users WHERE id = ?');
$stmt->bind_param('i', $adminId);
$stmt->execute();
$result = $stmt->get_result();
$adminData = $result->fetch_assoc();
$stmt->close();

// Extract admin data
$name = $adminData['name'] ?? '';
$email = $adminData['email'] ?? '';
$phone = $adminData['phone'] ?? '';
$username = $adminData['username'] ?? '';
$role = $adminData['role'] ?? '';
$lastLogin = $adminData['last_login'] ?? '';

// Format role for display
$roleDisplay = '';
switch ($role) {
    case 'super_admin':
        $roleDisplay = 'Super Admin';
        break;
    case 'admin':
        $roleDisplay = 'Administrator';
        break;
    case 'staff':
        $roleDisplay = 'Staff';
        break;
    case 'manager':
        $roleDisplay = 'Manager';
        break;
    default:
        $roleDisplay = ucfirst($role);
}

// Format last login
$lastLoginFormatted = $lastLogin ? date('F j, Y, g:i a', strtotime($lastLogin)) : 'Never';

// Initialize messages
$profileSuccess = false;
$profileError = '';
$passwordSuccess = false;
$passwordError = '';
$avatarSuccess = false;
$avatarError = '';

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    // CSRF token check
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== ($_SESSION['csrf_token'] ?? '')) {
        $profileError = 'Invalid CSRF token. Please refresh and try again.';
    } else {
        $newName = trim($_POST['name'] ?? '');
        $newEmail = trim($_POST['email'] ?? '');
        $newPhone = trim($_POST['phone'] ?? '');

        if ($newName === '' || $newEmail === '') {
            $profileError = 'Name and email are required.';
        } else {
            $stmt = $conn->prepare('UPDATE admin_users SET name=?, email=?, phone=? WHERE id=?');
            $stmt->bind_param('sssi', $newName, $newEmail, $newPhone, $adminId);

            if ($stmt->execute()) {
                $profileSuccess = true;
                $name = $newName;
                $email = $newEmail;
                $phone = $newPhone;

                // Update session data
                $_SESSION['name'] = $newName;
            } else {
                $profileError = 'Failed to update profile: ' . $conn->error;
            }
            $stmt->close();
        }
    }
}

// Handle password update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_password'])) {
    // CSRF token check
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== ($_SESSION['csrf_token'] ?? '')) {
        $passwordError = 'Invalid CSRF token. Please refresh and try again.';
    } else {
        $currentPassword = $_POST['current_password'] ?? '';
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';

        // Verify current password
        $stmt = $conn->prepare('SELECT password FROM admin_users WHERE id = ?');
        $stmt->bind_param('i', $adminId);
        $stmt->execute();
        $result = $stmt->get_result();
        $userData = $result->fetch_assoc();
        $stmt->close();

        if (!password_verify($currentPassword, $userData['password'])) {
            $passwordError = 'Current password is incorrect.';
        } elseif ($newPassword === '') {
            $passwordError = 'New password cannot be empty.';
        } elseif ($newPassword !== $confirmPassword) {
            $passwordError = 'New passwords do not match.';
        } else {
            $policyCheck = validate_password_policy($newPassword);
            if ($policyCheck !== true) {
                $passwordError = $policyCheck;
            } else {
                $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                $stmt = $conn->prepare('UPDATE admin_users SET password=? WHERE id=?');
                $stmt->bind_param('si', $hashedPassword, $adminId);

                if ($stmt->execute()) {
                    $passwordSuccess = true;
                } else {
                    $passwordError = 'Failed to update password: ' . $conn->error;
                }
                $stmt->close();
            }
        }
    }
}

// Add CSS and JS for profile page
echo '<link rel="stylesheet" href="assets/css/profile.css">';
?>

<style>
.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>

<div class="profile-container">
    <!-- Profile Header -->
    <div class="profile-header bg-white rounded-4 shadow-sm p-4 mb-4 d-flex flex-column flex-md-row align-items-center justify-content-between position-relative" style="min-height: 180px; border: 1px solid #e0e0e0;">
        <div class="d-flex align-items-center mb-3 mb-md-0">
            <div class="profile-avatar-wrapper me-4 position-relative" style="width: 90px; height: 90px;">
                <div class="profile-avatar d-flex align-items-center justify-content-center rounded-circle shadow" id="avatar-preview" style="width: 90px; height: 90px; font-size: 2.5rem; font-weight: 600; border: 4px solid #fff; background: #111; color: #fff;">
                    <?php echo strtoupper(substr($name ?? ($username ?? 'U'), 0, 1)); ?>
                </div>
                <label class="profile-avatar-upload position-absolute bottom-0 end-0 bg-white rounded-circle p-2 shadow-sm" id="avatar-upload" title="Upload profile picture" style="cursor:pointer; border: 1px solid #e0e0e0;">
                    <i class="fas fa-camera" style="color: #111;"></i>
                    <input type="file" id="avatar-input" accept="image/*" style="display:none;">
                </label>
                <form id="avatar-form" action="update_avatar.php" method="post" enctype="multipart/form-data" style="display: none;">
                    <input type="file" name="avatar" id="avatar-form-input">
                </form>
            </div>
            <div class="profile-info ms-2 ms-md-4">
                <h2 class="profile-name mb-1 fw-bold text-dark" style="font-size: 2rem; line-height: 1.2;">
                    <?php echo htmlspecialchars($name); ?>
                </h2>
                <div class="profile-role mb-2">
                    <span class="badge text-uppercase fw-semibold px-3 py-2" style="font-size: 1rem; letter-spacing: 0.5px; background: #111; color: #fff;">
                        <?php echo htmlspecialchars($roleDisplay); ?>
                    </span>
                </div>
                <div class="profile-meta d-flex flex-wrap align-items-center gap-3 text-muted" style="font-size: 1rem;">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-envelope me-2" style="color: #888;"></i>
                        <span><?php echo htmlspecialchars($email); ?></span>
                    </div>
                    <?php if ($phone): ?>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-phone ms-3 me-2" style="color: #888;"></i>
                        <span><?php echo htmlspecialchars($phone); ?></span>
                    </div>
                    <?php endif; ?>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock ms-3 me-2" style="color: #888;"></i>
                        <span>Last login: <?php echo htmlspecialchars($lastLoginFormatted); ?></span>
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-3 mt-md-0">
            <a href="logout.php" class="btn btn-outline-dark btn-lg px-4 py-2 fw-semibold shadow-sm">
                <i class="fas fa-sign-out-alt me-2"></i> Logout
            </a>
        </div>
    </div>

    <!-- Profile Content -->
    <div class="profile-content">
        <!-- Sidebar Navigation -->
        <div class="profile-sidebar">
            <div class="profile-nav">
                <div class="profile-nav-header">
                    <h5 class="profile-nav-title">Profile Settings</h5>
                </div>
                <ul class="profile-nav-list">
                    <li class="profile-nav-item">
                        <a href="#personal-info" class="profile-nav-link active">
                            <i class="fas fa-user"></i> Personal Information
                        </a>
                    </li>
                    <li class="profile-nav-item">
                        <a href="#security" class="profile-nav-link">
                            <i class="fas fa-lock"></i> Security
                        </a>
                    </li>
                    <li class="profile-nav-item">
                        <a href="#preferences" class="profile-nav-link">
                            <i class="fas fa-cog"></i> Preferences
                        </a>
                    </li>
                    <li class="profile-nav-item">
                        <a href="#activity" class="profile-nav-link">
                            <i class="fas fa-history"></i> Activity Log
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="profile-main">
            <!-- Personal Information Tab -->
            <div id="personal-info" class="profile-tab-content">
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h5 class="profile-card-title">Personal Information</h5>
                    </div>
                    <div class="profile-card-body">
                        <?php if ($profileSuccess): ?>
                            <div class="alert alert-success">Profile updated successfully.</div>
                        <?php elseif ($profileError): ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($profileError); ?></div>
                        <?php endif; ?>

                        <form id="profile-form" method="post" autocomplete="off">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token'] ?? ''); ?>">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-form-group">
                                        <label for="name" class="profile-form-label">Full Name</label>
                                        <input type="text" class="form-control profile-form-control" id="name" name="name" value="<?php echo htmlspecialchars($name); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-form-group">
                                        <label for="username" class="profile-form-label">Username</label>
                                        <input type="text" class="form-control profile-form-control" id="username" value="<?php echo htmlspecialchars($username); ?>" disabled>
                                        <div class="profile-form-text">Username cannot be changed</div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="profile-form-group">
                                        <label for="email" class="profile-form-label">Email Address</label>
                                        <input type="email" class="form-control profile-form-control" id="email" name="email" value="<?php echo htmlspecialchars($email); ?>" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="profile-form-group">
                                        <label for="phone" class="profile-form-label">Phone Number</label>
                                        <input type="text" class="form-control profile-form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($phone); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="profile-form-group">
                                <label for="role" class="profile-form-label">Role</label>
                                <input type="text" class="form-control profile-form-control" id="role" value="<?php echo htmlspecialchars($roleDisplay); ?>" disabled>
                                <div class="profile-form-text">Your role can only be changed by a super admin</div>
                            </div>

                            <input type="hidden" name="update_profile" value="1">
                        </div>
                        <div class="profile-card-footer">
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tab -->
            <div id="security" class="profile-tab-content" style="display: none;">
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h5 class="profile-card-title">Change Password</h5>
                    </div>
                    <div class="profile-card-body">
                        <?php if ($passwordSuccess): ?>
                            <div class="alert alert-success">Password updated successfully.</div>
                        <?php elseif ($passwordError): ?>
                            <div class="alert alert-danger"><?php echo htmlspecialchars($passwordError); ?></div>
                        <?php endif; ?>

                        <form id="password-form" method="post" autocomplete="off">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token'] ?? ''); ?>">
                            <div class="profile-form-group">
                                <label for="current-password" class="profile-form-label">Current Password</label>
                                <input type="password" class="form-control profile-form-control" id="current-password" name="current_password" required>
                            </div>

                            <div class="profile-form-group">
                                <label for="new-password" class="profile-form-label">New Password</label>
                                <input type="password" class="form-control profile-form-control" id="new-password" name="new_password" required>
                                <div class="mt-2">
                                    <div class="d-flex justify-content-between align-items-center mb-1">
                                        <span>Password Strength:</span>
                                        <span id="password-strength-text" class="small">No password</span>
                                    </div>
                                    <progress id="password-strength-meter" value="0" max="4" class="w-100"></progress>
                                </div>
                            </div>

                            <div class="profile-form-group">
                                <label for="confirm-password" class="profile-form-label">Confirm New Password</label>
                                <input type="password" class="form-control profile-form-control" id="confirm-password" name="confirm_password" required>
                            </div>

                            <input type="hidden" name="update_password" value="1">
                        </div>
                        <div class="profile-card-footer">
                            <button type="submit" class="btn btn-primary">Update Password</button>
                        </div>
                    </form>
                </div>

                <div class="profile-card mt-4">
                    <div class="profile-card-header">
                        <h5 class="profile-card-title">Login Sessions</h5>
                    </div>
                    <div class="profile-card-body">
                        <div class="d-flex align-items-center p-3 border-bottom">
                            <div class="me-3">
                                <i class="fas fa-desktop fa-2x text-primary"></i>
                            </div>
                            <div class="flex-grow-1">
                                <div class="fw-bold">Current Session</div>
                                <div class="small text-muted">
                                    <span>Started: <?php echo date('M d, Y h:i A'); ?></span>
                                    <span class="ms-2 badge bg-success">Active</span>
                                </div>
                            </div>
                        </div>

                        <div class="p-3">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                For security reasons, you can log out of all devices by changing your password.
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Preferences Tab -->
            <div id="preferences" class="profile-tab-content" style="display: none;">
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h5 class="profile-card-title">Interface Preferences</h5>
                    </div>
                    <div class="profile-card-body">
                        <div class="profile-form-group">
                            <label class="profile-form-label d-block mb-3">Theme</label>
                            <div class="d-flex gap-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="theme" id="theme-light" checked>
                                    <label class="form-check-label" for="theme-light">
                                        Light
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="theme" id="theme-dark">
                                    <label class="form-check-label" for="theme-dark">
                                        Dark
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="theme" id="theme-system">
                                    <label class="form-check-label" for="theme-system">
                                        System Default
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="profile-form-group">
                            <label class="profile-form-label d-block mb-3">Notifications</label>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                                <label class="form-check-label" for="email-notifications">
                                    Email Notifications
                                </label>
                            </div>
                            <div class="form-check form-switch mb-2">
                                <input class="form-check-input" type="checkbox" id="browser-notifications" checked>
                                <label class="form-check-label" for="browser-notifications">
                                    Browser Notifications
                                </label>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Preference settings will be available in a future update.
                        </div>
                    </div>
                </div>
            </div>

            <!-- Activity Log Tab -->
            <div id="activity" class="profile-tab-content" style="display: none;">
                <div class="profile-card">
                    <div class="profile-card-header">
                        <h5 class="profile-card-title">Recent Activity</h5>
                    </div>
                    <div class="profile-card-body p-0">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Logged in</h6>
                                    <small class="text-muted"><?php echo date('M d, Y h:i A'); ?></small>
                                </div>
                                <p class="mb-1 small text-muted">You logged in from IP address <?php echo $_SERVER['REMOTE_ADDR']; ?></p>
                            </div>

                            <?php if ($lastLogin): ?>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">Previous login</h6>
                                    <small class="text-muted"><?php echo date('M d, Y h:i A', strtotime($lastLogin)); ?></small>
                                </div>
                                <p class="mb-1 small text-muted">Previous session</p>
                            </div>
                            <?php endif; ?>
                        </div>

                        <div class="p-4 text-center">
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Detailed activity logs will be available in a future update.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include profile.js -->
<script src="assets/js/profile.js"></script>

<?php require_once 'includes/footer.php'; ?>