<?php
/**
 * User Helper Functions
 *
 * This file contains helper functions for user management and filtering
 */

/**
 * Get users based on filters and current admin role
 *
 * @param array $filters Associative array of filters
 * @param object $conn Database connection
 * @param object $auth Auth object
 * @param bool $countOnly Whether to return only the count
 * @param int $limit Limit for pagination
 * @param int $offset Offset for pagination
 * @return array|int Array of users or count
 */
function getUsersWithFilters($conn, $auth, $filters = [], $countOnly = false, $limit = null, $offset = null) {
    // Get current admin role and ID
    $currentAdminRole = $auth->getUserRole();
    $currentAdminId = $auth->getUserId();

    // Start building the query
    $baseQuery = "FROM users WHERE 1=1 AND id != 1 AND NOT EXISTS (SELECT 1 FROM admin_users WHERE admin_users.username = users.username)"; // Exclude all admin users
    $params = [];
    $types = "";

    // Apply role-based filtering
    if ($currentAdminRole === 'staff') {
        // Staff can only see users assigned to them
        $baseQuery .= " AND assigned_staff_id = ?";
        $params[] = $currentAdminId;
        $types .= "i";
    } elseif (isset($filters['staff_id']) && !empty($filters['staff_id']) && is_numeric($filters['staff_id'])) {
        // Super admin filtering by staff
        $baseQuery .= " AND assigned_staff_id = ?";
        $params[] = $filters['staff_id'];
        $types .= "i";
    }

    // Apply other filters
    if (isset($filters['search']) && !empty($filters['search'])) {
        $searchTerm = "%" . $filters['search'] . "%";
        $baseQuery .= " AND (name LIKE ? OR username LIKE ? OR phone_number LIKE ?)";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "sss";
    }

    if (isset($filters['status']) && !empty($filters['status'])) {
        if ($filters['status'] === 'active') {
            $baseQuery .= " AND is_active = 1";
        } elseif ($filters['status'] === 'inactive') {
            $baseQuery .= " AND is_active = 0";
        }
    }

    if (isset($filters['premium']) && !empty($filters['premium'])) {
        if ($filters['premium'] === 'yes') {
            $baseQuery .= " AND is_premium = 1";
        } elseif ($filters['premium'] === 'no') {
            $baseQuery .= " AND is_premium = 0";
        }
    }

    if (isset($filters['date_range']) && !empty($filters['date_range'])) {
        if ($filters['date_range'] === 'today') {
            $today = date('Y-m-d');
            $baseQuery .= " AND DATE(created_at) = '$today'";
        } elseif ($filters['date_range'] === 'week') {
            $weekStart = date('Y-m-d', strtotime('monday this week'));
            $baseQuery .= " AND DATE(created_at) >= '$weekStart'";
        } elseif ($filters['date_range'] === 'month') {
            $monthStart = date('Y-m-01');
            $baseQuery .= " AND DATE(created_at) >= '$monthStart'";
        } elseif ($filters['date_range'] === 'year') {
            $yearStart = date('Y-01-01');
            $baseQuery .= " AND DATE(created_at) >= '$yearStart'";
        }
    }

    // Return count if requested
    if ($countOnly) {
        $query = "SELECT COUNT(*) as total " . $baseQuery;
        $stmt = $conn->prepare($query);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_assoc()['total'];
    }

    // Add sorting
    $sort = isset($filters['sort']) && !empty($filters['sort']) ? $filters['sort'] : 'created_at';
    $order = isset($filters['order']) && !empty($filters['order']) ? $filters['order'] : 'DESC';

    // Validate sort field to prevent SQL injection
    $allowedSortFields = ['id', 'name', 'username', 'phone_number', 'created_at', 'last_login', 'is_premium', 'is_active'];
    if (!in_array($sort, $allowedSortFields)) {
        $sort = 'created_at';
    }

    // Validate order direction
    if ($order !== 'ASC' && $order !== 'DESC') {
        $order = 'DESC';
    }

    $query = "SELECT * " . $baseQuery . " ORDER BY $sort $order";

    // Add pagination if requested
    if ($limit !== null && $offset !== null) {
        $query .= " LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        $types .= "ii";
    }

    // Prepare and execute the query
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Fetch all users
    $users = [];
    while ($row = $result->fetch_assoc()) {
        $users[] = $row;
    }

    return $users;
}

/**
 * Get user statistics based on filters and current admin role
 *
 * @param array $filters Associative array of filters
 * @param object $conn Database connection
 * @param object $auth Auth object
 * @return array Statistics
 */
function getUserStatistics($conn, $auth, $filters = []) {
    // Get current admin role and ID
    $currentAdminRole = $auth->getUserRole();
    $currentAdminId = $auth->getUserId();

    // Start building the base query condition
    $baseCondition = "WHERE 1=1 AND id != 1 AND NOT EXISTS (SELECT 1 FROM admin_users WHERE admin_users.username = users.username)"; // Exclude all admin users
    $params = [];
    $types = "";

    // Apply role-based filtering
    if ($currentAdminRole === 'staff') {
        // Staff can only see users assigned to them
        $baseCondition .= " AND assigned_staff_id = ?";
        $params[] = $currentAdminId;
        $types .= "i";
    } elseif (isset($filters['staff_id']) && !empty($filters['staff_id']) && is_numeric($filters['staff_id'])) {
        // Super admin filtering by staff
        $baseCondition .= " AND assigned_staff_id = ?";
        $params[] = $filters['staff_id'];
        $types .= "i";
    }

    // Get total users count
    $totalQuery = "SELECT COUNT(*) as count FROM users $baseCondition";
    $totalStmt = $conn->prepare($totalQuery);

    if (!empty($params)) {
        $totalStmt->bind_param($types, ...$params);
    }

    $totalStmt->execute();
    $totalResult = $totalStmt->get_result();
    $totalUsers = $totalResult->fetch_assoc()['count'];

    // Get active users count
    $activeQuery = "SELECT COUNT(*) as count FROM users $baseCondition AND is_active = 1";
    $activeStmt = $conn->prepare($activeQuery);

    if (!empty($params)) {
        $activeStmt->bind_param($types, ...$params);
    }

    $activeStmt->execute();
    $activeResult = $activeStmt->get_result();
    $activeUsers = $activeResult->fetch_assoc()['count'];

    // Get premium users count
    $premiumQuery = "SELECT COUNT(*) as count FROM users $baseCondition AND is_premium = 1";
    $premiumStmt = $conn->prepare($premiumQuery);

    if (!empty($params)) {
        $premiumStmt->bind_param($types, ...$params);
    }

    $premiumStmt->execute();
    $premiumResult = $premiumStmt->get_result();
    $premiumUsers = $premiumResult->fetch_assoc()['count'];

    // Get new users (registered in the last 7 days)
    $newUsersQuery = "SELECT COUNT(*) as count FROM users $baseCondition AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    $newUsersStmt = $conn->prepare($newUsersQuery);

    if (!empty($params)) {
        $newUsersStmt->bind_param($types, ...$params);
    }

    $newUsersStmt->execute();
    $newUsersResult = $newUsersStmt->get_result();
    $newUsers = $newUsersResult->fetch_assoc()['count'];

    return [
        'total' => $totalUsers,
        'active' => $activeUsers,
        'premium' => $premiumUsers,
        'new' => $newUsers,
        'active_percentage' => $totalUsers > 0 ? round(($activeUsers / $totalUsers) * 100) : 0,
        'premium_percentage' => $totalUsers > 0 ? round(($premiumUsers / $totalUsers) * 100) : 0
    ];
}

/**
 * Get all staff members
 *
 * @param object $conn Database connection
 * @return array Staff members
 */
function getAllStaffMembers($conn) {
    $query = "SELECT id, username, name FROM admin_users WHERE role = 'staff' ORDER BY name";
    $result = $conn->query($query);

    $staff = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $staff[] = $row;
        }
    }

    return $staff;
}

/**
 * Get user IDs assigned to a staff member
 *
 * @param object $conn Database connection
 * @param int $staffId Staff ID (or null for current admin)
 * @param object $auth Auth object (required if staffId is null)
 * @return array User IDs
 */
function getAssignedUserIds($conn, $staffId = null, $auth = null) {
    // If staffId is not provided, get current admin ID
    if ($staffId === null && $auth !== null) {
        $currentAdminRole = $auth->getUserRole();
        $currentAdminId = $auth->getUserId();

        // Only apply filtering for staff role
        if ($currentAdminRole === 'staff') {
            $staffId = $currentAdminId;
        } else {
            // For super admins and admins, return empty array to indicate no filtering
            return [];
        }
    }

    // If we don't have a staff ID at this point, return empty array
    if ($staffId === null) {
        return [];
    }

    // Get all user IDs assigned to this staff member
    $query = "SELECT id FROM users WHERE assigned_staff_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $staffId);
    $stmt->execute();
    $result = $stmt->get_result();

    $userIds = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $userIds[] = $row['id'];
        }
    }

    return $userIds;
}

/**
 * Get courses with enrolled users assigned to a staff member
 *
 * @param object $conn Database connection
 * @param object $auth Auth object
 * @return array Course IDs
 */
function getStaffAssignedCourses($conn, $auth) {
    // Get current admin role and ID
    $currentAdminRole = $auth->getUserRole();
    $currentAdminId = $auth->getUserId();

    // If not staff, return empty array to indicate no filtering
    if ($currentAdminRole !== 'staff') {
        return [];
    }

    // Get all course IDs that have users assigned to this staff member
    $query = "SELECT DISTINCT c.id
              FROM courses c
              JOIN user_course_enrollments e ON c.id = e.course_id
              JOIN users u ON e.user_id = u.id
              WHERE u.assigned_staff_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $currentAdminId);
    $stmt->execute();
    $result = $stmt->get_result();

    $courseIds = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $courseIds[] = $row['id'];
        }
    }

    return $courseIds;
}

/**
 * Check if a user is assigned to the current staff member
 *
 * @param int $userId User ID to check
 * @param object $conn Database connection
 * @param object $auth Auth object
 * @return bool True if user is assigned to current staff or admin is not staff
 */
function isUserAssignedToStaff($userId, $conn, $auth) {
    // Get current admin role and ID
    $currentAdminRole = $auth->getUserRole();
    $currentAdminId = $auth->getUserId();

    // If not staff, return true (no restriction)
    if ($currentAdminRole !== 'staff') {
        return true;
    }

    // Check if user is assigned to this staff member
    $query = "SELECT COUNT(*) as count FROM users WHERE id = ? AND assigned_staff_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ii", $userId, $currentAdminId);
    $stmt->execute();
    $result = $stmt->get_result();

    return $result->fetch_assoc()['count'] > 0;
}

/**
 * Get staff performance statistics
 *
 * @param object $conn Database connection
 * @param int $staffId Staff ID (optional, for filtering)
 * @return array Staff statistics
 */
function getStaffStatistics($conn, $staffId = null) {
    try {
        $stats = [];
        $params = [];
        $types = "";
        $staffCondition = "";

        // Check if is_active column exists in admin_users table
        $checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'is_active'";
        $checkColumnResult = $conn->query($checkColumnQuery);
        $isActiveColumnExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

        // If staff ID is provided, filter by that staff
        if ($staffId !== null && is_numeric($staffId)) {
            $staffCondition = " AND id = ?";
            $params[] = $staffId;
            $types .= "i";
        }

        // Get all staff members
        $staffQuery = "SELECT id, name, username FROM admin_users WHERE role = 'staff'";

        // Add active filter if column exists and we're not filtering by a specific staff ID
        if ($isActiveColumnExists && $staffId === null) {
            $staffQuery .= " AND is_active = 1";
        }

        $staffQuery .= $staffCondition;
        $staffStmt = $conn->prepare($staffQuery);

        if (!empty($params)) {
            $staffStmt->bind_param($types, ...$params);
        }

        $staffStmt->execute();
        $staffResult = $staffStmt->get_result();

        if (!$staffResult) {
            throw new Exception("Error executing staff query: " . $conn->error);
        }

        while ($staff = $staffResult->fetch_assoc()) {
            $staffId = $staff['id'];

            // Get assigned users count
            $usersQuery = "SELECT COUNT(*) as count FROM users WHERE assigned_staff_id = ?";
            $usersStmt = $conn->prepare($usersQuery);
            $usersStmt->bind_param("i", $staffId);
            $usersStmt->execute();
            $usersResult = $usersStmt->get_result();
            if (!$usersResult) {
                throw new Exception("Error executing assigned users query: " . $conn->error);
            }
            $assignedUsers = $usersResult->fetch_assoc()['count'];

            // Get active users count
            $activeQuery = "SELECT COUNT(*) as count FROM users WHERE assigned_staff_id = ? AND is_active = 1";
            $activeStmt = $conn->prepare($activeQuery);
            $activeStmt->bind_param("i", $staffId);
            $activeStmt->execute();
            $activeResult = $activeStmt->get_result();
            if (!$activeResult) {
                throw new Exception("Error executing active users query: " . $conn->error);
            }
            $activeUsers = $activeResult->fetch_assoc()['count'];

            // Get premium users count
            $premiumQuery = "SELECT COUNT(*) as count FROM users WHERE assigned_staff_id = ? AND is_premium = 1";
            $premiumStmt = $conn->prepare($premiumQuery);
            $premiumStmt->bind_param("i", $staffId);
            $premiumStmt->execute();
            $premiumResult = $premiumStmt->get_result();
            if (!$premiumResult) {
                throw new Exception("Error executing premium users query: " . $conn->error);
            }
            $premiumUsers = $premiumResult->fetch_assoc()['count'];

            // Get new users in last 7 days
            $newUsersQuery = "SELECT COUNT(*) as count FROM users WHERE assigned_staff_id = ? AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $newUsersStmt = $conn->prepare($newUsersQuery);
            $newUsersStmt->bind_param("i", $staffId);
            $newUsersStmt->execute();
            $newUsersResult = $newUsersStmt->get_result();
            if (!$newUsersResult) {
                throw new Exception("Error executing new users query: " . $conn->error);
            }
            $newUsers = $newUsersResult->fetch_assoc()['count'];

            // Check if completion_status column exists in workout_records table
            $checkWorkoutColumnQuery = "SHOW COLUMNS FROM workout_records LIKE 'completion_status'";
            $checkWorkoutColumnResult = $conn->query($checkWorkoutColumnQuery);
            $completionStatusExists = ($checkWorkoutColumnResult && $checkWorkoutColumnResult->num_rows > 0);

            // Get workout completion rate
            if ($completionStatusExists) {
                $workoutQuery = "SELECT
                                COUNT(*) as total_workouts,
                                SUM(CASE WHEN completion_status = 'completed' THEN 1 ELSE 0 END) as completed_workouts
                                FROM workout_records wr
                                JOIN users u ON wr.user_id = u.id
                                WHERE u.assigned_staff_id = ?";
            } else {
                // If completion_status doesn't exist, count all workouts as completed
                $workoutQuery = "SELECT
                                COUNT(*) as total_workouts,
                                COUNT(*) as completed_workouts
                                FROM workout_records wr
                                JOIN users u ON wr.user_id = u.id
                                WHERE u.assigned_staff_id = ?";
            }

            $workoutStmt = $conn->prepare($workoutQuery);
            $workoutStmt->bind_param("i", $staffId);
            $workoutStmt->execute();
            $workoutResult = $workoutStmt->get_result();
            if (!$workoutResult) {
                throw new Exception("Error executing workout query: " . $conn->error);
            }
            $workoutData = $workoutResult->fetch_assoc();
            $totalWorkouts = $workoutData['total_workouts'];
            $completedWorkouts = $workoutData['completed_workouts'];
            $completionRate = $totalWorkouts > 0 ? round(($completedWorkouts / $totalWorkouts) * 100) : 0;

            // Get user engagement rate (users who logged in within the last 7 days)
            $engagementQuery = "SELECT COUNT(*) as count FROM users
                               WHERE assigned_staff_id = ?
                               AND last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            $engagementStmt = $conn->prepare($engagementQuery);
            $engagementStmt->bind_param("i", $staffId);
            $engagementStmt->execute();
            $engagementResult = $engagementStmt->get_result();
            if (!$engagementResult) {
                throw new Exception("Error executing engagement query: " . $conn->error);
            }
            $engagedUsers = $engagementResult->fetch_assoc()['count'];
            $engagementRate = $assignedUsers > 0 ? round(($engagedUsers / $assignedUsers) * 100) : 0;

            // Add to stats array
            $stats[] = [
                'id' => $staffId,
                'name' => $staff['name'],
                'username' => $staff['username'],
                'assigned_users' => $assignedUsers,
                'active_users' => $activeUsers,
                'premium_users' => $premiumUsers,
                'new_users' => $newUsers,
                'total_workouts' => $totalWorkouts,
                'completed_workouts' => $completedWorkouts,
                'completion_rate' => $completionRate,
                'engaged_users' => $engagedUsers,
                'engagement_rate' => $engagementRate,
                'active_percentage' => $assignedUsers > 0 ? round(($activeUsers / $assignedUsers) * 100) : 0,
                'premium_percentage' => $assignedUsers > 0 ? round(($premiumUsers / $assignedUsers) * 100) : 0
            ];
        }

        return $stats;
    } catch (Exception $e) {
        // Log the error
        error_log("Error in getStaffStatistics: " . $e->getMessage());

        // Return empty array
        return [];
    }
}

/**
 * Get overall staff performance metrics
 *
 * @param object $conn Database connection
 * @return array Overall staff metrics
 */
function getOverallStaffMetrics($conn) {
    try {
        // Check if is_active column exists in admin_users table
        $checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'is_active'";
        $checkColumnResult = $conn->query($checkColumnQuery);
        $isActiveColumnExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

        // Get total staff count - consider active status if column exists
        if ($isActiveColumnExists) {
            $staffCountQuery = "SELECT COUNT(*) as count FROM admin_users WHERE role = 'staff'";
            $staffCountResult = $conn->query($staffCountQuery);
            if (!$staffCountResult) {
                throw new Exception("Error executing staff count query: " . $conn->error);
            }
            $totalStaff = $staffCountResult->fetch_assoc()['count'];

            // Also get active staff count for reference
            $activeStaffQuery = "SELECT COUNT(*) as count FROM admin_users WHERE role = 'staff' AND is_active = 1";
            $activeStaffResult = $conn->query($activeStaffQuery);
            if (!$activeStaffResult) {
                throw new Exception("Error executing active staff count query: " . $conn->error);
            }
            $activeStaff = $activeStaffResult->fetch_assoc()['count'];
        } else {
            // If is_active column doesn't exist, count all staff
            $staffCountQuery = "SELECT COUNT(*) as count FROM admin_users WHERE role = 'staff'";
            $staffCountResult = $conn->query($staffCountQuery);
            if (!$staffCountResult) {
                throw new Exception("Error executing staff count query: " . $conn->error);
            }
            $totalStaff = $staffCountResult->fetch_assoc()['count'];
            $activeStaff = $totalStaff; // Assume all are active if column doesn't exist
        }

        // Get total users assigned to staff
        $assignedUsersQuery = "SELECT COUNT(*) as count FROM users WHERE assigned_staff_id IS NOT NULL AND assigned_staff_id > 0";
        $assignedUsersResult = $conn->query($assignedUsersQuery);
        if (!$assignedUsersResult) {
            throw new Exception("Error executing assigned users query: " . $conn->error);
        }
        $totalAssignedUsers = $assignedUsersResult->fetch_assoc()['count'];

        // Get average users per staff (use active staff count if available)
        $staffCountForAvg = $activeStaff > 0 ? $activeStaff : $totalStaff;
        $avgUsersPerStaff = $staffCountForAvg > 0 ? round($totalAssignedUsers / $staffCountForAvg, 1) : 0;

        // Get staff with most users
        $topStaffQuery = "SELECT au.id, au.name, COUNT(u.id) as user_count
                         FROM admin_users au
                         LEFT JOIN users u ON au.id = u.assigned_staff_id
                         WHERE au.role = 'staff'";

        // Add active filter if column exists
        if ($isActiveColumnExists) {
            $topStaffQuery .= " AND au.is_active = 1";
        }

        $topStaffQuery .= " GROUP BY au.id
                           ORDER BY user_count DESC
                           LIMIT 1";

        $topStaffResult = $conn->query($topStaffQuery);
        if (!$topStaffResult) {
            throw new Exception("Error executing top staff query: " . $conn->error);
        }
        $topStaff = $topStaffResult->fetch_assoc();

        // Get staff with highest user engagement
        $engagementQuery = "SELECT
                           au.id,
                           au.name,
                           COUNT(DISTINCT CASE WHEN u.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.id ELSE NULL END) as engaged_users,
                           COUNT(DISTINCT u.id) as total_users,
                           ROUND(COUNT(DISTINCT CASE WHEN u.last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.id ELSE NULL END) /
                                 NULLIF(COUNT(DISTINCT u.id), 0) * 100) as engagement_rate
                           FROM admin_users au
                           LEFT JOIN users u ON au.id = u.assigned_staff_id
                           WHERE au.role = 'staff'";

        // Add active filter if column exists
        if ($isActiveColumnExists) {
            $engagementQuery .= " AND au.is_active = 1";
        }

        $engagementQuery .= " AND u.id IS NOT NULL
                             GROUP BY au.id
                             HAVING total_users > 0
                             ORDER BY engagement_rate DESC
                             LIMIT 1";

        $engagementResult = $conn->query($engagementQuery);
        if (!$engagementResult) {
            throw new Exception("Error executing engagement query: " . $conn->error);
        }
        $topEngagementStaff = $engagementResult->fetch_assoc();

        return [
            'total_staff' => $totalStaff,
            'active_staff' => $activeStaff,
            'total_assigned_users' => $totalAssignedUsers,
            'avg_users_per_staff' => $avgUsersPerStaff,
            'top_staff' => $topStaff,
            'top_engagement_staff' => $topEngagementStaff
        ];
    } catch (Exception $e) {
        // Log the error
        error_log("Error in getOverallStaffMetrics: " . $e->getMessage());

        // Return default values
        return [
            'total_staff' => 0,
            'active_staff' => 0,
            'total_assigned_users' => 0,
            'avg_users_per_staff' => 0,
            'top_staff' => null,
            'top_engagement_staff' => null
        ];
    }
}

/**
 * Validate password against security policy
 * @param string $password
 * @return true|string True if valid, or error message string
 */
function validate_password_policy($password) {
    $settingsManager = Settings::getInstance();
    $minLength = $settingsManager->get('password_min_length', 8);
    $requireSpecial = $settingsManager->get('require_special', 1);
    $requireNumber = $settingsManager->get('require_number', 1);

    if (strlen($password) < $minLength) {
        return 'Password must be at least ' . $minLength . ' characters long.';
    }
    if ($requireSpecial && !preg_match('/[^a-zA-Z0-9]/', $password)) {
        return 'Password must contain at least one special character.';
    }
    if ($requireNumber && !preg_match('/[0-9]/', $password)) {
        return 'Password must contain at least one number.';
    }
    return true;
}
