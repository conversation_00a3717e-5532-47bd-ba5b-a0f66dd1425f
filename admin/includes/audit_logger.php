<?php
/**
 * Audit Logger
 *
 * This file contains functions for logging staff activities in the audit_logs table
 */

/**
 * Log a staff activity
 *
 * @param object $conn Database connection
 * @param int $staffId Staff ID
 * @param string $actionType Type of action (e.g., 'user_edit', 'course_add', etc.)
 * @param int|null $affectedUserId ID of the affected user (if applicable)
 * @param string|null $details Additional details about the action
 * @return bool True if logging was successful, false otherwise
 */
function logStaffActivity($conn, $staffId, $actionType, $affectedUserId = null, $details = null) {
    // Check if audit_logs table exists
    $tableExistsQuery = "SHOW TABLES LIKE 'audit_logs'";
    $tableExistsResult = $conn->query($tableExistsQuery);
    $tableExists = ($tableExistsResult && $tableExistsResult->num_rows > 0);

    // If table doesn't exist, return false
    if (!$tableExists) {
        error_log("Audit logs table doesn't exist. Skipping log entry.");
        return false;
    }

    // Prepare the query
    $query = "INSERT INTO audit_logs (staff_id, action_type, timestamp, affected_user_id, details, ip_address, user_agent)
              VALUES (?, ?, NOW(), ?, ?, ?, ?)";

    // Get IP address and user agent
    $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

    // Prepare and execute the statement
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ississ", $staffId, $actionType, $affectedUserId, $details, $ipAddress, $userAgent);
    $result = $stmt->execute();
    $stmt->close();

    return $result;
}

/**
 * Get audit logs with filtering options
 *
 * @param object $conn Database connection
 * @param array $filters Associative array of filters
 * @param int $limit Limit for pagination
 * @param int $offset Offset for pagination
 * @return array Array of audit logs
 */
function getAuditLogs($conn, $filters = [], $limit = 50, $offset = 0) {
    // Check if audit_logs table exists
    $tableExistsQuery = "SHOW TABLES LIKE 'audit_logs'";
    $tableExistsResult = $conn->query($tableExistsQuery);
    $tableExists = ($tableExistsResult && $tableExistsResult->num_rows > 0);

    // If table doesn't exist, return empty array
    if (!$tableExists) {
        return [];
    }

    // Start building the query
    $query = "SELECT al.*,
              au.name as staff_name, au.username as staff_username,
              u.name as affected_user_name, u.username as affected_user_username
              FROM audit_logs al
              LEFT JOIN admin_users au ON al.staff_id = au.id
              LEFT JOIN users u ON al.affected_user_id = u.id
              WHERE 1=1";

    $params = [];
    $types = "";

    // Apply filters
    if (isset($filters['staff_id']) && !empty($filters['staff_id'])) {
        $query .= " AND al.staff_id = ?";
        $params[] = $filters['staff_id'];
        $types .= "i";
    }

    if (isset($filters['action_type']) && !empty($filters['action_type'])) {
        $query .= " AND al.action_type = ?";
        $params[] = $filters['action_type'];
        $types .= "s";
    }

    if (isset($filters['affected_user_id']) && !empty($filters['affected_user_id'])) {
        $query .= " AND al.affected_user_id = ?";
        $params[] = $filters['affected_user_id'];
        $types .= "i";
    }

    if (isset($filters['date_from']) && !empty($filters['date_from'])) {
        $query .= " AND DATE(al.timestamp) >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }

    if (isset($filters['date_to']) && !empty($filters['date_to'])) {
        $query .= " AND DATE(al.timestamp) <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }

    if (isset($filters['search']) && !empty($filters['search'])) {
        $query .= " AND (al.details LIKE ? OR au.name LIKE ? OR au.username LIKE ? OR u.name LIKE ? OR u.username LIKE ?)";
        $searchTerm = "%" . $filters['search'] . "%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "sssss";
    }

    // Add ordering
    $query .= " ORDER BY al.timestamp DESC";

    // Add pagination
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";

    // Prepare and execute the statement
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();

    // Fetch all logs
    $logs = [];
    while ($row = $result->fetch_assoc()) {
        $logs[] = $row;
    }

    $stmt->close();

    return $logs;
}

/**
 * Count total audit logs with filtering options
 *
 * @param object $conn Database connection
 * @param array $filters Associative array of filters
 * @return int Total count of audit logs
 */
function countAuditLogs($conn, $filters = []) {
    // Check if audit_logs table exists
    $tableExistsQuery = "SHOW TABLES LIKE 'audit_logs'";
    $tableExistsResult = $conn->query($tableExistsQuery);
    $tableExists = ($tableExistsResult && $tableExistsResult->num_rows > 0);

    // If table doesn't exist, return 0
    if (!$tableExists) {
        return 0;
    }

    // Start building the query
    $query = "SELECT COUNT(*) as total
              FROM audit_logs al
              LEFT JOIN admin_users au ON al.staff_id = au.id
              LEFT JOIN users u ON al.affected_user_id = u.id
              WHERE 1=1";

    $params = [];
    $types = "";

    // Apply filters (same as getAuditLogs)
    if (isset($filters['staff_id']) && !empty($filters['staff_id'])) {
        $query .= " AND al.staff_id = ?";
        $params[] = $filters['staff_id'];
        $types .= "i";
    }

    if (isset($filters['action_type']) && !empty($filters['action_type'])) {
        $query .= " AND al.action_type = ?";
        $params[] = $filters['action_type'];
        $types .= "s";
    }

    if (isset($filters['affected_user_id']) && !empty($filters['affected_user_id'])) {
        $query .= " AND al.affected_user_id = ?";
        $params[] = $filters['affected_user_id'];
        $types .= "i";
    }

    if (isset($filters['date_from']) && !empty($filters['date_from'])) {
        $query .= " AND DATE(al.timestamp) >= ?";
        $params[] = $filters['date_from'];
        $types .= "s";
    }

    if (isset($filters['date_to']) && !empty($filters['date_to'])) {
        $query .= " AND DATE(al.timestamp) <= ?";
        $params[] = $filters['date_to'];
        $types .= "s";
    }

    if (isset($filters['search']) && !empty($filters['search'])) {
        $query .= " AND (al.details LIKE ? OR au.name LIKE ? OR au.username LIKE ? OR u.name LIKE ? OR u.username LIKE ?)";
        $searchTerm = "%" . $filters['search'] . "%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $types .= "sssss";
    }

    // Prepare and execute the statement
    $stmt = $conn->prepare($query);

    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }

    $stmt->execute();
    $result = $stmt->get_result();
    $count = $result->fetch_assoc()['total'];

    $stmt->close();

    return $count;
}

/**
 * Get all unique action types from the audit logs
 *
 * @param object $conn Database connection
 * @return array Array of action types
 */
function getActionTypes($conn) {
    // Check if audit_logs table exists
    $tableExistsQuery = "SHOW TABLES LIKE 'audit_logs'";
    $tableExistsResult = $conn->query($tableExistsQuery);
    $tableExists = ($tableExistsResult && $tableExistsResult->num_rows > 0);

    // If table doesn't exist, return empty array
    if (!$tableExists) {
        return [];
    }

    $query = "SELECT DISTINCT action_type FROM audit_logs ORDER BY action_type";
    $result = $conn->query($query);

    $actionTypes = [];
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $actionTypes[] = $row['action_type'];
        }
    }

    return $actionTypes;
}
?>
