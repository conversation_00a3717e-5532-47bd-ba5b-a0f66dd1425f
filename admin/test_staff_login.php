<?php
/**
 * Test Staff Login
 *
 * This script tests the staff login functionality after disabling development mode.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Initialize auth
$auth = new Auth();

// Check if a staff user exists
$query = "SELECT id, username, role FROM admin_users WHERE role = 'staff' LIMIT 1";
$result = $conn->query($query);

echo "<h1>Staff Login Test</h1>";

// Display development mode status
echo "<h2>Development Mode Status</h2>";
echo "<p>DEV_MODE constant: " . (defined('DEV_MODE') && DEV_MODE ? "true" : "false") . "</p>";

// Check if staff user exists
if ($result && $result->num_rows > 0) {
    $staffUser = $result->fetch_assoc();
    echo "<h2>Staff User Found</h2>";
    echo "<p>Username: " . htmlspecialchars($staffUser['username']) . "</p>";
    echo "<p>Role: " . htmlspecialchars($staffUser['role']) . "</p>";
    echo "<p>ID: " . htmlspecialchars($staffUser['id']) . "</p>";
    
    // Check if staff user has permissions
    $query = "SELECT p.name FROM admin_user_permissions up 
              JOIN admin_permissions p ON up.permission_id = p.id 
              WHERE up.admin_user_id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $staffUser['id']);
    $stmt->execute();
    $permResult = $stmt->get_result();
    
    echo "<h3>Staff Permissions</h3>";
    if ($permResult && $permResult->num_rows > 0) {
        echo "<ul>";
        while ($perm = $permResult->fetch_assoc()) {
            echo "<li>" . htmlspecialchars($perm['name']) . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>No permissions found for this staff user.</p>";
    }
    
    // Create a reset password link
    echo "<h3>Reset Password</h3>";
    echo "<p>If you need to reset the password for this staff user, use the form below:</p>";
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_password') {
        $newPassword = $_POST['new_password'] ?? '';
        $userId = $_POST['user_id'] ?? '';
        
        if (!empty($newPassword) && !empty($userId)) {
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
            $updateStmt = $conn->prepare("UPDATE admin_users SET password = ? WHERE id = ?");
            $updateStmt->bind_param("si", $hashedPassword, $userId);
            
            if ($updateStmt->execute()) {
                echo "<div style='color:green;padding:10px;background:#e8f5e9;border-radius:5px;margin-bottom:15px;'>
                    Password updated successfully! You can now login with the new password.
                </div>";
            } else {
                echo "<div style='color:red;padding:10px;background:#ffebee;border-radius:5px;margin-bottom:15px;'>
                    Failed to update password. Error: " . $conn->error . "
                </div>";
            }
        } else {
            echo "<div style='color:red;padding:10px;background:#ffebee;border-radius:5px;margin-bottom:15px;'>
                Please provide a new password.
            </div>";
        }
    }
    
    echo "<form method='post' style='background:#f5f5f5;padding:15px;border-radius:5px;'>";
    echo "<input type='hidden' name='action' value='reset_password'>";
    echo "<input type='hidden' name='user_id' value='" . htmlspecialchars($staffUser['id']) . "'>";
    echo "<div style='margin-bottom:10px;'>";
    echo "<label for='new_password' style='display:block;margin-bottom:5px;'>New Password:</label>";
    echo "<input type='password' id='new_password' name='new_password' required style='padding:8px;width:100%;'>";
    echo "</div>";
    echo "<button type='submit' style='background:#4CAF50;color:white;border:none;padding:10px 15px;border-radius:4px;cursor:pointer;'>Reset Password</button>";
    echo "</form>";
    
    // Add a login link
    echo "<h3>Login</h3>";
    echo "<p>Try logging in with the staff account:</p>";
    echo "<a href='login.php' style='display:inline-block;background:#2196F3;color:white;text-decoration:none;padding:10px 15px;border-radius:4px;'>Go to Login Page</a>";
    
} else {
    echo "<h2>No Staff Users Found</h2>";
    echo "<p>No staff users were found in the database. You need to create a staff user first.</p>";
    
    // Add a link to create a staff user
    echo "<h3>Create Staff User</h3>";
    echo "<p>You can create a staff user from the admin panel:</p>";
    echo "<a href='admin_add.php' style='display:inline-block;background:#2196F3;color:white;text-decoration:none;padding:10px 15px;border-radius:4px;'>Create Staff User</a>";
}

// Add a link to go back to the admin panel
echo "<p style='margin-top:20px;'><a href='index.php'>Return to Admin Panel</a></p>";
?>
