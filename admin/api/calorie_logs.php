<?php
require_once 'config.php';

// Validate token
$tokenData = validateToken();
$userId = $tokenData['user_id'];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get calorie logs for the user
        $conn = getDbConnection();

        // Get date from query parameters (default to today)
        $date = isset($_GET['date']) ? $_GET['date'] : date('Y-m-d');

        // Check if we're requesting a date range
        $startDate = isset($_GET['start_date']) ? $_GET['start_date'] : null;
        $endDate = isset($_GET['end_date']) ? $_GET['end_date'] : null;

        if ($startDate && $endDate) {
            // Get logs for a date range
            $query = "SELECT * FROM calorie_logs
                     WHERE user_id = ?
                     AND log_date BETWEEN ? AND ?
                     ORDER BY log_date DESC, meal_type, created_at";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("iss", $userId, $startDate, $endDate);
        } else {
            // Get logs for a specific date
            $query = "SELECT * FROM calorie_logs
                     WHERE user_id = ? AND log_date = ?
                     ORDER BY meal_type, created_at";
            $stmt = $conn->prepare($query);
            $stmt->bind_param("is", $userId, $date);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        $logs = [];
        while ($row = $result->fetch_assoc()) {
            $logs[] = $row;
        }

        // Get calorie goals
        $goalsQuery = "SELECT * FROM calorie_goals
                      WHERE user_id = ?
                      AND is_active = 1
                      AND (end_date IS NULL OR end_date >= CURDATE())
                      ORDER BY start_date DESC LIMIT 1";
        $goalsStmt = $conn->prepare($goalsQuery);
        $goalsStmt->bind_param("i", $userId);
        $goalsStmt->execute();
        $goalsResult = $goalsStmt->get_result();

        $goals = null;
        if ($goalsResult->num_rows > 0) {
            $goals = $goalsResult->fetch_assoc();
        }

        // Calculate daily totals if we have a date range
        $dailyTotals = [];
        if ($startDate && $endDate) {
            $totalsQuery = "SELECT
                           log_date,
                           SUM(calories * quantity) as total_calories,
                           SUM(protein * quantity) as total_protein,
                           SUM(carbs * quantity) as total_carbs,
                           SUM(fat * quantity) as total_fat
                           FROM calorie_logs
                           WHERE user_id = ?
                           AND log_date BETWEEN ? AND ?
                           GROUP BY log_date
                           ORDER BY log_date";
            $totalsStmt = $conn->prepare($totalsQuery);
            $totalsStmt->bind_param("iss", $userId, $startDate, $endDate);
            $totalsStmt->execute();
            $totalsResult = $totalsStmt->get_result();

            while ($row = $totalsResult->fetch_assoc()) {
                $dailyTotals[$row['log_date']] = [
                    'calories' => (int)$row['total_calories'],
                    'protein' => (float)$row['total_protein'],
                    'carbs' => (float)$row['total_carbs'],
                    'fat' => (float)$row['total_fat']
                ];
            }
        }

        // Return response
        returnResponse([
            'logs' => $logs,
            'goals' => $goals,
            'daily_totals' => $dailyTotals
        ]);
        break;

    case 'POST':
        // Add a new calorie log entry
        $conn = getDbConnection();

        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate required fields
        if (!isset($data['log_date']) || !isset($data['meal_type']) || !isset($data['food_name']) || !isset($data['calories'])) {
            returnError('Missing required fields', 400);
        }

        // Prepare query
        $query = "INSERT INTO calorie_logs
                 (user_id, log_date, meal_type, food_item_id, food_name, calories, protein, carbs, fat, quantity, serving_size, notes, image_url)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);

        // Set default values for optional fields
        $foodItemId = isset($data['food_item_id']) ? $data['food_item_id'] : null;
        $protein = isset($data['protein']) ? $data['protein'] : null;
        $carbs = isset($data['carbs']) ? $data['carbs'] : null;
        $fat = isset($data['fat']) ? $data['fat'] : null;
        $quantity = isset($data['quantity']) ? $data['quantity'] : 1;
        $servingSize = isset($data['serving_size']) ? $data['serving_size'] : null;
        $notes = isset($data['notes']) ? $data['notes'] : null;
        $imageUrl = isset($data['image_url']) ? $data['image_url'] : null;

        // Correct type string: i (userId) + s (log_date) + s (meal_type) + i (food_item_id) + s (food_name) +
        // i (calories) + d (protein) + d (carbs) + d (fat) + d (quantity) + s (serving_size) + s (notes) + s (image_url)
        // Count: 13 parameters total
        $stmt->bind_param("issisddddssss", $userId, $data['log_date'], $data['meal_type'], $foodItemId,
                         $data['food_name'], $data['calories'], $protein, $carbs, $fat, $quantity, $servingSize, $notes, $imageUrl);

        if ($stmt->execute()) {
            $logId = $conn->insert_id;

            // Get the inserted record
            $selectQuery = "SELECT * FROM calorie_logs WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $logId);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $log = $result->fetch_assoc();

            returnResponse([
                'success' => true,
                'message' => 'Calorie log entry added successfully',
                'log' => $log
            ]);
        } else {
            returnError('Failed to add calorie log entry: ' . $stmt->error, 500);
        }
        break;

    case 'PUT':
        // Update an existing calorie log entry
        $conn = getDbConnection();

        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);

        // Validate required fields
        if (!isset($data['id'])) {
            returnError('Missing log entry ID', 400);
        }

        // Check if the log entry exists and belongs to the user
        $checkQuery = "SELECT * FROM calorie_logs WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $data['id'], $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();

        if ($result->num_rows === 0) {
            returnError('Log entry not found or access denied', 404);
        }

        // Build update query
        $updateFields = [];
        $params = [];
        $types = "";

        if (isset($data['log_date'])) {
            $updateFields[] = "log_date = ?";
            $params[] = $data['log_date'];
            $types .= "s";
        }

        if (isset($data['meal_type'])) {
            $updateFields[] = "meal_type = ?";
            $params[] = $data['meal_type'];
            $types .= "s";
        }

        if (isset($data['food_name'])) {
            $updateFields[] = "food_name = ?";
            $params[] = $data['food_name'];
            $types .= "s";
        }

        if (isset($data['calories'])) {
            $updateFields[] = "calories = ?";
            $params[] = $data['calories'];
            $types .= "i";
        }

        if (isset($data['protein'])) {
            $updateFields[] = "protein = ?";
            $params[] = $data['protein'];
            $types .= "d";
        }

        if (isset($data['carbs'])) {
            $updateFields[] = "carbs = ?";
            $params[] = $data['carbs'];
            $types .= "d";
        }

        if (isset($data['fat'])) {
            $updateFields[] = "fat = ?";
            $params[] = $data['fat'];
            $types .= "d";
        }

        if (isset($data['quantity'])) {
            $updateFields[] = "quantity = ?";
            $params[] = $data['quantity'];
            $types .= "d";
        }

        if (isset($data['serving_size'])) {
            $updateFields[] = "serving_size = ?";
            $params[] = $data['serving_size'];
            $types .= "s";
        }

        if (isset($data['notes'])) {
            $updateFields[] = "notes = ?";
            $params[] = $data['notes'];
            $types .= "s";
        }

        if (isset($data['image_url'])) {
            $updateFields[] = "image_url = ?";
            $params[] = $data['image_url'];
            $types .= "s";
        }

        if (empty($updateFields)) {
            returnError('No fields to update', 400);
        }

        // Add ID and user_id to params
        $params[] = $data['id'];
        $params[] = $userId;
        $types .= "ii";

        $updateQuery = "UPDATE calorie_logs SET " . implode(", ", $updateFields) . " WHERE id = ? AND user_id = ?";
        $updateStmt = $conn->prepare($updateQuery);

        // Bind parameters dynamically
        $bindParams = array_merge([$updateStmt, $types], $params);
        call_user_func_array('mysqli_stmt_bind_param', $bindParams);

        if ($updateStmt->execute()) {
            // Get the updated record
            $selectQuery = "SELECT * FROM calorie_logs WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $data['id']);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $log = $result->fetch_assoc();

            returnResponse([
                'success' => true,
                'message' => 'Calorie log entry updated successfully',
                'log' => $log
            ]);
        } else {
            returnError('Failed to update calorie log entry: ' . $updateStmt->error, 500);
        }
        break;

    case 'DELETE':
        // Delete a calorie log entry
        $conn = getDbConnection();

        // Get log ID from query parameters
        $logId = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if ($logId <= 0) {
            returnError('Invalid log entry ID', 400);
        }

        // Check if the log entry exists and belongs to the user
        $checkQuery = "SELECT * FROM calorie_logs WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $logId, $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();

        if ($result->num_rows === 0) {
            returnError('Log entry not found or access denied', 404);
        }

        // Delete the log entry
        $deleteQuery = "DELETE FROM calorie_logs WHERE id = ? AND user_id = ?";
        $deleteStmt = $conn->prepare($deleteQuery);
        $deleteStmt->bind_param("ii", $logId, $userId);

        if ($deleteStmt->execute()) {
            returnResponse([
                'success' => true,
                'message' => 'Calorie log entry deleted successfully'
            ]);
        } else {
            returnError('Failed to delete calorie log entry: ' . $deleteStmt->error, 500);
        }
        break;

    default:
        returnError('Method not allowed', 405);
}
