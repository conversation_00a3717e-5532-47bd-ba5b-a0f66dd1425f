<?php
// Set appropriate headers for API response
header('Content-Type: application/json');
// Prevent caching
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Disable output buffering
if (ob_get_level()) ob_end_clean();

// Simple mock response for testing
$mockResponse = [
    'success' => true,
    'image_url' => 'admin/assets/food_images/food_test_' . time() . '.jpg',
    'ai_analysis' => [
        'food_name' => 'Test Food Item',
        'calories' => 350,
        'protein' => 15.5,
        'carbs' => 45.2,
        'fat' => 12.3,
        'serving_size' => '1 serving'
    ]
];

// Return the mock response
echo json_encode($mockResponse);
exit;
?>
