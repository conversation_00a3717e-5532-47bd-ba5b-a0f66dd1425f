<?php
// Database configuration
if (!defined('DB_HOST')) {
    define('DB_HOST', 'localhost');
}
if (!defined('DB_USER')) {
    define('DB_USER', 'myclo4dz_new_kftdb');
}
if (!defined('DB_PASS')) {
    define('DB_PASS', 'U.q.!)hDK+gR');
}
if (!defined('DB_NAME')) {
    define('DB_NAME', 'myclo4dz_new_kftdb');
}
if (!defined('APP_URL')) {
    define('APP_URL', 'https://mycloudforge.com/admin');
}

// API configuration
if (!defined('API_VERSION')) {
    define('API_VERSION', '1.0');
}
if (!defined('TOKEN_EXPIRY')) {
    define('TOKEN_EXPIRY', 86400 * 30); // 30 days
}
// JWT secret for token generation
if (!defined('APP_SECRET')) {
    define('APP_SECRET', 'kft_fitness_jwt_secret_key_2025');
}

// Session configuration
if (!defined('SESSION_NAME')) {
    define('SESSION_NAME', 'kft_admin_session');
}
if (!defined('SESSION_LIFETIME')) {
    define('SESSION_LIFETIME', 86400); // 24 hours
}

// Include required files
require_once __DIR__ . '/../includes/database.php';
require_once __DIR__ . '/../includes/settings.php';
require_once __DIR__ . '/../includes/cors.php';

// Initialize settings
$settingsManager = Settings::getInstance();

// Check if development mode is defined, otherwise set to false for enforcing authentication
if (!defined('DEV_MODE')) {
    define('DEV_MODE', false);
}

// Development mode: Don't display errors directly, handle them as JSON
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);

// Set up custom error handler to convert PHP errors to JSON
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    $error = [
        'success' => false,
        'message' => "PHP Error: $errstr in $errfile on line $errline",
        'error_type' => 'php_error',
        'error_code' => $errno,
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ];

    header('Content-Type: application/json; charset=UTF-8');
    echo json_encode($error);
    exit;
});

// Set up exception handler to convert uncaught exceptions to JSON
set_exception_handler(function($exception) {
    $error = [
        'success' => false,
        'message' => "Exception: " . $exception->getMessage(),
        'error_type' => 'exception',
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ];

    header('Content-Type: application/json; charset=UTF-8');
    echo json_encode($error);
    exit;
});

// Log that we're in development mode
error_log('API running in development mode');

// Set content type header
header('Content-Type: application/json; charset=UTF-8');

// Helper function to return error response
function returnError($message, $statusCode = 400) {
    http_response_code($statusCode);
    echo json_encode([
        'success' => false,
        'message' => $message,
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ]);
    exit;
}

// Helper function to return success response
function returnResponse($data) {
    $data['_server_time'] = date('Y-m-d H:i:s');
    $data['_api_version'] = '1.0';
    $data['_dev_mode'] = defined('DEV_MODE') && DEV_MODE === true;

    // Ensure proper JSON encoding with no extra spaces
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// Helper function to return a clean JSON response with no extra spaces
function returnCleanResponse($data) {
    // Add standard metadata
    $data['_server_time'] = date('Y-m-d H:i:s');
    $data['_api_version'] = '1.0';
    $data['_dev_mode'] = defined('DEV_MODE') && DEV_MODE === true;

    // Set content type header
    header('Content-Type: application/json; charset=UTF-8');

    // Use JSON_UNESCAPED_UNICODE to prevent Unicode escaping
    // Use JSON_UNESCAPED_SLASHES to prevent escaping forward slashes
    // Use JSON_PRESERVE_ZERO_FRACTION to ensure numbers are consistent
    $json = json_encode($data,
        JSON_UNESCAPED_UNICODE |
        JSON_UNESCAPED_SLASHES |
        JSON_PRESERVE_ZERO_FRACTION
    );

    // Log the JSON in development mode
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('Clean JSON response: ' . $json);
    }

    echo $json;
    exit;
}

// Connect to database
function getDbConnection() {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);

    if ($conn->connect_error) {
        returnError('Database connection failed: ' . $conn->connect_error, 500);
    }

    $conn->set_charset('utf8mb4');
    return $conn;
}

// Helper functions are defined above

function sanitizeInput($input) {
    if (is_array($input)) {
        foreach ($input as $key => $value) {
            $input[$key] = sanitizeInput($value);
        }
    } else {
        $input = trim($input);
        $input = stripslashes($input);
        $input = htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
    }
    return $input;
}

// Authentication functions
function generateToken($userId) {
    $token = bin2hex(random_bytes(32));
    $expiresAt = date('Y-m-d H:i:s', time() + TOKEN_EXPIRY);

    $conn = getDbConnection();

    // Delete any existing tokens for this user
    $deleteQuery = "DELETE FROM api_tokens WHERE user_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $userId);
    $stmt->execute();

    // Create new token
    $insertQuery = "INSERT INTO api_tokens (user_id, token, expires_at) VALUES (?, ?, ?)";
    $stmt = $conn->prepare($insertQuery);
    $stmt->bind_param("iss", $userId, $token, $expiresAt);

    if (!$stmt->execute()) {
        returnError('Failed to generate token', 500);
    }

    return [
        'token' => $token,
        'expires_at' => $expiresAt
    ];
}

function validateToken() {
    // Get authorization header
    $headers = getallheaders();

    // Case-insensitive header lookup (headers can be lowercase in some environments)
    $authHeader = '';
    foreach ($headers as $key => $value) {
        if (strtolower($key) === 'authorization') {
            $authHeader = $value;
            break;
        }
    }

    // Check if token is provided
    if (empty($authHeader) || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        // Log the headers in development mode
        if (defined('DEV_MODE') && DEV_MODE === true) {
            error_log('Headers received: ' . print_r($headers, true));
            error_log('Authorization header missing or invalid: ' . $authHeader);

            // In development mode, allow access with a default user ID
            error_log('DEV MODE: Bypassing authentication, using user ID 1');
            return [
                'user_id' => 1,
                'username' => 'dev_user',
                'name' => 'Development User',
                'is_premium' => 1,
                'is_active' => 1
            ];
        }
        returnError('Authorization token required', 401);
    }

    $token = $matches[1];
    $conn = getDbConnection();

    // Check if token exists and is valid
    $query = "SELECT t.*, u.id as user_id, u.username, u.name, u.is_premium, u.is_active
              FROM api_tokens t
              JOIN users u ON t.user_id = u.id
              WHERE t.token = ? AND t.expires_at > NOW()";

    // Log the query in development mode
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('Token validation query: ' . $query);
        error_log('Token being validated: ' . $token);
    }

    $stmt = $conn->prepare($query);
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        // Check if token exists but is expired
        $expiredQuery = "SELECT expires_at FROM api_tokens WHERE token = ?";
        $expiredStmt = $conn->prepare($expiredQuery);
        $expiredStmt->bind_param("s", $token);
        $expiredStmt->execute();
        $expiredResult = $expiredStmt->get_result();

        if ($expiredResult->num_rows > 0) {
            $expiredData = $expiredResult->fetch_assoc();
            if (defined('DEV_MODE') && DEV_MODE === true) {
                error_log('Token found but expired. Expiry date: ' . $expiredData['expires_at']);
                error_log('Current server time: ' . date('Y-m-d H:i:s'));

                // In development mode, allow access with a default user ID even if token is expired
                error_log('DEV MODE: Bypassing expired token, using user ID 1');
                return [
                    'user_id' => 1,
                    'username' => 'dev_user',
                    'name' => 'Development User',
                    'is_premium' => 1,
                    'is_active' => 1
                ];
            }
            returnError('Token has expired', 401);
        } else {
            if (defined('DEV_MODE') && DEV_MODE === true) {
                error_log('Token not found in database');

                // In development mode, allow access with a default user ID even if token is not found
                error_log('DEV MODE: Bypassing token validation, using user ID 1');
                return [
                    'user_id' => 1,
                    'username' => 'dev_user',
                    'name' => 'Development User',
                    'is_premium' => 1,
                    'is_active' => 1
                ];
            }
            returnError('Invalid token', 401);
        }
    }

    $tokenData = $result->fetch_assoc();

    // Check if user is active
    if (!$tokenData['is_active']) {
        returnError('User account is inactive', 403);
    }

    // Enforce device ID check for all API requests
    $userId = $tokenData['user_id'];
    $deviceIdFromRequest = $_SERVER['HTTP_X_DEVICE_ID'] ?? null; // Get from header
    $stmt = $conn->prepare("SELECT device_id FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    if (!$user || !$deviceIdFromRequest || $user['device_id'] !== $deviceIdFromRequest) {
        returnError('Account is being used on another device', 401);
    }

    return $tokenData;
}

// Get request data
function getRequestData() {
    // First try to get JSON data from the request body
    $rawInput = file_get_contents('php://input');
    $data = json_decode($rawInput, true);

    // Log raw input in development mode
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('Raw request input: ' . $rawInput);
        error_log('JSON decode result: ' . print_r($data, true));
        error_log('JSON last error: ' . json_last_error_msg());
    }

    if (json_last_error() !== JSON_ERROR_NONE) {
        // If not valid JSON, try POST data
        $data = $_POST;

        // Log POST data in development mode
        if (defined('DEV_MODE') && DEV_MODE === true) {
            error_log('Using POST data: ' . print_r($data, true));
        }
    }

    // If still empty, check if we have a content type header for application/x-www-form-urlencoded
    if (empty($data) && isset($_SERVER['CONTENT_TYPE']) &&
        strpos($_SERVER['CONTENT_TYPE'], 'application/x-www-form-urlencoded') !== false) {
        parse_str($rawInput, $data);

        // Log parsed form data in development mode
        if (defined('DEV_MODE') && DEV_MODE === true) {
            error_log('Parsed form data: ' . print_r($data, true));
        }
    }

    return $data ?? [];
}
