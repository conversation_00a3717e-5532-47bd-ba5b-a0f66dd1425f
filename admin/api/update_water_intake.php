<?php
require_once 'config.php';

// Validate token
$tokenData = validateToken();
$userId = $tokenData['user_id'];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get water intake data for today
        $conn = getDbConnection();
        $today = date('Y-m-d');
        
        $query = "SELECT * FROM water_intake WHERE user_id = ? AND date = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("is", $userId, $today);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // No data for today, return default
            returnResponse([
                'user_id' => $userId,
                'date' => $today,
                'current_intake' => 0,
                'target_intake' => 2000,
                'logs' => []
            ]);
        } else {
            $intake = $result->fetch_assoc();
            
            // Get logs for today
            $logsQuery = "SELECT * FROM water_logs WHERE user_id = ? AND DATE(timestamp) = ? ORDER BY timestamp ASC";
            $logsStmt = $conn->prepare($logsQuery);
            $logsStmt->bind_param("is", $userId, $today);
            $logsStmt->execute();
            $logsResult = $logsStmt->get_result();
            
            $logs = [];
            while ($log = $logsResult->fetch_assoc()) {
                $logs[] = [
                    'id' => (int)$log['id'],
                    'amount' => (int)$log['amount'],
                    'timestamp' => $log['timestamp']
                ];
            }
            
            returnResponse([
                'user_id' => (int)$intake['user_id'],
                'date' => $intake['date'],
                'current_intake' => (int)$intake['current_intake'],
                'target_intake' => (int)$intake['target_intake'],
                'logs' => $logs
            ]);
        }
        break;
        
    case 'POST':
        // Add water intake
        $data = getRequestData();
        $conn = getDbConnection();
        
        // Validate required fields
        if (!isset($data['amount']) || !is_numeric($data['amount']) || $data['amount'] <= 0) {
            returnError('Valid amount is required');
        }
        
        $amount = (int)$data['amount'];
        $currentIntake = isset($data['current_intake']) ? (int)$data['current_intake'] : 0;
        $targetIntake = isset($data['target_intake']) ? (int)$data['target_intake'] : 2000;
        $today = date('Y-m-d');
        $now = date('Y-m-d H:i:s');
        
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Insert water log
            $logQuery = "INSERT INTO water_logs (user_id, amount, timestamp) VALUES (?, ?, ?)";
            $logStmt = $conn->prepare($logQuery);
            $logStmt->bind_param("iis", $userId, $amount, $now);
            
            if (!$logStmt->execute()) {
                throw new Exception('Failed to insert water log: ' . $conn->error);
            }
            
            $logId = $conn->insert_id;
            
            // Update or insert daily water intake
            $checkQuery = "SELECT current_intake FROM water_intake WHERE user_id = ? AND date = ?";
            $checkStmt = $conn->prepare($checkQuery);
            $checkStmt->bind_param("is", $userId, $today);
            $checkStmt->execute();
            $checkResult = $checkStmt->get_result();
            
            if ($checkResult->num_rows === 0) {
                // Insert new daily record
                $insertQuery = "INSERT INTO water_intake (user_id, date, current_intake, target_intake, last_updated) VALUES (?, ?, ?, ?, ?)";
                $insertStmt = $conn->prepare($insertQuery);
                $insertStmt->bind_param("isiss", $userId, $today, $amount, $targetIntake, $now);
                
                if (!$insertStmt->execute()) {
                    throw new Exception('Failed to insert water intake: ' . $conn->error);
                }
                
                $newCurrentIntake = $amount;
            } else {
                // Update existing daily record
                $existing = $checkResult->fetch_assoc();
                $newCurrentIntake = $existing['current_intake'] + $amount;
                
                $updateQuery = "UPDATE water_intake SET current_intake = ?, target_intake = ?, last_updated = ? WHERE user_id = ? AND date = ?";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->bind_param("iisis", $newCurrentIntake, $targetIntake, $now, $userId, $today);
                
                if (!$updateStmt->execute()) {
                    throw new Exception('Failed to update water intake: ' . $conn->error);
                }
            }
            
            // Commit transaction
            $conn->commit();
            
            returnResponse([
                'success' => true,
                'message' => 'Water intake updated successfully',
                'log_id' => $logId,
                'current_intake' => $newCurrentIntake,
                'target_intake' => $targetIntake,
                'amount_added' => $amount,
                'timestamp' => $now
            ]);
            
        } catch (Exception $e) {
            // Rollback transaction
            $conn->rollback();
            returnError('Failed to update water intake: ' . $e->getMessage(), 500);
        }
        break;
        
    case 'PUT':
        // Update target intake for today
        $data = getRequestData();
        $conn = getDbConnection();
        
        if (!isset($data['target_intake']) || !is_numeric($data['target_intake']) || $data['target_intake'] <= 0) {
            returnError('Valid target_intake is required');
        }
        
        $targetIntake = (int)$data['target_intake'];
        $today = date('Y-m-d');
        $now = date('Y-m-d H:i:s');
        
        // Update or insert daily water intake record
        $checkQuery = "SELECT id FROM water_intake WHERE user_id = ? AND date = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("is", $userId, $today);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        
        if ($checkResult->num_rows === 0) {
            // Insert new daily record with target
            $insertQuery = "INSERT INTO water_intake (user_id, date, current_intake, target_intake, last_updated) VALUES (?, ?, 0, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("isis", $userId, $today, $targetIntake, $now);
            
            if (!$insertStmt->execute()) {
                returnError('Failed to insert water intake target: ' . $conn->error, 500);
            }
        } else {
            // Update existing daily record
            $updateQuery = "UPDATE water_intake SET target_intake = ?, last_updated = ? WHERE user_id = ? AND date = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("isis", $targetIntake, $now, $userId, $today);
            
            if (!$updateStmt->execute()) {
                returnError('Failed to update water intake target: ' . $conn->error, 500);
            }
        }
        
        returnResponse([
            'success' => true,
            'message' => 'Water intake target updated successfully',
            'target_intake' => $targetIntake,
            'date' => $today
        ]);
        break;
        
    default:
        returnError('Method not allowed', 405);
}
