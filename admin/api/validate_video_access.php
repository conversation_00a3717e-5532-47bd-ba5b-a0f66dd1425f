<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $videoId = $input['video_id'] ?? null;
    $userId = $input['user_id'] ?? null;

    if (!$videoId || !$userId) {
        throw new Exception('Missing required parameters: video_id and user_id');
    }

    // Validate user access to the video
    $hasAccess = validateUserVideoAccess($pdo, $videoId, $userId);

    if ($hasAccess) {
        // Log the access attempt for security monitoring
        logVideoAccessAttempt($pdo, $videoId, $userId, 'access_granted');

        echo json_encode([
            'success' => true,
            'has_access' => true,
            'message' => 'Access granted'
        ]);
    } else {
        // Log the denied access attempt
        logVideoAccessAttempt($pdo, $videoId, $userId, 'access_denied');

        echo json_encode([
            'success' => true,
            'has_access' => false,
            'message' => 'Access denied'
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function validateUserVideoAccess($pdo, $videoId, $userId) {
    try {
        // First, get the course that contains this video
        $stmt = $pdo->prepare("
            SELECT cv.course_id, c.title as course_title
            FROM course_videos cv
            JOIN courses c ON cv.course_id = c.id
            WHERE cv.id = ?
        ");
        $stmt->execute([$videoId]);
        $videoInfo = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$videoInfo) {
            return false; // Video not found
        }

        $courseId = $videoInfo['course_id'];

        // Check if user is enrolled in the course
        $stmt = $pdo->prepare("
            SELECT ue.id, ue.enrollment_date, ue.status
            FROM user_enrollments ue
            WHERE ue.user_id = ? AND ue.course_id = ? AND ue.status = 'active'
        ");
        $stmt->execute([$userId, $courseId]);
        $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$enrollment) {
            return false; // User not enrolled or enrollment not active
        }

        // Check if the video exists and is active
        $stmt = $pdo->prepare("
            SELECT cv.*
            FROM course_videos cv
            WHERE cv.id = ? AND cv.is_active = 1
        ");
        $stmt->execute([$videoId]);
        $video = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$video) {
            return false; // Video not found or inactive
        }

        // For now, allow access to all active videos for enrolled users
        // In production, you can add unlock_date logic here

        // Additional security checks can be added here
        // For example: subscription status, payment verification, etc.

        return true;

    } catch (Exception $e) {
        error_log("Video access validation error: " . $e->getMessage());
        return false;
    }
}

function logVideoAccessAttempt($pdo, $videoId, $userId, $action) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO video_access_logs (video_id, user_id, action, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

        $stmt->execute([$videoId, $userId, $action, $ipAddress, $userAgent]);

    } catch (Exception $e) {
        error_log("Failed to log video access attempt: " . $e->getMessage());
    }
}

// Create the video_access_logs table if it doesn't exist
function createVideoAccessLogsTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS video_access_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            video_id INT NOT NULL,
            user_id INT NOT NULL,
            action VARCHAR(50) NOT NULL,
            ip_address VARCHAR(45),
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_video_user (video_id, user_id),
            INDEX idx_created_at (created_at)
        )";

        $pdo->exec($sql);

    } catch (Exception $e) {
        error_log("Failed to create video_access_logs table: " . $e->getMessage());
    }
}

// Initialize the table
createVideoAccessLogsTable($pdo);
?>
