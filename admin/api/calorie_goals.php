<?php
require_once 'config.php';

// Validate token
$tokenData = validateToken();
$userId = $tokenData['user_id'];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get calorie goals for the user
        $conn = getDbConnection();
        
        // Get active goal
        $query = "SELECT * FROM calorie_goals 
                 WHERE user_id = ? 
                 AND is_active = 1 
                 AND (end_date IS NULL OR end_date >= CURDATE()) 
                 ORDER BY start_date DESC LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $activeGoal = null;
        if ($result->num_rows > 0) {
            $activeGoal = $result->fetch_assoc();
        }
        
        // Get goal history
        $historyQuery = "SELECT * FROM calorie_goals 
                        WHERE user_id = ? 
                        ORDER BY start_date DESC";
        $historyStmt = $conn->prepare($historyQuery);
        $historyStmt->bind_param("i", $userId);
        $historyStmt->execute();
        $historyResult = $historyStmt->get_result();
        
        $goalHistory = [];
        while ($row = $historyResult->fetch_assoc()) {
            $goalHistory[] = $row;
        }
        
        // Return response
        returnResponse([
            'active_goal' => $activeGoal,
            'goal_history' => $goalHistory
        ]);
        break;
        
    case 'POST':
        // Add a new calorie goal
        $conn = getDbConnection();
        
        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        if (!isset($data['daily_calories']) || !isset($data['start_date'])) {
            returnError('Missing required fields', 400);
        }
        
        // Deactivate existing active goals if requested
        if (isset($data['deactivate_existing']) && $data['deactivate_existing']) {
            $deactivateQuery = "UPDATE calorie_goals 
                              SET is_active = 0, end_date = ? 
                              WHERE user_id = ? AND is_active = 1";
            $deactivateStmt = $conn->prepare($deactivateQuery);
            $yesterday = date('Y-m-d', strtotime('-1 day'));
            $deactivateStmt->bind_param("si", $yesterday, $userId);
            $deactivateStmt->execute();
        }
        
        // Prepare query
        $query = "INSERT INTO calorie_goals 
                 (user_id, daily_calories, protein_goal, carbs_goal, fat_goal, start_date, end_date, is_active) 
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        
        // Set default values for optional fields
        $proteinGoal = isset($data['protein_goal']) ? $data['protein_goal'] : null;
        $carbsGoal = isset($data['carbs_goal']) ? $data['carbs_goal'] : null;
        $fatGoal = isset($data['fat_goal']) ? $data['fat_goal'] : null;
        $endDate = isset($data['end_date']) ? $data['end_date'] : null;
        $isActive = isset($data['is_active']) ? $data['is_active'] : 1;
        
        $stmt->bind_param("idddssi", $userId, $data['daily_calories'], $proteinGoal, $carbsGoal, $fatGoal, $data['start_date'], $endDate, $isActive);
        
        if ($stmt->execute()) {
            $goalId = $conn->insert_id;
            
            // Get the inserted record
            $selectQuery = "SELECT * FROM calorie_goals WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $goalId);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $goal = $result->fetch_assoc();
            
            returnResponse([
                'success' => true,
                'message' => 'Calorie goal added successfully',
                'goal' => $goal
            ]);
        } else {
            returnError('Failed to add calorie goal: ' . $stmt->error, 500);
        }
        break;
        
    case 'PUT':
        // Update an existing calorie goal
        $conn = getDbConnection();
        
        // Get request body
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Validate required fields
        if (!isset($data['id'])) {
            returnError('Missing goal ID', 400);
        }
        
        // Check if the goal exists and belongs to the user
        $checkQuery = "SELECT * FROM calorie_goals WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $data['id'], $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows === 0) {
            returnError('Goal not found or access denied', 404);
        }
        
        // Build update query
        $updateFields = [];
        $params = [];
        $types = "";
        
        if (isset($data['daily_calories'])) {
            $updateFields[] = "daily_calories = ?";
            $params[] = $data['daily_calories'];
            $types .= "i";
        }
        
        if (isset($data['protein_goal'])) {
            $updateFields[] = "protein_goal = ?";
            $params[] = $data['protein_goal'];
            $types .= "d";
        }
        
        if (isset($data['carbs_goal'])) {
            $updateFields[] = "carbs_goal = ?";
            $params[] = $data['carbs_goal'];
            $types .= "d";
        }
        
        if (isset($data['fat_goal'])) {
            $updateFields[] = "fat_goal = ?";
            $params[] = $data['fat_goal'];
            $types .= "d";
        }
        
        if (isset($data['start_date'])) {
            $updateFields[] = "start_date = ?";
            $params[] = $data['start_date'];
            $types .= "s";
        }
        
        if (isset($data['end_date'])) {
            $updateFields[] = "end_date = ?";
            $params[] = $data['end_date'];
            $types .= "s";
        }
        
        if (isset($data['is_active'])) {
            $updateFields[] = "is_active = ?";
            $params[] = $data['is_active'];
            $types .= "i";
        }
        
        if (empty($updateFields)) {
            returnError('No fields to update', 400);
        }
        
        // Add ID and user_id to params
        $params[] = $data['id'];
        $params[] = $userId;
        $types .= "ii";
        
        $updateQuery = "UPDATE calorie_goals SET " . implode(", ", $updateFields) . " WHERE id = ? AND user_id = ?";
        $updateStmt = $conn->prepare($updateQuery);
        
        // Bind parameters dynamically
        $bindParams = array_merge([$updateStmt, $types], $params);
        call_user_func_array('mysqli_stmt_bind_param', $bindParams);
        
        if ($updateStmt->execute()) {
            // Get the updated record
            $selectQuery = "SELECT * FROM calorie_goals WHERE id = ?";
            $selectStmt = $conn->prepare($selectQuery);
            $selectStmt->bind_param("i", $data['id']);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $goal = $result->fetch_assoc();
            
            returnResponse([
                'success' => true,
                'message' => 'Calorie goal updated successfully',
                'goal' => $goal
            ]);
        } else {
            returnError('Failed to update calorie goal: ' . $updateStmt->error, 500);
        }
        break;
        
    case 'DELETE':
        // Delete a calorie goal
        $conn = getDbConnection();
        
        // Get goal ID from query parameters
        $goalId = isset($_GET['id']) ? intval($_GET['id']) : 0;
        
        if ($goalId <= 0) {
            returnError('Invalid goal ID', 400);
        }
        
        // Check if the goal exists and belongs to the user
        $checkQuery = "SELECT * FROM calorie_goals WHERE id = ? AND user_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("ii", $goalId, $userId);
        $checkStmt->execute();
        $result = $checkStmt->get_result();
        
        if ($result->num_rows === 0) {
            returnError('Goal not found or access denied', 404);
        }
        
        // Delete the goal
        $deleteQuery = "DELETE FROM calorie_goals WHERE id = ? AND user_id = ?";
        $deleteStmt = $conn->prepare($deleteQuery);
        $deleteStmt->bind_param("ii", $goalId, $userId);
        
        if ($deleteStmt->execute()) {
            returnResponse([
                'success' => true,
                'message' => 'Calorie goal deleted successfully'
            ]);
        } else {
            returnError('Failed to delete calorie goal: ' . $deleteStmt->error, 500);
        }
        break;
        
    default:
        returnError('Method not allowed', 405);
}
