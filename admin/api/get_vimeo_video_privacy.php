<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

try {
    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    
    $vimeoId = $input['vimeo_id'] ?? '';
    $accessToken = $input['access_token'] ?? '';
    $clientId = $input['client_id'] ?? '';
    
    // Validate required parameters
    if (empty($vimeoId) || empty($accessToken)) {
        throw new Exception('Missing required parameters for Vimeo video privacy check');
    }
    
    // Get video privacy settings from Vimeo Pro API
    $privacySettings = getVimeoVideoPrivacySettings($vimeoId, $accessToken);
    
    if ($privacySettings) {
        echo json_encode([
            'success' => true,
            'privacy_settings' => $privacySettings,
            'message' => 'Video privacy settings retrieved successfully'
        ]);
    } else {
        throw new Exception('Failed to get video privacy settings from Vimeo Pro API');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get video privacy settings from Vimeo Pro API
 */
function getVimeoVideoPrivacySettings($vimeoId, $accessToken) {
    try {
        // Get video details from Vimeo Pro API
        $url = "https://api.vimeo.com/videos/{$vimeoId}";
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Authorization: Bearer ' . $accessToken,
                'Accept: application/vnd.vimeo.*+json;version=3.4',
                'User-Agent: KFT-Fitness-App/1.0 (Vimeo Pro Integration)'
            ],
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_SSL_VERIFYPEER => true,
            CURLOPT_FOLLOWLOCATION => true
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            error_log("Vimeo Pro API cURL error: " . $error);
            return false;
        }
        
        if ($httpCode === 200) {
            $videoData = json_decode($response, true);
            
            if (isset($videoData['privacy'])) {
                $privacy = $videoData['privacy'];
                
                // Extract relevant privacy information
                $privacySettings = [
                    'view' => $privacy['view'] ?? 'anybody',
                    'embed' => $privacy['embed'] ?? 'public',
                    'download' => $privacy['download'] ?? false,
                    'add' => $privacy['add'] ?? true,
                    'comments' => $privacy['comments'] ?? 'anybody'
                ];
                
                // Add password protection info if available
                if (isset($videoData['password'])) {
                    $privacySettings['password_protected'] = !empty($videoData['password']);
                }
                
                // Add domain restrictions info if available
                if (isset($privacy['embed_domains'])) {
                    $privacySettings['embed_domains'] = $privacy['embed_domains'];
                }
                
                // Add additional metadata
                $privacySettings['video_id'] = $videoData['uri'] ?? null;
                $privacySettings['status'] = $videoData['status'] ?? 'available';
                $privacySettings['duration'] = $videoData['duration'] ?? 0;
                $privacySettings['created_time'] = $videoData['created_time'] ?? null;
                $privacySettings['modified_time'] = $videoData['modified_time'] ?? null;
                
                // Check if video is live or on-demand
                $privacySettings['type'] = $videoData['type'] ?? 'video';
                
                return $privacySettings;
            }
        }
        
        error_log("Vimeo Pro API request failed. HTTP Code: $httpCode, Response: $response");
        return false;
        
    } catch (Exception $e) {
        error_log("Failed to get Vimeo video privacy settings: " . $e->getMessage());
        return false;
    }
}
?>
