<?php
require_once 'config.php';

// Set appropriate headers for API response
header('Content-Type: application/json');

// Function to get AI settings
function getAiSettings($conn) {
    $settings = [
        'openai_api_key' => '',
        'openai_model' => 'gpt-4-vision-preview',
        'calorie_analysis_enabled' => '1',
        'calorie_analysis_prompt' => 'Analyze this food image and provide the following information in JSON format: {"food_name": "name of the food", "calories": estimated calories per serving, "protein": estimated protein in grams, "carbs": estimated carbs in grams, "fat": estimated fat in grams, "serving_size": "standard serving size"}. Be as accurate as possible with your estimates.'
    ];

    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM ai_settings");
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    return $settings;
}

// Function to call OpenAI API for image analysis
function analyzeImageWithOpenAI($imageData, $apiKey, $model, $prompt) {
    $url = 'https://api.openai.com/v1/chat/completions';
    
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ];
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a nutritionist AI assistant that analyzes food images to estimate calories and nutritional content.'
            ],
            [
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $prompt
                    ],
                    [
                        'type' => 'image_url',
                        'image_url' => [
                            'url' => $imageData
                        ]
                    ]
                ]
            ]
        ],
        'max_tokens' => 500
    ];
    
    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    
    if (curl_errno($ch)) {
        throw new Exception('cURL error: ' . curl_error($ch));
    }
    
    curl_close($ch);
    
    if ($httpCode !== 200) {
        $responseData = json_decode($response, true);
        $errorMessage = isset($responseData['error']['message']) ? $responseData['error']['message'] : 'Unknown error';
        throw new Exception('OpenAI API error (' . $httpCode . '): ' . $errorMessage);
    }
    
    $responseData = json_decode($response, true);
    
    if (!isset($responseData['choices'][0]['message']['content'])) {
        throw new Exception('Invalid response from OpenAI API');
    }
    
    $content = $responseData['choices'][0]['message']['content'];
    
    // Try to extract JSON from the response
    if (preg_match('/\{.*\}/s', $content, $matches)) {
        $jsonStr = $matches[0];
        $jsonData = json_decode($jsonStr, true);
        
        if ($jsonData === null) {
            throw new Exception('Failed to parse JSON from OpenAI response');
        }
        
        return $jsonData;
    } else {
        throw new Exception('No JSON found in OpenAI response');
    }
}

// Main API logic
try {
    // Check if calorie analysis is enabled
    $conn = getDbConnection();
    $aiSettings = getAiSettings($conn);
    
    if ($aiSettings['calorie_analysis_enabled'] !== '1') {
        returnError('Food calorie analysis is disabled', 403);
    }
    
    // Check if OpenAI API key is configured
    if (empty($aiSettings['openai_api_key'])) {
        returnError('OpenAI API key is not configured', 500);
    }
    
    // Handle different request methods
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'POST':
            // Check if this is a test request from admin or a real request from app
            $isTestRequest = isset($_POST['action']) && $_POST['action'] === 'test_analysis';
            
            if ($isTestRequest) {
                // Admin test request - no authentication needed
                if (!isset($_FILES['image'])) {
                    returnError('No image uploaded', 400);
                }
                
                // Process the uploaded image
                $imageTmpPath = $_FILES['image']['tmp_name'];
                $imageData = 'data:image/jpeg;base64,' . base64_encode(file_get_contents($imageTmpPath));
                
                // Analyze the image
                $result = analyzeImageWithOpenAI(
                    $imageData,
                    $aiSettings['openai_api_key'],
                    $aiSettings['openai_model'],
                    $aiSettings['calorie_analysis_prompt']
                );
                
                // Return the analysis result
                returnResponse(['success' => true, 'result' => $result]);
            } else {
                // App request - requires authentication
                $tokenData = validateToken();
                $userId = $tokenData['user_id'];
                
                // Get request data
                $data = json_decode(file_get_contents('php://input'), true);
                
                // Check if image data is provided
                if (!isset($data['image_data']) || empty($data['image_data'])) {
                    returnError('No image data provided', 400);
                }
                
                // Analyze the image
                $result = analyzeImageWithOpenAI(
                    $data['image_data'],
                    $aiSettings['openai_api_key'],
                    $aiSettings['openai_model'],
                    $aiSettings['calorie_analysis_prompt']
                );
                
                // Return the analysis result
                returnResponse(['success' => true, 'result' => $result]);
            }
            break;
            
        default:
            returnError('Method not allowed', 405);
            break;
    }
} catch (Exception $e) {
    returnError($e->getMessage(), 500);
}
?>
