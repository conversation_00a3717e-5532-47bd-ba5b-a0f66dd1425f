<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $vimeoId = $input['vimeo_id'] ?? null;
    $userId = $input['user_id'] ?? null;

    if (!$vimeoId || !$userId) {
        throw new Exception('Missing required parameters: vimeo_id and user_id');
    }

    // First validate that the user has access to this video
    $videoId = getVideoIdByVimeoId($pdo, $vimeoId);
    if (!$videoId) {
        throw new Exception('Video not found');
    }

    // Validate user access (reuse the validation from validate_video_access.php)
    require_once 'validate_video_access.php';
    $hasAccess = validateUserVideoAccess($pdo, $videoId, $userId);

    if (!$hasAccess) {
        throw new Exception('Access denied to this video');
    }

    // Get Vimeo metadata
    $metadata = getVimeoMetadata($vimeoId);

    if ($metadata) {
        echo json_encode([
            'success' => true,
            'metadata' => $metadata
        ]);
    } else {
        throw new Exception('Failed to retrieve video metadata');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getVideoIdByVimeoId($pdo, $vimeoId) {
    try {
        // Extract Vimeo ID from URL if needed
        $cleanVimeoId = extractVimeoIdFromUrl($vimeoId);

        $stmt = $pdo->prepare("
            SELECT id FROM course_videos
            WHERE video_url LIKE ? OR video_url LIKE ? OR video_id = ?
        ");
        $stmt->execute([
            "%vimeo.com/{$cleanVimeoId}%",
            "%player.vimeo.com/video/{$cleanVimeoId}%",
            $cleanVimeoId
        ]);

        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['id'] : null;

    } catch (Exception $e) {
        error_log("Error finding video by Vimeo ID: " . $e->getMessage());
        return null;
    }
}

function extractVimeoIdFromUrl($url) {
    // Handle various Vimeo URL formats
    $patterns = [
        '/vimeo\.com\/([0-9]+)/',
        '/player\.vimeo\.com\/video\/([0-9]+)/',
        '/vimeo\.com\/([0-9]+)\/([a-zA-Z0-9]+)/',
        '/player\.vimeo\.com\/video\/([0-9]+)\?h=([a-zA-Z0-9]+)/',
    ];

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }

    // If no pattern matches, assume it's already a clean ID
    return preg_replace('/[^0-9]/', '', $url);
}

function getVimeoMetadata($vimeoId) {
    try {
        // Use Vimeo API to get video metadata
        $vimeoAccessToken = getVimeoAccessToken();

        if (!$vimeoAccessToken) {
            // Fallback to oEmbed API (public videos only)
            return getVimeoOEmbedData($vimeoId);
        }

        // Use Vimeo API with access token
        $url = "https://api.vimeo.com/videos/{$vimeoId}";

        $headers = [
            'Authorization: Bearer ' . $vimeoAccessToken,
            'Content-Type: application/json'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);

            return [
                'title' => $data['name'] ?? '',
                'description' => $data['description'] ?? '',
                'duration' => $data['duration'] ?? 0,
                'thumbnail' => $data['pictures']['sizes'][0]['link'] ?? '',
                'width' => $data['width'] ?? 1920,
                'height' => $data['height'] ?? 1080,
                'created_time' => $data['created_time'] ?? '',
                'privacy' => $data['privacy']['view'] ?? 'unknown'
            ];
        }

        return null;

    } catch (Exception $e) {
        error_log("Error getting Vimeo metadata: " . $e->getMessage());
        return null;
    }
}

function getVimeoOEmbedData($vimeoId) {
    try {
        $url = "https://vimeo.com/api/oembed.json?url=https://vimeo.com/{$vimeoId}";

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200 && $response) {
            $data = json_decode($response, true);

            return [
                'title' => $data['title'] ?? '',
                'description' => $data['description'] ?? '',
                'duration' => $data['duration'] ?? 0,
                'thumbnail' => $data['thumbnail_url'] ?? '',
                'width' => $data['width'] ?? 1920,
                'height' => $data['height'] ?? 1080,
                'author_name' => $data['author_name'] ?? '',
                'provider_name' => $data['provider_name'] ?? 'Vimeo'
            ];
        }

        return null;

    } catch (Exception $e) {
        error_log("Error getting Vimeo oEmbed data: " . $e->getMessage());
        return null;
    }
}

function getVimeoAccessToken() {
    // This should be stored securely in your environment variables or config
    // You can get this from your Vimeo app settings
    return $_ENV['VIMEO_ACCESS_TOKEN'] ?? null;
}
?>
