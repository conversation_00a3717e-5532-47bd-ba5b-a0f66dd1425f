<?php
// Include the API configuration file which has error handlers and helper functions
require_once 'config.php';
require_once '../includes/auth.php'; // Include auth class

// Set content type header to ensure clean JSON response
header('Content-Type: application/json; charset=UTF-8');

// Handle CORS
handleCors();

// Get authorization header (case-insensitive)
$headers = getallheaders();
$authHeader = '';

// Case-insensitive header lookup
foreach ($headers as $key => $value) {
    if (strtolower($key) === 'authorization') {
        $authHeader = $value;
        break;
    }
}

$token = '';
if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Log the token for debugging
error_log("API Token received: " . substr($token, 0, 20) . "...");

// For debugging, log the full token
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log("Full token: $token");
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

// Log the authentication result
if ($userId) {
    error_log("User authenticated successfully: User ID = $userId");
} else {
    error_log("Authentication failed for token: " . substr($token, 0, 20) . "...");
}

if (!$userId) {
    returnError('Unauthorized: Please log in again', 401);
}

// Get database connection
$conn = getDbConnection();

// Check what type of courses to return
$enrolled = isset($_GET['enrolled']) ? $_GET['enrolled'] : null;
$available = isset($_GET['available']) ? $_GET['available'] : null;

$courses = [];

if ($enrolled === '1') {
    // Get user's enrolled courses with all required fields
    $stmt = $conn->prepare('
        SELECT
            c.id,
            c.title,
            c.description,
            c.thumbnail_url,
            c.duration_weeks,
            DATE(uce.start_date) as start_date,
            DATE(uce.end_date) as end_date,
            uce.status,
            (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,
            (SELECT COUNT(*) FROM user_video_progress uvp
             JOIN course_videos cv ON uvp.video_id = cv.id
             WHERE cv.course_id = c.id AND uvp.user_id = ? AND uvp.is_unlocked = 1) as unlocked_videos,
            (SELECT COUNT(*) FROM user_video_progress uvp
             JOIN course_videos cv ON uvp.video_id = cv.id
             WHERE cv.course_id = c.id AND uvp.user_id = ? AND uvp.is_completed = 1) as completed_videos,
            c.thumbnail_url as image_url,
            "Unknown" as instructor,
            c.category as level
        FROM user_course_enrollments uce
        JOIN courses c ON uce.course_id = c.id
        WHERE uce.user_id = ?
        ORDER BY uce.start_date DESC
    ');
    $stmt->bind_param('iii', $userId, $userId, $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        // Calculate progress percentage
        $row['progress_percentage'] = $row['total_videos'] > 0 
            ? round(($row['completed_videos'] / $row['total_videos']) * 100) 
            : 0;
        $courses[] = $row;
    }
} elseif ($available === '1') {
    // Get available courses (not enrolled by user) with all required fields
    $stmt = $conn->prepare('
        SELECT
            c.id,
            c.title,
            c.description,
            c.thumbnail_url,
            c.duration_weeks,
            DATE(c.created_at) as start_date,
            NULL as end_date,
            CASE WHEN c.is_active = 1 THEN "active" ELSE "inactive" END as status,
            0 as total_videos,
            0 as unlocked_videos,
            0 as completed_videos,
            0 as progress_percentage,
            c.thumbnail_url as image_url,
            "Unknown" as instructor,
            c.category as level
        FROM courses c
        WHERE c.is_active = 1
        AND c.id NOT IN (SELECT course_id FROM user_course_enrollments WHERE user_id = ?)
        ORDER BY c.created_at DESC
    ');
    $stmt->bind_param('i', $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $courses[] = $row;
    }
} else {
    // Get all courses if no specific parameter with all required fields
    $stmt = $conn->prepare('
        SELECT
            c.id,
            c.title,
            c.description,
            c.thumbnail_url,
            c.duration_weeks,
            DATE(c.created_at) as start_date,
            NULL as end_date,
            CASE WHEN c.is_active = 1 THEN "active" ELSE "inactive" END as status,
            0 as total_videos,
            0 as unlocked_videos,
            0 as completed_videos,
            0 as progress_percentage,
            c.thumbnail_url as image_url,
            "Unknown" as instructor,
            c.category as level
        FROM courses c
        WHERE c.is_active = 1
        ORDER BY c.created_at DESC
    ');
    $stmt->execute();
    $result = $stmt->get_result();
    while ($row = $result->fetch_assoc()) {
        $courses[] = $row;
    }
}

returnResponse(['success' => true, 'courses' => $courses]);
