<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $vimeoId = $input['vimeo_id'] ?? null;
    $videoId = $input['video_id'] ?? null;
    $userId = $input['user_id'] ?? null;
    $appDomain = $input['app_domain'] ?? null;

    if (!$vimeoId || !$videoId || !$userId || !$appDomain) {
        throw new Exception('Missing required parameters');
    }

    // Validate user access to the video
    require_once 'validate_video_access.php';
    $hasAccess = validateUserVideoAccess($pdo, $videoId, $userId);

    if (!$hasAccess) {
        throw new Exception('Access denied to this video');
    }

    // Generate a one-time playback token
    $playbackToken = generateSecurePlaybackToken($vimeoId, $videoId, $userId, $appDomain);

    // Store the token in database with expiration
    $tokenId = storePlaybackToken($pdo, $playbackToken, $videoId, $userId);

    if ($tokenId) {
        echo json_encode([
            'success' => true,
            'playback_token' => $playbackToken,
            'expires_in' => 3600, // 1 hour
            'token_id' => $tokenId
        ]);
    } else {
        throw new Exception('Failed to generate playback token');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function generateSecurePlaybackToken($vimeoId, $videoId, $userId, $appDomain) {
    $secretKey = getSecretKey();
    $timestamp = time();
    $expiresAt = $timestamp + 3600; // 1 hour expiration
    $nonce = bin2hex(random_bytes(16));

    // Create token data
    $tokenData = [
        'vimeo_id' => $vimeoId,
        'video_id' => $videoId,
        'user_id' => $userId,
        'app_domain' => $appDomain,
        'timestamp' => $timestamp,
        'expires_at' => $expiresAt,
        'nonce' => $nonce
    ];

    // Create signature
    $dataString = json_encode($tokenData);
    $signature = hash_hmac('sha256', $dataString, $secretKey);

    // Combine data and signature
    $token = base64_encode(json_encode([
        'data' => $tokenData,
        'signature' => $signature
    ]));

    return $token;
}

function storePlaybackToken($pdo, $token, $videoId, $userId) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO playback_tokens (token, video_id, user_id, expires_at, created_at)
            VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 1 HOUR), NOW())
        ");

        $stmt->execute([$token, $videoId, $userId]);

        return $pdo->lastInsertId();

    } catch (Exception $e) {
        error_log("Failed to store playback token: " . $e->getMessage());
        return null;
    }
}

function validatePlaybackToken($token) {
    try {
        $secretKey = getSecretKey();

        // Decode token
        $tokenData = json_decode(base64_decode($token), true);

        if (!$tokenData || !isset($tokenData['data']) || !isset($tokenData['signature'])) {
            return false;
        }

        $data = $tokenData['data'];
        $signature = $tokenData['signature'];

        // Verify signature
        $dataString = json_encode($data);
        $expectedSignature = hash_hmac('sha256', $dataString, $secretKey);

        if (!hash_equals($expectedSignature, $signature)) {
            return false;
        }

        // Check expiration
        if (time() > $data['expires_at']) {
            return false;
        }

        return $data;

    } catch (Exception $e) {
        error_log("Token validation error: " . $e->getMessage());
        return false;
    }
}

function getSecretKey() {
    // This should be stored securely in environment variables
    return $_ENV['VIDEO_SECRET_KEY'] ?? 'your-secret-key-here-change-this';
}

function revokePlaybackToken($pdo, $token) {
    try {
        $stmt = $pdo->prepare("
            UPDATE playback_tokens
            SET revoked_at = NOW()
            WHERE token = ? AND revoked_at IS NULL
        ");

        $stmt->execute([$token]);

        return $stmt->rowCount() > 0;

    } catch (Exception $e) {
        error_log("Failed to revoke playback token: " . $e->getMessage());
        return false;
    }
}

function cleanupExpiredTokens($pdo) {
    try {
        $stmt = $pdo->prepare("
            DELETE FROM playback_tokens
            WHERE expires_at < NOW() OR revoked_at IS NOT NULL
        ");

        $stmt->execute();

        return $stmt->rowCount();

    } catch (Exception $e) {
        error_log("Failed to cleanup expired tokens: " . $e->getMessage());
        return 0;
    }
}

// Create the playback_tokens table if it doesn't exist
function createPlaybackTokensTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS playback_tokens (
            id INT AUTO_INCREMENT PRIMARY KEY,
            token TEXT NOT NULL,
            video_id INT NOT NULL,
            user_id INT NOT NULL,
            expires_at TIMESTAMP NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            revoked_at TIMESTAMP NULL,
            INDEX idx_video_user (video_id, user_id),
            INDEX idx_expires_at (expires_at)
        )";

        $pdo->exec($sql);

    } catch (Exception $e) {
        error_log("Failed to create playback_tokens table: " . $e->getMessage());
    }
}

// Initialize the table and cleanup old tokens
createPlaybackTokensTable($pdo);
cleanupExpiredTokens($pdo);
?>
