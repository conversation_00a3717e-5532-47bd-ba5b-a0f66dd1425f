<?php
/**
 * Save Notification Settings API
 *
 * This file provides an API endpoint for saving notification settings.
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Set content type to JSON
header('Content-Type: application/json');

// Function to return JSON response
function jsonResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message,
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ];

    if ($data !== null) {
        $response['data'] = $data;
    }

    echo json_encode($response);
    exit;
}

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    jsonResponse(false, 'Unauthorized');
}

// Check if user has permission to view notifications
if (!$auth->hasRole('super_admin') && !$auth->hasRole('manager') && !$auth->hasPermission('view_notifications')) {
    http_response_code(403);
    jsonResponse(false, 'Forbidden');
}

// Get the admin ID
$adminId = $auth->getUserId();

// Check if the notification_settings table exists
$tableExistsQuery = "SHOW TABLES LIKE 'notification_settings'";
$result = $conn->query($tableExistsQuery);
$tableExists = $result->num_rows > 0;

// Create the notification_settings table if it doesn't exist
if (!$tableExists) {
    $createTableQuery = "
    CREATE TABLE notification_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        admin_id INT NOT NULL,
        email_notifications BOOLEAN DEFAULT TRUE,
        notification_types TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (admin_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    if (!$conn->query($createTableQuery)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create notification_settings table']);
        exit;
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate admin ID
        if (!$adminId) {
            throw new Exception('Invalid admin ID');
        }

        // Get form data
        $emailNotifications = isset($_POST['email_notifications']) ? 1 : 0;
        $notificationTypes = isset($_POST['notification_types']) ? $_POST['notification_types'] : [];

        // Convert notification types to JSON
        $notificationTypesJson = json_encode($notificationTypes);

        // Check if settings already exist for this admin
        $checkQuery = "SELECT id FROM notification_settings WHERE admin_id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        if (!$checkStmt) {
            throw new Exception('Database error: ' . $conn->error);
        }

        $checkStmt->bind_param("i", $adminId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows > 0) {
            // Update existing settings
            $updateQuery = "UPDATE notification_settings SET email_notifications = ?, notification_types = ? WHERE admin_id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            if (!$updateStmt) {
                throw new Exception('Database error: ' . $conn->error);
            }

            $updateStmt->bind_param("isi", $emailNotifications, $notificationTypesJson, $adminId);

            if ($updateStmt->execute()) {
                Utilities::setFlashMessage('success', 'Notification settings updated successfully.');
            } else {
                throw new Exception('Failed to update notification settings: ' . $updateStmt->error);
            }
        } else {
            // Insert new settings
            $insertQuery = "INSERT INTO notification_settings (admin_id, email_notifications, notification_types) VALUES (?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            if (!$insertStmt) {
                throw new Exception('Database error: ' . $conn->error);
            }

            $insertStmt->bind_param("iis", $adminId, $emailNotifications, $notificationTypesJson);

            if ($insertStmt->execute()) {
                Utilities::setFlashMessage('success', 'Notification settings saved successfully.');
            } else {
                throw new Exception('Failed to save notification settings: ' . $insertStmt->error);
            }
        }

        // Redirect back to notifications page
        header('Location: ../notifications.php');
        exit;
    } catch (Exception $e) {
        // Log the error
        error_log('Error saving notification settings: ' . $e->getMessage());

        // Check if this is an AJAX request
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

        if ($isAjax) {
            // Return JSON response for AJAX requests
            http_response_code(500);
            jsonResponse(false, $e->getMessage(), [
                'error_type' => 'exception',
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
        } else {
            // Set flash message and redirect for regular form submissions
            Utilities::setFlashMessage('error', 'Error: ' . $e->getMessage());
            header('Location: ../notifications.php');
            exit;
        }
    }
} else {
    // Method not allowed
    http_response_code(405);
    jsonResponse(false, 'Method not allowed');
}
