<?php
/**
 * Simple API endpoint to test authentication
 *
 * This endpoint returns a success response if the user is authenticated,
 * or an error response if not.
 */

// Include the API configuration file which has error handlers and helper functions
require_once 'config.php';
require_once '../includes/auth.php'; // Include auth class

// Set content type header to ensure clean JSON response
header('Content-Type: application/json; charset=UTF-8');

// Handle CORS
handleCors();

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Get authorization header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Log the token for debugging
error_log("API Token received: " . substr($token, 0, 20) . "...");

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

// Log the authentication result
if ($userId) {
    error_log("User authenticated successfully: User ID = $userId");
} else {
    error_log("Authentication failed for token: " . substr($token, 0, 20) . "...");
}

if (!$userId) {
    returnError('Unauthorized: Please log in again', 401);
}

// Return success response
returnResponse([
    'success' => true,
    'message' => 'Authentication successful',
    'user_id' => $userId,
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD']
]);
