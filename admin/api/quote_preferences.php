<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Log the token for debugging
error_log("Quote Preferences API Token received: " . substr($token, 0, 20) . "...");

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

// Log the authentication result
if ($userId) {
    error_log("Quote Preferences API: User authenticated successfully: User ID = $userId");
} else {
    error_log("Quote Preferences API: Authentication failed for token: " . substr($token, 0, 20) . "...");
}

if (!$userId) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access',
        'message' => 'Unauthorized: Please log in again'
    ]);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'GET':
        getPreferences($conn, $userId);
        break;
    case 'PUT':
        updatePreferences($conn, $userId);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        break;
}

// Function to get user quote preferences
function getPreferences($conn, $userId) {
    // Get user preferences
    $stmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $preferences = $result->fetch_assoc();
        echo json_encode(['success' => true, 'preferences' => $preferences]);
    } else {
        // Get default categories from settings
        $defaultCategories = '';
        $defaultStmt = $conn->prepare("SELECT setting_value FROM quote_settings WHERE setting_key = 'default_categories'");
        $defaultStmt->execute();
        $defaultResult = $defaultStmt->get_result();
        if ($defaultResult->num_rows > 0) {
            $defaultCategories = $defaultResult->fetch_assoc()['setting_value'];
        }
        $defaultStmt->close();

        // Create default preferences
        $insertStmt = $conn->prepare("INSERT INTO user_quote_preferences (user_id, preferred_categories, personalization_enabled) VALUES (?, ?, 1)");
        $insertStmt->bind_param("is", $userId, $defaultCategories);
        $insertStmt->execute();
        $insertStmt->close();

        // Return the default preferences
        $preferences = [
            'user_id' => $userId,
            'preferred_categories' => $defaultCategories,
            'personalization_enabled' => 1,
            'last_quote_id' => null,
            'last_quote_date' => null
        ];

        echo json_encode(['success' => true, 'preferences' => $preferences, 'is_default' => true]);
    }

    $stmt->close();
}

// Function to update user quote preferences
function updatePreferences($conn, $userId) {
    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);

    // Check if preferences exist
    $checkStmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
    $checkStmt->bind_param("i", $userId);
    $checkStmt->execute();
    $result = $checkStmt->get_result();
    $exists = $result->num_rows > 0;
    $checkStmt->close();

    // Prepare data for update/insert
    $preferredCategories = isset($data['preferred_categories']) ? $data['preferred_categories'] : null;
    $personalizationEnabled = isset($data['personalization_enabled']) ? (int)$data['personalization_enabled'] : 1;

    if ($exists) {
        // Update existing preferences
        $updateQuery = "UPDATE user_quote_preferences SET ";
        $updateParams = [];
        $updateTypes = "";

        if ($preferredCategories !== null) {
            $updateQuery .= "preferred_categories = ?, ";
            $updateParams[] = $preferredCategories;
            $updateTypes .= "s";
        }

        $updateQuery .= "personalization_enabled = ? WHERE user_id = ?";
        $updateParams[] = $personalizationEnabled;
        $updateParams[] = $userId;
        $updateTypes .= "ii";

        $updateStmt = $conn->prepare($updateQuery);
        $updateStmt->bind_param($updateTypes, ...$updateParams);

        if ($updateStmt->execute()) {
            // Get updated preferences
            $getStmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
            $getStmt->bind_param("i", $userId);
            $getStmt->execute();
            $result = $getStmt->get_result();
            $preferences = $result->fetch_assoc();
            $getStmt->close();

            echo json_encode(['success' => true, 'message' => 'Preferences updated successfully', 'preferences' => $preferences]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to update preferences: ' . $updateStmt->error]);
        }

        $updateStmt->close();
    } else {
        // Insert new preferences
        $insertStmt = $conn->prepare("INSERT INTO user_quote_preferences (user_id, preferred_categories, personalization_enabled) VALUES (?, ?, ?)");
        $insertStmt->bind_param("isi", $userId, $preferredCategories, $personalizationEnabled);

        if ($insertStmt->execute()) {
            // Get the new preferences
            $getStmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
            $getStmt->bind_param("i", $userId);
            $getStmt->execute();
            $result = $getStmt->get_result();
            $preferences = $result->fetch_assoc();
            $getStmt->close();

            echo json_encode(['success' => true, 'message' => 'Preferences created successfully', 'preferences' => $preferences]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to create preferences: ' . $insertStmt->error]);
        }

        $insertStmt->close();
    }
}
