<?php
require_once 'config.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Device-ID, X-Session-ID');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

try {
    // Get input data
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        returnError('Invalid JSON input', 400);
    }

    // Get headers
    $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
    $deviceId = $_SERVER['HTTP_X_DEVICE_ID'] ?? '';
    $sessionId = $_SERVER['HTTP_X_SESSION_ID'] ?? '';

    // Validate required fields
    if (empty($authHeader)) {
        returnError('Authorization header required', 401);
    }

    if (empty($deviceId)) {
        returnError('Device ID required', 400);
    }

    // Extract token from Authorization header
    if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        returnError('Invalid authorization header format', 401);
    }
    $token = $matches[1];

    // Validate token and get user data
    $tokenData = validateApiToken($token);
    if (!$tokenData) {
        returnError('Invalid or expired token', 401);
    }

    $userId = $tokenData['user_id'];

    // Get user's current device ID from database
    $stmt = $conn->prepare("SELECT device_id, name, is_active FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();
    $user = $result->fetch_assoc();
    $stmt->close();

    if (!$user || !$user['is_active']) {
        returnError('User not found or inactive', 401);
    }

    // Check token revocation status in database (for never-expiring tokens)
    $tokenStmt = $conn->prepare("
        SELECT expires_at, is_revoked, revoked_by_admin, revoked_at, revocation_reason 
        FROM api_tokens 
        WHERE token = ? AND user_id = ?
    ");
    $tokenStmt->bind_param("si", $token, $userId);
    $tokenStmt->execute();
    $tokenResult = $tokenStmt->get_result();
    $tokenInfo = $tokenResult->fetch_assoc();
    $tokenStmt->close();

    if (!$tokenInfo) {
        // Token not found in database - this might be a JWT-only token
        // For never-expiring tokens, we'll validate the JWT directly
        if (defined('DEV_MODE') && DEV_MODE === true) {
            error_log('Token not found in database, validating JWT directly');
        }
    } else {
        // Check if token is revoked by admin
        if ($tokenInfo['is_revoked'] == 1) {
            $revocationReason = $tokenInfo['revocation_reason'] ?? 'Device access revoked by administrator';
            returnError('Device access revoked by administrator: ' . $revocationReason, 403);
        }

        // For never-expiring tokens, expires_at will be NULL
        // Only check expiry if it's not NULL (for backward compatibility)
        if ($tokenInfo['expires_at'] !== null && strtotime($tokenInfo['expires_at']) < time()) {
            returnError('Token has expired', 401);
        }
    }

    // Check if device ID matches user's current device
    if ($user['device_id'] !== $deviceId) {
        returnError('Device ID mismatch - session invalidated', 403);
    }

    // Update last activity timestamp if token exists in database
    if ($tokenInfo) {
        $updateStmt = $conn->prepare("UPDATE api_tokens SET last_used = NOW() WHERE token = ?");
        $updateStmt->bind_param("s", $token);
        $updateStmt->execute();
        $updateStmt->close();
    }

    // Update user's last login timestamp
    $updateUserStmt = $conn->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
    $updateUserStmt->bind_param("i", $userId);
    $updateUserStmt->execute();
    $updateUserStmt->close();

    // Session is valid
    returnResponse([
        'success' => true,
        'message' => 'Session is valid',
        'user_id' => $userId,
        'device_id' => $user['device_id'],
        'session_id' => $sessionId,
        'validated_at' => date('Y-m-d H:i:s'),
        'expires_at' => $tokenInfo['expires_at'] ?? null, // NULL for never-expiring tokens
        'token_type' => $tokenInfo['expires_at'] === null ? 'never_expiring' : 'expiring'
    ]);

} catch (Exception $e) {
    error_log("Session validation error: " . $e->getMessage());
    returnError('Internal server error during session validation', 500);
}

/**
 * Validate API token using existing validation logic
 */
function validateApiToken($token) {
    global $conn;
    
    try {
        // First try JWT validation
        require_once '../includes/jwt.php';
        $secret = defined('APP_SECRET') ? APP_SECRET : 'your-secret-key';
        
        $payload = validate_jwt($token, $secret);
        if ($payload && isset($payload['user_id'])) {
            // For never-expiring tokens, don't check 'exp' field
            // Only check if 'exp' exists and is valid
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return null; // Token has expired
            }
            
            return [
                'user_id' => $payload['user_id'],
                'name' => $payload['name'] ?? '',
                'exp' => $payload['exp'] ?? null
            ];
        }
    } catch (Exception $e) {
        error_log("JWT validation failed: " . $e->getMessage());
    }
    
    // Fall back to database token validation - only check non-revoked tokens
    $stmt = $conn->prepare("
        SELECT t.*, u.id as user_id, u.name, u.is_active
        FROM api_tokens t
        JOIN users u ON t.user_id = u.id
        WHERE t.token = ? AND u.is_active = 1 AND (t.is_revoked = 0 OR t.is_revoked IS NULL)
        AND (t.expires_at IS NULL OR t.expires_at > NOW())
    ");
    $stmt->bind_param("s", $token);
    $stmt->execute();
    $result = $stmt->get_result();
    $tokenData = $result->fetch_assoc();
    $stmt->close();
    
    return $tokenData ?: null;
}

/**
 * Return JSON response
 */
function returnResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    echo json_encode($data);
    exit;
}

/**
 * Return error response
 */
function returnError($message, $statusCode = 400) {
    returnResponse([
        'success' => false,
        'error' => $message,
        'timestamp' => date('Y-m-d H:i:s')
    ], $statusCode);
}
?>
