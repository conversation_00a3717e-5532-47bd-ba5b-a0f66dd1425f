<?php
require_once 'config.php';

// Validate token
$tokenData = validateToken();
$userId = $tokenData['user_id'];

// Handle different request methods
switch ($_SERVER['REQUEST_METHOD']) {
    case 'GET':
        // Get water reminder settings for the user
        $conn = getDbConnection();
        
        $query = "SELECT * FROM water_reminders WHERE user_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // Create default water reminder settings if none exist
            $defaultInterval = 2;
            $defaultStartTime = '08:00:00';
            $defaultEndTime = '22:00:00';
            $defaultActive = 1;
            
            $insertQuery = "INSERT INTO water_reminders (user_id, interval_hours, start_time, end_time, is_active) 
                           VALUES (?, ?, ?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iissi", $userId, $defaultInterval, $defaultStartTime, $defaultEndTime, $defaultActive);
            $insertStmt->execute();
            
            $reminder = [
                'id' => $insertStmt->insert_id,
                'user_id' => $userId,
                'interval_hours' => $defaultInterval,
                'start_time' => $defaultStartTime,
                'end_time' => $defaultEndTime,
                'is_active' => $defaultActive
            ];
        } else {
            $reminder = $result->fetch_assoc();
        }
        
        returnResponse($reminder);
        break;
        
    case 'PUT':
        // Update water reminder settings
        $data = getRequestData();
        $conn = getDbConnection();
        
        // Validate inputs
        if (isset($data['interval_hours']) && ($data['interval_hours'] < 1 || $data['interval_hours'] > 12)) {
            returnError('Interval must be between 1 and 12 hours');
        }
        
        // Get current reminder settings
        $query = "SELECT id FROM water_reminders WHERE user_id = ?";
        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $userId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows === 0) {
            // Create new water reminder settings
            $intervalHours = isset($data['interval_hours']) ? (int)$data['interval_hours'] : 2;
            $startTime = isset($data['start_time']) ? $data['start_time'] : '08:00:00';
            $endTime = isset($data['end_time']) ? $data['end_time'] : '22:00:00';
            $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 1;
            
            $insertQuery = "INSERT INTO water_reminders (user_id, interval_hours, start_time, end_time, is_active) 
                           VALUES (?, ?, ?, ?, ?)";
            $insertStmt = $conn->prepare($insertQuery);
            $insertStmt->bind_param("iissi", $userId, $intervalHours, $startTime, $endTime, $isActive);
            
            if (!$insertStmt->execute()) {
                returnError('Failed to create water reminder settings: ' . $conn->error, 500);
            }
            
            $reminderId = $insertStmt->insert_id;
        } else {
            // Update existing water reminder settings
            $reminder = $result->fetch_assoc();
            $reminderId = $reminder['id'];
            
            $updates = [];
            $params = [];
            $paramTypes = '';
            
            if (isset($data['interval_hours'])) {
                $updates[] = "interval_hours = ?";
                $params[] = (int)$data['interval_hours'];
                $paramTypes .= 'i';
            }
            
            if (isset($data['start_time'])) {
                $updates[] = "start_time = ?";
                $params[] = $data['start_time'];
                $paramTypes .= 's';
            }
            
            if (isset($data['end_time'])) {
                $updates[] = "end_time = ?";
                $params[] = $data['end_time'];
                $paramTypes .= 's';
            }
            
            if (isset($data['is_active'])) {
                $updates[] = "is_active = ?";
                $params[] = (int)$data['is_active'];
                $paramTypes .= 'i';
            }
            
            if (empty($updates)) {
                returnError('No fields to update');
            }
            
            // Add reminder ID to params
            $params[] = $reminderId;
            $paramTypes .= 'i';
            
            $updateQuery = "UPDATE water_reminders SET " . implode(', ', $updates) . " WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param($paramTypes, ...$params);
            
            if (!$updateStmt->execute()) {
                returnError('Failed to update water reminder settings: ' . $conn->error, 500);
            }
        }
        
        // Get updated reminder settings
        $getQuery = "SELECT * FROM water_reminders WHERE id = ?";
        $getStmt = $conn->prepare($getQuery);
        $getStmt->bind_param("i", $reminderId);
        $getStmt->execute();
        $getResult = $getStmt->get_result();
        $updatedReminder = $getResult->fetch_assoc();
        
        returnResponse([
            'success' => true,
            'message' => 'Water reminder settings updated successfully',
            'reminder' => $updatedReminder
        ]);
        break;
        
    default:
        returnError('Method not allowed', 405);
}
