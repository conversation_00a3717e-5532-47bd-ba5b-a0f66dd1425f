<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        throw new Exception('Invalid JSON input');
    }

    $vimeoId = $input['vimeo_id'] ?? null;
    $videoId = $input['video_id'] ?? null;
    $quality = $input['quality'] ?? 'auto';

    if (!$vimeoId || !$videoId) {
        throw new Exception('Missing required parameters');
    }

    // Validate user access to the video first
    require_once 'validate_video_access.php';
    $userId = getCurrentUserId();
    $hasAccess = validateUserVideoAccess($pdo, $videoId, $userId);

    if (!$hasAccess) {
        throw new Exception('Access denied to this video');
    }

    // Get Vimeo access token
    $vimeoAccessToken = getVimeoAccessToken();
    if (!$vimeoAccessToken) {
        throw new Exception('Vimeo access token not configured');
    }

    // Get direct video URL from Vimeo
    $directUrl = getVimeoDirectUrl($vimeoId, $vimeoAccessToken, $quality);

    if ($directUrl) {
        // Log successful direct URL access
        logDirectUrlAccess($pdo, $vimeoId, $videoId, $userId, 'success');
        
        echo json_encode([
            'success' => true,
            'direct_url' => $directUrl,
            'quality' => $quality,
            'expires_in' => 3600 // URLs typically expire in 1 hour
        ]);
    } else {
        // Log failed direct URL access
        logDirectUrlAccess($pdo, $vimeoId, $videoId, $userId, 'failed');
        
        echo json_encode([
            'success' => false,
            'message' => 'Failed to get direct video URL - video may not support direct access'
        ]);
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function getCurrentUserId() {
    // Get user ID from JWT token or session
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (strpos($authHeader, 'Bearer ') === 0) {
        $token = substr($authHeader, 7);
        
        try {
            // Decode JWT token to get user ID
            $payload = decodeJWT($token);
            return $payload['user_id'] ?? null;
        } catch (Exception $e) {
            error_log("Failed to decode JWT: " . $e->getMessage());
        }
    }
    
    return null;
}

function decodeJWT($token) {
    // Simple JWT decode - in production, use a proper JWT library
    $parts = explode('.', $token);
    if (count($parts) !== 3) {
        throw new Exception('Invalid JWT format');
    }
    
    $payload = json_decode(base64_decode($parts[1]), true);
    if (!$payload) {
        throw new Exception('Invalid JWT payload');
    }
    
    return $payload;
}

function getVimeoAccessToken() {
    // Get Vimeo access token from environment or settings
    $token = getenv('VIMEO_ACCESS_TOKEN');
    if (!$token) {
        // Fallback to database settings
        global $pdo;
        try {
            $stmt = $pdo->prepare("SELECT setting_value FROM settings WHERE setting_key = 'vimeo_access_token'");
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $token = $result ? $result['setting_value'] : null;
        } catch (Exception $e) {
            error_log("Failed to get Vimeo access token: " . $e->getMessage());
        }
    }
    return $token;
}

function getVimeoDirectUrl($vimeoId, $accessToken, $quality) {
    try {
        // Get video files from Vimeo API
        $url = "https://api.vimeo.com/videos/{$vimeoId}?fields=files";
        
        $headers = [
            'Authorization: Bearer ' . $accessToken,
            'Content-Type: application/json',
            'Accept: application/vnd.vimeo.*+json;version=3.4'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_USERAGENT, 'KFT-Fitness-App/1.0');

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($httpCode === 200) {
            $data = json_decode($response, true);
            
            if (isset($data['files']) && is_array($data['files'])) {
                $files = $data['files'];
                
                // Sort files by quality (highest first)
                usort($files, function($a, $b) {
                    $heightA = $a['height'] ?? 0;
                    $heightB = $b['height'] ?? 0;
                    return $heightB - $heightA;
                });
                
                // Select appropriate quality
                $selectedFile = selectVideoQuality($files, $quality);
                
                if ($selectedFile && isset($selectedFile['link'])) {
                    return $selectedFile['link'];
                }
            }
        }

        return null;
    } catch (Exception $e) {
        error_log("Failed to get Vimeo direct URL: " . $e->getMessage());
        return null;
    }
}

function selectVideoQuality($files, $requestedQuality) {
    if (empty($files)) {
        return null;
    }
    
    // Quality mapping
    $qualityMap = [
        '1080p' => 1080,
        '720p' => 720,
        '480p' => 480,
        '360p' => 360,
        '240p' => 240
    ];
    
    if ($requestedQuality === 'auto' || !isset($qualityMap[$requestedQuality])) {
        // Return highest quality available
        return $files[0];
    }
    
    $targetHeight = $qualityMap[$requestedQuality];
    
    // Find closest quality match
    $bestMatch = null;
    $smallestDiff = PHP_INT_MAX;
    
    foreach ($files as $file) {
        $height = $file['height'] ?? 0;
        $diff = abs($height - $targetHeight);
        
        if ($diff < $smallestDiff) {
            $smallestDiff = $diff;
            $bestMatch = $file;
        }
    }
    
    return $bestMatch ?: $files[0];
}

function logDirectUrlAccess($pdo, $vimeoId, $videoId, $userId, $status) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO direct_url_access_logs (vimeo_id, video_id, user_id, status, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $stmt->execute([$vimeoId, $videoId, $userId, $status, $ipAddress, $userAgent]);
        
    } catch (Exception $e) {
        error_log("Failed to log direct URL access: " . $e->getMessage());
    }
}
?>
