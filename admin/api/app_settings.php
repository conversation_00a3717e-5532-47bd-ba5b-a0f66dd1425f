<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'GET':
        getAppSettings($conn);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        break;
}

// Function to get application settings
function getAppSettings($conn) {
    // Get quote settings
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM quote_settings");
    $stmt->execute();
    $result = $stmt->get_result();
    
    $settings = [];
    
    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    
    $stmt->close();
    
    // Convert settings to appropriate types
    $appSettings = [
        'quotes_enabled' => isset($settings['quotes_enabled']) ? ($settings['quotes_enabled'] == '1') : true,
        'quote_refresh_frequency' => $settings['quote_refresh_frequency'] ?? 'daily',
        'personalization_enabled' => isset($settings['personalization_enabled']) ? ($settings['personalization_enabled'] == '1') : true,
    ];
    
    echo json_encode(['success' => true, 'settings' => $appSettings]);
}
