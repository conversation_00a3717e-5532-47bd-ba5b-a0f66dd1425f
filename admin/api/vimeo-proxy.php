<?php
// admin/api/vimeo-proxy.php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: text/html');

// Extract video ID from URL path
$request_uri = $_SERVER['REQUEST_URI'];
$path_parts = explode('/', trim($request_uri, '/'));
$vimeo_id = end($path_parts);

// Remove query string if present in the ID
if (strpos($vimeo_id, '?') !== false) {
    $vimeo_id = substr($vimeo_id, 0, strpos($vimeo_id, '?'));
}

$params = $_GET;

// Cache key based on video ID and parameters
$cache_key = 'vimeo_' . md5($vimeo_id . serialize($params));
$cache_file = __DIR__ . '/cache/' . $cache_key;
$cache_time = 3600; // 1 hour cache

// For debugging in development mode
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('Vimeo Proxy: ID=' . $vimeo_id . ', Cache Key=' . $cache_key);
}

// Serve from cache if available and fresh
if (file_exists($cache_file) && time() - filemtime($cache_file) < $cache_time) {
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('Vimeo Proxy: Serving from cache');
    }
    readfile($cache_file);
    exit;
}

// Forward to Vimeo if no cache
$vimeo_url = "https://player.vimeo.com/video/{$vimeo_id}?" . http_build_query($params);

if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log('Vimeo Proxy: Fetching from Vimeo URL: ' . $vimeo_url);
}

$content = @file_get_contents($vimeo_url);

// Handle errors
if ($content === false) {
    if (defined('DEV_MODE') && DEV_MODE === true) {
        error_log('Vimeo Proxy: Error fetching content from Vimeo');
    }
    header('HTTP/1.1 404 Not Found');
    echo 'Error fetching video content';
    exit;
}

// Cache the response
if (!is_dir(__DIR__ . '/cache')) {
    mkdir(__DIR__ . '/cache', 0755, true);
}
file_put_contents($cache_file, $content);

// Add a comment to indicate this is proxied content
$content = str_replace('</head>', "<!-- Proxied via mycloudforge.com --></head>", $content);

echo $content; 