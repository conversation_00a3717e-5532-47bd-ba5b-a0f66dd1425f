<?php
require_once 'config.php';

// Create water intake tables
$conn = getDbConnection();

// Create water_intake table for daily totals (without foreign key for now)
$waterIntakeTable = "
CREATE TABLE IF NOT EXISTS water_intake (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    date DATE NOT NULL,
    current_intake INT DEFAULT 0,
    target_intake INT DEFAULT 2000,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_date (user_id, date),
    INDEX idx_user_id (user_id)
)";

// Create water_logs table for individual intake entries (without foreign key for now)
$waterLogsTable = "
CREATE TABLE IF NOT EXISTS water_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    amount INT NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_timestamp (user_id, timestamp)
)";

try {
    // Create water_intake table
    if ($conn->query($waterIntakeTable)) {
        echo "✅ water_intake table created successfully\n";
    } else {
        echo "❌ Error creating water_intake table: " . $conn->error . "\n";
    }
    
    // Create water_logs table
    if ($conn->query($waterLogsTable)) {
        echo "✅ water_logs table created successfully\n";
    } else {
        echo "❌ Error creating water_logs table: " . $conn->error . "\n";
    }
    
    echo "🎉 Water intake database tables setup complete!\n";
    
} catch (Exception $e) {
    echo "❌ Error setting up water intake tables: " . $e->getMessage() . "\n";
}

$conn->close();
?>
