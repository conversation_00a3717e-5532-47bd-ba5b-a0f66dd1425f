<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config.php';

// Initialize database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed'
    ]);
    exit;
}

try {
    // Get user ID from JWT token (using the existing auth system)
    $headers = getallheaders();
    $authHeader = $headers['Authorization'] ?? '';
    
    if (!$authHeader || !str_starts_with($authHeader, 'Bearer ')) {
        // In development mode, use default user ID
        $userId = 29; // The user ID from the logs
    } else {
        $token = substr($authHeader, 7);
        $userData = authenticateUser($token);
        $userId = $userData['user_id'];
    }
    
    $courseId = 2; // The course ID from the logs
    
    // Create user_enrollments table if it doesn't exist
    createUserEnrollmentsTable($pdo);
    
    // Check if user is already enrolled
    $stmt = $pdo->prepare("
        SELECT id FROM user_enrollments 
        WHERE user_id = ? AND course_id = ?
    ");
    $stmt->execute([$userId, $courseId]);
    $existingEnrollment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existingEnrollment) {
        echo json_encode([
            'success' => true,
            'message' => 'User already enrolled',
            'enrollment_id' => $existingEnrollment['id'],
            'user_id' => $userId,
            'course_id' => $courseId
        ]);
    } else {
        // Enroll the user
        $stmt = $pdo->prepare("
            INSERT INTO user_enrollments (user_id, course_id, enrollment_date, status, created_at)
            VALUES (?, ?, NOW(), 'active', NOW())
        ");
        $stmt->execute([$userId, $courseId]);
        
        $enrollmentId = $pdo->lastInsertId();
        
        echo json_encode([
            'success' => true,
            'message' => 'User enrolled successfully',
            'enrollment_id' => $enrollmentId,
            'user_id' => $userId,
            'course_id' => $courseId
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function createUserEnrollmentsTable($pdo) {
    try {
        $sql = "
        CREATE TABLE IF NOT EXISTS user_enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            course_id INT NOT NULL,
            enrollment_date DATE NOT NULL,
            status ENUM('active', 'inactive', 'completed', 'cancelled') DEFAULT 'active',
            progress_percentage DECIMAL(5,2) DEFAULT 0.00,
            last_accessed TIMESTAMP NULL,
            completion_date DATE NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_course (user_id, course_id),
            INDEX idx_user_id (user_id),
            INDEX idx_course_id (course_id),
            INDEX idx_status (status)
        )";
        
        $pdo->exec($sql);
        
    } catch (Exception $e) {
        error_log("Failed to create user_enrollments table: " . $e->getMessage());
        throw $e;
    }
}

// Simple authentication function for development
function authenticateUser($token) {
    // In development mode, return a default user
    return [
        'user_id' => 29,
        'username' => 'jafer',
        'name' => 'jafer sadik'
    ];
}
?>
