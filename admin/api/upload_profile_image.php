<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
require_once __DIR__ . '/config.php';

header('Content-Type: application/json');

// Authenticate user using API token
try {
    // Get token from request
    $token = null;

    // First check if token is in the Authorization header
    $headers = getallheaders();
    $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
    if (!empty($authHeader) && preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
    }

    // If not in header, check if it's in POST or GET parameters
    if (empty($token)) {
        $token = $_POST['token'] ?? $_GET['token'] ?? null;
    }

    // Validate the token and get user data
    if (!empty($token)) {
        // Use the validateToken function from config.php
        $userData = validateToken();
        $userId = $userData['user_id'];
    } else {
        // For development mode, allow a test user
        if (defined('DEV_MODE') && DEV_MODE === true) {
            $userId = 1; // Use user ID 1 for testing
            error_log("DEV MODE: Using user ID 1 for profile image upload");
        } else {
            http_response_code(401);
            echo json_encode(['success' => false, 'error' => 'Authentication token required']);
            exit;
        }
    }

    if (!$userId) {
        http_response_code(401);
        echo json_encode(['success' => false, 'error' => 'User not found']);
        exit;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Authentication error: ' . $e->getMessage(),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => defined('DEV_MODE') && DEV_MODE === true
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

if (!isset($_FILES['profile_image'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'No image uploaded']);
    error_log("Profile image upload failed: No image uploaded");
    exit;
}

if ($_FILES['profile_image']['error'] !== UPLOAD_ERR_OK) {
    $errorMessage = 'Upload error: ';
    switch ($_FILES['profile_image']['error']) {
        case UPLOAD_ERR_INI_SIZE:
            $errorMessage .= 'The uploaded file exceeds the upload_max_filesize directive in php.ini';
            break;
        case UPLOAD_ERR_FORM_SIZE:
            $errorMessage .= 'The uploaded file exceeds the MAX_FILE_SIZE directive in the HTML form';
            break;
        case UPLOAD_ERR_PARTIAL:
            $errorMessage .= 'The uploaded file was only partially uploaded';
            break;
        case UPLOAD_ERR_NO_FILE:
            $errorMessage .= 'No file was uploaded';
            break;
        case UPLOAD_ERR_NO_TMP_DIR:
            $errorMessage .= 'Missing a temporary folder';
            break;
        case UPLOAD_ERR_CANT_WRITE:
            $errorMessage .= 'Failed to write file to disk';
            break;
        case UPLOAD_ERR_EXTENSION:
            $errorMessage .= 'A PHP extension stopped the file upload';
            break;
        default:
            $errorMessage .= 'Unknown upload error';
    }
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $errorMessage]);
    error_log("Profile image upload failed: " . $errorMessage);
    exit;
}

$uploadDir = __DIR__ . '/../assets/profile_images/';
if (!is_dir($uploadDir)) {
    if (!mkdir($uploadDir, 0777, true)) {
        $error = error_get_last();
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to create upload directory: ' . ($error['message'] ?? 'Unknown error')]);
        error_log("Failed to create upload directory: " . ($error['message'] ?? 'Unknown error'));
        exit;
    }
} elseif (!is_writable($uploadDir)) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Upload directory is not writable']);
    error_log("Upload directory is not writable: " . $uploadDir);
    exit;
}

// Log file information for debugging
$fileType = $_FILES['profile_image']['type'];
$fileName = $_FILES['profile_image']['name'];
$fileSize = $_FILES['profile_image']['size'];
$ext = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));

error_log("File upload details - Name: $fileName, Type: $fileType, Size: $fileSize, Extension: $ext");

// Validate file type with more flexibility
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'application/octet-stream'];
$allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'];

// In development mode, be more lenient with file types
if (defined('DEV_MODE') && DEV_MODE === true) {
    // If we have a valid extension but unknown mime type, proceed anyway
    if (in_array($ext, $allowedExtensions)) {
        error_log("DEV MODE: Allowing file with valid extension ($ext) despite mime type ($fileType)");
    } else if (!in_array($fileType, $allowedTypes)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Invalid file type. Allowed types: JPG, PNG, GIF, WEBP',
            'debug_info' => [
                'file_type' => $fileType,
                'file_name' => $fileName,
                'file_size' => $fileSize,
                'extension' => $ext
            ]
        ]);
        error_log("Invalid file type: " . $fileType);
        exit;
    }
} else {
    // In production, be more strict
    if (!in_array($fileType, $allowedTypes)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid file type. Allowed types: JPG, PNG, GIF, WEBP']);
        error_log("Invalid file type: " . $fileType);
        exit;
    }

    if (!in_array($ext, $allowedExtensions)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Invalid file extension. Allowed extensions: jpg, jpeg, png, gif, webp']);
        error_log("Invalid file extension: " . $ext);
        exit;
    }
}

$filename = 'user_' . $userId . '_' . time() . '.' . $ext;
$targetPath = $uploadDir . $filename;

if (!move_uploaded_file($_FILES['profile_image']['tmp_name'], $targetPath)) {
    $error = error_get_last();
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to save image: ' . ($error['message'] ?? 'Unknown error')]);
    error_log("Failed to save image: " . ($error['message'] ?? 'Unknown error'));
    exit;
}

// Log successful file upload
error_log("Profile image uploaded successfully: " . $targetPath);

// Use a relative path for better compatibility across environments
$imageUrl = 'admin/assets/profile_images/' . $filename;

// Log the image URL for debugging
error_log("Profile image URL (relative): " . $imageUrl);

// Also log the full URL for reference
// Don't include config.php again as it's already included at the top
$baseUrl = defined('APP_URL') ? APP_URL : 'http://localhost:8000';
$fullImageUrl = $baseUrl . '/' . $imageUrl;
error_log("Full profile image URL: " . $fullImageUrl);

// Update the user's profile_image_url in the database
$conn = getDbConnection();
if (!$conn) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to connect to database']);
    error_log("Failed to connect to database for profile image update");
    exit;
}

$stmt = $conn->prepare('UPDATE users SET profile_image_url = ? WHERE id = ?');
if (!$stmt) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to prepare database statement: ' . $conn->error]);
    error_log("Failed to prepare database statement: " . $conn->error);
    exit;
}

$stmt->bind_param('si', $imageUrl, $userId);
if (!$stmt->execute()) {
    http_response_code(500);
    echo json_encode(['success' => false, 'error' => 'Failed to update user profile: ' . $stmt->error]);
    error_log("Failed to update user profile: " . $stmt->error);
    $stmt->close();
    exit;
}

// Check if the update actually affected any rows
if ($stmt->affected_rows === 0) {
    error_log("Warning: Profile image URL updated but no rows were affected. User ID: " . $userId);
}

$stmt->close();

// Respond with the image URL
http_response_code(200);
echo json_encode(['success' => true, 'image_url' => $imageUrl]);