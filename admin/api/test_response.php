<?php
require_once 'config.php';

// Set appropriate headers for API response
header('Content-Type: application/json');
// Prevent caching
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Disable output buffering
if (ob_get_level()) ob_end_clean();

// Return a simple JSON response
echo json_encode([
    'success' => true,
    'message' => 'Test response successful',
    'timestamp' => time(),
    '_server_time' => date('Y-m-d H:i:s'),
    '_api_version' => '1.0'
]);
exit;
?>
