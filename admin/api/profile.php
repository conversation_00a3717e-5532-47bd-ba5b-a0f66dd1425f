<?php
// Include the API configuration file which has error handlers and helper functions
require_once 'config.php';
require_once '../includes/auth.php'; // Include auth class

// Set content type header to ensure clean JSON response
header('Content-Type: application/json; charset=UTF-8');

// Handle CORS
handleCors();

// Get authorization header (case-insensitive)
$headers = getallheaders();
$authHeader = '';

// Case-insensitive header lookup
foreach ($headers as $key => $value) {
    if (strtolower($key) === 'authorization') {
        $authHeader = $value;
        break;
    }
}

$token = '';
if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Enhanced logging for debugging
error_log("=== PROFILE API REQUEST ===");
error_log("Request method: " . $_SERVER['REQUEST_METHOD']);
error_log("Request URI: " . $_SERVER['REQUEST_URI']);
error_log("Authorization header: " . ($authHeader ?: 'NOT PROVIDED'));
error_log("Extracted token: " . ($token ? substr($token, 0, 20) . "..." : 'EMPTY'));

// For debugging, log the full token
if (defined('DEV_MODE') && DEV_MODE === true) {
    error_log("Full token: $token");
    error_log("All headers: " . print_r($headers, true));
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

// Enhanced authentication result logging
if ($userId) {
    error_log("✅ User authenticated successfully: User ID = $userId");
} else {
    error_log("❌ Authentication failed for token: " . substr($token, 0, 20) . "...");
    error_log("Auth object type: " . get_class($auth));
}
error_log("=== END PROFILE API REQUEST ===");

if (!$userId) {
    returnError('Unauthorized: Please log in again', 401);
}

// Get database connection
$conn = getDbConnection();

// Handle different HTTP methods
$method = $_SERVER['REQUEST_METHOD'];

switch ($method) {
    case 'GET':
        handleGetProfile($conn, $userId);
        break;
    case 'PUT':
        handleUpdateProfile($conn, $userId);
        break;
    default:
        returnError('Method not allowed', 405);
}

/**
 * Handle GET request - Retrieve user profile
 */
function handleGetProfile($conn, $userId) {
    $stmt = $conn->prepare('SELECT id, name, phone, phone_number, email, profile_image_url, height, weight, age, gender, fitness_goal, assigned_staff_id FROM users WHERE id = ? LIMIT 1');
    $stmt->bind_param('i', $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        // Calculate BMI if height and weight are available
        $bmi = null;
        $bmiCategory = '';
        if (!empty($user['height']) && !empty($user['weight'])) {
            $heightInMeters = $user['height'] / 100;
            $bmi = $user['weight'] / ($heightInMeters * $heightInMeters);

            // Determine BMI category
            if ($bmi < 18.5) {
                $bmiCategory = 'Underweight';
            } elseif ($bmi < 25) {
                $bmiCategory = 'Normal weight';
            } elseif ($bmi < 30) {
                $bmiCategory = 'Overweight';
            } else {
                $bmiCategory = 'Obese';
            }
        }

        $user['bmi'] = $bmi;
        $user['bmi_category'] = $bmiCategory;

        // Ensure assigned_staff_id is always present in the response
        if (!isset($user['assigned_staff_id'])) {
            $user['assigned_staff_id'] = null;
        }

        returnResponse(['success' => true, 'profile' => $user]);
    } else {
        returnError('User not found', 404);
    }
}

/**
 * Handle PUT request - Update user profile
 */
function handleUpdateProfile($conn, $userId) {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        returnError('Invalid JSON input', 400);
    }

    error_log("Profile update request for user $userId: " . json_encode($input));

    // Validate and sanitize input
    $updateFields = [];
    $updateValues = [];
    $updateTypes = '';

    // Handle name update
    if (isset($input['name']) && !empty(trim($input['name']))) {
        $updateFields[] = 'name = ?';
        $updateValues[] = trim($input['name']);
        $updateTypes .= 's';
    }

    // Handle email update
    if (isset($input['email'])) {
        $email = trim($input['email']);
        if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $updateFields[] = 'email = ?';
            $updateValues[] = $email;
            $updateTypes .= 's';
        } elseif (!empty($email)) {
            returnError('Invalid email format', 400);
        }
    }

    // Handle height update
    if (isset($input['height'])) {
        $height = floatval($input['height']);
        if ($height > 0 && $height <= 300) { // Reasonable height range in cm
            $updateFields[] = 'height = ?';
            $updateValues[] = $height;
            $updateTypes .= 'd';
        } elseif ($height > 0) {
            returnError('Height must be between 1 and 300 cm', 400);
        }
    }

    // Handle weight update
    if (isset($input['weight'])) {
        $weight = floatval($input['weight']);
        if ($weight > 0 && $weight <= 1000) { // Reasonable weight range in kg
            $updateFields[] = 'weight = ?';
            $updateValues[] = $weight;
            $updateTypes .= 'd';
        } elseif ($weight > 0) {
            returnError('Weight must be between 1 and 1000 kg', 400);
        }
    }

    // Handle age update
    if (isset($input['age'])) {
        $age = intval($input['age']);
        if ($age > 0 && $age <= 150) {
            $updateFields[] = 'age = ?';
            $updateValues[] = $age;
            $updateTypes .= 'i';
        } elseif ($age > 0) {
            returnError('Age must be between 1 and 150', 400);
        }
    }

    // Handle gender update
    if (isset($input['gender']) && in_array($input['gender'], ['male', 'female', 'other'])) {
        $updateFields[] = 'gender = ?';
        $updateValues[] = $input['gender'];
        $updateTypes .= 's';
    }

    // Handle fitness goal update
    if (isset($input['fitness_goal']) && !empty(trim($input['fitness_goal']))) {
        $updateFields[] = 'fitness_goal = ?';
        $updateValues[] = trim($input['fitness_goal']);
        $updateTypes .= 's';
    }

    if (empty($updateFields)) {
        returnError('No valid fields to update', 400);
    }

    // Add updated_at timestamp
    $updateFields[] = 'updated_at = NOW()';

    // Build and execute update query
    $sql = "UPDATE users SET " . implode(', ', $updateFields) . " WHERE id = ?";
    $updateValues[] = $userId;
    $updateTypes .= 'i';

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        error_log("Failed to prepare update statement: " . $conn->error);
        returnError('Database error', 500);
    }

    $stmt->bind_param($updateTypes, ...$updateValues);

    if ($stmt->execute()) {
        error_log("Profile updated successfully for user $userId");

        // Log BMI record if weight or height was updated
        if (isset($input['weight']) || isset($input['height'])) {
            logBMIRecord($conn, $userId);
        }

        // Real-time updates disabled - using refresh-based updates only
        // triggerRealTimeUpdate($userId, 'profile_updated');

        // Return updated profile data
        handleGetProfile($conn, $userId);
    } else {
        error_log("Failed to update profile for user $userId: " . $stmt->error);
        returnError('Failed to update profile', 500);
    }
}

/**
 * Log BMI record when weight or height is updated
 */
function logBMIRecord($conn, $userId) {
    // Get current height and weight
    $stmt = $conn->prepare('SELECT height, weight FROM users WHERE id = ?');
    $stmt->bind_param('i', $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($user = $result->fetch_assoc()) {
        $height = $user['height'];
        $weight = $user['weight'];

        if ($height > 0 && $weight > 0) {
            $heightInMeters = $height / 100;
            $bmi = $weight / ($heightInMeters * $heightInMeters);

            // Insert BMI record
            $bmiStmt = $conn->prepare('INSERT INTO bmi_records (user_id, weight, height, bmi, recorded_at) VALUES (?, ?, ?, ?, NOW()) ON DUPLICATE KEY UPDATE weight = VALUES(weight), height = VALUES(height), bmi = VALUES(bmi), recorded_at = VALUES(recorded_at)');
            $bmiStmt->bind_param('iddd', $userId, $weight, $height, $bmi);
            $bmiStmt->execute();

            error_log("BMI record logged for user $userId: BMI = $bmi");
        }
    }
}

/**
 * Trigger real-time update notification
 */
function triggerRealTimeUpdate($userId, $eventType) {
    // Create a simple file-based notification system for real-time updates
    $notificationFile = __DIR__ . '/../temp/realtime_updates.json';

    // Ensure temp directory exists
    $tempDir = dirname($notificationFile);
    if (!is_dir($tempDir)) {
        mkdir($tempDir, 0755, true);
    }

    $notification = [
        'user_id' => $userId,
        'event_type' => $eventType,
        'timestamp' => time(),
        'data' => [
            'message' => 'Profile updated successfully',
            'requires_refresh' => true
        ]
    ];

    // Read existing notifications
    $notifications = [];
    if (file_exists($notificationFile)) {
        $content = file_get_contents($notificationFile);
        $notifications = json_decode($content, true) ?: [];
    }

    // Add new notification
    $notifications[] = $notification;

    // Keep only last 100 notifications
    $notifications = array_slice($notifications, -100);

    // Save notifications
    file_put_contents($notificationFile, json_encode($notifications));

    error_log("Real-time update notification triggered for user $userId: $eventType");
}
