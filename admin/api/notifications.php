<?php
/**
 * Notifications API
 *
 * This file provides API endpoints for managing notifications.
 */

require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// Check if user has permission to view notifications
if (!$auth->hasRole('super_admin') && !$auth->hasRole('manager') && !$auth->hasPermission('view_notifications')) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

// Set content type to JSON
header('Content-Type: application/json');

// Get the request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle different request methods
switch ($method) {
    case 'GET':
        // Get notifications
        getNotifications($conn);
        break;
    case 'POST':
        // Create a new notification or perform an action on notifications
        $action = isset($_POST['action']) ? $_POST['action'] : '';
        
        if ($action === 'mark_read') {
            markNotificationAsRead($conn);
        } elseif ($action === 'mark_all_read') {
            markAllNotificationsAsRead($conn);
        } else {
            createNotification($conn);
        }
        break;
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Method not allowed']);
        break;
}

/**
 * Get notifications
 *
 * @param mysqli $conn Database connection
 */
function getNotifications($conn) {
    // Get query parameters
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 10;
    $offset = isset($_GET['offset']) ? (int)$_GET['offset'] : 0;
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    $isRead = isset($_GET['is_read']) ? (int)$_GET['is_read'] : null;
    
    // Build the query
    $query = "SELECT * FROM notifications WHERE 1=1";
    $countQuery = "SELECT COUNT(*) as total FROM notifications WHERE 1=1";
    $params = [];
    $types = "";
    
    // Add filters
    if (!empty($type)) {
        $query .= " AND type = ?";
        $countQuery .= " AND type = ?";
        $params[] = $type;
        $types .= "s";
    }
    
    if ($isRead !== null) {
        $query .= " AND is_read = ?";
        $countQuery .= " AND is_read = ?";
        $params[] = $isRead;
        $types .= "i";
    }
    
    // Add sorting
    $query .= " ORDER BY created_at DESC";
    
    // Add pagination
    $query .= " LIMIT ? OFFSET ?";
    $params[] = $limit;
    $params[] = $offset;
    $types .= "ii";
    
    // Prepare and execute the count query
    $countStmt = $conn->prepare($countQuery);
    if (!empty($params) && count($params) > 2) {
        // Remove the last two parameters (limit and offset) for the count query
        $countParams = array_slice($params, 0, -2);
        $countTypes = substr($types, 0, -2);
        
        if (!empty($countParams)) {
            $countStmt->bind_param($countTypes, ...$countParams);
        }
    }
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $totalCount = $countResult->fetch_assoc()['total'];
    
    // Prepare and execute the main query
    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();
    
    // Fetch all notifications
    $notifications = [];
    while ($row = $result->fetch_assoc()) {
        $notifications[] = $row;
    }
    
    // Return the notifications
    echo json_encode([
        'notifications' => $notifications,
        'total' => $totalCount,
        'limit' => $limit,
        'offset' => $offset
    ]);
}

/**
 * Mark a notification as read
 *
 * @param mysqli $conn Database connection
 */
function markNotificationAsRead($conn) {
    // Get the notification ID
    $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
    
    if ($id <= 0) {
        http_response_code(400);
        echo json_encode(['error' => 'Invalid notification ID']);
        return;
    }
    
    // Update the notification
    $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $id);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'Notification marked as read']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to mark notification as read']);
    }
}

/**
 * Mark all notifications as read
 *
 * @param mysqli $conn Database connection
 */
function markAllNotificationsAsRead($conn) {
    // Update all notifications
    $query = "UPDATE notifications SET is_read = 1, read_at = NOW() WHERE is_read = 0";
    $stmt = $conn->prepare($query);
    
    if ($stmt->execute()) {
        echo json_encode(['success' => true, 'message' => 'All notifications marked as read']);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to mark all notifications as read']);
    }
}

/**
 * Create a new notification
 *
 * @param mysqli $conn Database connection
 */
function createNotification($conn) {
    // Get the notification data
    $data = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($data['type']) || !isset($data['message'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Missing required fields']);
        return;
    }
    
    // Insert the notification
    $query = "INSERT INTO notifications (type, message, target_id, target_type, recipient_id, recipient_type) VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($query);
    
    $type = $data['type'];
    $message = $data['message'];
    $targetId = isset($data['target_id']) ? $data['target_id'] : null;
    $targetType = isset($data['target_type']) ? $data['target_type'] : null;
    $recipientId = isset($data['recipient_id']) ? $data['recipient_id'] : null;
    $recipientType = isset($data['recipient_type']) ? $data['recipient_type'] : 'admin';
    
    $stmt->bind_param("ssisss", $type, $message, $targetId, $targetType, $recipientId, $recipientType);
    
    if ($stmt->execute()) {
        $id = $conn->insert_id;
        echo json_encode(['success' => true, 'message' => 'Notification created', 'id' => $id]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create notification']);
    }
}
