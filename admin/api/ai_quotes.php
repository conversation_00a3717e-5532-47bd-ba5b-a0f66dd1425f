<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'POST':
        generateAiQuote($conn, $userId);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        break;
}

// Function to generate an AI quote
function generateAiQuote($conn, $userId) {
    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);

    // Get AI settings
    $settings = getAiSettings($conn);

    // Check if AI personalization is enabled
    if ($settings['personalization_enabled'] != '1') {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'AI personalization is disabled by admin']);
        return;
    }

    // Check if the user already has a personalized quote for today
    $today = date('Y-m-d');

    // First, check if the user already has a quote for today
    $userQuoteStmt = $conn->prepare("
        SELECT q.* FROM motivational_quotes q
        JOIN user_quote_preferences p ON p.last_quote_id = q.id
        WHERE p.user_id = ? AND p.last_quote_date = ? AND q.is_ai_generated = 1
    ");
    $userQuoteStmt->bind_param("is", $userId, $today);
    $userQuoteStmt->execute();
    $userQuoteResult = $userQuoteStmt->get_result();

    // If user already has a quote for today, return it
    if ($userQuoteResult->num_rows > 0) {
        $quoteData = $userQuoteResult->fetch_assoc();
        echo json_encode(['success' => true, 'message' => 'Today\'s personalized quote retrieved', 'quote' => $quoteData, 'is_new' => false]);
        $userQuoteStmt->close();
        return;
    }
    $userQuoteStmt->close();

    // Check if we've reached the daily limit for AI-generated quotes (app-wide)
    $maxQuotesPerDay = (int)$settings['max_ai_quotes_per_day'];

    $countStmt = $conn->prepare("SELECT COUNT(*) as count FROM motivational_quotes WHERE is_ai_generated = 1 AND DATE(created_at) = ?");
    $countStmt->bind_param("s", $today);
    $countStmt->execute();
    $result = $countStmt->get_result();
    $count = $result->fetch_assoc()['count'];
    $countStmt->close();

    if ($count >= $maxQuotesPerDay * 10) { // Allow for multiple users, but still have a reasonable limit
        http_response_code(429);
        echo json_encode(['success' => false, 'error' => 'Daily limit for AI-generated quotes reached. Please try again tomorrow.']);
        return;
    }

    // Get user profile for personalization
    $userProfile = getUserProfile($conn, $userId);

    // Get user preferences
    $preferencesStmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
    $preferencesStmt->bind_param("i", $userId);
    $preferencesStmt->execute();
    $result = $preferencesStmt->get_result();
    $preferences = $result->num_rows > 0 ? $result->fetch_assoc() : null;
    $preferencesStmt->close();

    // Determine the category
    $category = isset($data['category']) ? $data['category'] : null;
    if (!$category && $preferences && $preferences['preferred_categories']) {
        $categories = explode(',', $preferences['preferred_categories']);
        $category = $categories[array_rand($categories)];
    }
    if (!$category) {
        $category = 'motivation';
    }

    // Generate the prompt based on user profile and preferences
    $prompt = generatePrompt($userProfile, $category, $data);

    // Call the appropriate AI API based on settings
    $aiProvider = $settings['ai_provider'];
    $apiKey = $settings[$aiProvider . '_api_key'];
    $model = $settings['ai_model'];

    if (empty($apiKey)) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'AI API key not configured']);
        return;
    }

    try {
        $quote = '';
        $author = 'AI';

        if ($aiProvider == 'openai') {
            $response = callOpenAiApi($apiKey, $model, $prompt);
            $quote = $response['quote'];
            $author = $response['author'];
        } elseif ($aiProvider == 'deepseek') {
            $response = callDeepSeekApi($apiKey, $model, $prompt);
            $quote = $response['quote'];
            $author = $response['author'];
        } else {
            throw new Exception("Unsupported AI provider: $aiProvider");
        }

        // Save the generated quote
        $insertStmt = $conn->prepare("INSERT INTO motivational_quotes (quote, author, category, is_ai_generated, is_active) VALUES (?, ?, ?, 1, 1)");
        $insertStmt->bind_param("sss", $quote, $author, $category);

        if ($insertStmt->execute()) {
            $quoteId = $conn->insert_id;

            // Get the newly created quote
            $selectStmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
            $selectStmt->bind_param("i", $quoteId);
            $selectStmt->execute();
            $result = $selectStmt->get_result();
            $quoteData = $result->fetch_assoc();
            $selectStmt->close();

            // Update user's last quote preference
            $updatePrefStmt = $conn->prepare("
                INSERT INTO user_quote_preferences
                (user_id, last_quote_id, last_quote_date, personalization_enabled)
                VALUES (?, ?, ?, 1)
                ON DUPLICATE KEY UPDATE
                last_quote_id = VALUES(last_quote_id),
                last_quote_date = VALUES(last_quote_date)
            ");
            $updatePrefStmt->bind_param("iis", $userId, $quoteId, $today);
            $updatePrefStmt->execute();
            $updatePrefStmt->close();

            echo json_encode(['success' => true, 'message' => 'Personalized quote generated successfully', 'quote' => $quoteData, 'is_new' => true]);
        } else {
            http_response_code(500);
            echo json_encode(['success' => false, 'error' => 'Failed to save AI quote: ' . $insertStmt->error]);
        }

        $insertStmt->close();
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to generate AI quote: ' . $e->getMessage()]);
    }
}

// Function to get AI settings
function getAiSettings($conn) {
    $settings = [
        'ai_provider' => 'openai',
        'openai_api_key' => '',
        'deepseek_api_key' => '',
        'ai_model' => 'gpt-3.5-turbo',
        'personalization_enabled' => '1',
        'personalization_factors' => 'workout_history,fitness_goals,streak_days,bmi_progress',
        'personalization_prompt' => 'Generate a motivational fitness quote for a user who {user_context}. The quote should be encouraging, positive, and tailored to their current fitness journey.',
        'max_ai_quotes_per_day' => '10'
    ];

    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM quote_settings");
    $stmt->execute();
    $result = $stmt->get_result();

    while ($row = $result->fetch_assoc()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }

    $stmt->close();

    return $settings;
}

// Function to get user profile for personalization
function getUserProfile($conn, $userId) {
    $profile = [
        'name' => '',
        'age' => null,
        'height' => null,
        'weight' => null,
        'fitness_level' => 'intermediate',
        'goals' => [],
        'workout_history' => [],
        'streak_days' => 0,
        'bmi_history' => [],
        'performance_context' => ''
    ];

    // Get basic user info
    $stmt = $conn->prepare("SELECT name, age, height, weight FROM users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        $profile['name'] = $user['name'];
        $profile['age'] = $user['age'];
        $profile['height'] = $user['height'];
        $profile['weight'] = $user['weight'];
    }

    $stmt->close();

    // Get user fitness goals if available
    $goalsStmt = $conn->prepare("SELECT goal_type FROM user_goals WHERE user_id = ?");
    if ($goalsStmt) {
        $goalsStmt->bind_param("i", $userId);
        $goalsStmt->execute();
        $goalsResult = $goalsStmt->get_result();

        while ($goal = $goalsResult->fetch_assoc()) {
            $profile['goals'][] = $goal['goal_type'];
        }

        $goalsStmt->close();
    }

    // Get workout history (last 7 days)
    $workoutStmt = $conn->prepare("
        SELECT title, duration_minutes, recorded_at
        FROM workout_records
        WHERE user_id = ?
        ORDER BY recorded_at DESC
        LIMIT 7
    ");

    if ($workoutStmt) {
        $workoutStmt->bind_param("i", $userId);
        $workoutStmt->execute();
        $workoutResult = $workoutStmt->get_result();

        while ($workout = $workoutResult->fetch_assoc()) {
            $profile['workout_history'][] = $workout;
        }

        $workoutStmt->close();
    }

    // Get streak days count
    $streakStmt = $conn->prepare("
        SELECT COUNT(*) as streak_count
        FROM streak_days
        WHERE user_id = ?
    ");

    if ($streakStmt) {
        $streakStmt->bind_param("i", $userId);
        $streakStmt->execute();
        $streakResult = $streakStmt->get_result();

        if ($row = $streakResult->fetch_assoc()) {
            $profile['streak_days'] = (int)$row['streak_count'];
        }

        $streakStmt->close();
    }

    // Get BMI history
    $bmiStmt = $conn->prepare("
        SELECT weight, bmi, recorded_at
        FROM bmi_records
        WHERE user_id = ?
        ORDER BY recorded_at DESC
        LIMIT 5
    ");

    if ($bmiStmt) {
        $bmiStmt->bind_param("i", $userId);
        $bmiStmt->execute();
        $bmiResult = $bmiStmt->get_result();

        while ($bmi = $bmiResult->fetch_assoc()) {
            $profile['bmi_history'][] = $bmi;
        }

        $bmiStmt->close();
    }

    // Generate performance context
    $profile['performance_context'] = generatePerformanceContext($profile);

    return $profile;
}

// Generate a context description based on user performance
function generatePerformanceContext($profile) {
    $context = [];

    // Workout frequency
    if (count($profile['workout_history']) > 0) {
        $workoutCount = count($profile['workout_history']);
        if ($workoutCount >= 5) {
            $context[] = "has been very active with $workoutCount workouts in the past week";
        } elseif ($workoutCount >= 3) {
            $context[] = "has been consistently active with $workoutCount workouts recently";
        } elseif ($workoutCount >= 1) {
            $context[] = "has completed $workoutCount workout(s) recently";
        }
    } else {
        $context[] = "hasn't logged any recent workouts";
    }

    // Streak information
    if ($profile['streak_days'] > 0) {
        if ($profile['streak_days'] >= 30) {
            $context[] = "has maintained an impressive {$profile['streak_days']}-day workout streak";
        } elseif ($profile['streak_days'] >= 7) {
            $context[] = "has a {$profile['streak_days']}-day workout streak going";
        } else {
            $context[] = "has started a {$profile['streak_days']}-day workout streak";
        }
    }

    // BMI/Weight progress
    if (count($profile['bmi_history']) >= 2) {
        $latest = $profile['bmi_history'][0];
        $previous = $profile['bmi_history'][count($profile['bmi_history']) - 1];

        $weightDiff = $latest['weight'] - $previous['weight'];

        if (abs($weightDiff) >= 1) {
            if ($weightDiff < 0) {
                $context[] = "has lost " . abs(round($weightDiff, 1)) . " kg recently";
            } else {
                $context[] = "has gained " . round($weightDiff, 1) . " kg recently";
            }
        }
    }

    // Goals
    if (!empty($profile['goals'])) {
        $context[] = "is focused on " . implode(', ', $profile['goals']);
    }

    return implode(' and ', $context);
}

// Function to generate a prompt for the AI
function generatePrompt($userProfile, $category, $data) {
    // Get AI settings
    global $conn;
    $settings = getAiSettings($conn);

    // Check if we have a custom prompt template
    if (isset($settings['personalization_prompt']) && !empty($settings['personalization_prompt'])) {
        $promptTemplate = $settings['personalization_prompt'];

        // Replace user context placeholder with actual context
        $prompt = str_replace('{user_context}', $userProfile['performance_context'], $promptTemplate);
    } else {
        // Default prompt generation
        $prompt = "Generate an inspiring and motivational quote";

        if ($category) {
            $prompt .= " about $category";
        }

        if (!empty($userProfile['performance_context'])) {
            $prompt .= " for a user who " . $userProfile['performance_context'];
        } elseif (!empty($userProfile['goals'])) {
            $goals = implode(', ', $userProfile['goals']);
            $prompt .= " related to the following fitness goals: $goals";
        }

        if (isset($data['theme']) && !empty($data['theme'])) {
            $prompt .= " with a theme of " . $data['theme'];
        }
    }

    // Always add the formatting instructions
    $prompt .= " The quote should be concise, impactful, and original. Please format your response as JSON with 'quote' and 'author' fields. The author can be 'Anonymous' or a fictional name that sounds like a fitness expert or motivational speaker.";

    return $prompt;
}

// Function to call DeepSeek API (primary provider)
function callDeepSeekApi($apiKey, $model, $prompt) {
    $url = 'https://api.deepseek.com/v1/chat/completions';

    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ];

    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a motivational quote generator specializing in fitness, health, and wellness. You create personalized, impactful quotes that inspire users based on their fitness journey.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'temperature' => 0.7,
        'max_tokens' => 150
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        throw new Exception('cURL error: ' . curl_error($ch));
    }

    curl_close($ch);

    if ($httpCode != 200) {
        throw new Exception('DeepSeek API error: ' . $response);
    }

    $responseData = json_decode($response, true);
    $content = $responseData['choices'][0]['message']['content'];

    // Try to parse JSON from the response
    try {
        $quoteData = json_decode($content, true);
        if (isset($quoteData['quote']) && isset($quoteData['author'])) {
            return $quoteData;
        }
    } catch (Exception $e) {
        // If JSON parsing fails, extract quote manually
    }

    // Fallback: extract quote and author manually
    $lines = explode("\n", $content);
    $quote = trim($content);
    $author = 'DeepSeek AI';

    foreach ($lines as $line) {
        if (strpos($line, 'Quote:') === 0) {
            $quote = trim(substr($line, 6));
        } elseif (strpos($line, 'Author:') === 0) {
            $author = trim(substr($line, 7));
        }
    }

    return ['quote' => $quote, 'author' => $author];
}

// Function to call OpenAI API (fallback provider)
function callOpenAiApi($apiKey, $model, $prompt) {
    $url = 'https://api.openai.com/v1/chat/completions';

    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $apiKey
    ];

    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a motivational quote generator specializing in fitness, health, and wellness.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'temperature' => 0.7,
        'max_tokens' => 150
    ];

    $ch = curl_init($url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

    if (curl_errno($ch)) {
        throw new Exception('cURL error: ' . curl_error($ch));
    }

    curl_close($ch);

    if ($httpCode != 200) {
        throw new Exception('OpenAI API error: ' . $response);
    }

    $responseData = json_decode($response, true);
    $content = $responseData['choices'][0]['message']['content'];

    // Try to parse JSON from the response
    try {
        $quoteData = json_decode($content, true);
        if (isset($quoteData['quote']) && isset($quoteData['author'])) {
            return $quoteData;
        }
    } catch (Exception $e) {
        // If JSON parsing fails, extract quote manually
    }

    // Fallback: extract quote and author manually
    $lines = explode("\n", $content);
    $quote = trim($content);
    $author = 'AI';

    foreach ($lines as $line) {
        if (strpos($line, 'Quote:') === 0) {
            $quote = trim(substr($line, 6));
        } elseif (strpos($line, 'Author:') === 0) {
            $author = trim(substr($line, 7));
        }
    }

    return ['quote' => $quote, 'author' => $author];
}
