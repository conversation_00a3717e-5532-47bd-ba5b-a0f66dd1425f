<?php
/**
 * API endpoint for toggling favorite status of a quote
 * 
 * This endpoint allows users to mark/unmark quotes as favorites
 */

// Include required files
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// Only allow POST requests
if ($method !== 'POST') {
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'error' => 'Method not allowed',
        'message' => 'Only POST method is allowed'
    ]);
    exit;
}

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'Unauthorized access',
        'message' => 'Authentication failed'
    ]);
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate required fields
if (!isset($data['quote_id']) || !is_numeric($data['quote_id'])) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => 'Invalid request',
        'message' => 'Quote ID is required and must be a number'
    ]);
    exit;
}

$quoteId = intval($data['quote_id']);
$isFavorite = isset($data['is_favorite']) ? (bool)$data['is_favorite'] : false;

// Check if the quote exists
$quoteStmt = $conn->prepare("SELECT id FROM motivational_quotes WHERE id = ?");
$quoteStmt->bind_param("i", $quoteId);
$quoteStmt->execute();
$quoteResult = $quoteStmt->get_result();

if ($quoteResult->num_rows === 0) {
    http_response_code(404);
    echo json_encode([
        'success' => false,
        'error' => 'Not found',
        'message' => 'Quote not found'
    ]);
    $quoteStmt->close();
    exit;
}
$quoteStmt->close();

// Check if the user already has this quote in favorites
$checkStmt = $conn->prepare("SELECT * FROM user_favorite_quotes WHERE user_id = ? AND quote_id = ?");
$checkStmt->bind_param("ii", $userId, $quoteId);
$checkStmt->execute();
$checkResult = $checkStmt->get_result();
$exists = $checkResult->num_rows > 0;
$checkStmt->close();

if ($isFavorite) {
    // Add to favorites if not already there
    if (!$exists) {
        $insertStmt = $conn->prepare("INSERT INTO user_favorite_quotes (user_id, quote_id, added_at) VALUES (?, ?, NOW())");
        $insertStmt->bind_param("ii", $userId, $quoteId);
        
        if ($insertStmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Quote added to favorites',
                'is_favorite' => true
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Database error',
                'message' => 'Failed to add quote to favorites: ' . $insertStmt->error
            ]);
        }
        
        $insertStmt->close();
    } else {
        // Already in favorites
        echo json_encode([
            'success' => true,
            'message' => 'Quote is already in favorites',
            'is_favorite' => true
        ]);
    }
} else {
    // Remove from favorites if it exists
    if ($exists) {
        $deleteStmt = $conn->prepare("DELETE FROM user_favorite_quotes WHERE user_id = ? AND quote_id = ?");
        $deleteStmt->bind_param("ii", $userId, $quoteId);
        
        if ($deleteStmt->execute()) {
            echo json_encode([
                'success' => true,
                'message' => 'Quote removed from favorites',
                'is_favorite' => false
            ]);
        } else {
            http_response_code(500);
            echo json_encode([
                'success' => false,
                'error' => 'Database error',
                'message' => 'Failed to remove quote from favorites: ' . $deleteStmt->error
            ]);
        }
        
        $deleteStmt->close();
    } else {
        // Not in favorites
        echo json_encode([
            'success' => true,
            'message' => 'Quote is not in favorites',
            'is_favorite' => false
        ]);
    }
}
