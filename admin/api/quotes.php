<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Handle CORS
handleCors();

// Set content type to JSON
header('Content-Type: application/json');

// Get request method
$method = $_SERVER['REQUEST_METHOD'];

// Get auth token from header
$headers = getallheaders();
$authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
$token = '';

if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
    $token = $matches[1];
}

// Check if user is authenticated
$auth = new Auth();
$userId = $auth->validateToken($token);

if (!$userId) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// Handle different request methods
switch ($method) {
    case 'GET':
        getQuote($conn, $userId);
        break;
    case 'POST':
        addQuote($conn, $userId);
        break;
    case 'PUT':
        updateQuote($conn, $userId);
        break;
    case 'DELETE':
        deleteQuote($conn, $userId);
        break;
    default:
        http_response_code(405);
        echo json_encode(['success' => false, 'error' => 'Method not allowed']);
        break;
}

// Function to get a personalized quote for the user
function getQuote($conn, $userId) {
    // Check if a specific quote ID is requested
    $quoteId = isset($_GET['id']) ? intval($_GET['id']) : null;

    if ($quoteId) {
        // Get a specific quote by ID
        $stmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ? AND is_active = 1");
        $stmt->bind_param("i", $quoteId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $quote = $result->fetch_assoc();
            echo json_encode(['success' => true, 'quote' => $quote]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Quote not found']);
        }

        $stmt->close();
        return;
    }

    // Get user preferences
    $stmt = $conn->prepare("SELECT * FROM user_quote_preferences WHERE user_id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    $preferences = null;
    if ($result->num_rows > 0) {
        $preferences = $result->fetch_assoc();
    }
    $stmt->close();

    // Check if we should return a new quote or the last one
    $today = date('Y-m-d');
    $lastQuoteDate = $preferences ? $preferences['last_quote_date'] : null;
    $lastQuoteId = $preferences ? $preferences['last_quote_id'] : null;

    // Get quote refresh frequency from settings
    $refreshFrequency = 'daily'; // Default
    $stmt = $conn->prepare("SELECT setting_value FROM quote_settings WHERE setting_key = 'quote_refresh_frequency'");
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $refreshFrequency = $result->fetch_assoc()['setting_value'];
    }
    $stmt->close();

    // If we have a last quote and it's from today (and refresh is daily), return it
    if ($lastQuoteId && $lastQuoteDate == $today && $refreshFrequency == 'daily') {
        $stmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
        $stmt->bind_param("i", $lastQuoteId);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $quote = $result->fetch_assoc();
            echo json_encode(['success' => true, 'quote' => $quote, 'is_new' => false]);
            $stmt->close();
            return;
        }
        $stmt->close();
    }

    // Check if personalization is enabled
    $personalizationEnabled = true;
    $stmt = $conn->prepare("SELECT setting_value FROM quote_settings WHERE setting_key = 'personalization_enabled'");
    $stmt->execute();
    $result = $stmt->get_result();
    if ($result->num_rows > 0) {
        $personalizationEnabled = $result->fetch_assoc()['setting_value'] == '1';
    }
    $stmt->close();

    // If personalization is enabled, try to generate a personalized quote
    if ($personalizationEnabled) {
        // Redirect to AI quotes API to generate a personalized quote
        require_once 'ai_quotes.php';
        generateAiQuote($conn, $userId);
        return;
    }

    // Get a new quote based on user preferences
    $categories = [];
    if ($preferences && $preferences['preferred_categories']) {
        $categories = explode(',', $preferences['preferred_categories']);
    } else {
        // Get default categories from settings
        $stmt = $conn->prepare("SELECT setting_value FROM quote_settings WHERE setting_key = 'default_categories'");
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $defaultCategories = $result->fetch_assoc()['setting_value'];
            $categories = explode(',', $defaultCategories);
        }
        $stmt->close();
    }

    // Build the query based on categories
    $query = "SELECT * FROM motivational_quotes WHERE is_active = 1";
    $params = [];
    $types = "";

    if (!empty($categories)) {
        $placeholders = implode(',', array_fill(0, count($categories), '?'));
        $query .= " AND category IN ($placeholders)";
        $params = $categories;
        $types = str_repeat("s", count($categories));
    }

    // Exclude the last quote if we have one
    if ($lastQuoteId) {
        $query .= " AND id != ?";
        $params[] = $lastQuoteId;
        $types .= "i";
    }

    // Order randomly and limit to 1
    $query .= " ORDER BY RAND() LIMIT 1";

    $stmt = $conn->prepare($query);
    if (!empty($params)) {
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $quote = $result->fetch_assoc();

        // Update user preferences with the new quote
        if ($preferences) {
            $updateStmt = $conn->prepare("UPDATE user_quote_preferences SET last_quote_id = ?, last_quote_date = ? WHERE user_id = ?");
            $updateStmt->bind_param("isi", $quote['id'], $today, $userId);
            $updateStmt->execute();
            $updateStmt->close();
        } else {
            // Create user preferences if they don't exist
            $insertStmt = $conn->prepare("INSERT INTO user_quote_preferences (user_id, last_quote_id, last_quote_date) VALUES (?, ?, ?)");
            $insertStmt->bind_param("iis", $userId, $quote['id'], $today);
            $insertStmt->execute();
            $insertStmt->close();
        }

        echo json_encode(['success' => true, 'quote' => $quote, 'is_new' => true]);
    } else {
        // If no quote found, try to get any active quote
        $fallbackStmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE is_active = 1 ORDER BY RAND() LIMIT 1");
        $fallbackStmt->execute();
        $fallbackResult = $fallbackStmt->get_result();

        if ($fallbackResult->num_rows > 0) {
            $quote = $fallbackResult->fetch_assoc();
            echo json_encode(['success' => true, 'quote' => $quote, 'is_new' => true]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'No quotes available']);
        }

        $fallbackStmt->close();
    }

    $stmt->close();
}

// Function to add a new quote (admin only)
function addQuote($conn, $userId) {
    // Check if user is admin
    $isAdmin = checkIfAdmin($conn, $userId);
    if (!$isAdmin) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Forbidden: Admin access required']);
        return;
    }

    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['quote']) || empty($data['quote'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Quote text is required']);
        return;
    }

    // Prepare and execute the query
    $stmt = $conn->prepare("INSERT INTO motivational_quotes (quote, author, category, is_ai_generated, is_active) VALUES (?, ?, ?, ?, ?)");
    $author = isset($data['author']) ? $data['author'] : null;
    $category = isset($data['category']) ? $data['category'] : null;
    $isAiGenerated = isset($data['is_ai_generated']) ? (int)$data['is_ai_generated'] : 0;
    $isActive = isset($data['is_active']) ? (int)$data['is_active'] : 1;

    $stmt->bind_param("sssii", $data['quote'], $author, $category, $isAiGenerated, $isActive);

    if ($stmt->execute()) {
        $quoteId = $conn->insert_id;

        // Get the newly created quote
        $selectStmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
        $selectStmt->bind_param("i", $quoteId);
        $selectStmt->execute();
        $result = $selectStmt->get_result();
        $quote = $result->fetch_assoc();
        $selectStmt->close();

        echo json_encode(['success' => true, 'message' => 'Quote added successfully', 'quote' => $quote]);
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to add quote: ' . $stmt->error]);
    }

    $stmt->close();
}

// Function to update an existing quote (admin only)
function updateQuote($conn, $userId) {
    // Check if user is admin
    $isAdmin = checkIfAdmin($conn, $userId);
    if (!$isAdmin) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Forbidden: Admin access required']);
        return;
    }

    // Get request body
    $data = json_decode(file_get_contents('php://input'), true);

    // Validate required fields
    if (!isset($data['id']) || empty($data['id'])) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Quote ID is required']);
        return;
    }

    // Build the update query
    $updateFields = [];
    $params = [];
    $types = "";

    if (isset($data['quote'])) {
        $updateFields[] = "quote = ?";
        $params[] = $data['quote'];
        $types .= "s";
    }

    if (isset($data['author'])) {
        $updateFields[] = "author = ?";
        $params[] = $data['author'];
        $types .= "s";
    }

    if (isset($data['category'])) {
        $updateFields[] = "category = ?";
        $params[] = $data['category'];
        $types .= "s";
    }

    if (isset($data['is_ai_generated'])) {
        $updateFields[] = "is_ai_generated = ?";
        $params[] = (int)$data['is_ai_generated'];
        $types .= "i";
    }

    if (isset($data['is_active'])) {
        $updateFields[] = "is_active = ?";
        $params[] = (int)$data['is_active'];
        $types .= "i";
    }

    if (empty($updateFields)) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'No fields to update']);
        return;
    }

    // Add the ID parameter
    $params[] = (int)$data['id'];
    $types .= "i";

    // Prepare and execute the query
    $query = "UPDATE motivational_quotes SET " . implode(", ", $updateFields) . " WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);

    if ($stmt->execute()) {
        // Get the updated quote
        $selectStmt = $conn->prepare("SELECT * FROM motivational_quotes WHERE id = ?");
        $selectStmt->bind_param("i", $data['id']);
        $selectStmt->execute();
        $result = $selectStmt->get_result();

        if ($result->num_rows > 0) {
            $quote = $result->fetch_assoc();
            echo json_encode(['success' => true, 'message' => 'Quote updated successfully', 'quote' => $quote]);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Quote not found after update']);
        }

        $selectStmt->close();
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to update quote: ' . $stmt->error]);
    }

    $stmt->close();
}

// Function to delete a quote (admin only)
function deleteQuote($conn, $userId) {
    // Check if user is admin
    $isAdmin = checkIfAdmin($conn, $userId);
    if (!$isAdmin) {
        http_response_code(403);
        echo json_encode(['success' => false, 'error' => 'Forbidden: Admin access required']);
        return;
    }

    // Get quote ID from query parameters
    $quoteId = isset($_GET['id']) ? intval($_GET['id']) : null;

    if (!$quoteId) {
        http_response_code(400);
        echo json_encode(['success' => false, 'error' => 'Quote ID is required']);
        return;
    }

    // Prepare and execute the query
    $stmt = $conn->prepare("DELETE FROM motivational_quotes WHERE id = ?");
    $stmt->bind_param("i", $quoteId);

    if ($stmt->execute()) {
        if ($stmt->affected_rows > 0) {
            echo json_encode(['success' => true, 'message' => 'Quote deleted successfully']);
        } else {
            http_response_code(404);
            echo json_encode(['success' => false, 'error' => 'Quote not found']);
        }
    } else {
        http_response_code(500);
        echo json_encode(['success' => false, 'error' => 'Failed to delete quote: ' . $stmt->error]);
    }

    $stmt->close();
}

// Helper function to check if a user is an admin
function checkIfAdmin($conn, $userId) {
    // For development mode, assume the user is an admin
    if (defined('DEV_MODE') && DEV_MODE === true) {
        return true;
    }

    // Try to get from admin_users table first
    $stmt = $conn->prepare("SELECT role FROM admin_users WHERE id = ?");
    $stmt->bind_param("i", $userId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $user = $result->fetch_assoc();
        return $user['role'] === 'admin';
    }

    // If not in admin_users, check if user has admin role in session
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }

    return false;
}
