<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/auth.php';
require_once '../includes/cors.php';

// Set content type header
header('Content-Type: application/json');

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    returnError('Method not allowed', 405);
}

// Get JSON data from request body
$jsonData = file_get_contents('php://input');
$data = json_decode($jsonData, true);

// Check if data is valid JSON
if (!$data) {
    returnError('Invalid JSON data');
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user is authenticated
$auth = new Auth($conn);
try {
    $userId = $auth->authenticateToken();

    if (!$userId) {
        returnError('Unauthorized access', 401);
    }
} catch (Exception $e) {
    // For development, allow access with a default user ID
    if (defined('DEV_MODE') && DEV_MODE === true) {
        $userId = 1; // Use admin user for development
    } else {
        returnError('Authentication error: ' . $e->getMessage(), 401);
    }
}

// Check required fields
if (!isset($data['course_id']) || !is_numeric($data['course_id'])) {
    returnError('Course ID is required');
}

$courseId = $data['course_id'];
$paymentMethod = isset($data['payment_method']) ? Utilities::sanitizeInput($data['payment_method']) : 'credit_card';
$transactionId = isset($data['transaction_id']) ? Utilities::sanitizeInput($data['transaction_id']) : generateTransactionId();

// Check if course exists and is active
$courseQuery = "SELECT * FROM courses WHERE id = ? AND is_active = 1";
$courseStmt = $conn->prepare($courseQuery);
$courseStmt->bind_param("i", $courseId);
$courseStmt->execute();
$courseResult = $courseStmt->get_result();

if ($courseResult->num_rows === 0) {
    returnError('Course not found or inactive', 404);
}

$course = $courseResult->fetch_assoc();

// Check if user already purchased this course
$checkQuery = "SELECT id FROM course_purchases WHERE user_id = ? AND course_id = ? AND status = 'completed'";
$checkStmt = $conn->prepare($checkQuery);
$checkStmt->bind_param("ii", $userId, $courseId);
$checkStmt->execute();
$checkResult = $checkStmt->get_result();

if ($checkResult->num_rows > 0) {
    returnError('You have already purchased this course', 400);
}

// Calculate final price
$originalPrice = $course['price'];
$discountPercentage = $course['discount_percentage'];
$finalPrice = $originalPrice;

if ($discountPercentage > 0) {
    $finalPrice = $originalPrice * (1 - $discountPercentage / 100);
}

// In a real app, you would process payment here
// For this example, we'll simulate a successful payment

// Insert purchase record
$insertQuery = "INSERT INTO course_purchases (user_id, course_id, purchase_date, amount_paid, payment_method, transaction_id, status)
                VALUES (?, ?, NOW(), ?, ?, ?, 'completed')";
$insertStmt = $conn->prepare($insertQuery);
$insertStmt->bind_param("iidss", $userId, $courseId, $finalPrice, $paymentMethod, $transactionId);

if (!$insertStmt->execute()) {
    returnError('Failed to record purchase: ' . $conn->error);
}

$purchaseId = $conn->insert_id;

// Create enrollment
$enrollmentQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                   VALUES (?, ?, CURDATE(), CURDATE(), DATE_ADD(CURDATE(), INTERVAL ? WEEK), 'active')";
$enrollmentStmt = $conn->prepare($enrollmentQuery);
$enrollmentStmt->bind_param("iii", $userId, $courseId, $course['duration_weeks']);

if (!$enrollmentStmt->execute()) {
    returnError('Failed to create enrollment: ' . $conn->error);
}

// Initialize video progress for first week videos
$videosQuery = "SELECT id FROM course_videos WHERE course_id = ? AND week_number = 1 ORDER BY sequence_number";
$videosStmt = $conn->prepare($videosQuery);
$videosStmt->bind_param("i", $courseId);
$videosStmt->execute();
$videosResult = $videosStmt->get_result();

while ($video = $videosResult->fetch_assoc()) {
    $progressQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                     VALUES (?, ?, 1, CURDATE())";
    $progressStmt = $conn->prepare($progressQuery);
    $progressStmt->bind_param("ii", $userId, $video['id']);
    $progressStmt->execute();
}

// Return success response
returnResponse([
    'success' => true,
    'message' => 'Course purchased successfully',
    'purchase_id' => $purchaseId,
    'transaction_id' => $transactionId,
    'amount_paid' => $finalPrice,
    'course' => [
        'id' => $course['id'],
        'title' => $course['title']
    ]
]);

// Helper function to generate a transaction ID
function generateTransactionId() {
    return 'TXN' . strtoupper(bin2hex(random_bytes(8)));
}

// Use the global helper functions from config.php
?>
