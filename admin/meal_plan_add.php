<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get all users
$usersQuery = "SELECT id, name, username FROM users WHERE is_active = 1 ORDER BY name ASC";
$stmt = $conn->prepare($usersQuery);
$stmt->execute();
$usersResult = $stmt->get_result();

// Define meal types and days of week
$mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'];
$daysOfWeek = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $userId = isset($_POST['user_id']) ? (int)$_POST['user_id'] : 0;
    $title = Utilities::sanitizeInput($_POST['title'] ?? '');
    $description = Utilities::sanitizeInput($_POST['description'] ?? '');
    $startDate = $_POST['start_date'] ?? '';
    $endDate = $_POST['end_date'] ?? '';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if (empty($title)) {
        $errors[] = 'Title is required.';
    }

    if ($userId <= 0) {
        $errors[] = 'Please select a user.';
    }

    if (empty($startDate)) {
        $errors[] = 'Start date is required.';
    }

    if (empty($endDate)) {
        $errors[] = 'End date is required.';
    }

    if (!empty($startDate) && !empty($endDate) && strtotime($startDate) > strtotime($endDate)) {
        $errors[] = 'End date must be after start date.';
    }

    if (empty($errors)) {
        // Insert meal plan
        $insertQuery = "INSERT INTO meal_plans (user_id, title, description, start_date, end_date, is_active)
                        VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        $stmt->bind_param("issssi", $userId, $title, $description, $startDate, $endDate, $isActive);

        if ($stmt->execute()) {
            $mealPlanId = $stmt->insert_id;

            // Process meals if any
            $mealSuccess = true;

            if (isset($_POST['meals']) && is_array($_POST['meals'])) {
                foreach ($_POST['meals'] as $meal) {
                    if (empty($meal['title'])) {
                        continue; // Skip empty meals
                    }

                    $mealTitle = Utilities::sanitizeInput($meal['title']);
                    $mealDescription = Utilities::sanitizeInput($meal['description'] ?? '');
                    $mealType = Utilities::sanitizeInput($meal['type'] ?? '');
                    $dayOfWeek = Utilities::sanitizeInput($meal['day'] ?? '');
                    $calories = !empty($meal['calories']) ? (int)$meal['calories'] : null;
                    $protein = !empty($meal['protein']) ? (float)$meal['protein'] : null;
                    $carbs = !empty($meal['carbs']) ? (float)$meal['carbs'] : null;
                    $fat = !empty($meal['fat']) ? (float)$meal['fat'] : null;

                    $mealQuery = "INSERT INTO meals (meal_plan_id, meal_type, title, description, calories, protein, carbs, fat, day_of_week)
                                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                    $mealStmt = $conn->prepare($mealQuery);
                    $mealStmt->bind_param("isssiddds", $mealPlanId, $mealType, $mealTitle, $mealDescription, $calories, $protein, $carbs, $fat, $dayOfWeek);

                    if (!$mealStmt->execute()) {
                        $mealSuccess = false;
                        break;
                    }
                }
            }

            if ($mealSuccess) {
                Utilities::setFlashMessage('success', 'Meal plan has been added successfully.');
                // Utilities::redirect('meal_plans.php');
            } else {
                Utilities::setFlashMessage('warning', 'Meal plan was created but there was an issue adding some meals.');
                // Utilities::redirect('meal_plan_edit.php?id=' . $mealPlanId);
            }
        } else {
            Utilities::setFlashMessage('danger', 'Failed to add meal plan: ' . $conn->error);
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Add New Meal Plan</h1>
    <!-- <a href="meal_plans.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Meal Plans
    </a> -->
</div>

<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form method="post" action="meal_plan_add.php" id="mealPlanForm">
            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <h5 class="mb-3">Basic Information</h5>

                    <div class="mb-3">
                        <label for="user_id" class="form-label">User <span class="text-danger">*</span></label>
                        <select class="form-select user-select" id="user_id" name="user_id" required>
                            <option value="">Select User</option>
                            <?php if ($usersResult && $usersResult->num_rows > 0): ?>
                                <?php while ($user = $usersResult->fetch_assoc()): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['name']); ?> (<?php echo htmlspecialchars($user['username']); ?>)
                                    </option>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="title" class="form-label">Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>

                <!-- Date Range and Status -->
                <div class="col-md-6">
                    <h5 class="mb-3">Date Range and Status</h5>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">Active Meal Plan</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Meals Section -->
            <div class="mt-4">
                <h5 class="mb-3">Meals</h5>

                <div class="card mb-3">
                    <div class="card-header bg-light">
                        <ul class="nav nav-tabs card-header-tabs" id="mealTabs" role="tablist">
                            <?php foreach ($daysOfWeek as $index => $day): ?>
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link <?php echo $index === 0 ? 'active' : ''; ?>"
                                            id="<?php echo $day; ?>-tab"
                                            data-bs-toggle="tab"
                                            data-bs-target="#<?php echo $day; ?>-content"
                                            type="button"
                                            role="tab"
                                            aria-controls="<?php echo $day; ?>-content"
                                            aria-selected="<?php echo $index === 0 ? 'true' : 'false'; ?>">
                                        <?php echo ucfirst($day); ?>
                                    </button>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <div class="card-body">
                        <div class="tab-content" id="mealTabsContent">
                            <?php foreach ($daysOfWeek as $index => $day): ?>
                                <div class="tab-pane fade <?php echo $index === 0 ? 'show active' : ''; ?>"
                                     id="<?php echo $day; ?>-content"
                                     role="tabpanel"
                                     aria-labelledby="<?php echo $day; ?>-tab">

                                    <?php foreach ($mealTypes as $type): ?>
                                        <div class="card mb-3">
                                            <div class="card-header bg-light">
                                                <h6 class="mb-0"><?php echo ucfirst($type); ?></h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="meal-item">
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Title</label>
                                                            <input type="text" class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][title]">
                                                            <input type="hidden" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][type]" value="<?php echo $type; ?>">
                                                            <input type="hidden" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][day]" value="<?php echo $day; ?>">
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Description</label>
                                                            <textarea class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][description]" rows="1"></textarea>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-3 mb-3">
                                                            <label class="form-label">Calories</label>
                                                            <input type="number" class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][calories]" min="0">
                                                        </div>
                                                        <div class="col-md-3 mb-3">
                                                            <label class="form-label">Protein (g)</label>
                                                            <input type="number" class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][protein]" min="0" step="0.1">
                                                        </div>
                                                        <div class="col-md-3 mb-3">
                                                            <label class="form-label">Carbs (g)</label>
                                                            <input type="number" class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][carbs]" min="0" step="0.1">
                                                        </div>
                                                        <div class="col-md-3 mb-3">
                                                            <label class="form-label">Fat (g)</label>
                                                            <input type="number" class="form-control" name="meals[<?php echo $day; ?>_<?php echo $type; ?>][fat]" min="0" step="0.1">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Save Meal Plan
                </button>
                <!-- <a href="meal_plans.php" class="btn btn-outline-secondary ms-2">Cancel</a> -->
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set default dates
    const today = new Date();
    const startDate = document.getElementById('start_date');
    const endDate = document.getElementById('end_date');

    // Format date as YYYY-MM-DD
    const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    // Set default start date to today
    startDate.value = formatDate(today);

    // Set default end date to 7 days from today
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 6);
    endDate.value = formatDate(nextWeek);

    // Form validation
    const form = document.getElementById('mealPlanForm');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }

        form.classList.add('was-validated');
    }, false);
});
</script>

<?php require_once 'includes/footer.php'; ?>
