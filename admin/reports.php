<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get date range for filtering
$startDate = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-d');

// Sanitize dates
$startDate = $db->escapeString($startDate);
$endDate = $db->escapeString($endDate);

// Get workout data for the selected period
$workoutQuery = "SELECT DATE(recorded_at) as workout_date, 
                COUNT(*) as workout_count,
                SUM(duration_minutes) as total_duration,
                AVG(duration_minutes) as avg_duration
                FROM workout_records 
                WHERE recorded_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59'
                GROUP BY DATE(recorded_at) 
                ORDER BY workout_date";
$workoutResult = $conn->query($workoutQuery);

$workoutDates = [];
$workoutCounts = [];
$workoutDurations = [];
$workoutAvgDurations = [];

if ($workoutResult && $workoutResult->num_rows > 0) {
    while ($row = $workoutResult->fetch_assoc()) {
        $workoutDates[] = date('M d', strtotime($row['workout_date']));
        $workoutCounts[] = $row['workout_count'];
        $workoutDurations[] = round($row['total_duration'] / 60, 1); // Convert to hours
        $workoutAvgDurations[] = round($row['avg_duration'], 1);
    }
}

// Get BMI data for the selected period
$bmiQuery = "SELECT DATE(recorded_at) as bmi_date, 
            AVG(bmi) as avg_bmi
            FROM bmi_records 
            WHERE recorded_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59'
            GROUP BY DATE(recorded_at) 
            ORDER BY bmi_date";
$bmiResult = $conn->query($bmiQuery);

$bmiDates = [];
$bmiValues = [];

if ($bmiResult && $bmiResult->num_rows > 0) {
    while ($row = $bmiResult->fetch_assoc()) {
        $bmiDates[] = date('M d', strtotime($row['bmi_date']));
        $bmiValues[] = round($row['avg_bmi'], 2);
    }
}

// Get user registration data
$userRegQuery = "SELECT DATE(created_at) as reg_date, 
                COUNT(*) as user_count
                FROM users 
                WHERE created_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59'
                GROUP BY DATE(created_at) 
                ORDER BY reg_date";
$userRegResult = $conn->query($userRegQuery);

$regDates = [];
$regCounts = [];

if ($userRegResult && $userRegResult->num_rows > 0) {
    while ($row = $userRegResult->fetch_assoc()) {
        $regDates[] = date('M d', strtotime($row['reg_date']));
        $regCounts[] = $row['user_count'];
    }
}

// Get program enrollment data
$enrollmentQuery = "SELECT DATE(created_at) as enroll_date, 
                   COUNT(*) as enrollment_count
                   FROM program_enrollments 
                   WHERE created_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59'
                   GROUP BY DATE(created_at) 
                   ORDER BY enroll_date";
$enrollmentResult = $conn->query($enrollmentQuery);

$enrollDates = [];
$enrollCounts = [];

if ($enrollmentResult && $enrollmentResult->num_rows > 0) {
    while ($row = $enrollmentResult->fetch_assoc()) {
        $enrollDates[] = date('M d', strtotime($row['enroll_date']));
        $enrollCounts[] = $row['enrollment_count'];
    }
}

// Get summary statistics
$summaryQuery = "SELECT 
                (SELECT COUNT(*) FROM workout_records WHERE recorded_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59') as total_workouts,
                (SELECT SUM(duration_minutes) FROM workout_records WHERE recorded_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59') as total_workout_minutes,
                (SELECT COUNT(*) FROM users WHERE created_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59') as new_users,
                (SELECT COUNT(*) FROM program_enrollments WHERE created_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59') as new_enrollments,
                (SELECT AVG(bmi) FROM bmi_records WHERE recorded_at BETWEEN '$startDate 00:00:00' AND '$endDate 23:59:59') as avg_bmi";
$summaryResult = $conn->query($summaryQuery);
$summary = $summaryResult->fetch_assoc();

// Calculate daily averages
$dateDiff = max(1, (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24));
$dailyWorkouts = $summary['total_workouts'] / $dateDiff;
$dailyWorkoutMinutes = $summary['total_workout_minutes'] / $dateDiff;
$dailyNewUsers = $summary['new_users'] / $dateDiff;
$dailyNewEnrollments = $summary['new_enrollments'] / $dateDiff;
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Reports & Analytics</h1>
    <div class="btn-group">
        <a href="reports.php?start_date=<?php echo date('Y-m-d', strtotime('-7 days')); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary">Last 7 Days</a>
        <a href="reports.php?start_date=<?php echo date('Y-m-d', strtotime('-30 days')); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary">Last 30 Days</a>
        <a href="reports.php?start_date=<?php echo date('Y-m-d', strtotime('-90 days')); ?>&end_date=<?php echo date('Y-m-d'); ?>" class="btn btn-outline-primary">Last 90 Days</a>
        <a href="reports.php?start_date=<?php echo date('Y-m-01'); ?>&end_date=<?php echo date('Y-m-t'); ?>" class="btn btn-outline-primary">This Month</a>
    </div>
</div>

<!-- Date Range Filter -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-body">
        <form action="reports.php" method="get" class="row g-3">
            <div class="col-md-4">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
            </div>
            <div class="col-md-4">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="submit" class="btn btn-primary w-100">
                    <i class="fas fa-filter me-2"></i> Apply Filter
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Summary Stats Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="card-icon bg-primary-gradient text-white me-3">
                        <i class="fas fa-dumbbell"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Total Workouts</h5>
                        <h2 class="mt-2 mb-0"><?php echo $summary['total_workouts'] ?? 0; ?></h2>
                        <p class="text-muted small mb-0"><?php echo number_format($dailyWorkouts, 1); ?> per day</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="card-icon bg-success-gradient text-white me-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Workout Time</h5>
                        <h2 class="mt-2 mb-0"><?php echo round(($summary['total_workout_minutes'] ?? 0) / 60, 1); ?> hrs</h2>
                        <p class="text-muted small mb-0"><?php echo round($dailyWorkoutMinutes, 1); ?> min per day</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="card-icon bg-warning-gradient text-white me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">New Users</h5>
                        <h2 class="mt-2 mb-0"><?php echo $summary['new_users'] ?? 0; ?></h2>
                        <p class="text-muted small mb-0"><?php echo number_format($dailyNewUsers, 1); ?> per day</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card dashboard-card border-0 h-100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="card-icon bg-danger-gradient text-white me-3">
                        <i class="fas fa-weight"></i>
                    </div>
                    <div>
                        <h5 class="card-title mb-0">Average BMI</h5>
                        <h2 class="mt-2 mb-0"><?php echo number_format($summary['avg_bmi'] ?? 0, 2); ?></h2>
                        <p class="text-muted small mb-0">
                            <?php 
                            $bmi = $summary['avg_bmi'] ?? 0;
                            if ($bmi < 18.5) echo "Underweight";
                            elseif ($bmi < 25) echo "Normal";
                            elseif ($bmi < 30) echo "Overweight";
                            else echo "Obese";
                            ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Workout Activity</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="workoutChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">BMI Trends</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="bmiChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="row mb-4">
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">User Registrations</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="userRegChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">Program Enrollments</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="enrollmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Export Options -->
<div class="card border-0 shadow-sm mb-4">
    <div class="card-header bg-white">
        <h5 class="card-title mb-0">Export Reports</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="export.php?type=workouts&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-file-csv me-2"></i> Export Workouts
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="export.php?type=users&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-file-csv me-2"></i> Export Users
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="export.php?type=bmi&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-file-csv me-2"></i> Export BMI Data
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="export.php?type=enrollments&start_date=<?php echo $startDate; ?>&end_date=<?php echo $endDate; ?>" class="btn btn-outline-primary w-100">
                    <i class="fas fa-file-csv me-2"></i> Export Enrollments
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Workout Activity Chart
    var workoutCtx = document.getElementById('workoutChart').getContext('2d');
    var workoutChart = new Chart(workoutCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($workoutDates); ?>,
            datasets: [{
                label: 'Workout Count',
                data: <?php echo json_encode($workoutCounts); ?>,
                backgroundColor: 'rgba(33, 147, 176, 0.7)',
                borderColor: '#2193b0',
                borderWidth: 1,
                yAxisID: 'y'
            }, {
                label: 'Avg Duration (min)',
                data: <?php echo json_encode($workoutAvgDurations); ?>,
                type: 'line',
                fill: false,
                backgroundColor: 'rgba(255, 159, 64, 0.7)',
                borderColor: '#ff9f40',
                borderWidth: 2,
                pointRadius: 3,
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'Workout Count'
                    }
                },
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: 'Duration (min)'
                    }
                }
            }
        }
    });
    
    // BMI Trends Chart
    var bmiCtx = document.getElementById('bmiChart').getContext('2d');
    var bmiChart = new Chart(bmiCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($bmiDates); ?>,
            datasets: [{
                label: 'Average BMI',
                data: <?php echo json_encode($bmiValues); ?>,
                backgroundColor: 'rgba(231, 76, 60, 0.2)',
                borderColor: '#e74c3c',
                borderWidth: 2,
                pointBackgroundColor: '#e74c3c',
                pointBorderColor: '#fff',
                pointRadius: 4,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: false,
                    suggestedMin: 18,
                    suggestedMax: 30
                }
            }
        }
    });
    
    // User Registration Chart
    var userRegCtx = document.getElementById('userRegChart').getContext('2d');
    var userRegChart = new Chart(userRegCtx, {
        type: 'bar',
        data: {
            labels: <?php echo json_encode($regDates); ?>,
            datasets: [{
                label: 'New Users',
                data: <?php echo json_encode($regCounts); ?>,
                backgroundColor: 'rgba(46, 204, 113, 0.7)',
                borderColor: '#2ecc71',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
    
    // Program Enrollment Chart
    var enrollmentCtx = document.getElementById('enrollmentChart').getContext('2d');
    var enrollmentChart = new Chart(enrollmentCtx, {
        type: 'line',
        data: {
            labels: <?php echo json_encode($enrollDates); ?>,
            datasets: [{
                label: 'Program Enrollments',
                data: <?php echo json_encode($enrollCounts); ?>,
                backgroundColor: 'rgba(155, 89, 182, 0.2)',
                borderColor: '#9b59b6',
                borderWidth: 2,
                pointBackgroundColor: '#9b59b6',
                pointBorderColor: '#fff',
                pointRadius: 4,
                tension: 0.3,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0
                    }
                }
            }
        }
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
