# KFT Fitness Admin Dashboard

This is the admin dashboard for the KFT Fitness App, built with PHP and Bootstrap.

## Features

- User management
- Workout tracking and analytics
- Program management
- BMI and health data visualization
- Comprehensive reporting
- Admin user management
- System settings and maintenance

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache, Nginx, etc.)

## Installation

1. **Set up the database**

   Import the SQL file to create the database and tables:

   ```
   mysql -u username -p < kft_fitness.sql
   ```

   Or use phpMyAdmin to import the `kft_fitness.sql` file.

2. **Configure database connection**

   Edit the `includes/config.php` file and update the database connection details:

   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'kft_fitness');
   ```

3. **Set up the web server**

   Configure your web server to point to the `admin` directory.

4. **Login to the dashboard**

   Access the dashboard through your web browser and log in with the default admin credentials:

   - Username: `admin`
   - Password: `admin123`

   **Important:** Change the default password immediately after the first login.

## Directory Structure

```
admin/
├── api/                  # API endpoints for mobile app
├── assets/               # Static assets
│   ├── css/              # CSS files
│   ├── js/               # JavaScript files
│   └── img/              # Images
├── includes/             # PHP includes
│   ├── auth.php          # Authentication class
│   ├── config.php        # Configuration file
│   ├── database.php      # Database connection class
│   ├── footer.php        # Footer template
│   ├── header.php        # Header template
│   └── utilities.php     # Utility functions
├── pages/                # Additional pages
├── index.php             # Dashboard home
├── login.php             # Login page
├── logout.php            # Logout script
├── users.php             # User management
├── workouts.php          # Workout management
├── programs.php          # Program management
├── reports.php           # Reports and analytics
├── settings.php          # System settings
├── kft_fitness.sql       # Database schema
└── README.md             # This file
```

## Security Considerations

- Change the default admin password immediately
- Use HTTPS for production environments
- Keep PHP and MySQL updated
- Regularly backup the database

## License

This project is proprietary and confidential. Unauthorized copying, distribution, or use is strictly prohibited.

## Support

For support, please contact the development <NAME_EMAIL>.
