<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get filter parameters
$search = isset($_GET['search']) ? Utilities::sanitizeInput($_GET['search']) : '';
$status = isset($_GET['status']) ? Utilities::sanitizeInput($_GET['status']) : '';
$premium = isset($_GET['premium']) ? Utilities::sanitizeInput($_GET['premium']) : '';
$dateRange = isset($_GET['date_range']) ? Utilities::sanitizeInput($_GET['date_range']) : '';
$sort = isset($_GET['sort']) ? Utilities::sanitizeInput($_GET['sort']) : 'created_at';
$order = isset($_GET['order']) ? Utilities::sanitizeInput($_GET['order']) : 'DESC';

// Validate sort field to prevent SQL injection
$allowedSortFields = ['id', 'name', 'username', 'phone_number', 'created_at', 'last_login', 'is_premium', 'is_active'];
if (!in_array($sort, $allowedSortFields)) {
    $sort = 'created_at';
}

// Validate order direction
if ($order !== 'ASC' && $order !== 'DESC') {
    $order = 'DESC';
}

// Build query with filters
$query = "SELECT * FROM users WHERE 1=1 AND id != 1";
$params = [];
$types = "";

if (!empty($search)) {
    $searchTerm = "%$search%";
    $query .= " AND (name LIKE ? OR username LIKE ? OR phone_number LIKE ?)";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $types .= "sss";
}

if ($status === 'active') {
    $query .= " AND is_active = 1";
} elseif ($status === 'inactive') {
    $query .= " AND is_active = 0";
}

// Filter by premium status
if ($premium === '1') {
    $query .= " AND is_premium = 1";
} elseif ($premium === '0') {
    $query .= " AND is_premium = 0";
}

// Filter by date range
if ($dateRange === 'today') {
    $today = date('Y-m-d');
    $query .= " AND DATE(created_at) = '$today'";
} elseif ($dateRange === 'week') {
    $weekStart = date('Y-m-d', strtotime('monday this week'));
    $query .= " AND DATE(created_at) >= '$weekStart'";
} elseif ($dateRange === 'month') {
    $monthStart = date('Y-m-01');
    $query .= " AND DATE(created_at) >= '$monthStart'";
} elseif ($dateRange === 'year') {
    $yearStart = date('Y-01-01');
    $query .= " AND DATE(created_at) >= '$yearStart'";
}

// Add sorting
$query .= " ORDER BY $sort $order";

// Prepare and execute the query
$stmt = $conn->prepare($query);
if (!empty($params)) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$result = $stmt->get_result();

// Preload all staff names for mapping
$staffMap = [];
$staffResult = $conn->query("SELECT id, name FROM admin_users");
if ($staffResult && $staffResult->num_rows > 0) {
    while ($staff = $staffResult->fetch_assoc()) {
        $staffMap[$staff['id']] = $staff['name'];
    }
}

// Set headers for CSV download
header('Content-Type: text/csv');
header('Content-Disposition: attachment; filename="kft_users_export_' . date('Y-m-d') . '.csv"');

// Create a file pointer connected to the output stream
$output = fopen('php://output', 'w');

// Output the column headings
fputcsv($output, [
    'ID', 'Username', 'Name', 'Email', 'Phone Number', 'Address', 'Age', 'Height (cm)', 'Weight (kg)', 'BMI',
    'Premium', 'Active', 'Assigned Staff', 'Last Login', 'Registration Date'
]);

// Fetch and output each row
while ($row = $result->fetch_assoc()) {
    // Calculate BMI if height and weight are available
    $bmi = null;
    if (!empty($row['height']) && !empty($row['weight'])) {
        $heightInMeters = $row['height'] / 100;
        $bmi = number_format($row['weight'] / ($heightInMeters * $heightInMeters), 1);
    }
    // Address field fallback
    $address = $row['address'] ?? ($row['address_line1'] ?? '') . (isset($row['address_line2']) ? (' ' . $row['address_line2']) : '');
    $address = trim($address) ?: 'N/A';
    // Assigned staff name
    $assignedStaff = '';
    if (!empty($row['assigned_staff_id']) && isset($staffMap[$row['assigned_staff_id']])) {
        $assignedStaff = $staffMap[$row['assigned_staff_id']];
    } else {
        $assignedStaff = 'N/A';
    }
    fputcsv($output, [
        $row['id'],
        $row['username'],
        $row['name'],
        $row['email'] ?? 'N/A',
        $row['phone_number'],
        $address,
        $row['age'] ?? 'N/A',
        $row['height'] ?? 'N/A',
        $row['weight'] ?? 'N/A',
        $bmi ?? 'N/A',
        $row['is_premium'] ? 'Yes' : 'No',
        $row['is_active'] ? 'Active' : 'Inactive',
        $assignedStaff,
        $row['last_login'] ? date('Y-m-d H:i:s', strtotime($row['last_login'])) : 'Never',
        date('Y-m-d H:i:s', strtotime($row['created_at']))
    ]);
}

// Close the file pointer
fclose($output);
exit;
