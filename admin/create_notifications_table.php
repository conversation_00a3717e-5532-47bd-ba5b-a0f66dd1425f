<?php
/**
 * Create Notifications Table
 *
 * This script creates the notifications table in the database.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this tool.');
}

// Check if the notifications table already exists
$tableExistsQuery = "SHOW TABLES LIKE 'notifications'";
$result = $conn->query($tableExistsQuery);
$tableExists = $result->num_rows > 0;

if ($tableExists) {
    $message = "The notifications table already exists.";
} else {
    // Create the notifications table
    $createTableQuery = "
    CREATE TABLE notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL,
        message TEXT NOT NULL,
        target_id INT NULL,
        target_type VARCHAR(50) NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        is_read BOOLEAN DEFAULT FALSE,
        read_at TIMESTAMP NULL,
        recipient_id INT NULL,
        recipient_type VARCHAR(50) DEFAULT 'admin',
        INDEX (type),
        INDEX (is_read),
        INDEX (recipient_id),
        INDEX (recipient_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";
    
    if ($conn->query($createTableQuery)) {
        $message = "Notifications table created successfully.";
        
        // Insert some sample notifications
        $sampleNotifications = [
            [
                'type' => 'user',
                'message' => 'New user registration: John Doe',
                'target_id' => 1,
                'target_type' => 'user',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 hour')),
                'is_read' => 0
            ],
            [
                'type' => 'system',
                'message' => 'System update completed successfully',
                'target_id' => null,
                'target_type' => null,
                'created_at' => date('Y-m-d H:i:s', strtotime('-3 hours')),
                'is_read' => 1
            ],
            [
                'type' => 'course',
                'message' => 'New course "Advanced Fitness" has been created',
                'target_id' => 1,
                'target_type' => 'course',
                'created_at' => date('Y-m-d H:i:s', strtotime('-1 day')),
                'is_read' => 0
            ]
        ];
        
        $insertQuery = "INSERT INTO notifications (type, message, target_id, target_type, created_at, is_read) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
        
        foreach ($sampleNotifications as $notification) {
            $stmt->bind_param("ssissi", 
                $notification['type'], 
                $notification['message'], 
                $notification['target_id'], 
                $notification['target_type'], 
                $notification['created_at'], 
                $notification['is_read']
            );
            $stmt->execute();
        }
        
        $message .= " Sample notifications added.";
    } else {
        $message = "Error creating notifications table: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Notifications Table</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Create Notifications Table</h1>
        
        <div class="alert alert-info">
            <?php echo $message; ?>
        </div>
        
        <div class="section">
            <h2>Table Structure</h2>
            <pre>
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    message TEXT NOT NULL,
    target_id INT NULL,
    target_type VARCHAR(50) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    recipient_id INT NULL,
    recipient_type VARCHAR(50) DEFAULT 'admin',
    INDEX (type),
    INDEX (is_read),
    INDEX (recipient_id),
    INDEX (recipient_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
            </pre>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
            <a href="notifications.php" class="btn btn-primary">Go to Notifications</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
