<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';
header('Content-Type: application/json');

if (!$auth->hasRole('super_admin')) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

$db = new Database();
$conn = $db->getConnection();
$staffStats = getStaffStatistics($conn);

// Only return name and completion_rate for chart
$chartData = array_map(function($staff) {
    return [
        'name' => $staff['name'],
        'completion_rate' => $staff['completion_rate']
    ];
}, $staffStats);

echo json_encode($chartData); 