<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has permission to access this page
if (!$auth->hasRole('admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to perform this action.');
    Utilities::redirect('index.php');
}

// Check if session_access table exists
$tableExistsQuery = "SHOW TABLES LIKE 'session_access'";
$tableExistsResult = $conn->query($tableExistsQuery);
$sessionAccessTableExists = ($tableExistsResult->num_rows > 0);

if ($sessionAccessTableExists) {
    Utilities::setFlashMessage('info', 'The session_access table already exists.');
} else {
    // Create the session_access table
    $createTableQuery = "CREATE TABLE session_access (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        session_type VARCHAR(50) NOT NULL,
        access_level ENUM('none', 'view', 'full') NOT NULL DEFAULT 'none',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY (user_id, session_type),
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    )";
    
    if ($conn->query($createTableQuery)) {
        // Set default access levels for all users
        $usersQuery = "SELECT id, is_premium FROM users";
        $usersResult = $conn->query($usersQuery);
        
        if ($usersResult->num_rows > 0) {
            $insertCount = 0;
            
            while ($user = $usersResult->fetch_assoc()) {
                $userId = $user['id'];
                $isPremium = $user['is_premium'] ?? 0;
                
                // Default session access values
                $defaultSessions = [
                    'workout' => 'full',
                    'nutrition' => 'view',
                    'progress' => 'full',
                    'premium' => $isPremium ? 'full' : 'none'
                ];
                
                foreach ($defaultSessions as $sessionType => $accessLevel) {
                    $accessQuery = "INSERT INTO session_access (user_id, session_type, access_level)
                                   VALUES (?, ?, ?)";
                    $accessStmt = $conn->prepare($accessQuery);
                    $accessStmt->bind_param("iss", $userId, $sessionType, $accessLevel);
                    
                    if ($accessStmt->execute()) {
                        $insertCount++;
                    }
                }
            }
            
            Utilities::setFlashMessage('success', "Session_access table created successfully. Added default access levels for {$insertCount} user-session combinations.");
        } else {
            Utilities::setFlashMessage('success', "Session_access table created successfully. No users found to add default access levels.");
        }
    } else {
        Utilities::setFlashMessage('error', "Failed to create session_access table: " . $conn->error);
    }
}

// Redirect back to dashboard
Utilities::redirect('index.php');
?>
