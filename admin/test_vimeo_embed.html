<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vimeo Embed Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .video-container {
            margin: 20px 0;
            border: 1px solid #ddd;
            padding: 10px;
        }
        iframe {
            width: 100%;
            height: 400px;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Vimeo Video Embed Test</h1>
    
    <div class="error">
        <strong>Current Issue:</strong> Video has embed privacy set to "whitelist" but the whitelist is empty, blocking all domains.
    </div>

    <h2>Test 1: Private URL with Hash</h2>
    <div class="video-container">
        <p><strong>URL:</strong> https://player.vimeo.com/video/1087487482?h=ae75b6e329</p>
        <iframe src="https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1" 
                frameborder="0" 
                allow="autoplay; fullscreen; picture-in-picture" 
                allowfullscreen>
        </iframe>
    </div>

    <h2>Test 2: Public URL (without hash)</h2>
    <div class="video-container">
        <p><strong>URL:</strong> https://player.vimeo.com/video/1087487482</p>
        <iframe src="https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1" 
                frameborder="0" 
                allow="autoplay; fullscreen; picture-in-picture" 
                allowfullscreen>
        </iframe>
    </div>

    <h2>Solutions to Fix This Issue:</h2>
    <div class="success">
        <h3>Option 1: Change Embed Privacy to "Public" (Recommended)</h3>
        <ol>
            <li>Go to your Vimeo video settings</li>
            <li>Navigate to Privacy → Embed</li>
            <li>Change from "Specific domains" to "Anywhere"</li>
        </ol>
    </div>

    <div class="success">
        <h3>Option 2: Add Domains to Whitelist</h3>
        <ol>
            <li>Keep "Specific domains" selected</li>
            <li>Add these domains to the whitelist:</li>
            <ul>
                <li>***************:8080</li>
                <li>localhost:8080</li>
                <li>com.kft.fitness (for Flutter app)</li>
                <li>*.kft.fitness (if you have a domain)</li>
            </ul>
        </ol>
    </div>

    <script>
        // Test if videos load and show results
        setTimeout(() => {
            const iframes = document.querySelectorAll('iframe');
            iframes.forEach((iframe, index) => {
                iframe.onload = () => {
                    console.log(`Video ${index + 1} loaded successfully`);
                };
                iframe.onerror = () => {
                    console.log(`Video ${index + 1} failed to load`);
                };
            });
        }, 1000);
    </script>
</body>
</html>
