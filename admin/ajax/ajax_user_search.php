<?php
require_once '../includes/config.php';
require_once '../includes/database.php';

header('Content-Type: application/json');

$q = isset($_GET['q']) ? trim($_GET['q']) : '';
$results = [];

if ($q !== '') {
    $db = new Database();
    $conn = $db->getConnection();
    $qLike = "%$q%";
    $sql = "SELECT id, name, username, phone_number FROM users WHERE id != 1 AND NOT EXISTS (SELECT 1 FROM admin_users WHERE admin_users.username = users.username) AND (name LIKE ? OR username LIKE ? OR phone_number LIKE ?) ORDER BY name ASC LIMIT 20";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param('sss', $qLike, $qLike, $qLike);
    $stmt->execute();
    $res = $stmt->get_result();
    while ($row = $res->fetch_assoc()) {
        $display = htmlspecialchars($row['name']);
        if (!empty($row['username'])) {
            $display .= ' (' . htmlspecialchars($row['username']) . ')';
        }
        if (!empty($row['phone_number'])) {
            $display .= ' - ' . htmlspecialchars($row['phone_number']);
        }
        $results[] = [
            'id' => $row['id'],
            'text' => $display
        ];
    }
}

echo json_encode(['results' => $results]); 