<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vimeo Video Test</title>
    <script src="https://player.vimeo.com/api/player.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #000;
            color: white;
            font-family: Arial, sans-serif;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .video-container {
            width: 100%;
            height: 450px;
            margin: 20px 0;
            background: #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        .test-info {
            background: #222;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4a8a4a;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #8a4a4a;
        }
        .warning {
            background: #5a5a2d;
            border: 1px solid #8a8a4a;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .log {
            background: #111;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎥 Vimeo Video Test</h1>
        
        <div class="test-info">
            <h3>Test Configuration</h3>
            <p><strong>Video ID:</strong> 1087487482</p>
            <p><strong>Private Hash:</strong> ae75b6e329</p>
            <p><strong>Domain:</strong> localhost (admin dashboard)</p>
        </div>

        <div class="test-result" id="test-result">
            <strong>Status:</strong> Testing...
        </div>

        <div class="video-container">
            <iframe id="vimeo-player"
                    src="https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&controls=1"
                    frameborder="0"
                    allow="autoplay; fullscreen; picture-in-picture"
                    allowfullscreen>
            </iframe>
        </div>

        <div>
            <button onclick="testBasicEmbed()">Test Basic Embed</button>
            <button onclick="testWithoutHash()">Test Without Hash</button>
            <button onclick="testWithAppParams()">Test With App Params</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>

        <div class="test-info">
            <h3>Test Log</h3>
            <div class="log" id="log"></div>
        </div>
    </div>

    <script>
        let player;
        let testResults = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateTestResult(status, message, type = 'warning') {
            const resultElement = document.getElementById('test-result');
            resultElement.className = `test-result ${type}`;
            resultElement.innerHTML = `<strong>Status:</strong> ${status}<br><small>${message}</small>`;
        }

        function initializePlayer() {
            try {
                const iframe = document.getElementById('vimeo-player');
                player = new Vimeo.Player(iframe);

                player.on('loaded', function() {
                    log('✅ Player loaded successfully');
                    updateTestResult('Player Loaded', 'Vimeo player initialized', 'success');
                });

                player.on('error', function(error) {
                    log(`❌ Player error: ${error.name} - ${error.message}`);
                    updateTestResult('Player Error', error.message, 'error');
                });

                // Test getting duration (this will fail if domain restrictions apply)
                player.getDuration().then(function(duration) {
                    log(`✅ Duration retrieved: ${duration} seconds`);
                    updateTestResult('Success', `Video duration: ${duration}s - No domain restrictions detected`, 'success');
                }).catch(function(error) {
                    log(`❌ Failed to get duration: ${error.name} - ${error.message}`);
                    updateTestResult('Privacy Error', 'Video has domain restrictions that prevent playback', 'error');
                });

                // Test other player methods
                player.getVideoTitle().then(function(title) {
                    log(`📹 Video title: ${title}`);
                }).catch(function(error) {
                    log(`⚠️ Could not get title: ${error.message}`);
                });

            } catch (error) {
                log(`❌ Player initialization failed: ${error.message}`);
                updateTestResult('Initialization Failed', error.message, 'error');
            }
        }

        function testBasicEmbed() {
            log('🔄 Testing basic embed...');
            const iframe = document.getElementById('vimeo-player');
            iframe.src = 'https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&controls=1';
            setTimeout(initializePlayer, 2000);
        }

        function testWithoutHash() {
            log('🔄 Testing without private hash...');
            const iframe = document.getElementById('vimeo-player');
            iframe.src = 'https://player.vimeo.com/video/1087487482?autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&controls=1';
            setTimeout(initializePlayer, 2000);
        }

        function testWithAppParams() {
            log('🔄 Testing with app parameters...');
            const iframe = document.getElementById('vimeo-player');
            iframe.src = 'https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1&controls=1&sharing=0&download=0&fullscreen=1&app=1&referrer=app%3A%2F%2Fcom.kft.fitness';
            setTimeout(initializePlayer, 2000);
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            log('🚀 Starting Vimeo video test...');
            setTimeout(initializePlayer, 1000);
        });
    </script>
</body>
</html>
