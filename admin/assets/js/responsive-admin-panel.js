/**
 * Responsive Admin Panel JavaScript
 * Handles responsive navigation and sidebar behavior
 */

class ResponsiveAdminPanel {
  constructor() {
    // DOM elements
    this.layout = document.getElementById('adminLayout');
    this.sidebar = document.getElementById('adminSidebar');
    this.overlay = document.getElementById('sidebarOverlay');
    this.navToggle = document.getElementById('navToggle');
    this.sidebarClose = document.getElementById('sidebarClose');
    this.content = document.querySelector('.admin-content');

    // State
    this.isOpen = false;
    this.isMobile = false;
    this.isTablet = false;
    this.isDesktop = false;

    // Initialize
    this.init();
  }

  init() {
    this.updateDeviceType();
    this.setupEventListeners();
    this.setupKeyboardNavigation();
    this.restoreState();
    this.handleInitialState();
  }

  updateDeviceType() {
    const width = window.innerWidth;
    this.isMobile = width <= 767.98;
    this.isTablet = width >= 768 && width <= 991.98;
    this.isDesktop = width >= 992;
  }

  setupEventListeners() {
    // Navigation toggle button
    if (this.navToggle) {
      this.navToggle.addEventListener('click', (e) => {
        e.preventDefault();
        this.toggleSidebar();
      });
    }

    // Sidebar close button
    if (this.sidebarClose) {
      this.sidebarClose.addEventListener('click', (e) => {
        e.preventDefault();
        this.closeSidebar();
      });
    }

    // Overlay click
    if (this.overlay) {
      this.overlay.addEventListener('click', () => {
        this.closeSidebar();
      });
    }

    // Window resize
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // Navigation links (close sidebar on mobile after navigation)
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
      link.addEventListener('click', () => {
        if (this.isMobile || this.isTablet) {
          setTimeout(() => {
            this.closeSidebar();
          }, 150);
        }
      });
    });

    // Prevent body scroll when sidebar is open on mobile
    document.addEventListener('touchmove', (e) => {
      if (this.isOpen && (this.isMobile || this.isTablet)) {
        if (!this.sidebar.contains(e.target)) {
          e.preventDefault();
        }
      }
    }, { passive: false });
  }

  setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
      // ESC key to close sidebar
      if (e.key === 'Escape' && this.isOpen) {
        this.closeSidebar();
      }

      // Alt + S to toggle sidebar
      if (e.altKey && e.key === 's') {
        e.preventDefault();
        this.toggleSidebar();
      }

      // Tab navigation within sidebar
      if (e.key === 'Tab' && this.isOpen && (this.isMobile || this.isTablet)) {
        this.handleTabNavigation(e);
      }
    });
  }

  handleTabNavigation(e) {
    const focusableElements = this.sidebar.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (e.shiftKey) {
      if (document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    }
  }

  toggleSidebar() {
    if (this.isOpen) {
      this.closeSidebar();
    } else {
      this.openSidebar();
    }
  }

  openSidebar() {
    if (!this.layout || !this.sidebar) return;

    this.isOpen = true;
    this.layout.classList.add('sidebar-open');
    this.sidebar.classList.add('show');

    if (this.overlay && (this.isMobile || this.isTablet)) {
      this.overlay.classList.add('show');
    }

    if (this.navToggle) {
      this.navToggle.classList.add('active');
      this.navToggle.setAttribute('aria-expanded', 'true');
    }

    // Focus management
    if (this.isMobile || this.isTablet) {
      const firstFocusable = this.sidebar.querySelector('button, [href]');
      if (firstFocusable) {
        setTimeout(() => firstFocusable.focus(), 100);
      }
    }

    // Save state
    this.saveState();

    // Add haptic feedback on mobile
    if (this.isMobile && 'vibrate' in navigator) {
      navigator.vibrate(50);
    }
  }

  closeSidebar() {
    if (!this.layout || !this.sidebar) return;

    this.isOpen = false;
    this.layout.classList.remove('sidebar-open');
    this.sidebar.classList.remove('show');

    if (this.overlay) {
      this.overlay.classList.remove('show');
    }

    if (this.navToggle) {
      this.navToggle.classList.remove('active');
      this.navToggle.setAttribute('aria-expanded', 'false');
    }

    // Save state
    this.saveState();
  }

  handleResize() {
    const wasDesktop = this.isDesktop;
    this.updateDeviceType();

    // If switching from mobile/tablet to desktop, ensure sidebar is properly positioned
    if (!wasDesktop && this.isDesktop) {
      this.closeSidebar();
      this.layout.classList.remove('sidebar-open');
      this.sidebar.classList.remove('show');
      if (this.overlay) {
        this.overlay.classList.remove('show');
      }
    }

    // If switching from desktop to mobile/tablet, close sidebar
    if (wasDesktop && !this.isDesktop && this.isOpen) {
      this.closeSidebar();
    }
  }

  handleInitialState() {
    // On desktop, sidebar should be visible by default
    if (this.isDesktop) {
      this.isOpen = false; // Desktop sidebar is always visible, not "open" in overlay sense
      if (this.navToggle) {
        this.navToggle.setAttribute('aria-expanded', 'false');
      }
    } else {
      // On mobile/tablet, sidebar should be closed by default
      this.closeSidebar();
    }
  }

  saveState() {
    if (typeof localStorage !== 'undefined') {
      localStorage.setItem('adminSidebarOpen', this.isOpen.toString());
    }
  }

  restoreState() {
    if (typeof localStorage !== 'undefined') {
      const savedState = localStorage.getItem('adminSidebarOpen');
      if (savedState === 'true' && (this.isMobile || this.isTablet)) {
        // Only restore open state on mobile/tablet
        setTimeout(() => this.openSidebar(), 100);
      }
    }
  }

  // Public methods
  isCollapsed() {
    return !this.isOpen;
  }

  addLoadingState(element) {
    if (element) {
      element.classList.add('loading');
      setTimeout(() => {
        element.classList.remove('loading');
      }, 500);
    }
  }

  // Utility method to add smooth transitions
  addSmoothTransition(element, property, duration = 300) {
    if (element) {
      element.style.transition = `${property} ${duration}ms ease`;
      setTimeout(() => {
        element.style.transition = '';
      }, duration);
    }
  }

  // Method to handle navigation item clicks with analytics
  handleNavClick(navItem, url) {
    this.addLoadingState(navItem);

    // Add analytics if available
    if (typeof gtag !== 'undefined') {
      gtag('event', 'navigation_click', {
        'page_title': navItem.textContent.trim(),
        'page_location': url
      });
    }

    // Close sidebar on mobile after navigation
    if (this.isMobile || this.isTablet) {
      setTimeout(() => {
        this.closeSidebar();
      }, 150);
    }
  }
}

// Initialize the admin panel
document.addEventListener('DOMContentLoaded', () => {
  window.adminPanel = new ResponsiveAdminPanel();

  // Add smooth scroll behavior
  document.documentElement.style.scrollBehavior = 'smooth';

  // Initialize tooltips if Bootstrap is available
  if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
    });
  }
});

// Handle page unload
window.addEventListener('beforeunload', () => {
  if (window.adminPanel) {
    window.adminPanel.saveState();
  }
});

// Handle page visibility change (for mobile battery optimization)
document.addEventListener('visibilitychange', () => {
  if (document.hidden && window.adminPanel && window.adminPanel.isOpen) {
    // Close sidebar when page becomes hidden on mobile to save battery
    if (window.adminPanel.isMobile) {
      window.adminPanel.closeSidebar();
    }
  }
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ResponsiveAdminPanel;
}

