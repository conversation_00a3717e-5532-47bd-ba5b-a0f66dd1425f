/**
 * Enhanced Modern Action Menu
 * Refined JavaScript for smart positioning and subtle animations
 */

class ModernActionMenu {
    constructor() {
        // Track the currently open menu
        this.openMenu = null;

        // Track the position class of the open menu
        this.positionClass = null;

        // Initialize the action menus
        this.init();
    }

    init() {
        // Get all action buttons
        const actionButtons = document.querySelectorAll('.action-btn');

        // Add click handler to each button
        actionButtons.forEach(button => {
            button.addEventListener('click', this.handleButtonClick.bind(this));
        });

        // Close menu when clicking outside
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // Close menu on window resize or scroll
        window.addEventListener('resize', this.closeOpenMenu.bind(this));
        window.addEventListener('scroll', this.closeOpenMenu.bind(this));

        // Add keyboard navigation
        document.addEventListener('keydown', this.handleKeyDown.bind(this));

        // Add hover effect to menu items
        document.querySelectorAll('.action-menu-item').forEach(item => {
            item.addEventListener('mouseenter', () => {
                // Remove active class from all items
                document.querySelectorAll('.action-menu-item').forEach(i => {
                    i.classList.remove('active');
                });

                // Add active class to hovered item
                item.classList.add('active');
            });
        });
    }

    handleButtonClick(event) {
        // Prevent default behavior
        event.preventDefault();
        event.stopPropagation();

        // Get the action menu container and menu
        const container = event.currentTarget.closest('.action-menu-container');
        const menu = container ? container.querySelector('.action-menu') : null;

        if (!menu) return;

        // If this menu is already open, close it and return
        if (menu === this.openMenu) {
            this.closeOpenMenu();
            return;
        }

        // Close any open menu
        this.closeOpenMenu();

        // Position the menu
        this.positionMenu(event.currentTarget, menu);

        // Show the menu with a slight delay to ensure positioning is complete
        setTimeout(() => {
            menu.classList.add('show');

            // Track this as the open menu
            this.openMenu = menu;

            // Ensure the menu is clickable by setting a higher z-index
            menu.style.zIndex = '9999';

            // Add event listeners to menu items to ensure they're clickable
            const menuItems = menu.querySelectorAll('.action-menu-item');
            menuItems.forEach(item => {
                item.style.pointerEvents = 'auto';
            });
        }, 10);

        // Add ripple effect to button
        this.addRippleEffect(event.currentTarget, event);
    }

    addRippleEffect(button, event) {
        // Create ripple element
        const ripple = document.createElement('span');
        ripple.classList.add('ripple');

        // Position ripple
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = `${size}px`;
        ripple.style.left = `${x}px`;
        ripple.style.top = `${y}px`;

        // Add ripple to button
        button.appendChild(ripple);

        // Remove ripple after animation
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    positionMenu(button, menu) {
        // Get button position
        const buttonRect = button.getBoundingClientRect();

        // Get table and row information
        const row = button.closest('tr');
        const table = row ? row.closest('table') : null;

        // Determine position based on row location
        let position = 'bottom';

        if (table && row) {
            const tableRect = table.getBoundingClientRect();
            const rowRect = row.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const tableHeight = tableRect.height;

            // Calculate row position relative to table
            const rowTopFromTable = rowRect.top - tableRect.top;
            const rowBottomFromTable = rowRect.bottom - tableRect.top;

            // Check if row is in the bottom third of the table
            if (rowBottomFromTable > tableHeight * 0.7) {
                position = 'top';
            }
            // Check if row is in the middle third of the table
            else if (rowTopFromTable > tableHeight * 0.3 && rowBottomFromTable < tableHeight * 0.7) {
                // For middle rows, check if there's enough space on the right
                const spaceOnRight = window.innerWidth - buttonRect.right;
                const spaceOnLeft = buttonRect.left;

                // If there's more space on the right, use bottom position
                // This avoids the middle position which can have click issues
                position = (spaceOnRight > 250) ? 'bottom' : 'top';
            }
        }

        // Remove any existing position classes
        menu.classList.remove('position-top', 'position-middle', 'position-bottom');

        // Add the appropriate position class
        menu.classList.add(`position-${position}`);

        // Store the position class
        this.positionClass = `position-${position}`;

        // Show the menu to calculate its dimensions
        menu.style.display = 'block';
        menu.style.opacity = '0';
        menu.style.pointerEvents = 'auto'; // Ensure menu is clickable
        const menuRect = menu.getBoundingClientRect();

        // Calculate position
        let top, left;

        // For all positions, align to the right of the button
        // This avoids the middle position which can have click issues
        left = buttonRect.right - menuRect.width;

        // Ensure menu doesn't go off right edge
        if (left + menuRect.width > window.innerWidth - 10) {
            left = window.innerWidth - menuRect.width - 10;
        }

        // Ensure menu doesn't go off left edge
        if (left < 10) {
            left = 10;
        }

        // Vertical positioning based on determined position
        if (position === 'top') {
            // Position above button
            top = buttonRect.top - menuRect.height - 10;
        } else {
            // Position below button
            top = buttonRect.bottom + 10;
        }

        // Ensure menu doesn't go off top of screen
        if (top < 10) {
            top = 10;
        }

        // Ensure menu doesn't go off bottom of screen
        if (top + menuRect.height > window.innerHeight - 10) {
            top = window.innerHeight - menuRect.height - 10;
        }

        // Set menu position
        menu.style.top = `${top}px`;
        menu.style.left = `${left}px`;
        menu.style.opacity = '';

        // Set pointer-events to ensure menu is clickable
        menu.style.pointerEvents = 'auto';

        // Adjust arrow position based on menu position
        this.adjustArrowPosition(menu, buttonRect, position);
    }

    adjustArrowPosition(menu, buttonRect, position) {
        // Get the arrow element (pseudo-element)
        // We can't directly manipulate pseudo-elements, so we'll use CSS variables

        if (position === 'top') {
            // For top position, arrow should point down at the button
            const arrowLeft = buttonRect.left + (buttonRect.width / 2) - parseFloat(menu.style.left);
            menu.style.setProperty('--arrow-left', `${arrowLeft}px`);
        } else if (position === 'bottom') {
            // For bottom position, arrow should point up at the button
            const arrowLeft = buttonRect.left + (buttonRect.width / 2) - parseFloat(menu.style.left);
            menu.style.setProperty('--arrow-left', `${arrowLeft}px`);
        }
    }

    handleDocumentClick(event) {
        // Close the open menu if clicking outside
        if (this.openMenu && !event.target.closest('.action-menu-container')) {
            this.closeOpenMenu();
        }
    }

    handleKeyDown(event) {
        // If no menu is open, do nothing
        if (!this.openMenu) return;

        // Handle keyboard navigation
        switch (event.key) {
            case 'Escape':
                // Close menu on Escape
                this.closeOpenMenu();
                event.preventDefault();
                break;

            case 'ArrowDown':
                // Navigate to next item
                this.navigateMenu('next');
                event.preventDefault();
                break;

            case 'ArrowUp':
                // Navigate to previous item
                this.navigateMenu('prev');
                event.preventDefault();
                break;

            case 'Enter':
                // Activate current item
                const activeItem = this.openMenu.querySelector('.action-menu-item.active');
                if (activeItem) {
                    activeItem.click();
                }
                event.preventDefault();
                break;
        }
    }

    navigateMenu(direction) {
        // Get all menu items
        const items = Array.from(this.openMenu.querySelectorAll('.action-menu-item'));
        if (!items.length) return;

        // Get current active item
        const activeItem = this.openMenu.querySelector('.action-menu-item.active');
        let index = activeItem ? items.indexOf(activeItem) : -1;

        // Calculate new index
        if (direction === 'next') {
            index = (index + 1) % items.length;
        } else {
            index = (index - 1 + items.length) % items.length;
        }

        // Remove active class from all items
        items.forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to new item
        items[index].classList.add('active');

        // Scroll item into view if needed
        items[index].scrollIntoView({ block: 'nearest' });
    }

    closeOpenMenu() {
        // Close the open menu if any
        if (this.openMenu) {
            // Remove show class
            this.openMenu.classList.remove('show');

            // Reset styles that might interfere with future clicks
            this.openMenu.style.pointerEvents = '';
            this.openMenu.style.zIndex = '';
            this.openMenu.style.display = 'none';

            // Reset menu items
            const menuItems = this.openMenu.querySelectorAll('.action-menu-item');
            menuItems.forEach(item => {
                item.style.pointerEvents = '';
            });

            // Clear references
            this.openMenu = null;
            this.positionClass = null;
        }
    }
}

// Initialize the modern action menu when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernActionMenu();

    // Add ripple effect to action buttons
    document.head.insertAdjacentHTML('beforeend', `
        <style>
            .action-btn {
                position: relative;
                overflow: hidden;
            }

            .ripple {
                position: absolute;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s linear;
                pointer-events: none;
            }

            @keyframes ripple {
                to {
                    transform: scale(4);
                    opacity: 0;
                }
            }

            .action-menu-item.active {
                background-color: #f8f9fa;
            }
        </style>
    `);
});
