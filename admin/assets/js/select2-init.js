/**
 * Select2 Initialization for User Selection Dropdowns
 * 
 * This script initializes Select2 for all user selection dropdowns across the dashboard.
 * It targets any select element with ID 'user_id' or class 'user-select'.
 */
$(document).ready(function() {
    // Initialize Select2 for user selection dropdowns
    initUserSelect2();
    
    // Re-initialize Select2 when any modal with a user selection is shown
    $('.modal').on('shown.bs.modal', function() {
        initUserSelect2();
    });
    
    // --- NEW: AJAX Select2 for user search in users.php ---
    if ($('#user_search').length) {
        $('#user_search').select2({
            theme: 'bootstrap-5',
            width: '100%',
            placeholder: 'Type name, username or phone...',
            allowClear: true,
            minimumInputLength: 2,
            ajax: {
                url: 'ajax/ajax_user_search.php',
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return { q: params.term };
                },
                processResults: function (data) {
                    return { results: data.results };
                },
                cache: true
            },
            escapeMarkup: function(markup) { return markup; },
            templateResult: formatUserResult,
            templateSelection: formatUserSelection,
            dropdownParent: $('.users-header-search-wrapper')
        }).on('select2:select', function(e) {
            // Auto-submit the parent form on select
            $(this).closest('form').submit();
        });
    }
});

/**
 * Initialize Select2 for user selection dropdowns
 */
function initUserSelect2() {
    // Target all select elements with ID 'user_id' or class 'user-select'
    $('select#user_id, select.user-select').each(function() {
        // Check if Select2 is not already initialized
        if (!$(this).hasClass('select2-hidden-accessible')) {
            $(this).select2({
                theme: 'bootstrap-5',
                width: '100%',
                placeholder: 'Select a user',
                allowClear: true,
                dropdownParent: $(this).parent(),
                escapeMarkup: function(markup) {
                    return markup;
                },
                templateResult: formatUserResult,
                templateSelection: formatUserSelection
            });
        }
    });
}

/**
 * Format the dropdown results for users
 */
function formatUserResult(user) {
    if (!user.id) {
        return user.text;
    }
    
    // Extract user information from the option text
    // Expected format: "User Name (username) - phone"
    const userText = user.text;
    const userName = userText.split('(')[0].trim();
    
    let username = '';
    if (userText.includes('(') && userText.includes(')')) {
        username = userText.split('(')[1].split(')')[0].trim();
    }
    
    let phone = '';
    if (userText.includes('-') && userText.split('-').length > 1) {
        phone = userText.split('-')[1].trim();
    }
    
    // Create a formatted result with user avatar
    let result = `
        <div class="d-flex align-items-center">
            <div class="user-avatar-sm me-2" style="width: 32px; height: 32px; border-radius: 50%; background-color: #${getColorFromName(userName)}; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold; font-size: 14px;">
                ${userName.charAt(0).toUpperCase()}
            </div>
            <div>
                <div class="font-weight-medium">${userName}</div>
                ${username ? `<div class="small text-muted">${username}</div>` : ''}
                ${phone ? `<div class="small text-muted">${phone}</div>` : ''}
            </div>
        </div>
    `;
    
    return $(result);
}

/**
 * Format the selected user display
 */
function formatUserSelection(user) {
    if (!user.id) {
        return user.text;
    }
    
    // Extract user name from the option text
    const userName = user.text.split('(')[0].trim();
    
    return userName;
}

/**
 * Generate a consistent color from a name string
 */
function getColorFromName(name) {
    let hash = 0;
    for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    let color = '';
    for (let i = 0; i < 3; i++) {
        const value = (hash >> (i * 8)) & 0xFF;
        color += ('00' + value.toString(16)).substr(-2);
    }
    
    return color;
}
