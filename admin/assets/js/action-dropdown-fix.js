/**
 * Action Dropdown Fix
 * Targeted fix for action dropdowns in settings.php and staff_management.php
 */

document.addEventListener('DOMContentLoaded', function() {
    // Fix for action dropdowns
    const actionDropdowns = document.querySelectorAll('.action-dropdown-fix');
    
    actionDropdowns.forEach(function(dropdown) {
        const toggleButton = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggleButton && menu) {
            // Position the dropdown menu relative to the toggle button
            toggleButton.addEventListener('click', function(e) {
                // Get the button's position
                const buttonRect = toggleButton.getBoundingClientRect();
                
                // Calculate the menu position
                const menuTop = buttonRect.bottom + window.scrollY;
                const menuLeft = buttonRect.right - menu.offsetWidth + window.scrollX;
                
                // Set the menu position
                menu.style.top = menuTop + 'px';
                menu.style.left = Math.max(10, menuLeft) + 'px';
                
                // Ensure the menu doesn't go off the right edge of the screen
                const rightEdge = menuLeft + menu.offsetWidth;
                const windowWidth = window.innerWidth;
                
                if (rightEdge > windowWidth - 10) {
                    menu.style.left = (windowWidth - menu.offsetWidth - 10) + 'px';
                }
                
                // Toggle the menu visibility
                menu.classList.toggle('show');
                
                // Prevent the default dropdown behavior
                e.stopPropagation();
                e.preventDefault();
            });
            
            // Close the menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    menu.classList.remove('show');
                }
            });
            
            // Prevent menu from closing when clicking inside it
            menu.addEventListener('click', function(e) {
                if (e.target.classList.contains('dropdown-item') && !e.target.href) {
                    e.stopPropagation();
                }
            });
        }
    });
    
    // Fix for table overflow issues
    const tableResponsiveElements = document.querySelectorAll('.table-responsive');
    tableResponsiveElements.forEach(function(element) {
        element.style.overflow = 'visible';
    });
    
    // Fix for window resize
    window.addEventListener('resize', function() {
        document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
            menu.classList.remove('show');
        });
    });
});
