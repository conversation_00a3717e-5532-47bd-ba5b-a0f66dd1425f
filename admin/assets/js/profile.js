/**
 * KFT Fitness Admin Dashboard - Profile Page Scripts
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tab navigation
    initProfileTabs();
    
    // Initialize avatar upload
    initAvatarUpload();
    
    // Initialize form validation
    initFormValidation();
    
    // Initialize password strength meter
    initPasswordStrength();
});

/**
 * Initialize profile tab navigation
 */
function initProfileTabs() {
    const tabLinks = document.querySelectorAll('.profile-nav-link');
    const tabContents = document.querySelectorAll('.profile-tab-content');
    
    // Set default active tab
    if (tabLinks.length > 0 && tabContents.length > 0) {
        // Check if there's a hash in the URL
        const hash = window.location.hash;
        let activeTabFound = false;
        
        if (hash) {
            const targetTab = document.querySelector(hash);
            if (targetTab) {
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.style.display = 'none';
                });
                
                // Remove active class from all tab links
                tabLinks.forEach(link => {
                    link.classList.remove('active');
                });
                
                // Show the target tab content
                targetTab.style.display = 'block';
                
                // Add active class to the corresponding tab link
                document.querySelector(`a[href="${hash}"]`).classList.add('active');
                
                activeTabFound = true;
            }
        }
        
        // If no active tab was found from the hash, activate the first tab
        if (!activeTabFound) {
            tabContents.forEach((content, index) => {
                if (index === 0) {
                    content.style.display = 'block';
                } else {
                    content.style.display = 'none';
                }
            });
            
            tabLinks[0].classList.add('active');
        }
        
        // Add click event listeners to tab links
        tabLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                const target = this.getAttribute('href');
                
                // Hide all tab contents
                tabContents.forEach(content => {
                    content.style.display = 'none';
                });
                
                // Remove active class from all tab links
                tabLinks.forEach(link => {
                    link.classList.remove('active');
                });
                
                // Show the target tab content
                document.querySelector(target).style.display = 'block';
                
                // Add active class to the clicked tab link
                this.classList.add('active');
                
                // Update the URL hash
                window.location.hash = target;
            });
        });
    }
}

/**
 * Initialize avatar upload functionality
 */
function initAvatarUpload() {
    const avatarUpload = document.getElementById('avatar-upload');
    const avatarInput = document.getElementById('avatar-input');
    const avatarPreview = document.getElementById('avatar-preview');
    const avatarForm = document.getElementById('avatar-form');
    
    if (avatarUpload && avatarInput && avatarPreview) {
        // Open file dialog when upload button is clicked
        avatarUpload.addEventListener('click', function() {
            avatarInput.click();
        });
        
        // Handle file selection
        avatarInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                
                // Check if the file is an image
                if (!file.type.match('image.*')) {
                    showToast('Please select an image file', 'error');
                    return;
                }
                
                // Check file size (max 2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showToast('Image size should be less than 2MB', 'error');
                    return;
                }
                
                // Preview the image
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.innerHTML = `<img src="${e.target.result}" alt="Avatar Preview" style="width: 100%; height: 100%; object-fit: cover;">`;
                    
                    // Submit the form to update the avatar
                    if (avatarForm) {
                        avatarForm.submit();
                    }
                };
                reader.readAsDataURL(file);
            }
        });
    }
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    const profileForm = document.getElementById('profile-form');
    const passwordForm = document.getElementById('password-form');
    
    if (profileForm) {
        profileForm.addEventListener('submit', function(e) {
            const nameInput = document.getElementById('name');
            const emailInput = document.getElementById('email');
            const phoneInput = document.getElementById('phone');
            
            let isValid = true;
            
            // Validate name
            if (!nameInput.value.trim()) {
                showInputError(nameInput, 'Name is required');
                isValid = false;
            } else {
                clearInputError(nameInput);
            }
            
            // Validate email
            if (!emailInput.value.trim()) {
                showInputError(emailInput, 'Email is required');
                isValid = false;
            } else if (!isValidEmail(emailInput.value.trim())) {
                showInputError(emailInput, 'Please enter a valid email address');
                isValid = false;
            } else {
                clearInputError(emailInput);
            }
            
            // Validate phone
            if (!phoneInput.value.trim()) {
                showInputError(phoneInput, 'Phone number is required');
                isValid = false;
            } else {
                clearInputError(phoneInput);
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    }
    
    if (passwordForm) {
        passwordForm.addEventListener('submit', function(e) {
            const currentPasswordInput = document.getElementById('current-password');
            const newPasswordInput = document.getElementById('new-password');
            const confirmPasswordInput = document.getElementById('confirm-password');
            
            let isValid = true;
            
            // Validate current password
            if (!currentPasswordInput.value) {
                showInputError(currentPasswordInput, 'Current password is required');
                isValid = false;
            } else {
                clearInputError(currentPasswordInput);
            }
            
            // Validate new password
            if (!newPasswordInput.value) {
                showInputError(newPasswordInput, 'New password is required');
                isValid = false;
            } else if (newPasswordInput.value.length < 8) {
                showInputError(newPasswordInput, 'Password must be at least 8 characters long');
                isValid = false;
            } else {
                clearInputError(newPasswordInput);
            }
            
            // Validate confirm password
            if (!confirmPasswordInput.value) {
                showInputError(confirmPasswordInput, 'Please confirm your password');
                isValid = false;
            } else if (confirmPasswordInput.value !== newPasswordInput.value) {
                showInputError(confirmPasswordInput, 'Passwords do not match');
                isValid = false;
            } else {
                clearInputError(confirmPasswordInput);
            }
            
            if (!isValid) {
                e.preventDefault();
            }
        });
    }
}

/**
 * Initialize password strength meter
 */
function initPasswordStrength() {
    const passwordInput = document.getElementById('new-password');
    const strengthMeter = document.getElementById('password-strength-meter');
    const strengthText = document.getElementById('password-strength-text');
    
    if (passwordInput && strengthMeter && strengthText) {
        passwordInput.addEventListener('input', function() {
            const strength = calculatePasswordStrength(this.value);
            
            // Update the strength meter
            strengthMeter.value = strength.score;
            strengthMeter.className = `password-strength-meter strength-${strength.score}`;
            
            // Update the strength text
            strengthText.textContent = strength.message;
            strengthText.className = `password-strength-text strength-${strength.score}`;
        });
    }
}

/**
 * Helper functions
 */

function showInputError(input, message) {
    const formGroup = input.closest('.profile-form-group');
    const errorElement = formGroup.querySelector('.profile-form-error');
    
    input.classList.add('is-invalid');
    
    if (errorElement) {
        errorElement.textContent = message;
    } else {
        const error = document.createElement('div');
        error.className = 'profile-form-error text-danger mt-1 small';
        error.textContent = message;
        formGroup.appendChild(error);
    }
}

function clearInputError(input) {
    const formGroup = input.closest('.profile-form-group');
    const errorElement = formGroup.querySelector('.profile-form-error');
    
    input.classList.remove('is-invalid');
    
    if (errorElement) {
        errorElement.textContent = '';
    }
}

function isValidEmail(email) {
    const re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    return re.test(String(email).toLowerCase());
}

function calculatePasswordStrength(password) {
    if (!password) {
        return { score: 0, message: 'No password' };
    }
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Complexity checks
    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) score++;
    if (/\d/.test(password)) score++;
    if (/[^a-zA-Z0-9]/.test(password)) score++;
    
    // Map score to message
    const messages = [
        'Very weak',
        'Weak',
        'Fair',
        'Good',
        'Strong'
    ];
    
    return {
        score: score,
        message: messages[score]
    };
}

function showToast(message, type = 'info') {
    // Check if toast container exists
    let toastContainer = document.querySelector('.toast-container');
    
    // Create toast container if it doesn't exist
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type === 'error' ? 'danger' : type}`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    toast.setAttribute('id', toastId);
    
    // Create toast content
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toast);
    
    // Initialize and show toast
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 5000
    });
    bsToast.show();
    
    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}
