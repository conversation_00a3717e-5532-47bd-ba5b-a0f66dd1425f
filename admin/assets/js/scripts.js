/**
 * KFT Fitness Admin Dashboard Scripts
 */

// Document ready function
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize collapsible sidebar menus
    initializeSidebarCollapse();

    // Highlight active sidebar item
    highlightActiveSidebarItem();

    // Initialize smooth navigation
    initializeSmoothNavigation();

    // Initialize datepickers if any
    initializeDatepickers();

    // Initialize any data tables
    initializeDataTables();
});

/**
 * Initialize collapsible sidebar menus
 */
function initializeSidebarCollapse() {
    // Check if Bootstrap's Collapse is available
    if (typeof bootstrap !== 'undefined' && typeof bootstrap.Collapse !== 'undefined') {
        // Get all collapsible menu toggles
        var collapsibleToggles = document.querySelectorAll('[data-bs-toggle="collapse"]');

        // Initialize each toggle
        collapsibleToggles.forEach(function(toggle) {
            // Get the target collapse element
            var targetId = toggle.getAttribute('href');
            if (targetId && targetId.startsWith('#')) {
                var target = document.querySelector(targetId);
                if (target) {
                    // Create a new Collapse instance
                    var collapse = new bootstrap.Collapse(target, {
                        toggle: false // Don't toggle on initialization
                    });

                    // Add click event listener
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        collapse.toggle();
                    });
                }
            }
        });
    }
}

/**
 * Highlight the active sidebar item based on current page
 */
function highlightActiveSidebarItem() {
    // Get current page filename
    var currentPage = window.location.pathname.split('/').pop();

    // Find sidebar links
    var sidebarLinks = document.querySelectorAll('.sidebar-menu-item');

    // Loop through links and add active class to matching href
    sidebarLinks.forEach(function(link) {
        var href = link.getAttribute('href');
        if (href === currentPage) {
            link.classList.add('active');

            // If this is a submenu item, expand its parent
            var submenu = link.closest('.sidebar-submenu');
            if (submenu) {
                var parentCollapse = submenu.closest('.collapse');
                if (parentCollapse) {
                    parentCollapse.classList.add('show');
                    var parentToggle = document.querySelector('[href="#' + parentCollapse.id + '"]');
                    if (parentToggle) {
                        parentToggle.classList.add('active');
                        parentToggle.setAttribute('aria-expanded', 'true');
                    }
                }
            }
        }
    });
}

/**
 * Initialize Bootstrap datepickers
 */
function initializeDatepickers() {
    // Check if datepicker elements exist and if the plugin is available
    if (typeof $.fn.datepicker !== 'undefined') {
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true
        });
    }
}

/**
 * Initialize DataTables
 */
function initializeDataTables() {
    // Check if DataTable elements exist and if the plugin is available
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.datatable').DataTable({
            responsive: true,
            pageLength: 10,
            lengthMenu: [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    }
}

/**
 * Create a Chart.js chart
 * @param {string} elementId - The ID of the canvas element
 * @param {string} type - Chart type (line, bar, pie, etc.)
 * @param {object} data - Chart data
 * @param {object} options - Chart options
 */
function createChart(elementId, type, data, options) {
    var ctx = document.getElementById(elementId).getContext('2d');
    return new Chart(ctx, {
        type: type,
        data: data,
        options: options
    });
}

/**
 * Format number with commas
 * @param {number} number - The number to format
 * @returns {string} Formatted number
 */
function formatNumber(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

/**
 * Format date
 * @param {string} dateString - Date string
 * @param {string} format - Output format (default: 'MM/DD/YYYY')
 * @returns {string} Formatted date
 */
function formatDate(dateString, format = 'MM/DD/YYYY') {
    var date = new Date(dateString);
    var month = ('0' + (date.getMonth() + 1)).slice(-2);
    var day = ('0' + date.getDate()).slice(-2);
    var year = date.getFullYear();

    format = format.replace('MM', month);
    format = format.replace('DD', day);
    format = format.replace('YYYY', year);

    return format;
}

/**
 * Show confirmation dialog
 * @param {string} message - Confirmation message
 * @param {function} callback - Function to call if confirmed
 */
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * Show loading spinner
 * @param {string} targetId - ID of element to show spinner in
 * @param {string} size - Size of spinner (sm, md, lg)
 */
function showSpinner(targetId, size = 'md') {
    var target = document.getElementById(targetId);
    if (target) {
        var spinner = document.createElement('div');
        spinner.className = 'spinner-border text-primary spinner-border-' + size;
        spinner.setAttribute('role', 'status');

        var span = document.createElement('span');
        span.className = 'visually-hidden';
        span.textContent = 'Loading...';

        spinner.appendChild(span);
        target.appendChild(spinner);
    }
}

/**
 * Hide loading spinner
 * @param {string} targetId - ID of element containing spinner
 */
function hideSpinner(targetId) {
    var target = document.getElementById(targetId);
    if (target) {
        var spinner = target.querySelector('.spinner-border');
        if (spinner) {
            spinner.remove();
        }
    }
}

/**
 * Initialize smooth navigation for sidebar links
 */
function initializeSmoothNavigation() {
    // Store scroll position before navigation
    window.addEventListener('beforeunload', function() {
        sessionStorage.setItem('scrollPosition', window.scrollY.toString());
    });

    // Restore scroll position after page load (if coming from same domain)
    var savedScrollPosition = sessionStorage.getItem('scrollPosition');
    if (savedScrollPosition && document.referrer.includes(window.location.hostname)) {
        window.addEventListener('load', function() {
            setTimeout(function() {
                window.scrollTo(0, parseInt(savedScrollPosition));
                sessionStorage.removeItem('scrollPosition');
            }, 100);
        });
    }

    // Handle sidebar navigation clicks
    var sidebarLinks = document.querySelectorAll('.sidebar-menu-item');
    sidebarLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            // Don't interfere with current page links or dropdown toggles
            if (this.classList.contains('active') || this.hasAttribute('data-bs-toggle')) {
                return;
            }

            var href = this.getAttribute('href');
            if (href && href !== '#' && !href.startsWith('javascript:')) {
                // Store current scroll position for potential restoration
                sessionStorage.setItem('navigationScrollPosition', window.scrollY.toString());

                // Add visual feedback classes
                this.classList.add('navigating');

                // Remove visual feedback after navigation starts
                setTimeout(function() {
                    link.classList.remove('navigating');
                }, 300);

                // Allow normal navigation to proceed
                // The browser will handle the page change
            }
        });
    });
}

/**
 * Smooth scroll to element
 * @param {string} elementId - ID of element to scroll to
 * @param {number} offset - Offset from top (default: 0)
 */
function smoothScrollTo(elementId, offset = 0) {
    var element = document.getElementById(elementId);
    if (element) {
        var elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
        var offsetPosition = elementPosition - offset;

        window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
        });
    }
}

/**
 * Preserve scroll position during AJAX updates
 * @param {function} updateFunction - Function that updates the page content
 */
function preserveScrollDuringUpdate(updateFunction) {
    var currentScrollY = window.scrollY;

    updateFunction();

    // Restore scroll position after a short delay to allow content to render
    setTimeout(function() {
        window.scrollTo(0, currentScrollY);
    }, 50);
}
