/**
 * Dropdown Fix JavaScript
 * Ensures dropdowns are properly positioned and don't overflow
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all dropdowns with Bootstrap
    if (typeof bootstrap !== 'undefined') {
        // Enable all dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        dropdownElementList.forEach(function(dropdownToggleEl) {
            new bootstrap.Dropdown(dropdownToggleEl, {
                autoClose: 'outside',
                popperConfig: {
                    strategy: 'fixed',
                    modifiers: [
                        {
                            name: 'preventOverflow',
                            options: {
                                boundary: document.body,
                                padding: 8,
                                altAxis: true
                            }
                        },
                        {
                            name: 'flip',
                            options: {
                                fallbackPlacements: ['top', 'right', 'bottom', 'left'],
                                padding: 8
                            }
                        },
                        {
                            name: 'offset',
                            options: {
                                offset: [0, 4]
                            }
                        }
                    ]
                }
            });
        });

        // Add click handler to prevent immediate closing
        document.querySelectorAll('.dropdown-menu').forEach(function(menu) {
            menu.addEventListener('click', function(e) {
                // Only prevent default for clicks on dropdown items that aren't links
                if (e.target.classList.contains('dropdown-item') && !e.target.href) {
                    e.stopPropagation();
                }
            });
        });

        // Fix dropdown positioning on window resize
        window.addEventListener('resize', function() {
            document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                menu.classList.remove('show');
            });
        });
    }

    // Fix for table overflow issues
    const tableResponsiveElements = document.querySelectorAll('.table-responsive');
    tableResponsiveElements.forEach(function(element) {
        element.style.overflow = 'visible';
    });

    // Fix for action cells
    const actionsCells = document.querySelectorAll('.actions-cell');
    actionsCells.forEach(function(cell) {
        cell.style.position = 'relative';
        cell.style.zIndex = '10';
    });

    // Fix for dropdown menus in tables
    const tableDropdownMenus = document.querySelectorAll('.table .dropdown-menu');
    tableDropdownMenus.forEach(function(menu) {
        menu.classList.add('dropdown-menu-end');
    });
});
