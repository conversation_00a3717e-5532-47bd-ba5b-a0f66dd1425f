/**
 * Simple Updates JavaScript for Admin Dashboard
 * Handles refresh-based data synchronization (no real-time connections)
 */

class RealTimeUpdates {
    constructor() {
        this.listeners = new Map();
        this.lastEventId = 0;

        // No automatic connections - only manual refresh
        console.log('📋 Admin dashboard initialized (refresh-based updates)');
    }

    // No connection methods needed - refresh-based updates only

    // Simple notification system - no automatic updates
    showDataUpdateNotification(message = 'Data has been updated. Refresh to see changes.') {
        this.showUpdateNotification(message, 'info', 3000);
    }

    // No connection status needed for refresh-based updates

    showUpdateNotification(message, type = 'info') {
        // Create toast notification
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            top: 60px;
            right: 10px;
            padding: 12px 16px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 9998;
            max-width: 300px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        `;

        switch (type) {
            case 'success':
                toast.style.backgroundColor = '#d4edda';
                toast.style.color = '#155724';
                toast.style.border = '1px solid #c3e6cb';
                break;
            case 'error':
                toast.style.backgroundColor = '#f8d7da';
                toast.style.color = '#721c24';
                toast.style.border = '1px solid #f5c6cb';
                break;
            default:
                toast.style.backgroundColor = '#d1ecf1';
                toast.style.color = '#0c5460';
                toast.style.border = '1px solid #bee5eb';
        }

        toast.textContent = message;
        document.body.appendChild(toast);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            toast.style.opacity = '0';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    showLoadingIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'loading-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            padding: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            z-index: 9999;
            text-align: center;
        `;
        indicator.innerHTML = `
            <div style="margin-bottom: 10px;">
                <div style="border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto;"></div>
            </div>
            <div>Updating data...</div>
        `;

        // Add CSS animation
        if (!document.getElementById('loading-animation-style')) {
            const style = document.createElement('style');
            style.id = 'loading-animation-style';
            style.textContent = `
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(indicator);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 5000);
    }

    // Public methods for external use
    addEventListener(eventType, callback) {
        this.listeners.set(eventType, callback);
    }

    removeEventListener(eventType) {
        this.listeners.delete(eventType);
    }

    // Always return false since we don't maintain connections
    isConnectedToUpdates() {
        return false;
    }
}

// Initialize real-time updates when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.realTimeUpdates = new RealTimeUpdates();
});
