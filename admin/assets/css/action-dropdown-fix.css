/*
 * Action Dropdown Fix
 * Targeted fix for action dropdowns in settings.php and staff_management.php
 */

/* Critical fix for action dropdowns */
.action-dropdown-fix {
    position: static !important;
}

.action-dropdown-fix .dropdown-menu {
    position: fixed !important;
    top: auto !important;
    left: auto !important;
    right: auto !important;
    transform: none !important;
    margin-top: 2px !important;
    z-index: 9999 !important;
    max-height: 80vh !important;
    overflow-y: auto !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Fix for settings.php action dropdowns */
.settings-action-dropdown .dropdown-menu {
    min-width: 220px !important;
    max-width: 300px !important;
}

/* Fix for staff_management.php action dropdowns */
.staff-action-dropdown .dropdown-menu {
    min-width: 220px !important;
    max-width: 300px !important;
}

/* Fix for users.php action dropdowns */
.users-action-dropdown .dropdown-menu {
    min-width: 180px !important;
    max-width: 250px !important;
}

/* Fix for table overflow issues */
.table-responsive {
    overflow: visible !important;
}

/* Fix for action cells */
.actions-cell {
    position: static !important;
}

/* Fix for dropdown toggle buttons */
.action-btn {
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for dropdown menu positioning */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* Ensure dropdown menu is visible when shown */
.dropdown-menu.show {
    display: block !important;
}

/* Media queries for responsive behavior */
@media (max-width: 768px) {
    .action-dropdown-fix .dropdown-menu {
        position: fixed !important;
        top: auto !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 90% !important;
        max-width: 300px !important;
    }
}
