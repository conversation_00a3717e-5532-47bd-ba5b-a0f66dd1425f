/*
 * Dropdown Fix for Settings Page
 * Ensures dropdowns remain within the parent container
 */

/* Fix for dropdown overflow in tables */
.admin-table-container {
    position: relative !important;
    overflow: visible !important;
    z-index: 1 !important;
}

/* Ensure the table has proper overflow handling */
.table-responsive {
    overflow-x: auto !important;
    position: relative !important;
    z-index: 1 !important;
}

/* Fix for action button dropdown */
.actions-cell {
    position: relative !important;
}

/* Position the dropdown properly */
.actions-cell .dropdown {
    position: relative !important;
}

/* Ensure dropdown menu appears above other elements */
.actions-cell .dropdown-menu {
    position: absolute !important;
    right: 0 !important;
    left: auto !important;
    top: 100% !important;
    transform: none !important;
    z-index: 1050 !important;
    max-width: 250px !important;
    margin-top: 0.5rem !important;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15) !important;
    overflow: visible !important;
    min-width: 200px !important;
}

/* Ensure dropdown menu is contained within viewport */
.dropdown-menu {
    max-height: calc(100vh - 200px) !important;
    overflow-y: auto !important;
}

/* Ensure dropdown is properly positioned */
.dropdown-action {
    position: relative !important;
}

/* Fix for dropdown menu positioning */
.dropdown-menu-end {
    right: 0 !important;
    left: auto !important;
}

/* For smaller screens, adjust dropdown position */
@media (max-width: 768px) {
    .actions-cell .dropdown-menu {
        right: 0 !important;
        left: auto !important;
        max-width: 200px !important;
    }
}

/* Fix for action button */
.action-btn {
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure dropdown doesn't get cut off by table boundaries */
.pixel-perfect-table {
    overflow: visible !important;
}

/* Ensure dropdown menu is visible */
.dropdown-menu.show {
    display: block !important;
    z-index: 1050 !important;
}

/* Fix for dropdown positioning in admin tables */
.admin-table {
    position: relative !important;
    z-index: 1 !important;
}

/* Ensure dropdown menu doesn't overflow table container */
.admin-table .dropdown-menu {
    position: absolute !important;
    inset: 0px auto auto 0px !important;
    margin: 0px !important;
    transform: translate3d(0px, 38px, 0px) !important;
}

/* Fix for dropdown toggle button */
.dropdown-toggle::after {
    display: none !important;
}

/* Fix for dropdown menu positioning */
.dropdown-menu-end {
    --bs-position: end !important;
}

/* Fix for modal z-index to ensure dropdowns appear above it */
.modal-backdrop {
    z-index: 1040 !important;
}

.modal {
    z-index: 1045 !important;
}

/* Fix for dropdown in modal */
.modal .dropdown-menu {
    z-index: 1046 !important;
}
