/* Stats Grid Layout */
.stats-grid {
    display: grid;
    gap: 1rem;
    width: 100%;
}

/* Mobile First Design */
@media (max-width: 767px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-card.minimalist-card {
        padding: 1rem;
    }

    .minimalist-icon {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .minimalist-label {
        font-size: 0.8rem;
    }

    .minimalist-value {
        font-size: 1.2rem;
    }
}

/* Tablet Design */
@media (min-width: 768px) and (max-width: 991px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Design */
@media (min-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* Card Styles */
.dashboard-card.minimalist-card {
    background: #fff;
    border: 1px solid #eee;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.dashboard-card.minimalist-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.minimalist-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f9fa;
    color: #111;
    font-size: 1.25rem;
}

.minimalist-label {
    color: #666;
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.minimalist-value {
    color: #111;
    font-size: 1.5rem;
    font-weight: 600;
    line-height: 1.2;
} 