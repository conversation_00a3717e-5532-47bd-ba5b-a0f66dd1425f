/* 
 * Admin Forms CSS
 * Enhanced styling for admin forms with improved user experience
 */

:root {
    /* Form-specific variables */
    --form-bg: #fff;
    --form-border-radius: 12px;
    --form-shadow: 0 8px 30px rgba(0,0,0,0.08);
    --form-hover-shadow: 0 12px 40px rgba(0,0,0,0.12);
    --form-transition: all 0.3s ease;
    
    /* Input variables */
    --input-bg: #f9fafb;
    --input-border: #e5e7eb;
    --input-focus-border: #111;
    --input-focus-shadow: 0 0 0 4px rgba(0,0,0,0.1);
    
    /* Section variables */
    --section-header-bg: #f3f4f6;
    --section-border: #edf2f7;
    
    /* Button variables */
    --btn-primary-bg: #111;
    --btn-primary-hover-bg: #000;
    --btn-secondary-bg: #888;
    --btn-secondary-hover-bg: #333;
    
    /* Text variables */
    --text-primary: #111;
    --text-secondary: #888;
    --text-muted: #bbb;
    --text-hint: #888;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
}

/* Main form container */
.admin-form-container {
    max-width: 1200px;
    margin: 0 auto;
}

/* Form card styling */
.admin-form-card {
    background-color: var(--form-bg);
    border-radius: var(--form-border-radius);
    box-shadow: var(--form-shadow);
    transition: var(--form-transition);
    border: none;
    overflow: hidden;
}

.admin-form-card:hover {
    box-shadow: var(--form-hover-shadow);
}

/* Form header */
.admin-form-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--section-border);
    background: #111;
    color: #fff;
}

.admin-form-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.admin-form-header h5 i {
    margin-right: var(--spacing-sm);
}

/* Form sections */
.admin-form-section {
    margin-bottom: var(--spacing-lg);
    border-radius: var(--form-border-radius);
    overflow: hidden;
    border: 1px solid var(--section-border);
}

.admin-form-section-header {
    padding: var(--spacing-md);
    background-color: var(--section-header-bg);
    border-bottom: 1px solid var(--section-border);
    display: flex;
    align-items: center;
}

.admin-form-section-header h5 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.1rem;
    color: var(--text-primary);
}

.admin-form-section-header i {
    margin-right: var(--spacing-sm);
    color: var(--btn-primary-bg);
}

.admin-form-section-body {
    padding: var(--spacing-lg);
    background-color: var(--form-bg);
}

/* Form inputs */
.admin-form-control {
    background-color: var(--input-bg);
    border: 1px solid var(--input-border);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: var(--form-transition);
}

.admin-form-control:focus {
    border-color: var(--input-focus-border);
    box-shadow: var(--input-focus-shadow);
    background-color: white;
}

.admin-form-label {
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.admin-form-hint {
    font-size: 0.85rem;
    color: var(--text-hint);
    margin-top: var(--spacing-xs);
}

/* Form floating labels */
.form-floating > .admin-form-control {
    padding-top: 1.625rem;
    padding-bottom: 0.625rem;
}

.form-floating > label {
    padding: 1rem;
}

/* Permission checkboxes */
.permission-category {
    margin-bottom: var(--spacing-lg);
}

.permission-category-title {
    font-weight: 600;
    color: var(--text-primary);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--section-border);
    margin-bottom: var(--spacing-md);
}

.permission-item {
    padding: var(--spacing-sm);
    border-radius: 8px;
    transition: var(--form-transition);
}

.permission-item:hover {
    background-color: var(--input-bg);
}

.permission-item .form-check-label {
    font-weight: 500;
    color: var(--text-primary);
}

.permission-item .form-check-input:checked {
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-bg);
}

/* Buttons */
.admin-btn {
    padding: 0.6rem 1.5rem;
    font-weight: 500;
    border-radius: 8px;
    transition: var(--form-transition);
}

.admin-btn-primary {
    background-color: var(--btn-primary-bg);
    border-color: var(--btn-primary-bg);
    color: #fff;
}

.admin-btn-primary:hover {
    background-color: var(--btn-primary-hover-bg);
    border-color: var(--btn-primary-hover-bg);
    color: #fff;
    transform: translateY(-2px);
}

.admin-btn-secondary {
    background-color: var(--btn-secondary-bg);
    border-color: var(--btn-secondary-bg);
    color: #fff;
}

.admin-btn-secondary:hover {
    background-color: var(--btn-secondary-hover-bg);
    border-color: var(--btn-secondary-hover-bg);
    color: #fff;
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .admin-form-section-body {
        padding: var(--spacing-md);
    }
    
    .admin-form-header {
        padding: var(--spacing-md);
    }
}

/* Tooltip enhancements */
.admin-tooltip {
    position: relative;
    display: inline-block;
    cursor: help;
}

.admin-tooltip .admin-tooltip-text {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.admin-tooltip:hover .admin-tooltip-text {
    visibility: visible;
    opacity: 1;
}
