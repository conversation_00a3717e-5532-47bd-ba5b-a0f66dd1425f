/* New Dashboard Styles inspired by the vacation tracking dashboard */

/* Material Design Blue Dashboard Theme */
:root {
  --sidebar-bg: #3d5afe;
  --sidebar-text: #fff;
  --card-bg: #fff;
  --card-shadow: 0 4px 12px rgba(0,0,0,0.08);
  --primary-color: #3d5afe;
  --secondary-color: #888;
  --success-color: #111;
  --danger-color: #444;
  --text-primary: #111;
  --text-secondary: #666;
  --border-color: #e0e0e0;
  --hover-bg: #f5f5f5;
}

/* Main Layout */
body, #page-content-wrapper {
  background-color: #fff;
  color: var(--text-primary);
  font-family: 'Inter', sans-serif;
}

#page-content-wrapper {
  background-color: #f5f7fa;
}

/* Sidebar */
#sidebar-wrapper {
  background-color: var(--sidebar-bg) !important;
  color: var(--sidebar-text) !important;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

#sidebar-wrapper .sidebar-heading {
  color: var(--sidebar-text) !important;
  font-weight: 600;
  padding: 1.5rem 1.25rem;
}

.sidebar-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  margin-right: 12px;
  background-color: #222 !important;
  color: #fff !important;
}

.list-group-item-action {
  color: #fff !important;
  border: none;
  padding: 0.75rem 1.25rem;
  margin: 4px 8px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  background: none !important;
}

.list-group-item-action.active {
  background-color: #222 !important;
  color: #fff !important;
}

/* Dashboard Cards */
.dashboard-card {
  background-color: var(--card-bg) !important;
  border-radius: 12px;
  border: 1px solid var(--border-color) !important;
  box-shadow: var(--card-shadow);
  padding: 1.5rem;
  height: 100%;
  transition: transform 0.2s, box-shadow 0.2s;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0,0,0,0.12);
}

.dashboard-card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  background: #f0f0f0 !important;
  color: #111 !important;
  font-size: 1.25rem;
}

.dashboard-card-title {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  color: var(--text-secondary) !important;
  margin-bottom: 0.5rem;
}

.dashboard-card-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary) !important;
  margin-bottom: 0.5rem;
}

.dashboard-card-trend {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: #111 !important;
}

.trend-up, .trend-down {
  color: #111 !important;
}

.dashboard-card-footer {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #eee !important;
  font-size: 0.875rem;
}

/* Progress bar styles */
.progress {
  background-color: #eee !important;
  overflow: hidden;
  border-radius: 10px;
}

.progress-bar {
  background-color: #111 !important;
}

/* Refresh button animation */
@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.fa-sync-alt.spinning {
  animation: spin 1s linear infinite;
}

/* Status Indicators */
.status-indicator, .status-approved, .status-pending, .status-rejected {
  background-color: #111 !important;
}

/* Tables */
.table {
  color: var(--text-primary) !important;
}

.table th {
  font-weight: 600;
  color: var(--text-secondary) !important;
  border-top: none;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 0.75rem 1rem;
}

.table td {
  padding: 1rem;
  vertical-align: middle;
  border-color: var(--border-color) !important;
}

.table-hover tbody tr:hover {
  background-color: #f5f5f5 !important;
}

/* Charts */
.card-header {
  background-color: transparent !important;
  border-bottom: 1px solid var(--border-color) !important;
  padding: 1.25rem 1.5rem;
}

.chart-container {
  padding: 1rem;
  height: 300px;
}

/* Buttons */
.btn-primary, .btn-outline-primary {
  background-color: #111 !important;
  border-color: #111 !important;
  color: #fff !important;
}

.btn-primary:hover, .btn-primary:focus, .btn-outline-primary:hover {
  background-color: #333 !important;
  border-color: #333 !important;
  color: #fff !important;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-card {
    margin-bottom: 1rem;
  }
}
