/* Settings Page Styles */
:root {
    --primary-color: #111;
    --primary-hover: #000;
    --secondary-color: #888;
    --success-color: #111;
    --danger-color: #000;
    --warning-color: #333;
    --info-color: #222;
    --light-color: #fff;
    --dark-color: #000;
    --border-color: #ccc;
    --card-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
    --hover-shadow: 0 0.5rem 1rem rgba(0,0,0,0.15);
    --transition-speed: 0.2s;
}

/* Page Header */
.settings-header {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-header h1 {
    margin-bottom: 0;
    font-weight: 600;
    font-size: 1.75rem;
    color: var(--dark-color);
}

.settings-header-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: #fff;
    border-radius: 10px;
    margin-right: 1rem;
    font-size: 1.25rem;
}

/* Settings Navigation */
.settings-nav {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.settings-nav-tabs {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    border-bottom: 1px solid var(--border-color);
    overflow-x: auto;
    white-space: nowrap;
}

.settings-nav-item {
    margin-bottom: -1px;
}

.settings-nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.25rem;
    color: var(--secondary-color);
    text-decoration: none;
    border-bottom: 2px solid transparent;
    transition: all var(--transition-speed) ease;
}

.settings-nav-link:hover {
    color: var(--primary-color);
    background-color: #eee;
}

.settings-nav-link.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    font-weight: 500;
}

.settings-nav-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* Settings Content */
.settings-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    margin-bottom: 1.5rem;
}

.settings-section {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.settings-section:last-child {
    border-bottom: none;
}

.settings-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.25rem;
}

.settings-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.settings-section-icon {
    color: var(--primary-color);
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.settings-section-actions {
    display: flex;
    gap: 0.5rem;
}

/* Admin Users Table */
.admin-table-container {
    margin-top: 1rem;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.admin-table {
    width: 100%;
    margin-bottom: 0;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: var(--secondary-color);
    padding: 0.75rem 1rem;
    border-top: none;
    white-space: nowrap;
}

.admin-table td {
    padding: 1rem;
    vertical-align: middle;
}

.admin-table tbody tr {
    transition: background-color var(--transition-speed) ease;
}

.admin-table tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.03);
}

/* Role Badges */
.role-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.65rem;
    font-size: 0.75rem;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 50rem;
}

.role-badge i {
    margin-right: 0.35rem;
}

.role-badge-filled {
    background: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(39, 174, 96, 0.08);
}
.role-badge-filled i {
    color: #fff !important;
}

/* Search and Filter */
.settings-search-container {
    position: relative;
    max-width: 300px;
}

.settings-search-input {
    padding-left: 2.5rem;
    border-radius: 50rem;
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) ease;
}

.settings-search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.settings-search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--secondary-color);
    pointer-events: none;
}

/* Buttons */
.btn-settings {
    border-radius: 0.375rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    transition: all var(--transition-speed) ease;
}

.btn-settings-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-settings-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    color: #fff;
}

.btn-settings-outline {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--secondary-color);
}

.btn-settings-outline:hover {
    background-color: var(--light-color);
    color: var(--dark-color);
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .settings-section {
        padding: 1.25rem;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 0.75rem;
    }
    
    .settings-nav-link {
        padding: 0.75rem 1rem;
    }
}

/* Pixel Perfect Table Redesign */
.pixel-perfect-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background: #fff;
    box-shadow: 0 2px 12px rgba(0,0,0,0.04);
    border-radius: 12px;
    overflow: hidden;
}
.pixel-perfect-table th, .pixel-perfect-table td {
    padding: 1.1rem 1.25rem;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
    font-size: 1rem;
    background: #fff;
}
.pixel-perfect-table th {
    font-weight: 700;
    color: #111;
    background: #fafafa;
    border-top: none;
    letter-spacing: 0.02em;
    font-size: 0.95rem;
}
.pixel-perfect-table tbody tr:hover {
    background: #f5f5f5;
    transition: background 0.2s;
}
.pixel-avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background: #111;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.pixel-role-badge {
    background: #fff;
    color: #111;
    border: 1px solid #ccc;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    padding: 0.35rem 0.85rem;
    display: inline-flex;
    align-items: center;
    gap: 0.4em;
    box-shadow: none;
}
.pixel-role-badge i {
    margin-right: 0.3em;
    font-size: 0.95em;
}
.pixel-username {
    font-size: 1.08rem;
    font-weight: 700;
    color: #111;
    letter-spacing: 0.01em;
}
.pixel-action-btn {
    background: #fff;
    color: #111;
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: 0 1px 4px rgba(0,0,0,0.06);
    transition: background 0.15s, color 0.15s;
}
.pixel-action-btn:hover, .pixel-action-btn:focus {
    background: #eee;
    color: #000;
    outline: none;
}
.pixel-perfect-table .actions-cell {
    min-width: 44px;
    text-align: center;
}
.pixel-perfect-table .dropdown-menu {
    min-width: 180px;
    border-radius: 10px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.10);
    border: 1px solid #eee;
    background: #fff;
    padding: 0.5rem 0;
}
.pixel-perfect-table .dropdown-item {
    color: #111;
    font-size: 0.97rem;
    padding: 0.7rem 1.2rem;
    border-radius: 6px;
    transition: background 0.15s;
}
.pixel-perfect-table .dropdown-item:hover {
    background: #f5f5f5;
    color: #000;
}
.pixel-perfect-table .dropdown-divider {
    background: #eee;
    margin: 0.3rem 0;
}
@media (max-width: 991.98px) {
    .pixel-perfect-table th, .pixel-perfect-table td {
        padding: 0.7rem 0.6rem;
        font-size: 0.95rem;
    }
    .pixel-avatar {
        width: 36px;
        height: 36px;
        font-size: 1rem;
    }
}
