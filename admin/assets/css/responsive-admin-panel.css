/* Enhanced Responsive Admin Panel with Native Mobile Experience */

:root {
  /* Responsive Breakpoints */
  --mobile-max: 767.98px;
  --tablet-min: 768px;
  --tablet-max: 991.98px;
  --desktop-min: 992px;

  /* Colors */
  --topbar-bg: #ffffff;
  --topbar-text: #333333;
  --topbar-border: #e9ecef;

  /* Animation Variables */
  --transition-fast: 0.2s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.4s ease;

  /* Touch Targets */
  --touch-target-min: 44px;
}

/* Main Layout Structure */
.admin-layout {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  margin: 0;
  padding: 0;
  position: relative;
}

/* Fix negative margins and spacing issues */
* {
  box-sizing: border-box;
}

body {
  margin: 0 !important;
  padding: 0 !important;
  overflow-x: hidden;
}

/* Sidebar Navigation - Material Design Blue */
.admin-sidebar {
  width: 280px;
  min-height: 100vh;
  background: #3d5afe !important;
  background: linear-gradient(135deg, #3d5afe 0%, #2962ff 100%) !important;
  color: white;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  transform: translateX(-100%);
  transition: transform var(--transition-normal);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.admin-sidebar.show {
  transform: translateX(0);
}

/* Sidebar Header */
.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.25rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  text-decoration: none;
}

.brand-text {
  margin-left: 0.5rem;
}

.sidebar-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.25rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  min-width: var(--touch-target-min);
  min-height: var(--touch-target-min);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Sidebar Navigation */
.sidebar-nav {
  padding: 1rem 0;
}

.nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  margin: 0.25rem 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.875rem 1.25rem;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all var(--transition-fast);
  border-radius: 0;
  min-height: var(--touch-target-min);
  position: relative;
}

.nav-link:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.1);
  text-decoration: none;
}

.nav-link.active {
  color: white;
  background-color: rgba(255, 255, 255, 0.15);
  border-right: 3px solid white;
}

.nav-icon {
  width: 1.25rem;
  text-align: center;
  margin-right: 0.75rem;
  font-size: 1rem;
}

.nav-text {
  font-weight: 500;
}

.nav-divider {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 1rem;
  padding-top: 1rem;
}

/* Sidebar Overlay */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1040;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
}

.sidebar-overlay.show {
  opacity: 1;
  visibility: visible;
}

/* Main Content Area */
.admin-content {
  flex: 1;
  width: 100%;
  padding: 1rem;
  transition: all var(--transition-normal);
  margin-left: 0;
}

/* Top Navigation Bar */
.admin-topbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: var(--topbar-bg);
  border-bottom: 1px solid var(--topbar-border);
  margin-bottom: 1rem;
  position: sticky;
  top: 0;
  z-index: 1030;
}

.topbar-left {
  display: flex;
  align-items: center;
}

/* Navigation Toggle Button (Hamburger) */
.nav-toggle {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: var(--touch-target-min);
  height: var(--touch-target-min);
  border-radius: 0.375rem;
  transition: background-color var(--transition-fast);
  position: relative;
}

.nav-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.nav-toggle:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(13, 110, 253, 0.25);
}

.hamburger-line {
  width: 20px;
  height: 2px;
  background-color: #333;
  margin: 2px 0;
  transition: all var(--transition-fast);
  border-radius: 1px;
}

/* Hamburger Animation */
.nav-toggle.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active .hamburger-line:nth-child(2) {
  opacity: 0;
}

.nav-toggle.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

.topbar-user {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background-color: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

/* Desktop Layout (992px+) */
@media (min-width: 992px) {
  .admin-sidebar {
    position: fixed;
    transform: translateX(0);
    box-shadow: none;
    border-right: 1px solid var(--topbar-border);
    width: 280px;
    height: 100vh;
  }

  .admin-content {
    margin-left: 280px;
    width: calc(100% - 280px);
    padding: 0;
  }

  .admin-content .container-fluid {
    padding: 1.5rem;
    max-width: 100%;
  }

  .nav-toggle {
    display: none;
  }

  .sidebar-overlay {
    display: none;
  }

  .sidebar-close {
    display: none;
  }
}

/* Tablet Layout (768px - 991px) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .admin-sidebar {
    width: 260px;
  }

  .admin-content {
    padding: 0.875rem;
  }

  .admin-topbar {
    padding: 0.875rem;
  }

  /* Show sidebar when toggled */
  .admin-layout.sidebar-open .admin-sidebar {
    transform: translateX(0);
  }

  .admin-layout.sidebar-open .sidebar-overlay {
    opacity: 1;
    visibility: visible;
  }
}

/* Mobile Layout (< 768px) */
@media (max-width: 767.98px) {
  .admin-sidebar {
    width: 100%;
    max-width: 320px;
  }

  .admin-content {
    padding: 0.5rem;
    margin-left: 0;
    width: 100%;
  }

  .admin-topbar {
    padding: 0.75rem;
  }

  .topbar-user span {
    display: none !important;
  }

  /* Full overlay on mobile */
  .admin-layout.sidebar-open .admin-sidebar {
    transform: translateX(0);
  }

  .admin-layout.sidebar-open .sidebar-overlay {
    opacity: 1;
    visibility: visible;
  }

  /* Adjust navigation for mobile */
  .nav-link {
    padding: 1rem 1.25rem;
    font-size: 1rem;
  }

  .nav-icon {
    font-size: 1.125rem;
    margin-right: 1rem;
  }

  /* Larger touch targets on mobile */
  .sidebar-close {
    min-width: 48px;
    min-height: 48px;
  }

  .nav-toggle {
    min-width: 48px;
    min-height: 48px;
  }
}

/* Extra small devices */
@media (max-width: 575.98px) {
  .admin-content {
    padding: 0.25rem;
  }

  .admin-topbar {
    padding: 0.5rem;
  }

  .container-fluid {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }
}

/* Enhanced Animation Classes */
.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

/* Keyframe Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Enhanced Animation Classes */
.slide-in-left {
  animation: slideInLeft 0.3s ease-out;
}

.slide-out-left {
  animation: slideOutLeft 0.3s ease-out;
}

/* Keyframe Animations */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

/* Loading state animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Accessibility */
.nav-link:focus {
  outline: 2px solid #007bff;
  outline-offset: -2px;
}

/* Integration with existing dashboard styles and spacing fixes */
.admin-content .container-fluid {
  padding-top: 0;
  margin: 0;
  max-width: 100%;
}

/* Fix all negative margins globally */
.admin-content * {
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* Restore necessary margins for specific elements */
.admin-content .me-1 { margin-right: 0.25rem !important; }
.admin-content .me-2 { margin-right: 0.5rem !important; }
.admin-content .me-3 { margin-right: 1rem !important; }
.admin-content .ms-1 { margin-left: 0.25rem !important; }
.admin-content .ms-2 { margin-left: 0.5rem !important; }
.admin-content .ms-3 { margin-left: 1rem !important; }
.admin-content .mb-1 { margin-bottom: 0.25rem !important; }
.admin-content .mb-2 { margin-bottom: 0.5rem !important; }
.admin-content .mb-3 { margin-bottom: 1rem !important; }
.admin-content .mb-4 { margin-bottom: 1.5rem !important; }
.admin-content .mt-1 { margin-top: 0.25rem !important; }
.admin-content .mt-2 { margin-top: 0.5rem !important; }
.admin-content .mt-3 { margin-top: 1rem !important; }
.admin-content .mt-4 { margin-top: 1.5rem !important; }

.admin-content .card {
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  margin-bottom: 1rem;
}

.admin-content .btn-primary {
  background-color: #007bff;
  border-color: #007bff;
}

.admin-content .btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

/* Ensure compatibility with existing dashboard cards */
.dashboard-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1rem;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
}

/* Fix for flash messages positioning */
.alert {
  margin-bottom: 1rem !important;
  border-radius: 0.5rem;
}

/* Fix breadcrumb spacing */
.breadcrumb {
  margin-bottom: 1rem !important;
  padding: 0.75rem 0;
}

/* Fix table spacing */
.table-responsive {
  margin-bottom: 1rem;
}

/* Fix form spacing */
.form-group,
.mb-3 {
  margin-bottom: 1rem !important;
}

/* Responsive table improvements */
@media (max-width: 767.98px) {
  .table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
  }

  .admin-content .container-fluid {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* Print Styles */
@media print {
  .admin-sidebar,
  .admin-topbar {
    display: none;
  }

  .admin-content {
    margin-left: 0;
  }
}

/* Animation Classes */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* Loading States */
.loading {
  position: relative;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  z-index: 1;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus States for Accessibility */
.nav-link:focus,
.nav-toggle:focus,
.sidebar-close:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .admin-sidebar {
    background: #000;
    border-right: 2px solid #fff;
  }

  .nav-link {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .nav-link.active {
    background-color: #fff;
    color: #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .admin-sidebar,
  .sidebar-overlay,
  .nav-toggle,
  .hamburger-line,
  .nav-link {
    transition: none !important;
    animation: none !important;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .admin-topbar {
    background-color: #1a1a1a;
    border-bottom-color: #333;
    color: #fff;
  }

  .hamburger-line {
    background-color: #fff;
  }

  .nav-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
}

/* Print Styles */
@media print {
  .admin-sidebar,
  .sidebar-overlay,
  .nav-toggle {
    display: none !important;
  }

  .admin-content {
    margin-left: 0 !important;
    width: 100% !important;
  }
}

/* Remove mobile bottom navigation styles */
.mobile-bottom-nav,
.bottom-nav-container,
.bottom-nav-item,
.bottom-nav-icon,
.bottom-nav-label {
  display: none !important;
}
