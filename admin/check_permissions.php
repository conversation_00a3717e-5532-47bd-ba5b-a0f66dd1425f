<?php
/**
 * Check Permissions
 *
 * This script checks the permissions in the database and allows adding missing permissions.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this tool.');
}

// Get all permissions from the database
$query = "SELECT * FROM admin_permissions ORDER BY name";
$result = $conn->query($query);
$permissions = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $permissions[] = $row;
    }
}

// Define required permissions
$requiredPermissions = [
    [
        'slug' => 'view_notifications',
        'name' => 'View Notifications',
        'description' => 'Allows staff to view notifications in the admin panel'
    ],
    [
        'slug' => 'manage_users',
        'name' => 'Manage Users',
        'description' => 'Allows staff to manage users'
    ],
    [
        'slug' => 'view_users',
        'name' => 'View Users',
        'description' => 'Allows staff to view users'
    ],
    [
        'slug' => 'manage_courses',
        'name' => 'Manage Courses',
        'description' => 'Allows staff to manage courses'
    ],
    [
        'slug' => 'view_courses',
        'name' => 'View Courses',
        'description' => 'Allows staff to view courses'
    ],
    [
        'slug' => 'manage_workouts',
        'name' => 'Manage Workouts',
        'description' => 'Allows staff to manage workouts'
    ],
    [
        'slug' => 'view_workouts',
        'name' => 'View Workouts',
        'description' => 'Allows staff to view workouts'
    ],
    [
        'slug' => 'view_reports',
        'name' => 'View Reports',
        'description' => 'Allows staff to view reports'
    ],
    [
        'slug' => 'chat_with_users',
        'name' => 'Chat with Users',
        'description' => 'Allows staff to chat with users'
    ]
];

// Process form submission
$message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add_permission') {
        $slug = $_POST['slug'] ?? '';
        $name = $_POST['name'] ?? '';
        $description = $_POST['description'] ?? '';
        
        if (empty($slug) || empty($name)) {
            $message = 'Slug and name are required.';
        } else {
            // Check if permission already exists
            $checkQuery = "SELECT id FROM admin_permissions WHERE slug = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("s", $slug);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $message = "Permission with slug '$slug' already exists.";
            } else {
                // Add the permission
                $insertQuery = "INSERT INTO admin_permissions (slug, name, description) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("sss", $slug, $name, $description);
                
                if ($stmt->execute()) {
                    $message = "Permission '$name' added successfully.";
                    
                    // Refresh permissions
                    $query = "SELECT * FROM admin_permissions ORDER BY name";
                    $result = $conn->query($query);
                    $permissions = [];
                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                            $permissions[] = $row;
                        }
                    }
                } else {
                    $message = "Failed to add permission: " . $stmt->error;
                }
            }
        }
    } elseif ($action === 'add_missing_permissions') {
        $addedCount = 0;
        $existingCount = 0;
        
        foreach ($requiredPermissions as $permission) {
            // Check if permission already exists
            $checkQuery = "SELECT id FROM admin_permissions WHERE slug = ?";
            $stmt = $conn->prepare($checkQuery);
            $stmt->bind_param("s", $permission['slug']);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                $existingCount++;
            } else {
                // Add the permission
                $insertQuery = "INSERT INTO admin_permissions (slug, name, description) VALUES (?, ?, ?)";
                $stmt = $conn->prepare($insertQuery);
                $stmt->bind_param("sss", $permission['slug'], $permission['name'], $permission['description']);
                
                if ($stmt->execute()) {
                    $addedCount++;
                }
            }
        }
        
        $message = "Added $addedCount missing permissions. $existingCount permissions already existed.";
        
        // Refresh permissions
        $query = "SELECT * FROM admin_permissions ORDER BY name";
        $result = $conn->query($query);
        $permissions = [];
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $permissions[] = $row;
            }
        }
    }
}

// Get all staff members
$staffQuery = "SELECT id, username, name FROM admin_users WHERE role = 'staff'";
$staffResult = $conn->query($staffQuery);
$staffMembers = [];
if ($staffResult && $staffResult->num_rows > 0) {
    while ($row = $staffResult->fetch_assoc()) {
        $staffMembers[] = $row;
    }
}

// Process staff permission update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_staff_permissions') {
    $staffId = (int)$_POST['staff_id'];
    $selectedPermissions = $_POST['permissions'] ?? [];
    
    // Delete existing permissions
    $deleteQuery = "DELETE FROM admin_user_permissions WHERE admin_user_id = ?";
    $stmt = $conn->prepare($deleteQuery);
    $stmt->bind_param("i", $staffId);
    $stmt->execute();
    
    // Add new permissions
    if (!empty($selectedPermissions)) {
        $insertQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
        $stmt = $conn->prepare($insertQuery);
        
        foreach ($selectedPermissions as $permissionId) {
            $stmt->bind_param("ii", $staffId, $permissionId);
            $stmt->execute();
        }
    }
    
    $message = "Permissions updated for staff member.";
}

// Get staff permissions if a staff member is selected
$staffPermissions = [];
$selectedStaff = null;
if (isset($_GET['staff_id']) && !empty($_GET['staff_id'])) {
    $staffId = (int)$_GET['staff_id'];
    
    // Get staff info
    $staffInfoQuery = "SELECT id, username, name FROM admin_users WHERE id = ? AND role = 'staff'";
    $stmt = $conn->prepare($staffInfoQuery);
    $stmt->bind_param("i", $staffId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $selectedStaff = $result->fetch_assoc();
        
        // Get staff permissions
        $permissionsQuery = "SELECT permission_id FROM admin_user_permissions WHERE admin_user_id = ?";
        $stmt = $conn->prepare($permissionsQuery);
        $stmt->bind_param("i", $staffId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $staffPermissions[] = $row['permission_id'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Permissions</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Check Permissions</h1>
        <p>This tool checks the permissions in the database and allows adding missing permissions.</p>
        
        <?php if (!empty($message)): ?>
        <div class="alert alert-info">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>Current Permissions</h2>
            <?php if (!empty($permissions)): ?>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Slug</th>
                        <th>Name</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($permissions as $permission): ?>
                    <tr>
                        <td><?php echo $permission['id']; ?></td>
                        <td><?php echo htmlspecialchars($permission['slug']); ?></td>
                        <td><?php echo htmlspecialchars($permission['name']); ?></td>
                        <td><?php echo htmlspecialchars($permission['description']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php else: ?>
            <div class="alert alert-warning">
                No permissions found in the database.
            </div>
            <?php endif; ?>
        </div>
        
        <div class="section">
            <h2>Add Missing Permissions</h2>
            <form method="post" action="">
                <input type="hidden" name="action" value="add_missing_permissions">
                <button type="submit" class="btn btn-primary">Add Missing Permissions</button>
            </form>
        </div>
        
        <div class="section">
            <h2>Add Custom Permission</h2>
            <form method="post" action="">
                <input type="hidden" name="action" value="add_permission">
                
                <div class="mb-3">
                    <label for="slug" class="form-label">Slug</label>
                    <input type="text" class="form-control" id="slug" name="slug" required>
                    <div class="form-text">Unique identifier for the permission (e.g., view_notifications)</div>
                </div>
                
                <div class="mb-3">
                    <label for="name" class="form-label">Name</label>
                    <input type="text" class="form-control" id="name" name="name" required>
                    <div class="form-text">Display name for the permission (e.g., View Notifications)</div>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">Description</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    <div class="form-text">Description of what this permission allows</div>
                </div>
                
                <button type="submit" class="btn btn-primary">Add Permission</button>
            </form>
        </div>
        
        <div class="section">
            <h2>Manage Staff Permissions</h2>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <h4>Select Staff Member</h4>
                    <form method="get" action="">
                        <div class="mb-3">
                            <select class="form-select" name="staff_id" onchange="this.form.submit()">
                                <option value="">-- Select Staff Member --</option>
                                <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>" <?php echo (isset($_GET['staff_id']) && $_GET['staff_id'] == $staff['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
            
            <?php if ($selectedStaff): ?>
            <h4>Update Permissions for <?php echo htmlspecialchars($selectedStaff['name']); ?></h4>
            <form method="post" action="">
                <input type="hidden" name="action" value="update_staff_permissions">
                <input type="hidden" name="staff_id" value="<?php echo $selectedStaff['id']; ?>">
                
                <div class="row">
                    <?php foreach ($permissions as $permission): ?>
                    <div class="col-md-4 mb-2">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="perm_<?php echo $permission['id']; ?>" 
                                   name="permissions[]" value="<?php echo $permission['id']; ?>"
                                   <?php echo in_array($permission['id'], $staffPermissions) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="perm_<?php echo $permission['id']; ?>">
                                <?php echo htmlspecialchars($permission['name']); ?>
                                <small class="d-block text-muted"><?php echo htmlspecialchars($permission['slug']); ?></small>
                            </label>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                
                <button type="submit" class="btn btn-primary mt-3">Update Permissions</button>
            </form>
            <?php endif; ?>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
