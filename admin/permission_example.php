<?php
require_once 'includes/header.php';

// Example of checking permission for a specific action
$canManageUsers = hasPermission('manage_users');
$canManageCourses = hasPermission('manage_courses');
$canViewReports = hasPermission('view_reports');

// Get all permissions for the current user
$userPermissions = getPermissionsArray();

// Example of checking permission before performing an action
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'delete_user':
                if (!hasPermission('manage_users')) {
                    Utilities::setFlashMessage('error', 'You do not have permission to delete users.');
                } else {
                    Utilities::setFlashMessage('success', 'User would be deleted (this is just a demo).');
                }
                break;
                
            case 'add_course':
                if (!hasPermission('manage_courses')) {
                    Utilities::setFlashMessage('error', 'You do not have permission to add courses.');
                } else {
                    Utilities::setFlashMessage('success', 'Course would be added (this is just a demo).');
                }
                break;
                
            case 'view_report':
                if (!hasPermission('view_reports')) {
                    Utilities::setFlashMessage('error', 'You do not have permission to view reports.');
                } else {
                    Utilities::setFlashMessage('success', 'Report would be displayed (this is just a demo).');
                }
                break;
        }
    }
}
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">Permission System Example</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
        <li class="breadcrumb-item active">Permission Example</li>
    </ol>
    
    <div class="row">
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-key me-1"></i>
                    Your Permissions
                </div>
                <div class="card-body">
                    <p>Current role: <strong><?php echo ucfirst($_SESSION['role']); ?></strong></p>
                    
                    <h5 class="mt-4">Permission Status:</h5>
                    <ul class="list-group mb-4">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Manage Users
                            <?php if ($canManageUsers): ?>
                                <span class="badge bg-success">Granted</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Denied</span>
                            <?php endif; ?>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Manage Courses
                            <?php if ($canManageCourses): ?>
                                <span class="badge bg-success">Granted</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Denied</span>
                            <?php endif; ?>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            View Reports
                            <?php if ($canViewReports): ?>
                                <span class="badge bg-success">Granted</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Denied</span>
                            <?php endif; ?>
                        </li>
                    </ul>
                    
                    <h5>All Your Permissions:</h5>
                    <?php if (empty($userPermissions)): ?>
                        <p class="text-muted">No specific permissions assigned.</p>
                    <?php else: ?>
                        <ul class="list-group">
                            <?php foreach ($userPermissions as $permission => $value): ?>
                                <li class="list-group-item">
                                    <?php echo htmlspecialchars($permission); ?>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-lock me-1"></i>
                    Test Permissions
                </div>
                <div class="card-body">
                    <p>Click the buttons below to test your permissions:</p>
                    
                    <form method="post" class="mb-3">
                        <input type="hidden" name="action" value="delete_user">
                        <button type="submit" class="btn btn-danger mb-3 w-100">
                            <i class="fas fa-user-minus me-2"></i> Delete User (requires 'manage_users' permission)
                        </button>
                    </form>
                    
                    <form method="post" class="mb-3">
                        <input type="hidden" name="action" value="add_course">
                        <button type="submit" class="btn btn-success mb-3 w-100">
                            <i class="fas fa-plus me-2"></i> Add Course (requires 'manage_courses' permission)
                        </button>
                    </form>
                    
                    <form method="post">
                        <input type="hidden" name="action" value="view_report">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-chart-bar me-2"></i> View Report (requires 'view_reports' permission)
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-code me-1"></i>
                    Code Example
                </div>
                <div class="card-body">
                    <p>Here's how to check permissions in your code:</p>
                    <pre><code>// Check if user has permission
if (hasPermission('manage_users')) {
    // User can manage users
    // Perform action...
} else {
    // User doesn't have permission
    // Show error or redirect...
}

// Or use the checkPagePermission function
checkPagePermission('manage_users');
// This will automatically redirect to unauthorized.php
// if the user doesn't have permission</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
