<?php
// Debug Vimeo API Connection
header('Content-Type: application/json');

$accessToken = 'e181f678c92844bd2be36504f316fabd';

function makeVimeoRequest($endpoint, $accessToken) {
    $url = "https://api.vimeo.com" . $endpoint;
    
    $headers = [
        "Authorization: Bearer {$accessToken}",
        "Content-Type: application/json",
        "Accept: application/vnd.vimeo.*+json;version=3.4"
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    return [
        'http_code' => $httpCode,
        'response' => $response ? json_decode($response, true) : null,
        'error' => $error,
        'url' => $url
    ];
}

echo "=== VIMEO API DEBUG REPORT ===\n\n";

// Test 1: Check if token is valid by getting user info
echo "1. Testing token validity...\n";
$userTest = makeVimeoRequest('/me', $accessToken);

if ($userTest['http_code'] === 200) {
    $user = $userTest['response'];
    echo "✅ Token is valid!\n";
    echo "   User: " . ($user['name'] ?? 'Unknown') . "\n";
    echo "   Account: " . ($user['account'] ?? 'Unknown') . "\n";
    echo "   Videos: " . ($user['metadata']['connections']['videos']['total'] ?? 0) . "\n\n";
} else {
    echo "❌ Token is invalid!\n";
    echo "   HTTP Code: " . $userTest['http_code'] . "\n";
    echo "   Error: " . json_encode($userTest['response']) . "\n\n";
    exit;
}

// Test 2: Get user's videos
echo "2. Getting your videos...\n";
$videosTest = makeVimeoRequest('/me/videos?per_page=10', $accessToken);

if ($videosTest['http_code'] === 200) {
    $videos = $videosTest['response']['data'] ?? [];
    echo "✅ Found " . count($videos) . " videos in your account:\n";
    
    foreach ($videos as $video) {
        $videoId = str_replace('/videos/', '', $video['uri']);
        echo "   - ID: {$videoId}\n";
        echo "     Title: " . ($video['name'] ?? 'Untitled') . "\n";
        echo "     Privacy: " . ($video['privacy']['view'] ?? 'unknown') . "\n";
        echo "     Status: " . ($video['status'] ?? 'unknown') . "\n";
        echo "     URL: https://vimeo.com/{$videoId}\n\n";
    }
} else {
    echo "❌ Could not get videos!\n";
    echo "   HTTP Code: " . $videosTest['http_code'] . "\n";
    echo "   Error: " . json_encode($videosTest['response']) . "\n\n";
}

// Test 3: Try to access the specific video
echo "3. Testing specific video (**********)...\n";
$specificVideoTest = makeVimeoRequest('/videos/**********', $accessToken);

if ($specificVideoTest['http_code'] === 200) {
    $video = $specificVideoTest['response'];
    echo "✅ Video found!\n";
    echo "   Title: " . ($video['name'] ?? 'Untitled') . "\n";
    echo "   Privacy View: " . ($video['privacy']['view'] ?? 'unknown') . "\n";
    echo "   Privacy Embed: " . ($video['privacy']['embed'] ?? 'unknown') . "\n";
    echo "   Status: " . ($video['status'] ?? 'unknown') . "\n";
    echo "   Owner: " . ($video['user']['name'] ?? 'unknown') . "\n\n";
} else {
    echo "❌ Video not accessible!\n";
    echo "   HTTP Code: " . $specificVideoTest['http_code'] . "\n";
    echo "   Error: " . json_encode($specificVideoTest['response']) . "\n";
    echo "   This means either:\n";
    echo "   - Video doesn't exist\n";
    echo "   - Video belongs to different account\n";
    echo "   - Video is private and token lacks permission\n\n";
}

// Test 4: Check token scopes
echo "4. Checking token scopes...\n";
$scopesTest = makeVimeoRequest('/oauth/verify', $accessToken);

if ($scopesTest['http_code'] === 200) {
    $scopes = $scopesTest['response']['scope'] ?? 'unknown';
    echo "✅ Token scopes: {$scopes}\n\n";
} else {
    echo "❌ Could not verify token scopes\n";
    echo "   HTTP Code: " . $scopesTest['http_code'] . "\n\n";
}

// Recommendations
echo "=== RECOMMENDATIONS ===\n";
echo "1. If video ********** is not in your account, you need to:\n";
echo "   - Upload the video to your Vimeo account\n";
echo "   - Or get access token from the video owner's account\n\n";

echo "2. If you want to use video **********:\n";
echo "   - Check if it exists: https://vimeo.com/**********\n";
echo "   - Make sure it's in the same account as your API app\n\n";

echo "3. To use one of your own videos:\n";
echo "   - Pick a video ID from the list above\n";
echo "   - Update your app to use that video ID instead\n\n";

echo "4. Required token scopes for private videos:\n";
echo "   - public (view public videos)\n";
echo "   - private (view private videos)\n";
echo "   - video_files (access video files)\n";
echo "   - interact (interact with videos)\n\n";
?>
