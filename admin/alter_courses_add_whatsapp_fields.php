<?php
// Run this script once to add WhatsApp-related columns to the 'courses' table
require_once __DIR__ . '/../api/config.php'; // Uses getDbConnection()

$conn = getDbConnection();

// Check if 'whatsapp_number' column exists
$checkWhatsappNumberColumn = $conn->query("SHOW COLUMNS FROM courses LIKE 'whatsapp_number'");
if ($checkWhatsappNumberColumn && $checkWhatsappNumberColumn->num_rows === 0) {
    $alterWhatsappNumber = $conn->query("ALTER TABLE courses ADD COLUMN whatsapp_number VARCHAR(20) DEFAULT NULL");
    if ($alterWhatsappNumber) {
        echo "Column 'whatsapp_number' added successfully.\n";
    } else {
        echo "Error adding column 'whatsapp_number': " . $conn->error . "\n";
    }
} else {
    echo "Column 'whatsapp_number' already exists.\n";
}

// Check if 'whatsapp_message_prefix' column exists
$checkWhatsappMessagePrefixColumn = $conn->query("SHOW COLUMNS FROM courses LIKE 'whatsapp_message_prefix'");
if ($checkWhatsappMessagePrefixColumn && $checkWhatsappMessagePrefixColumn->num_rows === 0) {
    $alterWhatsappMessagePrefix = $conn->query("ALTER TABLE courses ADD COLUMN whatsapp_message_prefix TEXT DEFAULT NULL");
    if ($alterWhatsappMessagePrefix) {
        echo "Column 'whatsapp_message_prefix' added successfully.\n";
    } else {
        echo "Error adding column 'whatsapp_message_prefix': " . $conn->error . "\n";
    }
} else {
    echo "Column 'whatsapp_message_prefix' already exists.\n";
}

$conn->close();
echo "Script completed.\n";
?>
