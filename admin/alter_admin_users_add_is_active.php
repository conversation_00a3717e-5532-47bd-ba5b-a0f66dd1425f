<?php
/**
 * Add is_active column to admin_users table
 * 
 * This script adds the is_active column to the admin_users table if it doesn't exist.
 */

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if the is_active column already exists
$checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'is_active'";
$checkColumnResult = $conn->query($checkColumnQuery);
$columnExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

if ($columnExists) {
    echo "The 'is_active' column already exists in the admin_users table.<br>";
} else {
    // Add the is_active column
    $alterTableQuery = "ALTER TABLE admin_users ADD COLUMN is_active TINYINT(1) NOT NULL DEFAULT 1 AFTER role";
    
    if ($conn->query($alterTableQuery)) {
        echo "Successfully added 'is_active' column to admin_users table.<br>";
        
        // Set all existing users to active
        $updateQuery = "UPDATE admin_users SET is_active = 1";
        if ($conn->query($updateQuery)) {
            echo "Successfully set all existing admin users to active.<br>";
        } else {
            echo "Error setting existing admin users to active: " . $conn->error . "<br>";
        }
    } else {
        echo "Error adding 'is_active' column: " . $conn->error . "<br>";
    }
}

// Add an index to the is_active column for better performance
$checkIndexQuery = "SHOW INDEX FROM admin_users WHERE Column_name = 'is_active'";
$checkIndexResult = $conn->query($checkIndexQuery);
$indexExists = ($checkIndexResult && $checkIndexResult->num_rows > 0);

if ($indexExists) {
    echo "The index on 'is_active' column already exists.<br>";
} else {
    $addIndexQuery = "ALTER TABLE admin_users ADD INDEX idx_is_active (is_active)";
    if ($conn->query($addIndexQuery)) {
        echo "Successfully added index on 'is_active' column.<br>";
    } else {
        echo "Error adding index on 'is_active' column: " . $conn->error . "<br>";
    }
}

echo "<br>Script completed. <a href='staff_management.php'>Return to Staff Management</a>";
?>
