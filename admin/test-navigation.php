<?php
/**
 * Test page for the responsive navigation button
 * This page demonstrates the new responsive navigation functionality
 */

// Include authentication and configuration
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize authentication
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Set page title
$pageTitle = 'Responsive Navigation Test';

// Include header
require_once 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bars me-2"></i>
                    Responsive Navigation Test
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Navigation Features</h6>
                    <ul class="mb-0">
                        <li><strong>Hamburger Menu:</strong> Click the ☰ button in the top-left to toggle navigation</li>
                        <li><strong>Responsive Design:</strong> Navigation adapts to screen size automatically</li>
                        <li><strong>Keyboard Support:</strong> Press Alt+S to toggle, ESC to close</li>
                        <li><strong>Touch Friendly:</strong> 44px minimum touch targets on mobile</li>
                        <li><strong>Accessibility:</strong> Full keyboard navigation and screen reader support</li>
                    </ul>
                </div>

                <div class="row g-4">
                    <div class="col-lg-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-desktop me-2"></i>
                                    Desktop (992px+)
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Sidebar always visible</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Fixed positioning</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Content adjusts automatically</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-tablet-alt me-2"></i>
                                    Tablet (768px-991px)
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Hidden by default</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Overlay when opened</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Click outside to close</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-mobile-alt me-2"></i>
                                    Mobile (<768px)
                                </h6>
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check text-success me-2"></i>Full-width overlay</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Touch-optimized</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-close after navigation</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Test Instructions:</h6>
                    <ol>
                        <li>Resize your browser window to test different breakpoints</li>
                        <li>Click the hamburger menu (☰) to toggle navigation</li>
                        <li>Try keyboard shortcuts: Alt+S (toggle), ESC (close)</li>
                        <li>On mobile, test touch interactions and overlay behavior</li>
                        <li>Navigate to different pages to test auto-close functionality</li>
                    </ol>
                </div>

                <div class="mt-4 p-3 bg-info bg-opacity-10 border border-info rounded">
                    <h6 class="text-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Current Screen Size
                    </h6>
                    <p class="mb-0">
                        <span id="screenSize">Loading...</span>
                        <br>
                        <small class="text-muted">Resize the window to see changes</small>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test script to show current screen size and device type
function updateScreenInfo() {
    const width = window.innerWidth;
    let device = 'Desktop';
    let color = 'success';
    
    if (width <= 767.98) {
        device = 'Mobile';
        color = 'primary';
    } else if (width <= 991.98) {
        device = 'Tablet';
        color = 'warning';
    }
    
    const screenSizeElement = document.getElementById('screenSize');
    if (screenSizeElement) {
        screenSizeElement.innerHTML = `
            <span class="badge bg-${color}">${device}</span>
            <strong>${width}px</strong> width
        `;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    updateScreenInfo();
    window.addEventListener('resize', updateScreenInfo);
    
    // Test navigation functionality
    console.log('Navigation test page loaded');
    console.log('Available shortcuts: Alt+S (toggle), ESC (close)');
});
</script>

<?php require_once 'includes/footer.php'; ?>
