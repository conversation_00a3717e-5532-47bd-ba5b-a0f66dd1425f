<?php
/**
 * Check Staff Account
 *
 * This script checks the staff account details and allows resetting the password.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this tool.');
}

// Define the email to check
$staffEmail = '<EMAIL>';
$message = '';
$staffInfo = null;

// Check if the staff account exists
$query = "SELECT * FROM admin_users WHERE email = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("s", $staffEmail);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $staffInfo = $result->fetch_assoc();
}

// Process password reset if requested
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_password') {
    $staffId = (int)$_POST['staff_id'];
    $newPassword = $_POST['new_password'];
    
    if (!empty($newPassword)) {
        // Hash the new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update the password in the database
        $updateQuery = "UPDATE admin_users SET password = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $hashedPassword, $staffId);
        
        if ($stmt->execute()) {
            $message = "Password for {$staffInfo['username']} has been reset successfully. You can now log in with the new password.";
            
            // Refresh staff info
            $stmt = $conn->prepare($query);
            $stmt->bind_param("s", $staffEmail);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $staffInfo = $result->fetch_assoc();
            }
        } else {
            $message = "Failed to reset password: " . $stmt->error;
        }
    } else {
        $message = "Please enter a new password.";
    }
}

// Process test login if requested
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_login') {
    $username = $_POST['username'];
    $password = $_POST['password'];
    
    // Test the login
    if ($auth->login($username, $password)) {
        $message = "Login successful for user: {$username}";
        // Logout immediately to avoid session conflicts
        $auth->logout();
    } else {
        $message = "Login failed for user: {$username}";
    }
}

// Check if the staff account can access the admin area
$canAccessAdmin = false;
if ($staffInfo) {
    $canAccessAdmin = ($staffInfo['role'] === 'admin' || $staffInfo['role'] === 'super_admin' || $staffInfo['role'] === 'staff');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Staff Account</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Check Staff Account</h1>
        <p>This tool checks the staff account details and allows resetting the password.</p>
        
        <?php if (!empty($message)): ?>
        <div class="alert alert-info">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>Staff Account Information</h2>
            <?php if ($staffInfo): ?>
            <table class="table table-bordered">
                <tr>
                    <th style="width: 150px;">ID</th>
                    <td><?php echo $staffInfo['id']; ?></td>
                </tr>
                <tr>
                    <th>Username</th>
                    <td><?php echo htmlspecialchars($staffInfo['username']); ?></td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td><?php echo htmlspecialchars($staffInfo['email']); ?></td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td><?php echo htmlspecialchars($staffInfo['name']); ?></td>
                </tr>
                <tr>
                    <th>Role</th>
                    <td><?php echo htmlspecialchars($staffInfo['role']); ?></td>
                </tr>
                <tr>
                    <th>Can Access Admin</th>
                    <td><?php echo $canAccessAdmin ? 'Yes' : 'No'; ?></td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td><?php echo $staffInfo['created_at']; ?></td>
                </tr>
                <tr>
                    <th>Last Login</th>
                    <td><?php echo $staffInfo['last_login'] ?: 'Never'; ?></td>
                </tr>
                <tr>
                    <th>Password Hash</th>
                    <td>
                        <div style="word-break: break-all;">
                            <?php echo $staffInfo['password']; ?>
                        </div>
                    </td>
                </tr>
            </table>
            <?php else: ?>
            <div class="alert alert-warning">
                Staff account with email <?php echo htmlspecialchars($staffEmail); ?> not found.
            </div>
            <?php endif; ?>
        </div>
        
        <?php if ($staffInfo): ?>
        <div class="section">
            <h2>Reset Password</h2>
            <form method="post" action="">
                <input type="hidden" name="action" value="reset_password">
                <input type="hidden" name="staff_id" value="<?php echo $staffInfo['id']; ?>">
                
                <div class="mb-3">
                    <label for="new_password" class="form-label">New Password</label>
                    <input type="text" class="form-control" id="new_password" name="new_password" value="Sadik999" required>
                    <div class="form-text">Enter the new password for the staff account.</div>
                </div>
                
                <button type="submit" class="btn btn-primary">Reset Password</button>
            </form>
        </div>
        
        <div class="section">
            <h2>Test Login</h2>
            <form method="post" action="">
                <input type="hidden" name="action" value="test_login">
                
                <div class="mb-3">
                    <label for="username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($staffInfo['username']); ?>" required>
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <input type="text" class="form-control" id="password" name="password" value="Sadik999" required>
                </div>
                
                <button type="submit" class="btn btn-success">Test Login</button>
            </form>
        </div>
        
        <div class="section">
            <h2>Authentication Debug</h2>
            <p>This section shows the authentication process for the staff account.</p>
            
            <h4>Auth::login() Method</h4>
            <pre style="background-color: #f1f1f1; padding: 10px; border-radius: 5px;">
// Login user
public function login($username, $password) {
    // First try admin_users table
    $stmt = $conn->prepare("SELECT id, username, password, role, name FROM admin_users WHERE username = ? LIMIT 1");
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 1) {
        $user = $result->fetch_assoc();

        // Verify password
        if (password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['name'] = $user['name'];
            $_SESSION['role'] = $user['role'];
            $_SESSION['is_admin'] = ($user['role'] === 'admin' || $user['role'] === 'super_admin') ? 1 : 0;
            $_SESSION['is_staff'] = ($user['role'] === 'staff') ? 1 : 0;
            $_SESSION['last_activity'] = time();

            return true;
        }
    }

    return false;
}
            </pre>
            
            <h4>Auth Check Process</h4>
            <pre style="background-color: #f1f1f1; padding: 10px; border-radius: 5px;">
// Check if user has admin or staff privileges
if ((!isset($_SESSION['is_admin']) || $_SESSION['is_admin'] != 1) && 
    (!isset($_SESSION['is_staff']) || $_SESSION['is_staff'] != 1)) {
    // Redirect to login page with error message
    $_SESSION['login_error'] = 'You do not have permission to access the admin area.';
    header('Location: ../login.php');
    exit;
}
            </pre>
        </div>
        <?php endif; ?>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
