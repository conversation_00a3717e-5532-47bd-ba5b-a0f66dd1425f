<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to modify database tables.');
    Utilities::redirect('index.php');
    exit;
}

// Add permissions_locked column to admin_users table
$alterTableQuery = "ALTER TABLE admin_users ADD COLUMN permissions_locked TINYINT(1) NOT NULL DEFAULT 0";

// Execute the query
if ($conn->query($alterTableQuery)) {
    Utilities::setFlashMessage('success', 'Successfully added permissions_locked column to admin_users table.');
} else {
    // Check if the error is because the column already exists
    if (strpos($conn->error, "Duplicate column name") !== false) {
        Utilities::setFlashMessage('info', 'The permissions_locked column already exists in the admin_users table.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to add permissions_locked column: ' . $conn->error);
    }
}

// Redirect back to admin dashboard
Utilities::redirect('staff_management.php');
?>
