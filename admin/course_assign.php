<?php
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    header('Location: index.php');
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $userId = isset($_POST['user_id']) ? intval($_POST['user_id']) : 0;
    $courseId = isset($_POST['course_id']) ? intval($_POST['course_id']) : 0;

    if ($userId > 0 && $courseId > 0) {
        // Check if user exists
        $userQuery = "SELECT id, name FROM users WHERE id = ?";
        $userStmt = $conn->prepare($userQuery);
        $userStmt->bind_param("i", $userId);
        $userStmt->execute();
        $userResult = $userStmt->get_result();

        if ($userResult->num_rows === 0) {
            Utilities::setFlashMessage('error', 'User not found.');
        } else {
            // Check if course exists
            $courseQuery = "SELECT id, title FROM courses WHERE id = ?";
            $courseStmt = $conn->prepare($courseQuery);
            $courseStmt->bind_param("i", $courseId);
            $courseStmt->execute();
            $courseResult = $courseStmt->get_result();

            if ($courseResult->num_rows === 0) {
                Utilities::setFlashMessage('error', 'Course not found.');
            } else {
                $course = $courseResult->fetch_assoc();

                // Check if user is already enrolled in this course
                $checkQuery = "SELECT id FROM user_course_enrollments WHERE user_id = ? AND course_id = ?";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bind_param("ii", $userId, $courseId);
                $checkStmt->execute();
                $checkResult = $checkStmt->get_result();

                if ($checkResult->num_rows > 0) {
                    Utilities::setFlashMessage('error', 'User is already enrolled in this course.');
                } else {
                    // Enroll user in course
                    $enrollmentDate = date('Y-m-d');
                    $startDate = date('Y-m-d');
                    $endDate = date('Y-m-d', strtotime('+30 days')); // Default 30 days access

                    $insertQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                                    VALUES (?, ?, ?, ?, ?, 'active')";
                    $insertStmt = $conn->prepare($insertQuery);
                    $insertStmt->bind_param("iisss", $userId, $courseId, $enrollmentDate, $startDate, $endDate);

                    if ($insertStmt->execute()) {
                        // Unlock first week videos
                        $videoQuery = "SELECT id FROM course_videos WHERE course_id = ? AND week_number = 1";
                        $videoStmt = $conn->prepare($videoQuery);
                        $videoStmt->bind_param("i", $courseId);
                        $videoStmt->execute();
                        $videoResult = $videoStmt->get_result();

                        while ($video = $videoResult->fetch_assoc()) {
                            $unlockQuery = "INSERT INTO user_video_progress (user_id, video_id, is_unlocked, unlock_date)
                                           VALUES (?, ?, 1, ?)";
                            $unlockStmt = $conn->prepare($unlockQuery);
                            $unlockStmt->bind_param("iis", $userId, $video['id'], $startDate);
                            $unlockStmt->execute();
                        }

                        Utilities::setFlashMessage('success', 'Course assigned successfully.');
                    } else {
                        Utilities::setFlashMessage('error', 'Failed to assign course: ' . $conn->error);
                    }
                }
            }
        }
    } else {
        Utilities::setFlashMessage('error', 'Invalid user or course ID.');
    }

    // Redirect to avoid form resubmission
    header('Location: course_assign.php');
    exit;
}

// Get all users
$userQuery = "SELECT id, username, name, phone_number, is_active FROM users WHERE is_active = 1 ORDER BY name";
$userResult = $conn->query($userQuery);
$users = [];
while ($row = $userResult->fetch_assoc()) {
    $users[] = $row;
}

// Get all courses
$courseQuery = "SELECT id, title, category, duration_weeks, price FROM courses WHERE is_active = 1 ORDER BY title";
$courseResult = $conn->query($courseQuery);
$courses = [];
while ($row = $courseResult->fetch_assoc()) {
    $courses[] = $row;
}

// Get recent course assignments
$assignmentQuery = "SELECT e.id, e.enrollment_date, e.status,
                    u.id as user_id, u.name as user_name,
                    c.id as course_id, c.title as course_title
                    FROM user_course_enrollments e
                    JOIN users u ON e.user_id = u.id
                    JOIN courses c ON e.course_id = c.id
                    ORDER BY e.enrollment_date DESC
                    LIMIT 10";
$assignmentResult = $conn->query($assignmentQuery);
$assignments = [];
while ($row = $assignmentResult->fetch_assoc()) {
    $assignments[] = $row;
}

// Page title
$pageTitle = 'Assign Courses';
include 'includes/header.php';
?>

<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">Assign Courses to Users</h1>
    <?php Utilities::displayFlashMessages(); ?>
    <div class="row g-4">
        <!-- Assignment Form -->
        <div class="col-lg-5">
            <div class="card border-0 shadow rounded">
                <div class="card-header bg-white border-bottom-0">
                    <h5 class="mb-0">Assign a Course</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="course_assign.php">
                        <div class="mb-3">
                            <label for="user_id" class="form-label">Select User</label>
                            <select class="form-select user-select" id="user_id" name="user_id" required>
                                <option value="">-- Select User --</option>
                                <?php foreach (
                                    $users as $user): ?>
                                <option value="<?php echo $user['id']; ?>">
                                    <?php echo htmlspecialchars($user['name']); ?>
                                    (<?php echo htmlspecialchars($user['username']); ?>)
                                    <?php if (!empty($user['phone_number'])): ?>
                                    - <?php echo htmlspecialchars($user['phone_number']); ?>
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="course_id" class="form-label">Select Course</label>
                            <select class="form-select" id="course_id" name="course_id" required>
                                <option value="">-- Select Course --</option>
                                <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>">
                                    <?php echo htmlspecialchars($course['title']); ?>
                                    (<?php echo htmlspecialchars($course['category']); ?>)
                                    - <?php echo $course['duration_weeks']; ?> weeks
                                    <?php if ($course['price'] > 0): ?>
                                    - ₹<?php echo number_format($course['price'], 2); ?>
                                    <?php else: ?>
                                    - Free
                                    <?php endif; ?>
                                </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">Assign Course</button>
                    </form>
                </div>
            </div>
        </div>
        <!-- Recent Assignments -->
        <div class="col-lg-7">
            <div class="card border-0 shadow rounded">
                <div class="card-header bg-white border-bottom-0">
                    <h5 class="mb-0">Recent Course Assignments</h5>
                </div>
                <div class="card-body">
                    <?php if (count($assignments) > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Course</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($assignments as $assignment): ?>
                                <tr>
                                    <td>
                                        <span class="avatar bg-primary text-white rounded-circle me-2" style="width:32px;display:inline-block;text-align:center;">
                                            <?php echo strtoupper(substr($assignment['user_name'], 0, 1)); ?>
                                        </span>
                                        <?php echo htmlspecialchars($assignment['user_name']); ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($assignment['course_title']); ?></td>
                                    <td><?php echo date('M d, Y', strtotime($assignment['enrollment_date'])); ?></td>
                                    <td>
                                        <span class="badge bg-<?php echo $assignment['status'] === 'active' ? 'success' : 'secondary'; ?>">
                                            <?php echo ucfirst($assignment['status']); ?>
                                        </span>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    <?php else: ?>
                    <p class="text-center text-muted">No recent course assignments found.</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Add Select2 JS for user dropdown -->
<script>
$(document).ready(function() {
    $('.user-select').select2({
        theme: 'bootstrap-5',
        width: '100%',
        placeholder: '-- Select User --'
    });
});
</script>
<?php include 'includes/footer.php'; ?>
