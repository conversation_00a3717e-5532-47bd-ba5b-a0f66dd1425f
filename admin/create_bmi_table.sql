-- Create BMI records table for tracking user BMI history
CREATE TABLE IF NOT EXISTS bmi_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    weight DECIMAL(5,2) NOT NULL,
    height DECIMAL(5,2) NOT NULL,
    bmi DECIMAL(4,2) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_recorded_at (recorded_at)
);

-- Add BMI-related columns to users table if they don't exist
-- Check and add height column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'height') = 0,
    'ALTER TABLE users ADD COLUMN height DECIMAL(5,2) DEFAULT NULL',
    'SELECT "height column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add weight column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'weight') = 0,
    'ALTER TABLE users ADD COLUMN weight DECIMAL(5,2) DEFAULT NULL',
    'SELECT "weight column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add age column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'age') = 0,
    'ALTER TABLE users ADD COLUMN age INT DEFAULT NULL',
    'SELECT "age column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add gender column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'gender') = 0,
    'ALTER TABLE users ADD COLUMN gender ENUM(\'male\', \'female\', \'other\') DEFAULT NULL',
    'SELECT "gender column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add fitness_goal column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'fitness_goal') = 0,
    'ALTER TABLE users ADD COLUMN fitness_goal TEXT DEFAULT NULL',
    'SELECT "fitness_goal column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add updated_at column
SET @sql = IF((SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = 'kft_fitness' AND TABLE_NAME = 'users' AND COLUMN_NAME = 'updated_at') = 0,
    'ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP',
    'SELECT "updated_at column already exists"');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
