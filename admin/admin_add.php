<?php
/**
 * Admin Add Page
 *
 * This page allows super admins to add new staff members with enhanced validation
 * and improved user experience.
 */

// Enable error reporting for development
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Add dashboard-new.css and admin-forms.css for enhanced styling
echo '<link rel="stylesheet" href="assets/css/dashboard-new.css">';
echo '<link rel="stylesheet" href="assets/css/admin-forms.css">';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if user has permission to access this page
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to add staff members.');
    Utilities::redirect('index.php');
    exit;
}

// Check if the is_active column exists
$checkColumnQuery = "SHOW COLUMNS FROM admin_users LIKE 'is_active'";
$checkColumnResult = $conn->query($checkColumnQuery);
$isActiveColumnExists = ($checkColumnResult && $checkColumnResult->num_rows > 0);

// If the column doesn't exist, show a message and link to the alter script
if (!$isActiveColumnExists) {
    Utilities::setFlashMessage('warning', 'The is_active column does not exist in the admin_users table. Please run the alter_admin_users_add_is_active.php script first.');
}

// Get all super_admin users for parent selection
$superAdminsQuery = "SELECT id, username, name FROM admin_users WHERE role = 'super_admin'";
$superAdminsResult = $conn->query($superAdminsQuery);
$superAdmins = [];
if ($superAdminsResult && $superAdminsResult->num_rows > 0) {
    while ($row = $superAdminsResult->fetch_assoc()) {
        $superAdmins[] = $row;
    }
}

// Get all permissions
$permissionsQuery = "SELECT id, name, slug, description FROM admin_permissions ORDER BY name";
$permissionsResult = $conn->query($permissionsQuery);
$permissions = [];
if ($permissionsResult && $permissionsResult->num_rows > 0) {
    while ($row = $permissionsResult->fetch_assoc()) {
        $permissions[] = $row;
    }
}

// Group permissions by category for better organization
$permissionCategories = [
    'user_management' => ['title' => 'User Management', 'permissions' => []],
    'content_management' => ['title' => 'Content Management', 'permissions' => []],
    'system' => ['title' => 'System', 'permissions' => []],
    'other' => ['title' => 'Other', 'permissions' => []]
];

foreach ($permissions as $permission) {
    $category = 'other';

    // Categorize permissions based on slug
    if (strpos($permission['slug'], 'user') !== false ||
        strpos($permission['slug'], 'staff') !== false) {
        $category = 'user_management';
    } elseif (strpos($permission['slug'], 'course') !== false ||
              strpos($permission['slug'], 'video') !== false ||
              strpos($permission['slug'], 'content') !== false) {
        $category = 'content_management';
    } elseif (strpos($permission['slug'], 'setting') !== false ||
              strpos($permission['slug'], 'system') !== false ||
              strpos($permission['slug'], 'admin') !== false) {
        $category = 'system';
    }

    $permissionCategories[$category]['permissions'][] = $permission;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get form data
    $username = Utilities::sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $email = Utilities::sanitizeInput($_POST['email'] ?? '');
    $name = Utilities::sanitizeInput($_POST['name'] ?? '');
    $phone = Utilities::sanitizeInput($_POST['phone'] ?? '');
    $role = isset($_POST['role']) ? $_POST['role'] : 'staff'; // Allow selection of role
    $parentAdminId = isset($_POST['parent_admin_id']) ? (int)$_POST['parent_admin_id'] : null;
    $selectedPermissions = $_POST['permissions'] ?? [];

    // Validate form data
    $errors = [];

    if (empty($username)) {
        $errors[] = 'Username is required.';
    } elseif (strlen($username) < 4) {
        $errors[] = 'Username must be at least 4 characters long.';
    } elseif (strpos($username, ' ') !== false) {
        $errors[] = 'Username cannot contain spaces.';
    } else {
        // Check if username already exists
        $checkUsernameQuery = "SELECT id FROM admin_users WHERE username = ?";
        $stmt = $conn->prepare($checkUsernameQuery);
        $stmt->bind_param("s", $username);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = 'Username already exists.';
        }
    }

    if (empty($password)) {
        $errors[] = 'Password is required.';
    } else {
        $policyResult = validate_password_policy($password);
        if ($policyResult !== true) {
            $errors[] = $policyResult;
        }
    }

    if (empty($email)) {
        $errors[] = 'Email is required.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Invalid email format.';
    } else {
        // Check if email already exists
        $checkEmailQuery = "SELECT id FROM admin_users WHERE email = ?";
        $stmt = $conn->prepare($checkEmailQuery);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        if ($result->num_rows > 0) {
            $errors[] = 'Email already exists.';
        }
    }

    if (empty($name)) {
        $errors[] = 'Name is required.';
    }

    if (empty($phone)) {
        $errors[] = 'Phone is required.';
    }

    if ($role === 'staff' && empty($parentAdminId)) {
        $errors[] = 'Parent admin is required for staff members.';
    }

    // If no errors, insert the staff member
    if (empty($errors)) {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Hash password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Insert staff member - handle case where is_active column doesn't exist
            if ($isActiveColumnExists) {
                $insertStaffQuery = "INSERT INTO admin_users (username, password, email, name, phone, role, parent_admin_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?, 1)";
            } else {
                $insertStaffQuery = "INSERT INTO admin_users (username, password, email, name, phone, role, parent_admin_id) VALUES (?, ?, ?, ?, ?, ?, ?)";
            }
            $stmt = $conn->prepare($insertStaffQuery);
            $stmt->bind_param("ssssssi", $username, $hashedPassword, $email, $name, $phone, $role, $parentAdminId);
            $stmt->execute();

            // Get the new staff member ID
            $staffId = $conn->insert_id;

            // Insert permissions
            if (!empty($selectedPermissions)) {
                $insertPermissionQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
                $stmt = $conn->prepare($insertPermissionQuery);

                foreach ($selectedPermissions as $permissionId) {
                    $stmt->bind_param("ii", $staffId, $permissionId);
                    $stmt->execute();
                }
            }

            // Commit transaction
            $conn->commit();

            // Log the activity if function exists
            if (function_exists('logStaffActivity')) {
                $details = "Added new staff member: {$name} ({$username})";
                logStaffActivity($conn, $auth->getUserId(), 'staff_add', $staffId, $details);
            }

            Utilities::setFlashMessage('success', "Staff member {$name} has been added successfully.");
            Utilities::redirect('staff_management.php');
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            Utilities::setFlashMessage('danger', 'Failed to add staff member: ' . $e->getMessage());
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}

// Include the HTML part
require_once 'admin_add_html.php';

require_once 'includes/footer.php';
?>

<style>
.dashboard-green-btn, .btn-success {
    background-color: #27ae60 !important;
    color: #fff !important;
    border: none !important;
    box-shadow: none !important;
}
.dashboard-green-btn:hover, .btn-success:hover {
    background-color: #219150 !important;
    color: #fff !important;
}
</style>
