<?php
require_once __DIR__ . '/../api/config.php';
$conn = getDbConnection();

// Check if 'enrollment_date' column exists
$checkColumn = $conn->query("SHOW COLUMNS FROM user_course_enrollments LIKE 'enrollment_date'");
if ($checkColumn && $checkColumn->num_rows === 0) {
    $alter = $conn->query("ALTER TABLE user_course_enrollments ADD COLUMN enrollment_date DATE DEFAULT NULL AFTER course_id");
    if ($alter) {
        echo "Column 'enrollment_date' added successfully.\n";
    } else {
        echo "Error adding column: " . $conn->error . "\n";
    }
} else {
    echo "Column 'enrollment_date' already exists.\n";
}
$conn->close(); 