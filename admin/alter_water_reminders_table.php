<?php
require_once __DIR__ . '/../api/config.php';
$conn = getDbConnection();

// Check if 'water_reminders' table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'water_reminders'");
if ($tableCheck && $tableCheck->num_rows === 0) {
    $createTable = $conn->query("CREATE TABLE water_reminders (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        interval_hours INT NOT NULL DEFAULT 2,
        start_time TIME NOT NULL DEFAULT '08:00:00',
        end_time TIME NOT NULL DEFAULT '22:00:00',
        is_active BOOLEAN DEFAULT TRUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;");
    if ($createTable) {
        echo "Table 'water_reminders' created successfully.\n";
    } else {
        echo "Error creating table: " . $conn->error . "\n";
    }
} else {
    echo "Table 'water_reminders' already exists.\n";
}
$conn->close(); 