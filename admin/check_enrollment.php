<?php
// Simple script to check enrollment status
$conn = new mysqli('localhost', 'root', '', 'kft_fitness');

if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

echo "=== Checking User 29 Enrollment Status ===\n";

// Check user exists
$userQuery = "SELECT id, name FROM users WHERE id = 29";
$userResult = $conn->query($userQuery);
if ($userResult->num_rows > 0) {
    $user = $userResult->fetch_assoc();
    echo "User found: ID={$user['id']}, Name={$user['name']}\n";
} else {
    echo "User 29 not found!\n";
    exit(1);
}

// Check course exists
$courseQuery = "SELECT id, title FROM courses WHERE id = 2";
$courseResult = $conn->query($courseQuery);
if ($courseResult->num_rows > 0) {
    $course = $courseResult->fetch_assoc();
    echo "Course found: ID={$course['id']}, Title={$course['title']}\n";
} else {
    echo "Course 2 not found!\n";
    exit(1);
}

// Check enrollment
$enrollmentQuery = "SELECT * FROM user_course_enrollments WHERE user_id = 29 AND course_id = 2";
$enrollmentResult = $conn->query($enrollmentQuery);
if ($enrollmentResult->num_rows > 0) {
    $enrollment = $enrollmentResult->fetch_assoc();
    echo "Enrollment found:\n";
    echo "  - Status: {$enrollment['status']}\n";
    echo "  - Start Date: {$enrollment['start_date']}\n";
    echo "  - End Date: {$enrollment['end_date']}\n";
    echo "  - Created: {$enrollment['created_at']}\n";
} else {
    echo "No enrollment found for user 29 in course 2!\n";
    
    // Check all enrollments for user 29
    $allEnrollmentsQuery = "SELECT uce.*, c.title FROM user_course_enrollments uce JOIN courses c ON uce.course_id = c.id WHERE uce.user_id = 29";
    $allEnrollmentsResult = $conn->query($allEnrollmentsQuery);
    echo "All enrollments for user 29:\n";
    while ($row = $allEnrollmentsResult->fetch_assoc()) {
        echo "  - Course {$row['course_id']}: {$row['title']} (Status: {$row['status']})\n";
    }
}

// Test the exact query used by course_videos.php
echo "\n=== Testing Course Videos API Query ===\n";
$enrollmentQuery = "SELECT e.* FROM user_course_enrollments e
                   JOIN users u ON e.user_id = u.id
                   WHERE e.user_id = 29
                   AND e.course_id = 2
                   AND e.status = 'active'
                   AND u.id = 29";
$enrollmentResult = $conn->query($enrollmentQuery);
echo "Course videos API query result: {$enrollmentResult->num_rows} rows\n";

if ($enrollmentResult->num_rows > 0) {
    $enrollment = $enrollmentResult->fetch_assoc();
    echo "  - Enrollment ID: {$enrollment['id']}\n";
    echo "  - Status: {$enrollment['status']}\n";
} else {
    echo "  - No active enrollment found with the API query!\n";
}

$conn->close();
?>
