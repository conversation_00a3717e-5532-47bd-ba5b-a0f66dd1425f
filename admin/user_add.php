<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
try {
$conn = $db->getConnection();
    if (!$conn) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    Utilities::setFlashMessage('danger', 'Database connection error: ' . $e->getMessage());
    Utilities::redirect('index.php');
    exit;
}

// Check if user has permission to access this page
if (!$auth->hasRole('admin') && !$auth->hasRole('super_admin') && !$auth->hasRole('staff') && !$auth->hasPermission('manage_users')) {
    Utilities::setFlashMessage('error', 'You do not have permission to add users.');
    Utilities::redirect('index.php');
}

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isSuperAdmin = $auth->hasRole('super_admin');
$isAdmin = $auth->hasRole('admin') || $isSuperAdmin;

// Check if we have prefilled data from pending user approval
$prefilledName = '';
$prefilledPhone = '';
$isPrefilled = false;

if (isset($_GET['prefill']) && $_GET['prefill'] == '1') {
    $isPrefilled = true;
    $prefilledName = isset($_GET['name']) ? urldecode($_GET['name']) : '';
    $prefilledPhone = isset($_GET['phone']) ? urldecode($_GET['phone']) : '';
}

// Check if we have a staff ID to assign the user to
$preselectedStaffId = null;
if (isset($_GET['assigned_staff_id']) && is_numeric($_GET['assigned_staff_id'])) {
    $preselectedStaffId = (int)$_GET['assigned_staff_id'];
} elseif ($currentAdminRole === 'staff') {
    // If current user is staff, automatically assign to themselves
    $preselectedStaffId = $currentAdminId;
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $username = Utilities::sanitizeInput($_POST['username'] ?? '');
    $name = Utilities::sanitizeInput($_POST['name'] ?? '');

    // Format phone number - add +91 prefix if not present
    $phoneNumber = Utilities::sanitizeInput($_POST['phone_number'] ?? '');
    if (!empty($phoneNumber) && !preg_match('/^\+/', $phoneNumber)) {
        $phoneNumber = '+91' . $phoneNumber;
    }

    // Convert empty phone number to NULL
    $phoneNumber = empty($phoneNumber) ? null : $phoneNumber;

    // Get phone field for the users table
    $phone = $phoneNumber;

    // Get other form fields
    $gender = Utilities::sanitizeInput($_POST['gender'] ?? null);
    $fitnessGoal = Utilities::sanitizeInput($_POST['fitness_goal'] ?? null);
    $age = isset($_POST['age']) && !empty($_POST['age']) ? (int)$_POST['age'] : null;
    $height = isset($_POST['height']) && !empty($_POST['height']) ? (float)$_POST['height'] : null;
    $weight = isset($_POST['weight']) && !empty($_POST['weight']) ? (float)$_POST['weight'] : null;

    // Handle premium status from both form types
    $isPremium = 1;

    $isActive = isset($_POST['is_active']) ? 1 : 0;
    if (!isset($_POST['is_active']) && !isset($_POST['detailed_is_active'])) {
        // Default to active if not specified
        $isActive = 1;
    }

    // Get assigned staff ID if provided
    $assignedStaffId = isset($_POST['assigned_staff_id']) && !empty($_POST['assigned_staff_id']) ? (int)$_POST['assigned_staff_id'] : null;

    // If current user is staff, automatically assign to themselves
    if ($currentAdminRole === 'staff' && empty($assignedStaffId)) {
        $assignedStaffId = $currentAdminId;
    }

    // Get selected program and course IDs
    $programId = isset($_POST['program_id']) && !empty($_POST['program_id']) ? (int)$_POST['program_id'] : null;
    $courseIds = isset($_POST['course_ids']) && is_array($_POST['course_ids']) ? $_POST['course_ids'] : [];

    // Check if this is a "Save & Add Another" request
    $saveAndAddAnother = isset($_POST['save_and_add']) && $_POST['save_and_add'] == '1';

    // Validate inputs
    $errors = [];

    if (empty($username)) {
        $errors[] = 'Username is required.';
    }

    if (empty($name)) {
        $errors[] = 'Name is required.';
    }

    if (empty($phoneNumber)) {
        $errors[] = 'Phone number is required.';
    } elseif (!preg_match('/^\+[0-9]{12,13}$/', $phoneNumber)) {
        $errors[] = 'Please enter a valid phone number (10 digits with country code).';
    }

    // Check if username already exists
    $checkQuery = "SELECT id FROM users WHERE username = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("s", $username);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $errors[] = 'Username already exists. Please choose a different username.';
    }

    // Check if phone number already exists
    $checkQuery = "SELECT id FROM users WHERE phone_number = ?";
    $stmt = $conn->prepare($checkQuery);
    $stmt->bind_param("s", $phoneNumber);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows > 0) {
        $errors[] = 'Phone number already exists. This user may already be registered.';
    }

    if (empty($errors)) {
        try {
            // Start transaction
            $conn->begin_transaction();

        // Generate a random 4-digit PIN for app login
        $userPin = sprintf('%04d', mt_rand(0, 9999));
        // Generate a random 6-digit verification code
        $verificationCode = sprintf("%06d", mt_rand(100000, 999999));
        // Set expiration time (24 hours from now)
        $expiresAt = date('Y-m-d H:i:s', strtotime('+24 hours'));

        // Insert user with PIN
        $insertQuery = "INSERT INTO users (username, name, phone_number, phone, gender, fitness_goal, age, height, weight, is_premium, is_active, pin, verification_code, verification_expires_at, assigned_staff_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insertQuery);
            if (!$stmt) {
                throw new Exception("Failed to prepare user insert statement: " . $conn->error);
            }
            
        $stmt->bind_param("ssssssiidiisssi", $username, $name, $phoneNumber, $phone, $gender, $fitnessGoal, $age, $height, $weight, $isPremium, $isActive, $userPin, $verificationCode, $expiresAt, $assignedStaffId);

            if (!$stmt->execute()) {
                throw new Exception("Failed to insert user: " . $stmt->error);
            }
            
            $userId = $stmt->insert_id;

            // Create default water reminder settings
            $waterQuery = "INSERT INTO water_reminders (user_id, interval_hours, start_time, end_time, is_active)
                          VALUES (?, 2, '08:00:00', '22:00:00', 1)";
            $waterStmt = $conn->prepare($waterQuery);
            if (!$waterStmt) {
                throw new Exception("Failed to prepare water reminder statement: " . $conn->error);
            }
            
            $waterStmt->bind_param("i", $userId);
            if (!$waterStmt->execute()) {
                throw new Exception("Failed to create water reminder: " . $waterStmt->error);
            }

            // Process session access if any
            // Check if session_access table exists
            $tableExistsQuery = "SHOW TABLES LIKE 'session_access'";
            $tableExistsResult = $conn->query($tableExistsQuery);
            $sessionAccessTableExists = ($tableExistsResult->num_rows > 0);

            if ($sessionAccessTableExists) {
                if (isset($_POST['session_access']) && is_array($_POST['session_access'])) {
                    foreach ($_POST['session_access'] as $sessionType => $accessLevel) {
                        $accessQuery = "INSERT INTO session_access (user_id, session_type, access_level)
                                       VALUES (?, ?, ?)";
                        $accessStmt = $conn->prepare($accessQuery);
                        $accessStmt->bind_param("iss", $userId, $sessionType, $accessLevel);
                        $accessStmt->execute();
                    }
                } else {
                    // Set default session access for quick add form
                    $defaultSessions = [
                        'workout' => 'full',
                        'nutrition' => 'view',
                        'progress' => 'full',
                        'premium' => $isPremium ? 'full' : 'none'
                    ];

                    foreach ($defaultSessions as $sessionType => $accessLevel) {
                        $accessQuery = "INSERT INTO session_access (user_id, session_type, access_level)
                                       VALUES (?, ?, ?)";
                        $accessStmt = $conn->prepare($accessQuery);
                        $accessStmt->bind_param("iss", $userId, $sessionType, $accessLevel);
                        $accessStmt->execute();
                    }
                }
            }

            // Assign workout program if selected
            if (!empty($programId)) {
                $startDate = date('Y-m-d');
                $programQuery = "SELECT duration_weeks FROM workout_programs WHERE id = ?";
                $programStmt = $conn->prepare($programQuery);
                $programStmt->bind_param("i", $programId);
                $programStmt->execute();
                $programResult = $programStmt->get_result();

                if ($programResult->num_rows > 0) {
                    $program = $programResult->fetch_assoc();
                    $endDate = date('Y-m-d', strtotime("+{$program['duration_weeks']} weeks"));

                    $enrollQuery = "INSERT INTO program_enrollments (user_id, program_id, start_date, end_date, status)
                                   VALUES (?, ?, ?, ?, 'active')";
                    $enrollStmt = $conn->prepare($enrollQuery);
                    $enrollStmt->bind_param("iiss", $userId, $programId, $startDate, $endDate);
                    $enrollStmt->execute();
                }
            }

            // Assign courses if selected
            if (!empty($courseIds)) {
                $enrollmentDate = date('Y-m-d');
                $startDate = date('Y-m-d');
                $amountPaidArr = $_POST['amount_paid'] ?? [];
                $paymentMethodArr = $_POST['payment_method'] ?? [];
                $transactionIdArr = $_POST['transaction_id'] ?? [];
                $expiryDateArr = $_POST['expiry_date'] ?? [];

                foreach ($courseIds as $courseId) {
                    // Get course duration
                    $courseQuery = "SELECT duration_weeks FROM courses WHERE id = ?";
                    $courseStmt = $conn->prepare($courseQuery);
                    $courseStmt->bind_param("i", $courseId);
                    $courseStmt->execute();
                    $courseResult = $courseStmt->get_result();

                    if ($courseResult->num_rows > 0) {
                        $course = $courseResult->fetch_assoc();
                        $endDate = isset($expiryDateArr[$courseId]) && Utilities::validateDate($expiryDateArr[$courseId]) ? $expiryDateArr[$courseId] : date('Y-m-d', strtotime("+{$course['duration_weeks']} weeks"));

                        $enrollQuery = "INSERT INTO user_course_enrollments (user_id, course_id, enrollment_date, start_date, end_date, status)
                                       VALUES (?, ?, ?, ?, ?, 'active')";
                        $enrollStmt = $conn->prepare($enrollQuery);
                        $enrollStmt->bind_param("iisss", $userId, $courseId, $enrollmentDate, $startDate, $endDate);
                        $enrollStmt->execute();

                        // Fix the payment record insertion
                        $amountPaid = isset($amountPaidArr[$courseId]) ? floatval($amountPaidArr[$courseId]) : 0;
                        $paymentMethod = isset($paymentMethodArr[$courseId]) ? Utilities::sanitizeInput($paymentMethodArr[$courseId]) : '';
                        $transactionId = isset($transactionIdArr[$courseId]) ? Utilities::sanitizeInput($transactionIdArr[$courseId]) : '';
                        
                        $purchaseQuery = "INSERT INTO course_purchases (user_id, course_id, purchase_date, amount_paid, payment_method, transaction_id, status)
                                         VALUES (?, ?, NOW(), ?, ?, ?, 'completed')";
                        $purchaseStmt = $conn->prepare($purchaseQuery);
                        if (!$purchaseStmt) {
                            throw new Exception("Failed to prepare course purchase statement: " . $conn->error);
                        }
                        
                        $purchaseStmt->bind_param("iidss", $userId, $courseId, $amountPaid, $paymentMethod, $transactionId);
                        if (!$purchaseStmt->execute()) {
                            throw new Exception("Failed to insert course purchase: " . $purchaseStmt->error);
                        }
                    }
                }
            }

            // Commit transaction
            $conn->commit();

            // Set success message
            if (isset($_SESSION['pending_user_id']) || isset($_POST['pending_user_id'])) {
                Utilities::setFlashMessage('success', "Registration for <strong>{$name}</strong> has been approved and the user has been added successfully with username <strong>{$username}</strong>.");
            } else {
                Utilities::setFlashMessage('success', "User <strong>{$name}</strong> has been added successfully with username <strong>{$username}</strong>.");
            }

            // Ensure we're using absolute path for redirection
            $redirectUrl = dirname($_SERVER['PHP_SELF']) . '/users.php';
            header('Location: ' . $redirectUrl);
exit;

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            Utilities::setFlashMessage('danger', 'Failed to add user: ' . $e->getMessage());
        }
    } else {
        // Display validation errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}

// Get available session types
$sessionTypes = [
    'workout' => 'Workout Sessions',
    'nutrition' => 'Nutrition Planning',
    'progress' => 'Progress Tracking',
    'premium' => 'Premium Content'
];

// Get available fitness goals
$fitnessGoals = [
    'weight_loss' => 'Weight Loss',
    'muscle_gain' => 'Muscle Gain',
    'general_fitness' => 'General Fitness',
    'strength' => 'Strength Training',
    'endurance' => 'Endurance Training',
    'flexibility' => 'Flexibility & Mobility'
];

// Get available workout programs
$programsQuery = "SELECT id, title, difficulty, duration_weeks FROM workout_programs WHERE is_active = 1 ORDER BY title";
$programsResult = $conn->query($programsQuery);
$workoutPrograms = [];
if ($programsResult && $programsResult->num_rows > 0) {
    while ($program = $programsResult->fetch_assoc()) {
        $workoutPrograms[] = $program;
    }
}

// Get available courses
$coursesQuery = "SELECT id, title, category, duration_weeks, price FROM courses WHERE is_active = 1 ORDER BY title";
$coursesResult = $conn->query($coursesQuery);
$courses = [];
if ($coursesResult && $coursesResult->num_rows > 0) {
    while ($course = $coursesResult->fetch_assoc()) {
        $courses[] = $course;
    }
}

// Get all staff members for the dropdown
$staffMembers = [];
// Include staff members for all user types (staff needs to see their own info)
$staffQuery = "SELECT id, name, username FROM admin_users WHERE role = 'staff' AND is_active = 1 ORDER BY name";
$staffResult = $conn->query($staffQuery);
if ($staffResult && $staffResult->num_rows > 0) {
    while ($staff = $staffResult->fetch_assoc()) {
        $staffMembers[] = $staff;
    }
}
?>

<style>
/* Modern Add User Page Styles */
.add-user-section-card {
    border-radius: 14px;
    box-shadow: 0 2px 16px rgba(0,0,0,0.06);
    background: #fff;
    margin-bottom: 2rem;
    border: none;
}
.add-user-section-header {
    font-size: 1.15rem;
    font-weight: 600;
    color: #222;
    margin-bottom: 0.5rem;
    letter-spacing: 0.01em;
}
.add-user-divider {
    border-top: 1px solid #f0f0f0;
    margin: 1.5rem 0;
}
.add-user-floating-label .form-label {
    font-weight: 500;
    color: #444;
    margin-bottom: 0.25rem;
}
.add-user-pin-card {
    background: #f8fafc;
    border-radius: 10px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    padding: 1.25rem 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1.5rem;
    justify-content: space-between;
}
.add-user-pin-value {
    font-size: 1.4rem;
    font-weight: 700;
    letter-spacing: 0.25em;
    color: #27ae60;
    background: linear-gradient(135deg, #f8fafc 60%, #e8f5e9 100%);
    border-radius: 12px;
    padding: 0.35rem 0.35rem;
    box-shadow: 0 1px 6px rgba(39,174,96,0.08), 0 1.5px 4px rgba(0,0,0,0.03);
    border: 1.5px solid #e0e0e0;
    width: 8ch;
    min-width: 8ch;
    max-width: 9ch;
    text-align: center;
    margin: 0 auto;
    display: block;
    font-family: monospace, 'Courier New', Courier;
    transition: border-color 0.2s, box-shadow 0.2s, background 0.2s, font-size 0.2s, width 0.2s;
    outline: none;
}
.add-user-pin-value:focus {
    border-color: #27ae60;
    box-shadow: 0 2px 10px rgba(39,174,96,0.13), 0 1.5px 4px rgba(0,0,0,0.04);
    background: linear-gradient(135deg, #e8f5e9 60%, #f8fafc 100%);
}
.add-user-pin-value::placeholder {
    color: #b5c7b7;
    opacity: 1;
    font-weight: 400;
    letter-spacing: 0.18em;
}
.add-user-pin-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}
.add-user-pin-actions .btn {
    border-radius: 50px;
    font-size: 1.1rem;
    padding: 0.4rem 1.1rem;
}
@media (max-width: 767px) {
    .add-user-pin-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
    }
    .add-user-pin-value {
        font-size: 1.1rem;
        padding: 0.25rem 0.25rem;
        width: 8ch;
        min-width: 8ch;
        max-width: 9ch;
    }
}
.add-user-pin-copy-btn {
    border: none;
    background: transparent;
    color: #27ae60;
    font-size: 1.2rem;
    margin-left: 0.5rem;
    cursor: pointer;
    transition: color 0.2s;
    outline: none;
    padding: 0.2rem 0.4rem;
    border-radius: 50%;
    position: relative;
}
.add-user-pin-copy-btn:active,
.add-user-pin-copy-btn:focus {
    color: #219150;
    background: #e8f5e9;
}
.add-user-pin-copy-btn.copied {
    color: #198754;
}
.add-user-pin-copy-btn .copy-tooltip {
    display: none;
    position: absolute;
    top: -2.2rem;
    left: 50%;
    transform: translateX(-50%);
    background: #222;
    color: #fff;
    font-size: 0.85rem;
    padding: 0.2rem 0.7rem;
    border-radius: 6px;
    white-space: nowrap;
    z-index: 10;
}
.add-user-pin-copy-btn.copied .copy-tooltip {
    display: block;
}
</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="fw-bold" style="font-size: 2rem;">Add New User</h1>
    <a href="users.php" class="btn btn-outline-secondary rounded-pill px-3">
        <i class="fas fa-arrow-left me-2"></i> Back to Users
    </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<!-- Modern Add User Card -->
<div class="card add-user-section-card">
    <div class="card-body">
        <?php if ($isPrefilled): ?>
        <div class="alert alert-info mb-4">
            <div class="d-flex align-items-center">
                <i class="fas fa-info-circle me-3 fs-4"></i>
                <div>
                    <h5 class="mb-1">Adding User from Pending Registration</h5>
                    <p class="mb-0">This form has been prefilled with information from a pending registration. Complete the remaining fields and click "Save User" to approve this registration.</p>
                </div>
            </div>
        </div>
        <?php endif; ?>
        <form method="post" action="user_add.php" id="addUserForm" autocomplete="off">
            <?php if (isset($_SESSION['pending_user_id'])): ?>
            <input type="hidden" name="pending_user_id" value="<?php echo intval($_SESSION['pending_user_id']); ?>">
            <?php endif; ?>
            <div class="row g-4">
                <div class="col-md-6 add-user-floating-label">
                    <label for="name" class="form-label">Full Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="name" name="name" required placeholder="Enter user's full name" value="<?php echo htmlspecialchars($prefilledName ?? ''); ?>">
                    <?php if ($isPrefilled && !empty($prefilledName)): ?><div class="form-text text-success"><i class="fas fa-check-circle me-1"></i> Prefilled from pending registration</div><?php endif; ?>
                </div>
                <div class="col-md-6 add-user-floating-label">
                    <label for="username" class="form-label">Username <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="username" name="username" required>
                        <button class="btn btn-outline-secondary" type="button" id="generateUsername">
                            <i class="fas fa-magic me-1"></i> Generate
                        </button>
                    </div>
                    <div class="form-text">Username will be used for app login</div>
                </div>
            </div>
            <div class="row g-4 mt-1">
                <div class="col-md-6 add-user-floating-label">
                    <label for="phone_number" class="form-label">Phone Number <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">+91</span>
                        <input type="tel" class="form-control" id="phone_number" name="phone_number" required placeholder="10-digit mobile number" pattern="[0-9]{10}" maxlength="10" value="<?php echo htmlspecialchars($prefilledPhone ?? ''); ?>">
                        <div id="phone-exists-error" class="validation-error text-danger small mt-1" style="display:none;"></div>
                    </div>
                    <div class="form-text">Enter a 10-digit mobile number without country code</div>
                </div>
                <div class="col-md-6 add-user-floating-label">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" placeholder="Email address (optional)">
                </div>
            </div>
            <div class="row g-4 mt-1">
                <div class="col-md-3 add-user-floating-label">
                    <label for="age" class="form-label">Age</label>
                    <input type="number" class="form-control" id="age" name="age" min="1" max="120">
                </div>
                <div class="col-md-3 add-user-floating-label">
                    <label for="height" class="form-label">Height (cm)</label>
                    <input type="number" class="form-control" id="height" name="height" step="0.01" min="0">
                </div>
                <div class="col-md-3 add-user-floating-label">
                    <label for="weight" class="form-label">Weight (kg)</label>
                    <input type="number" class="form-control" id="weight" name="weight" step="0.01" min="0">
                </div>
                <div class="col-md-3 add-user-floating-label">
                    <label for="gender" class="form-label">Gender</label>
                    <select class="form-select" id="gender" name="gender">
                        <option value="">Select Gender</option>
                        <option value="male">Male</option>
                        <option value="female">Female</option>
                        <option value="other">Other</option>
                    </select>
                </div>
            </div>
            <div class="row g-4 mt-1">
                <div class="col-md-6 add-user-floating-label">
                    <label for="fitness_goal" class="form-label">Fitness Goal</label>
                    <select class="form-select" id="fitness_goal" name="fitness_goal">
                        <option value="">Select Fitness Goal</option>
                        <?php foreach ($fitnessGoals as $value => $label): ?>
                            <option value="<?php echo $value; ?>"><?php echo $label; ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <?php if ($currentAdminRole === 'staff'): ?>
                    <div class="add-user-floating-label mb-3">
                        <label for="assigned_staff_id" class="form-label">Assign to Staff Member</label>
                        <input type="hidden" name="assigned_staff_id" value="<?php echo $currentAdminId; ?>">
                        <input type="text" class="form-control" value="<?php
                            $staffName = '';
                            foreach ($staffMembers as $staff) {
                                if ($staff['id'] == $currentAdminId) {
                                    $staffName = $staff['name'] . ' (' . $staff['username'] . ')';
                                    break;
                                }
                            }
                            echo htmlspecialchars($staffName);
                        ?>" disabled>
                        <div class="form-text">Users you add are automatically assigned to you</div>
                    </div>
                    <?php elseif (($isSuperAdmin || $isAdmin) && !empty($staffMembers)): ?>
                    <div class="add-user-floating-label mb-3">
                        <label for="assigned_staff_id" class="form-label">Assign to Staff Member</label>
                        <select class="form-select" id="assigned_staff_id" name="assigned_staff_id">
                            <option value="">No Staff Assignment</option>
                            <?php foreach ($staffMembers as $staff): ?>
                                <option value="<?php echo $staff['id']; ?>" <?php echo ($preselectedStaffId !== null && $preselectedStaffId == $staff['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($staff['name'] . ' (' . $staff['username'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="form-text">Assign this user to a staff member for management</div>
                    </div>
                    <?php endif; ?>
                    <div class="form-check form-switch mt-4">
                        <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                        <label class="form-check-label" for="is_active">Active Account</label>
                    </div>
                </div>
            </div>
            <div class="add-user-divider"></div>
            <!-- Course Assignment Section (unchanged) -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-graduation-cap me-2"></i> Course Assignment
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Remove Workout Program Assignment section -->
                        <!-- Only show Courses section -->
                        <div class="col-md-12">
                            <h6 class="fw-bold mb-3">Courses</h6>
                            <?php if (empty($courses)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i> No active courses available.
                                </div>
                            <?php else: ?>
                                <div class="mb-3">
                                    <div class="row">
                                        <?php foreach ($courses as $course): ?>
                                            <div class="col-md-12 mb-2">
                                                <div class="form-check">
                                                    <input class="form-check-input course-checkbox" type="checkbox" id="course_<?php echo $course['id']; ?>" name="course_ids[]" value="<?php echo $course['id']; ?>" data-duration="<?php echo $course['duration_weeks']; ?>">
                                                    <label class="form-check-label" for="course_<?php echo $course['id']; ?>">
                                                        <?php echo htmlspecialchars($course['title'] ?? ''); ?>
                                                        <small class="text-muted d-block">
                                                            <?php echo htmlspecialchars($course['category'] ?? ''); ?> |
                                                            <?php echo $course['duration_weeks']; ?> weeks
                                                        </small>
                                                    </label>
                                                </div>
                                                <div class="row mt-2" id="course_fields_<?php echo $course['id']; ?>" style="display:none;">
                                                    <div class="col-md-6">
                                                        <input type="date" class="form-control expiry-date" name="expiry_date[<?php echo $course['id']; ?>]" placeholder="Expiry Date" readonly>
                                                        <div class="form-text">This is the course end date based on course duration.</div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                                <script>
                                document.addEventListener('DOMContentLoaded', function() {
                                    document.querySelectorAll('.course-checkbox').forEach(function(checkbox) {
                                        checkbox.addEventListener('change', function() {
                                            var fields = document.getElementById('course_fields_' + this.value);
                                            var expiry = fields.querySelector('input[name^=expiry_date]');
                                            if (this.checked) {
                                                fields.style.display = 'flex';
                                                // Set expiry date based on course duration
                                                var duration = parseInt(this.getAttribute('data-duration'));
                                                if (!isNaN(duration)) {
                                                    var today = new Date();
                                                    today.setDate(today.getDate() + (duration * 7)); // Convert weeks to days
                                                    expiry.value = today.toISOString().slice(0,10);
                                                }
                                                expiry.required = true;
                                            } else {
                                                fields.style.display = 'none';
                                                expiry.value = '';
                                                expiry.required = false;
                                            }
                                        });
                                    });

                                    // Enhanced validation: highlight missing fields and show error messages
                                    document.getElementById('addUserForm').addEventListener('submit', function(e) {
                                        var valid = true;
                                        // Remove previous error messages
                                        document.querySelectorAll('.validation-error').forEach(function(el) { el.remove(); });
                                        document.querySelectorAll('.is-invalid').forEach(function(el) { el.classList.remove('is-invalid'); });
                                        document.querySelectorAll('.course-fields').forEach(function(fields) { fields.style.border = ''; });
                                        
                                        // Validate main required fields
                                        this.querySelectorAll('[required]').forEach(function(input) {
                                            if (input.offsetParent !== null && !input.value.trim()) {
                                                valid = false;
                                                input.classList.add('is-invalid');
                                                var error = document.createElement('div');
                                                error.className = 'validation-error text-danger small mt-1';
                                                error.innerText = 'This field is required.';
                                                if (!input.nextElementSibling || !input.nextElementSibling.classList.contains('validation-error')) {
                                                    input.parentNode.appendChild(error);
                                                }
                                            }
                                        });

                                        // Validate expiry dates for checked courses
                                        document.querySelectorAll('.course-checkbox').forEach(function(checkbox) {
                                            if (checkbox.checked) {
                                                var fields = document.getElementById('course_fields_' + checkbox.value);
                                                var expiry = fields.querySelector('input[name^=expiry_date]');
                                                if (!expiry.value.trim()) {
                                                    valid = false;
                                                    expiry.classList.add('is-invalid');
                                                    var error = document.createElement('div');
                                                    error.className = 'validation-error text-danger small mt-1';
                                                    error.innerText = 'This field is required.';
                                                    if (!expiry.nextElementSibling || !expiry.nextElementSibling.classList.contains('validation-error')) {
                                                        expiry.parentNode.appendChild(error);
                                                    }
                                                }
                                            }
                                        });

                                        if (!valid) {
                                            e.preventDefault();
                                            // Optionally scroll to first error
                                            var firstError = document.querySelector('.is-invalid');
                                            if (firstError) {
                                                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                                                firstError.focus();
                                            }
                                        }
                                    });
                                    // Real-time phone number validation
                                    var phoneInput = document.getElementById('phone_number');
                                    var phoneError = document.getElementById('phone-exists-error');
                                    var nameInput = document.getElementById('name');
                                    var usernameInput = document.getElementById('username');
                                    phoneInput.addEventListener('input', function() {
                                        var phone = phoneInput.value.trim();
                                        if (phone.length === 10 && /^\d{10}$/.test(phone)) {
                                            var xhr = new XMLHttpRequest();
                                            xhr.open('POST', 'phone_check.php', true);
                                            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                                            xhr.onreadystatechange = function() {
                                                if (xhr.readyState === 4 && xhr.status === 200) {
                                                    try {
                                                        var res = JSON.parse(xhr.responseText);
                                                        if (res.exists) {
                                                            phoneError.style.display = '';
                                                            phoneError.innerText = 'Phone number already exists. This user may already be registered.';
                                                            if (res.name) nameInput.value = res.name;
                                                            if (res.username) usernameInput.value = res.username;
                                                            phoneInput.classList.add('is-invalid');
                                                        } else {
                                                            phoneError.style.display = 'none';
                                                            phoneError.innerText = '';
                                                            phoneInput.classList.remove('is-invalid');
                                                        }
                                                    } catch (e) {
                                                        phoneError.style.display = 'none';
                                                        phoneError.innerText = '';
                                                        phoneInput.classList.remove('is-invalid');
                                                    }
                                                }
                                            };
                                            xhr.send('phone_number=' + encodeURIComponent(phone));
                                        } else {
                                            phoneError.style.display = 'none';
                                            phoneError.innerText = '';
                                            phoneInput.classList.remove('is-invalid');
                                        }
                                    });
                                    // PIN auto-generate
                                    function generatePin() {
                                        var pin = Math.floor(1000 + Math.random() * 9000);
                                        document.getElementById('user_pin').value = pin;
                                    }
                                    document.getElementById('generatePinBtn').addEventListener('click', generatePin);
                                    generatePin();

                                    // Copy PIN to clipboard
                                    var copyBtn = document.getElementById('copyPinBtn');
                                    var pinInput = document.getElementById('user_pin');
                                    copyBtn.addEventListener('click', function() {
                                        pinInput.select();
                                        pinInput.setSelectionRange(0, 4);
                                        try {
                                            document.execCommand('copy');
                                        } catch (err) {
                                            navigator.clipboard.writeText(pinInput.value);
                                        }
                                        copyBtn.classList.add('copied');
                                        setTimeout(function() {
                                            copyBtn.classList.remove('copied');
                                        }, 1200);
                                    });
                                });
                                </script>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex justify-content-between mt-4">
                <div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i> Save User
                    </button>
                    <a href="users.php" class="btn btn-outline-secondary ms-2">Cancel</a>
                </div>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
