<?php
ini_set('display_errors', 0);
ini_set('display_startup_errors', 0);
error_reporting(E_ALL);

// Custom error handler to return JSON errors
set_exception_handler(function($e) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => 'Server error: ' . $e->getMessage()]);
    exit;
});
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => "PHP error [$errno]: $errstr in $errfile on line $errline"]);
    exit;
});

require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize auth
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Authentication required']);
    exit;
}

// Check if user has admin role
if (!$auth->hasRole('admin')) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Permission denied']);
    exit;
}

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get user ID from request
$userId = isset($_GET['user_id']) ? intval($_GET['user_id']) : 0;

if ($userId <= 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Invalid user ID']);
    exit;
}

// Get user data
$userQuery = "SELECT * FROM users WHERE id = ?";
$userStmt = $conn->prepare($userQuery);
$userStmt->bind_param("i", $userId);
$userStmt->execute();
$userResult = $userStmt->get_result();

if ($userResult->num_rows === 0) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'User not found']);
    exit;
}

$user = $userResult->fetch_assoc();

// Get user statistics
$statsQuery = "SELECT
    (SELECT COUNT(*) FROM workout_records WHERE user_id = ?) as total_workouts,
    (SELECT SUM(duration) FROM workout_records WHERE user_id = ?) as total_workout_minutes,
    (SELECT COUNT(*) FROM user_course_enrollments WHERE user_id = ? AND status = 'active') as total_courses,
    (SELECT COUNT(*) FROM streak_days WHERE user_id = ?) as streak_days,
    (SELECT bmi FROM bmi_records WHERE user_id = ? ORDER BY recorded_at DESC LIMIT 1) as current_bmi";
$statsStmt = $conn->prepare($statsQuery);
$statsStmt->bind_param("iiiii", $userId, $userId, $userId, $userId, $userId);
$statsStmt->execute();
$stats = $statsStmt->get_result()->fetch_assoc();

// Get workout records
$workoutsQuery = "SELECT * FROM workout_records WHERE user_id = ? ORDER BY recorded_at DESC LIMIT 20";
$workoutsStmt = $conn->prepare($workoutsQuery);
$workoutsStmt->bind_param("i", $userId);
$workoutsStmt->execute();
$workoutsResult = $workoutsStmt->get_result();
$workouts = [];
while ($workout = $workoutsResult->fetch_assoc()) {
    $workouts[] = $workout;
}

// Get BMI records
$bmiQuery = "SELECT * FROM bmi_records WHERE user_id = ? ORDER BY recorded_at DESC LIMIT 20";
$bmiStmt = $conn->prepare($bmiQuery);
$bmiStmt->bind_param("i", $userId);
$bmiStmt->execute();
$bmiResult = $bmiStmt->get_result();
$bmiRecords = [];
while ($bmi = $bmiResult->fetch_assoc()) {
    $bmiRecords[] = $bmi;
}

// Get course enrollments with progress
$coursesQuery = "SELECT
    e.id as enrollment_id, e.course_id, e.start_date, e.end_date, e.status,
    c.title, c.category, c.duration_weeks,
    (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as total_videos,
    (SELECT COUNT(*) FROM user_video_progress uvp
     JOIN course_videos cv ON uvp.video_id = cv.id
     WHERE cv.course_id = c.id AND uvp.user_id = ? AND uvp.is_unlocked = 1) as unlocked_videos,
    (SELECT COUNT(*) FROM user_video_progress uvp
     JOIN course_videos cv ON uvp.video_id = cv.id
     WHERE cv.course_id = c.id AND uvp.user_id = ? AND uvp.is_completed = 1) as completed_videos
    FROM user_course_enrollments e
    JOIN courses c ON e.course_id = c.id
    WHERE e.user_id = ?
    ORDER BY e.start_date DESC";
$coursesStmt = $conn->prepare($coursesQuery);
$coursesStmt->bind_param("iii", $userId, $userId, $userId);
$coursesStmt->execute();
$coursesResult = $coursesStmt->get_result();
$courses = [];
while ($course = $coursesResult->fetch_assoc()) {
    // Calculate progress percentage
    $course['progress_percentage'] = $course['total_videos'] > 0
        ? round(($course['completed_videos'] / $course['total_videos']) * 100)
        : 0;
    $courses[] = $course;
}

// Get recent activity (combine workouts, BMI updates, and course progress)
$recentActivity = [];

// Add workouts to activity
foreach ($workouts as $workout) {
    // Use a generic workout title
    $workoutTitle = 'Workout';

    // Add workout ID to title if available
    if (isset($workout['workout_id']) && $workout['workout_id'] > 0) {
        $workoutTitle .= ' #' . $workout['workout_id'];
    }

    // Get duration with fallback
    $duration = isset($workout['duration']) ? (int)$workout['duration'] : 0;

    $recentActivity[] = [
        'type' => 'workout',
        'title' => $workoutTitle,
        'duration_minutes' => $duration,
        'timestamp' => $workout['recorded_at'],
        'description' => "Completed a {$duration} minute workout: {$workoutTitle}"
    ];
}

// Add BMI updates to activity
foreach ($bmiRecords as $bmi) {
    $recentActivity[] = [
        'type' => 'bmi_update',
        'bmi' => $bmi['bmi'],
        'weight' => $bmi['weight'],
        'timestamp' => $bmi['recorded_at'],
        'description' => "Updated BMI to {$bmi['bmi']} (Weight: {$bmi['weight']} kg)"
    ];
}

// Add course progress to activity
$progressQuery = "SELECT
    uvp.id, uvp.video_id, uvp.is_completed, uvp.watch_duration_seconds, uvp.updated_at,
    cv.title as video_title, cv.course_id,
    c.title as course_title
    FROM user_video_progress uvp
    JOIN course_videos cv ON uvp.video_id = cv.id
    JOIN courses c ON cv.course_id = c.id
    WHERE uvp.user_id = ?
    ORDER BY uvp.updated_at DESC
    LIMIT 10";
$progressStmt = $conn->prepare($progressQuery);
$progressStmt->bind_param("i", $userId);
$progressStmt->execute();
$progressResult = $progressStmt->get_result();

while ($progress = $progressResult->fetch_assoc()) {
    $recentActivity[] = [
        'type' => 'course_progress',
        'video_title' => $progress['video_title'],
        'course_title' => $progress['course_title'],
        'is_completed' => $progress['is_completed'],
        'timestamp' => $progress['updated_at'],
        'description' => "Watched video: {$progress['video_title']} in course: {$progress['course_title']}"
    ];
}

// Sort all activities by timestamp (most recent first)
usort($recentActivity, function($a, $b) {
    return strtotime($b['timestamp']) - strtotime($a['timestamp']);
});

// Limit to 10 most recent activities
$recentActivity = array_slice($recentActivity, 0, 10);

// Prepare response data
$response = [
    'user' => $user,
    'stats' => $stats,
    'workouts' => $workouts,
    'bmi_records' => $bmiRecords,
    'courses' => $courses,
    'recent_activity' => $recentActivity
];

// Return JSON response
header('Content-Type: application/json');
echo json_encode($response);
exit;
