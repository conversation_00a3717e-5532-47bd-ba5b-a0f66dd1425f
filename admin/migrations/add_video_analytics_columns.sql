-- Add missing columns to video_analytics table
ALTER TABLE video_analytics
ADD COLUMN total_watch_duration INT DEFAULT 0 AFTER total_completions;

ALTER TABLE video_analytics
ADD COLUMN average_watch_duration INT DEFAULT 0 AFTER total_watch_duration;

-- Update existing records to calculate watch duration from video_access_logs
UPDATE video_analytics va
JOIN (
    SELECT video_id,
           SUM(watch_duration_seconds) as total_duration,
           AVG(watch_duration_seconds) as avg_duration
    FROM video_access_logs
    GROUP BY video_id
) logs ON va.video_id = logs.video_id
SET va.total_watch_duration = logs.total_duration,
    va.average_watch_duration = logs.avg_duration; 