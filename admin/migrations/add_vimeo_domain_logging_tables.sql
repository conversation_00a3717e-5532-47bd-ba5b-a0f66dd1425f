-- Migration: Add Vimeo domain verification and direct URL access logging tables
-- Created: 2025-01-27
-- Purpose: Support stable Vimeo video player with domain verification and security logging

-- Table for logging domain access attempts
CREATE TABLE IF NOT EXISTS `domain_access_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `vimeo_id` varchar(50) NOT NULL,
    `video_id` int(11) NOT NULL,
    `domain` varchar(255) NOT NULL,
    `status` enum('verified','failed','unauthorized') NOT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_vimeo_id` (`vimeo_id`),
    KEY `idx_video_id` (`video_id`),
    KEY `idx_domain` (`domain`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for logging direct URL access attempts
CREATE TABLE IF NOT EXISTS `direct_url_access_logs` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `vimeo_id` varchar(50) NOT NULL,
    `video_id` int(11) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `status` enum('success','failed','unauthorized') NOT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_vimeo_id` (`vimeo_id`),
    KEY `idx_video_id` (`video_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for storing Vimeo configuration settings
CREATE TABLE IF NOT EXISTS `vimeo_settings` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `setting_key` varchar(100) NOT NULL UNIQUE,
    `setting_value` text DEFAULT NULL,
    `description` text DEFAULT NULL,
    `is_encrypted` tinyint(1) DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default Vimeo settings
INSERT INTO `vimeo_settings` (`setting_key`, `setting_value`, `description`, `is_encrypted`) VALUES
('vimeo_access_token', '', 'Vimeo API access token for video operations', 1),
('vimeo_domain_verification_enabled', '1', 'Enable domain verification for Vimeo videos', 0),
('vimeo_direct_url_enabled', '1', 'Enable direct URL access for better performance', 0),
('vimeo_allowed_domains', 'com.kft.fitness,http://***************:8080,http://localhost:8080', 'Comma-separated list of allowed domains', 0),
('vimeo_security_logging_enabled', '1', 'Enable security logging for video access', 0),
('vimeo_direct_url_cache_duration', '3600', 'Cache duration for direct URLs in seconds', 0)
ON DUPLICATE KEY UPDATE
    `description` = VALUES(`description`),
    `updated_at` = CURRENT_TIMESTAMP;

-- Table for caching Vimeo direct URLs to improve performance
CREATE TABLE IF NOT EXISTS `vimeo_url_cache` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `vimeo_id` varchar(50) NOT NULL,
    `quality` varchar(20) NOT NULL DEFAULT 'auto',
    `direct_url` text NOT NULL,
    `expires_at` timestamp NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_vimeo_quality` (`vimeo_id`, `quality`),
    KEY `idx_expires_at` (`expires_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Table for tracking video player performance metrics
CREATE TABLE IF NOT EXISTS `video_player_metrics` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `video_id` int(11) NOT NULL,
    `user_id` int(11) DEFAULT NULL,
    `player_type` enum('webview','direct_video','vimeo_widget') NOT NULL,
    `load_time_ms` int(11) DEFAULT NULL,
    `buffer_events` int(11) DEFAULT 0,
    `error_count` int(11) DEFAULT 0,
    `completion_rate` decimal(5,2) DEFAULT NULL,
    `session_duration` int(11) DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_video_id` (`video_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_player_type` (`player_type`),
    KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Note: Indexes for existing tables can be added manually if needed

-- Note: View creation skipped - can be created manually after verifying table structure

-- Create a cleanup procedure for old logs
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS CleanupVideoLogs()
BEGIN
    -- Clean up domain access logs older than 90 days
    DELETE FROM domain_access_logs
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Clean up direct URL access logs older than 90 days
    DELETE FROM direct_url_access_logs
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Clean up expired URL cache entries
    DELETE FROM vimeo_url_cache
    WHERE expires_at < NOW();

    -- Clean up old video player metrics (keep last 30 days)
    DELETE FROM video_player_metrics
    WHERE created_at < DATE_SUB(NOW(), INTERVAL 30 DAY);

    SELECT 'Video logs cleanup completed' as message;
END //
DELIMITER ;

-- Create an event to run cleanup automatically (if event scheduler is enabled)
-- CREATE EVENT IF NOT EXISTS video_logs_cleanup
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO CALL CleanupVideoLogs();
