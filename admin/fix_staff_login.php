<?php
/**
 * Fix Staff Login
 *
 * This script fixes the staff account login issue by updating the staff account in the database.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this tool.');
}

// Define the staff account details
$staffEmail = '<EMAIL>';
$staffUsername = 'staff1';
$newPassword = 'Sadik999';
$message = '';

// Check if the staff account exists
$query = "SELECT * FROM admin_users WHERE email = ? OR username = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ss", $staffEmail, $staffUsername);
$stmt->execute();
$result = $stmt->get_result();

$staffInfo = null;
if ($result->num_rows > 0) {
    $staffInfo = $result->fetch_assoc();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'update_password') {
        $staffId = (int)$_POST['staff_id'];
        $password = $_POST['password'];
        
        // Hash the password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Update the password
        $updateQuery = "UPDATE admin_users SET password = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("si", $hashedPassword, $staffId);
        
        if ($stmt->execute()) {
            $message = "Password updated successfully for staff ID: $staffId";
            
            // Refresh staff info
            $stmt = $conn->prepare($query);
            $stmt->bind_param("ss", $staffEmail, $staffUsername);
            $stmt->execute();
            $result = $stmt->get_result();
            if ($result->num_rows > 0) {
                $staffInfo = $result->fetch_assoc();
            }
        } else {
            $message = "Failed to update password: " . $stmt->error;
        }
    } elseif ($action === 'create_staff') {
        $username = $_POST['username'];
        $email = $_POST['email'];
        $name = $_POST['name'];
        $password = $_POST['password'];
        $phone = $_POST['phone'];
        $parentAdminId = (int)$_POST['parent_admin_id'];
        
        // Hash the password
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        // Check if the staff account already exists
        $checkQuery = "SELECT id FROM admin_users WHERE username = ? OR email = ?";
        $stmt = $conn->prepare($checkQuery);
        $stmt->bind_param("ss", $username, $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $message = "Staff account already exists with this username or email.";
        } else {
            // Create the staff account
            $insertQuery = "INSERT INTO admin_users (username, email, name, password, role, phone, parent_admin_id) VALUES (?, ?, ?, ?, 'staff', ?, ?)";
            $stmt = $conn->prepare($insertQuery);
            $stmt->bind_param("sssssi", $username, $email, $name, $hashedPassword, $phone, $parentAdminId);
            
            if ($stmt->execute()) {
                $staffId = $conn->insert_id;
                $message = "Staff account created successfully with ID: $staffId";
                
                // Refresh staff info
                $stmt = $conn->prepare($query);
                $stmt->bind_param("ss", $email, $username);
                $stmt->execute();
                $result = $stmt->get_result();
                if ($result->num_rows > 0) {
                    $staffInfo = $result->fetch_assoc();
                }
            } else {
                $message = "Failed to create staff account: " . $stmt->error;
            }
        }
    } elseif ($action === 'test_login') {
        $username = $_POST['username'];
        $password = $_POST['password'];
        
        // Test the login
        if ($auth->login($username, $password)) {
            $message = "Login successful for user: $username";
            // Logout immediately to avoid session conflicts
            $auth->logout();
        } else {
            $message = "Login failed for user: $username";
        }
    }
}

// Get all super admins for parent selection
$superAdminsQuery = "SELECT id, username, name FROM admin_users WHERE role = 'super_admin'";
$superAdminsResult = $conn->query($superAdminsQuery);
$superAdmins = [];
if ($superAdminsResult && $superAdminsResult->num_rows > 0) {
    while ($row = $superAdminsResult->fetch_assoc()) {
        $superAdmins[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Staff Login</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Fix Staff Login</h1>
        <p>This tool helps fix the staff account login issue.</p>
        
        <?php if (!empty($message)): ?>
        <div class="alert alert-info">
            <?php echo $message; ?>
        </div>
        <?php endif; ?>
        
        <div class="section">
            <h2>Staff Account Information</h2>
            <?php if ($staffInfo): ?>
            <table class="table table-bordered">
                <tr>
                    <th style="width: 150px;">ID</th>
                    <td><?php echo $staffInfo['id']; ?></td>
                </tr>
                <tr>
                    <th>Username</th>
                    <td><?php echo htmlspecialchars($staffInfo['username']); ?></td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td><?php echo htmlspecialchars($staffInfo['email']); ?></td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td><?php echo htmlspecialchars($staffInfo['name']); ?></td>
                </tr>
                <tr>
                    <th>Role</th>
                    <td><?php echo htmlspecialchars($staffInfo['role']); ?></td>
                </tr>
                <tr>
                    <th>Password Hash</th>
                    <td>
                        <div style="word-break: break-all;">
                            <?php echo $staffInfo['password']; ?>
                        </div>
                    </td>
                </tr>
            </table>
            
            <div class="mt-3">
                <h3>Update Password</h3>
                <form method="post" action="">
                    <input type="hidden" name="action" value="update_password">
                    <input type="hidden" name="staff_id" value="<?php echo $staffInfo['id']; ?>">
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <input type="text" class="form-control" id="password" name="password" value="Sadik999" required>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Update Password</button>
                </form>
            </div>
            
            <div class="mt-3">
                <h3>Test Login</h3>
                <form method="post" action="">
                    <input type="hidden" name="action" value="test_login">
                    
                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($staffInfo['username']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="test_password" class="form-label">Password</label>
                        <input type="text" class="form-control" id="test_password" name="password" value="Sadik999" required>
                    </div>
                    
                    <button type="submit" class="btn btn-success">Test Login</button>
                </form>
            </div>
            <?php else: ?>
            <div class="alert alert-warning">
                Staff account not found with email <?php echo htmlspecialchars($staffEmail); ?> or username <?php echo htmlspecialchars($staffUsername); ?>.
            </div>
            
            <div class="mt-3">
                <h3>Create Staff Account</h3>
                <form method="post" action="">
                    <input type="hidden" name="action" value="create_staff">
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="create_username" name="username" value="staff1" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="create_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="create_email" name="email" value="<EMAIL>" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_name" class="form-label">Name</label>
                            <input type="text" class="form-control" id="create_name" name="name" value="Staff User" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="create_password" class="form-label">Password</label>
                            <input type="text" class="form-control" id="create_password" name="password" value="Sadik999" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="create_phone" class="form-label">Phone</label>
                            <input type="text" class="form-control" id="create_phone" name="phone" value="1234567890" required>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="create_parent_admin_id" class="form-label">Parent Admin</label>
                            <select class="form-select" id="create_parent_admin_id" name="parent_admin_id" required>
                                <option value="">Select Parent Admin</option>
                                <?php foreach ($superAdmins as $admin): ?>
                                <option value="<?php echo $admin['id']; ?>"><?php echo htmlspecialchars($admin['name'] . ' (' . $admin['username'] . ')'); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">Create Staff Account</button>
                </form>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="mt-4">
            <a href="index.php" class="btn btn-secondary">Back to Dashboard</a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
