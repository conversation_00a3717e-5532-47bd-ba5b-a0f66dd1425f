<?php
require_once 'includes/header.php';
header('Content-Type: application/json');

if (!$auth->hasRole('super_admin')) {
    http_response_code(403);
    echo json_encode(['error' => 'Forbidden']);
    exit;
}

$db = new Database();
$conn = $db->getConnection();

// Get new users registered in the last 7 days
$query = "SELECT DATE(created_at) as reg_date, COUNT(*) as count FROM users WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) GROUP BY DATE(created_at) ORDER BY reg_date";
$result = $conn->query($query);
$dates = [];
$counts = [];
if ($result && $result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $dates[] = date('M d', strtotime($row['reg_date']));
        $counts[] = (int)$row['count'];
    }
}
echo json_encode(['dates' => $dates, 'counts' => $counts]); 