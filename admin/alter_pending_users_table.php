<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Add status field to pending_users table if it doesn't exist
$alterTableQuery = "
ALTER TABLE pending_users 
ADD COLUMN IF NOT EXISTS status ENUM('pending', 'approved', 'not_interested') NOT NULL DEFAULT 'pending',
ADD COLUMN IF NOT EXISTS status_updated_at TIMESTAMP NULL DEFAULT NULL,
ADD COLUMN IF NOT EXISTS status_updated_by INT NULL DEFAULT NULL
";

// Execute the query
if ($conn->query($alterTableQuery) === TRUE) {
    echo "Table 'pending_users' altered successfully. Added status field.<br>";
} else {
    echo "Error altering table: " . $conn->error . "<br>";
}

// Create the pending_users table if it doesn't exist
$createTableQuery = "
CREATE TABLE IF NOT EXISTS `pending_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT '',
  `phone_number` varchar(20) NOT NULL,
  `status` ENUM('pending', 'approved', 'not_interested') NOT NULL DEFAULT 'pending',
  `status_updated_at` TIMESTAMP NULL DEFAULT NULL,
  `status_updated_by` INT NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `phone_number` (`phone_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the query
if ($conn->query($createTableQuery) === TRUE) {
    echo "Table 'pending_users' created successfully or already exists.<br>";
} else {
    echo "Error creating table: " . $conn->error . "<br>";
}

echo "Done!";
?>
