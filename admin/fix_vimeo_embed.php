<?php
require_once 'includes/config.php';

/**
 * Comprehensive Vimeo Embed Fix Script
 * This script provides multiple solutions to fix the Vimeo video playback issue
 */

// Vimeo API credentials
$clientId = 'eb6edcd564c33510af4f3a09a8c40aa7d43b2b87';
$clientSecret = 'KoOnKaeyxukkGZIdKTf4FMi5hoxK7a7S/rdlMwszUL0C2y2ClgaYpV4gdKN/42gHTpINyapIU9/wsPRexb+kbqr7qv8s5t1S+bMAO2RP3EGpGYR41gPL7cM4NKGZfyHR';
$accessToken = 'e181f678c92844bd2be36504f316fabd';

$videoId = '1087487482';

echo "=== VIMEO EMBED FIX DIAGNOSTIC ===\n\n";

// Step 1: Check current video settings
echo "1. CHECKING CURRENT VIDEO SETTINGS...\n";
$currentSettings = checkVideoSettings($accessToken, $videoId);
if ($currentSettings) {
    echo "✓ Video found and accessible\n";
    echo "  Title: " . $currentSettings['name'] . "\n";
    echo "  Privacy View: " . $currentSettings['privacy']['view'] . "\n";
    echo "  Privacy Embed: " . $currentSettings['privacy']['embed'] . "\n";
    echo "  Embed Domains: " . json_encode($currentSettings['privacy']['embed_domains']) . "\n";
    echo "  Status: " . $currentSettings['status'] . "\n\n";
} else {
    echo "✗ Failed to get video settings\n\n";
}

// Step 2: Test current embed URL
echo "2. TESTING CURRENT EMBED URLS...\n";
$privateUrl = 'https://vimeo.com/1087487482/ae75b6e329';
$embedUrl = 'https://player.vimeo.com/video/1087487482?h=ae75b6e329';

echo "Private URL: $privateUrl\n";
echo "Embed URL: $embedUrl\n";

// Test if URLs are accessible
$embedTest = testEmbedUrl($embedUrl);
echo "Embed URL Test: " . ($embedTest ? "✓ Accessible" : "✗ Blocked") . "\n\n";

// Step 3: Provide solutions
echo "3. SOLUTIONS TO FIX THE ISSUE:\n\n";

echo "SOLUTION 1: Change Embed Privacy to 'Anywhere' (RECOMMENDED)\n";
echo "-----------------------------------------------------------\n";
echo "1. Go to https://vimeo.com/manage/videos\n";
echo "2. Find video: CUSTOMISED WORKOUT 1[No Jumping] (ID: 1087487482)\n";
echo "3. Click 'Settings' → 'Privacy'\n";
echo "4. Under 'Where can this be embedded?', select 'Anywhere'\n";
echo "5. Save changes\n\n";

echo "SOLUTION 2: Add Specific Domains to Whitelist\n";
echo "----------------------------------------------\n";
echo "1. Go to video settings → Privacy → Embed\n";
echo "2. Keep 'Specific domains' selected\n";
echo "3. Add these domains:\n";
echo "   - ***************\n";
echo "   - localhost\n";
echo "   - *.kft.fitness\n";
echo "   - com.kft.fitness\n";
echo "4. Save changes\n\n";

echo "SOLUTION 3: Use Alternative Embed Method\n";
echo "-----------------------------------------\n";
echo "If privacy settings cannot be changed, we can:\n";
echo "1. Use a different video hosting service\n";
echo "2. Upload the video as 'Unlisted' instead of private\n";
echo "3. Use Vimeo Pro features for better domain control\n\n";

// Step 4: Generate alternative embed URLs
echo "4. ALTERNATIVE EMBED URLS TO TRY:\n\n";

$alternativeUrls = [
    'Basic embed' => "https://player.vimeo.com/video/$videoId",
    'With hash' => "https://player.vimeo.com/video/$videoId?h=ae75b6e329",
    'No restrictions' => "https://player.vimeo.com/video/$videoId?h=ae75b6e329&dnt=1&app_id=kft_fitness",
    'Mobile optimized' => "https://player.vimeo.com/video/$videoId?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1",
];

foreach ($alternativeUrls as $name => $url) {
    echo "$name: $url\n";
}

echo "\n5. TESTING RECOMMENDATIONS:\n\n";
echo "After making changes to Vimeo settings:\n";
echo "1. Wait 5-10 minutes for changes to propagate\n";
echo "2. Test the embed URL in a browser: $embedUrl\n";
echo "3. Clear Flutter app cache and restart\n";
echo "4. Test video playback in the app\n\n";

// Step 5: Update database with corrected URLs if needed
echo "6. DATABASE UPDATE (if needed):\n\n";
try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check current video URL in database
    $stmt = $pdo->prepare("SELECT id, title, video_url, video_embed_url FROM course_videos WHERE id = 1");
    $stmt->execute();
    $videoData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($videoData) {
        echo "Current database entry:\n";
        echo "  ID: " . $videoData['id'] . "\n";
        echo "  Title: " . $videoData['title'] . "\n";
        echo "  Video URL: " . $videoData['video_url'] . "\n";
        echo "  Embed URL: " . $videoData['video_embed_url'] . "\n\n";
        
        // Generate corrected embed URL
        $correctedEmbedUrl = "https://player.vimeo.com/video/$videoId?h=ae75b6e329&autoplay=0&title=0&byline=0&portrait=0&responsive=1&dnt=1";
        
        echo "Suggested corrected embed URL:\n";
        echo "  $correctedEmbedUrl\n\n";
        
        echo "To update database, run:\n";
        echo "UPDATE course_videos SET video_embed_url = '$correctedEmbedUrl' WHERE id = 1;\n\n";
    }
} catch (Exception $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n\n";
}

echo "=== END OF DIAGNOSTIC ===\n";

// Helper functions
function checkVideoSettings($accessToken, $videoId) {
    $url = "https://api.vimeo.com/videos/$videoId";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer $accessToken",
        "Content-Type: application/json"
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

function testEmbedUrl($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return $httpCode === 200;
}
?>
