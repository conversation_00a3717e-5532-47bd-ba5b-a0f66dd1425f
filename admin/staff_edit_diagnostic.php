<?php
/**
 * Staff Edit Diagnostic Tool
 * 
 * This script helps diagnose issues with staff editing functionality.
 * It provides detailed information about the staff member and their permissions.
 */

require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Enable error reporting for diagnostics
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    die('You must be a super admin to access this diagnostic tool.');
}

// Check if staff ID is provided
$staffId = isset($_GET['id']) ? (int)$_GET['id'] : null;

// Get staff information if ID is provided
$staffInfo = null;
$staffPermissions = [];
$availablePermissions = [];
$superAdmins = [];

if ($staffId) {
    // Get staff information
    $staffQuery = "SELECT * FROM admin_users WHERE id = ? AND role = 'staff'";
    $stmt = $conn->prepare($staffQuery);
    $stmt->bind_param("i", $staffId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $staffInfo = $result->fetch_assoc();
        
        // Get staff permissions
        $permissionsQuery = "SELECT p.id, p.name, p.slug, p.description 
                            FROM admin_user_permissions up 
                            JOIN admin_permissions p ON up.permission_id = p.id 
                            WHERE up.admin_user_id = ?";
        $stmt = $conn->prepare($permissionsQuery);
        $stmt->bind_param("i", $staffId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        while ($row = $result->fetch_assoc()) {
            $staffPermissions[] = $row;
        }
    }
}

// Get all available permissions
$permissionsQuery = "SELECT id, name, slug, description FROM admin_permissions ORDER BY name";
$result = $conn->query($permissionsQuery);
while ($row = $result->fetch_assoc()) {
    $availablePermissions[] = $row;
}

// Get all super admins
$superAdminsQuery = "SELECT id, username, name FROM admin_users WHERE role = 'super_admin'";
$result = $conn->query($superAdminsQuery);
while ($row = $result->fetch_assoc()) {
    $superAdmins[] = $row;
}

// Process form submission for testing update
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_update') {
    $testStaffId = (int)$_POST['staff_id'];
    $testUsername = Utilities::sanitizeInput($_POST['username']);
    $testEmail = Utilities::sanitizeInput($_POST['email']);
    $testName = Utilities::sanitizeInput($_POST['name']);
    $testPhone = Utilities::sanitizeInput($_POST['phone']);
    $testParentAdminId = (int)$_POST['parent_admin_id'];
    $testPermissions = isset($_POST['permissions']) ? $_POST['permissions'] : [];
    
    echo "<div style='background:#f8f9fa;padding:15px;margin-bottom:20px;border-radius:5px;'>";
    echo "<h3>Test Update Results</h3>";
    
    try {
        // Start transaction
        $conn->begin_transaction();
        
        // Update staff member
        $updateQuery = "UPDATE admin_users SET username = ?, email = ?, name = ?, phone = ?, parent_admin_id = ? WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ssssii", $testUsername, $testEmail, $testName, $testPhone, $testParentAdminId, $testStaffId);
        
        echo "<p>Executing query: $updateQuery</p>";
        echo "<p>Parameters: username=$testUsername, email=$testEmail, name=$testName, phone=$testPhone, parent_admin_id=$testParentAdminId, staff_id=$testStaffId</p>";
        
        if ($stmt->execute()) {
            echo "<p style='color:green'>✓ Staff information updated successfully.</p>";
            
            // Delete existing permissions
            $deleteQuery = "DELETE FROM admin_user_permissions WHERE admin_user_id = ?";
            $stmt = $conn->prepare($deleteQuery);
            $stmt->bind_param("i", $testStaffId);
            
            if ($stmt->execute()) {
                echo "<p style='color:green'>✓ Existing permissions deleted successfully.</p>";
                
                // Insert new permissions
                if (!empty($testPermissions)) {
                    $insertQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
                    $stmt = $conn->prepare($insertQuery);
                    
                    $successCount = 0;
                    $failCount = 0;
                    
                    foreach ($testPermissions as $permissionId) {
                        $permissionId = (int)$permissionId;
                        $stmt->bind_param("ii", $testStaffId, $permissionId);
                        
                        if ($stmt->execute()) {
                            $successCount++;
                        } else {
                            $failCount++;
                            echo "<p style='color:red'>✗ Failed to insert permission ID $permissionId: " . $stmt->error . "</p>";
                        }
                    }
                    
                    echo "<p style='color:green'>✓ $successCount permissions inserted successfully.</p>";
                    if ($failCount > 0) {
                        echo "<p style='color:red'>✗ $failCount permissions failed to insert.</p>";
                    }
                } else {
                    echo "<p style='color:blue'>ℹ No permissions selected for insertion.</p>";
                }
            } else {
                echo "<p style='color:red'>✗ Failed to delete existing permissions: " . $stmt->error . "</p>";
            }
        } else {
            echo "<p style='color:red'>✗ Failed to update staff information: " . $stmt->error . "</p>";
        }
        
        // Commit transaction
        $conn->commit();
        echo "<p style='color:green'>✓ Transaction committed successfully.</p>";
        
        // Refresh staff info
        header("Location: staff_edit_diagnostic.php?id=$testStaffId&updated=1");
        exit;
    } catch (Exception $e) {
        // Rollback transaction
        $conn->rollback();
        echo "<p style='color:red'>✗ Error occurred: " . $e->getMessage() . "</p>";
        echo "<p style='color:red'>✗ Transaction rolled back.</p>";
    }
    
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Staff Edit Diagnostic Tool</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { padding: 20px; }
        .diagnostic-section { margin-bottom: 30px; padding: 20px; border-radius: 5px; background-color: #f8f9fa; }
        .permission-item { margin-bottom: 10px; padding: 10px; border-radius: 5px; background-color: #fff; }
        .test-form { background-color: #e9ecef; padding: 20px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Staff Edit Diagnostic Tool</h1>
        <p>This tool helps diagnose issues with staff editing functionality.</p>
        
        <?php if (isset($_GET['updated']) && $_GET['updated'] == 1): ?>
        <div class="alert alert-success">
            Staff member updated successfully! The page has been refreshed with the latest data.
        </div>
        <?php endif; ?>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Select Staff Member</div>
                    <div class="card-body">
                        <form method="get" action="staff_edit_diagnostic.php">
                            <div class="mb-3">
                                <label for="staff_select" class="form-label">Select a staff member:</label>
                                <select class="form-select" id="staff_select" name="id" onchange="this.form.submit()">
                                    <option value="">-- Select Staff --</option>
                                    <?php
                                    $staffQuery = "SELECT id, username, name FROM admin_users WHERE role = 'staff' ORDER BY name";
                                    $result = $conn->query($staffQuery);
                                    while ($row = $result->fetch_assoc()) {
                                        $selected = ($staffId == $row['id']) ? 'selected' : '';
                                        echo "<option value=\"{$row['id']}\" $selected>{$row['name']} ({$row['username']})</option>";
                                    }
                                    ?>
                                </select>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">Quick Links</div>
                    <div class="card-body">
                        <a href="settings.php" class="btn btn-primary">Back to Settings</a>
                        <?php if ($staffId): ?>
                        <a href="admin_edit.php?id=<?php echo $staffId; ?>" class="btn btn-success">Edit Staff Member</a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($staffInfo): ?>
        <div class="diagnostic-section">
            <h2>Staff Information</h2>
            <table class="table table-bordered">
                <tr>
                    <th style="width: 150px;">ID</th>
                    <td><?php echo $staffInfo['id']; ?></td>
                </tr>
                <tr>
                    <th>Username</th>
                    <td><?php echo htmlspecialchars($staffInfo['username']); ?></td>
                </tr>
                <tr>
                    <th>Name</th>
                    <td><?php echo htmlspecialchars($staffInfo['name']); ?></td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td><?php echo htmlspecialchars($staffInfo['email']); ?></td>
                </tr>
                <tr>
                    <th>Phone</th>
                    <td><?php echo htmlspecialchars($staffInfo['phone']); ?></td>
                </tr>
                <tr>
                    <th>Parent Admin</th>
                    <td>
                        <?php
                        $parentId = $staffInfo['parent_admin_id'];
                        $parentName = "None";
                        foreach ($superAdmins as $admin) {
                            if ($admin['id'] == $parentId) {
                                $parentName = htmlspecialchars($admin['name'] . ' (' . $admin['username'] . ')');
                                break;
                            }
                        }
                        echo $parentName;
                        ?>
                    </td>
                </tr>
                <tr>
                    <th>Created At</th>
                    <td><?php echo $staffInfo['created_at']; ?></td>
                </tr>
                <tr>
                    <th>Last Login</th>
                    <td><?php echo $staffInfo['last_login'] ?: 'Never'; ?></td>
                </tr>
            </table>
        </div>
        
        <div class="diagnostic-section">
            <h2>Staff Permissions (<?php echo count($staffPermissions); ?>)</h2>
            <?php if (empty($staffPermissions)): ?>
            <div class="alert alert-warning">This staff member has no permissions assigned.</div>
            <?php else: ?>
            <div class="row">
                <?php foreach ($staffPermissions as $permission): ?>
                <div class="col-md-4">
                    <div class="permission-item">
                        <h5><?php echo htmlspecialchars($permission['name']); ?></h5>
                        <p class="text-muted"><?php echo htmlspecialchars($permission['description']); ?></p>
                        <small class="text-secondary">Slug: <?php echo htmlspecialchars($permission['slug']); ?></small>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <div class="diagnostic-section">
            <h2>Test Update Form</h2>
            <p>Use this form to test updating the staff member without affecting the actual admin_edit.php page.</p>
            
            <form method="post" action="staff_edit_diagnostic.php?id=<?php echo $staffId; ?>" class="test-form">
                <input type="hidden" name="action" value="test_update">
                <input type="hidden" name="staff_id" value="<?php echo $staffId; ?>">
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username" value="<?php echo htmlspecialchars($staffInfo['username']); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" class="form-control" id="email" name="email" value="<?php echo htmlspecialchars($staffInfo['email']); ?>" required>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="name" class="form-label">Name</label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo htmlspecialchars($staffInfo['name']); ?>" required>
                    </div>
                    <div class="col-md-6">
                        <label for="phone" class="form-label">Phone</label>
                        <input type="text" class="form-control" id="phone" name="phone" value="<?php echo htmlspecialchars($staffInfo['phone']); ?>" required>
                    </div>
                </div>
                
                <div class="mb-3">
                    <label for="parent_admin_id" class="form-label">Parent Admin</label>
                    <select class="form-select" id="parent_admin_id" name="parent_admin_id" required>
                        <option value="">-- Select Parent Admin --</option>
                        <?php foreach ($superAdmins as $admin): ?>
                        <option value="<?php echo $admin['id']; ?>" <?php echo ($staffInfo['parent_admin_id'] == $admin['id']) ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($admin['name'] . ' (' . $admin['username'] . ')'); ?>
                        </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                
                <div class="mb-3">
                    <label class="form-label">Permissions</label>
                    <div class="row">
                        <?php 
                        $currentPermissionIds = array_map(function($p) { return $p['id']; }, $staffPermissions);
                        foreach ($availablePermissions as $permission): 
                        ?>
                        <div class="col-md-4 mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="perm_<?php echo $permission['id']; ?>" 
                                       name="permissions[]" value="<?php echo $permission['id']; ?>"
                                       <?php echo in_array($permission['id'], $currentPermissionIds) ? 'checked' : ''; ?>>
                                <label class="form-check-label" for="perm_<?php echo $permission['id']; ?>">
                                    <?php echo htmlspecialchars($permission['name']); ?>
                                </label>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-primary">Test Update</button>
            </form>
        </div>
        <?php else: ?>
        <div class="alert alert-info">
            Please select a staff member to view diagnostic information.
        </div>
        <?php endif; ?>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
