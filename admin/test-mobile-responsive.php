<?php
/**
 * Test page for the enhanced responsive admin panel with mobile experience
 * This page demonstrates the new mobile-first responsive design
 */

// Include authentication and configuration
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize authentication
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Set page title
$pageTitle = 'Mobile Responsive Test';

// Include header
require_once 'includes/header.php';
?>

<div class="row">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Enhanced Mobile Responsive Admin Panel
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>New Features Implemented</h6>
                    <ul class="mb-0">
                        <li><strong>Mobile Bottom Navigation:</strong> Native app-like experience on mobile devices</li>
                        <li><strong>Fixed Spacing Issues:</strong> Eliminated negative margins and padding problems</li>
                        <li><strong>Smooth Animations:</strong> Enhanced transitions and haptic feedback</li>
                        <li><strong>Touch Optimized:</strong> 44px minimum touch targets for mobile</li>
                        <li><strong>Session Timeout:</strong> Updated to 5 hours as requested</li>
                    </ul>
                </div>

                <div class="row g-4">
                    <div class="col-md-4">
                        <div class="card bg-light h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-mobile-alt me-2 text-primary"></i>
                                    Mobile Experience
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success me-2"></i>Bottom navigation bar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>5 key navigation items</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Haptic feedback animations</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Full-screen content area</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Native app feel</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-light h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-tablet-alt me-2 text-info"></i>
                                    Tablet Experience
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success me-2"></i>Overlay sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Touch-friendly interface</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Proper spacing</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Smooth transitions</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Auto-close navigation</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card bg-light h-100">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-desktop me-2 text-warning"></i>
                                    Desktop Experience
                                </h6>
                                <ul class="list-unstyled mb-0 small">
                                    <li><i class="fas fa-check text-success me-2"></i>Fixed sidebar spacing</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Collapsible sidebar</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Hover effects</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Keyboard navigation</li>
                                    <li><i class="fas fa-check text-success me-2"></i>Premium aesthetics</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Mobile Bottom Navigation Items:</h6>
                    <div class="row g-3">
                        <div class="col-6 col-md-2">
                            <div class="d-flex flex-column align-items-center p-3 border rounded">
                                <i class="fas fa-tachometer-alt mb-2 text-primary" style="font-size: 1.5rem;"></i>
                                <small class="text-center">Dashboard</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-2">
                            <div class="d-flex flex-column align-items-center p-3 border rounded">
                                <i class="fas fa-users mb-2 text-primary" style="font-size: 1.5rem;"></i>
                                <small class="text-center">Users</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-2">
                            <div class="d-flex flex-column align-items-center p-3 border rounded">
                                <i class="fas fa-graduation-cap mb-2 text-primary" style="font-size: 1.5rem;"></i>
                                <small class="text-center">Courses</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-2">
                            <div class="d-flex flex-column align-items-center p-3 border rounded">
                                <i class="fas fa-users-cog mb-2 text-primary" style="font-size: 1.5rem;"></i>
                                <small class="text-center">Staff</small>
                            </div>
                        </div>
                        <div class="col-6 col-md-2">
                            <div class="d-flex flex-column align-items-center p-3 border rounded">
                                <i class="fas fa-cogs mb-2 text-primary" style="font-size: 1.5rem;"></i>
                                <small class="text-center">Settings</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <h6>Testing Instructions:</h6>
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="alert alert-info">
                                <strong>Mobile Testing (< 768px):</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Bottom navigation should be visible</li>
                                    <li>Sidebar should be hidden</li>
                                    <li>Touch targets should be 44px minimum</li>
                                    <li>Haptic feedback on touch</li>
                                </ul>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="alert alert-warning">
                                <strong>Desktop Testing (> 992px):</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Sidebar should be visible</li>
                                    <li>Bottom navigation should be hidden</li>
                                    <li>No negative spacing issues</li>
                                    <li>Smooth hover effects</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4 p-3 bg-success bg-opacity-10 border border-success rounded">
                    <h6 class="text-success">
                        <i class="fas fa-tools me-2"></i>
                        Fixed Issues
                    </h6>
                    <ul class="mb-0">
                        <li><strong>Staff Management Page:</strong> Fixed layout to work with new responsive panel</li>
                        <li><strong>Negative Margins:</strong> Eliminated all negative spacing issues</li>
                        <li><strong>Mobile Navigation:</strong> Added native app-like bottom navigation</li>
                        <li><strong>Touch Targets:</strong> Ensured 44px minimum for mobile accessibility</li>
                        <li><strong>Animations:</strong> Added smooth transitions and haptic feedback</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Test script for mobile responsive features
document.addEventListener('DOMContentLoaded', function() {
    console.log('Enhanced Mobile Responsive Admin Panel Test Loaded');
    
    // Log current device type and features
    function logDeviceInfo() {
        const width = window.innerWidth;
        let device = 'Desktop';
        let features = [];
        
        if (width <= 767.98) {
            device = 'Mobile';
            features = ['Bottom Navigation', 'Haptic Feedback', 'Full Screen'];
        } else if (width <= 991.98) {
            device = 'Tablet';
            features = ['Overlay Sidebar', 'Touch Optimized', 'Auto-close'];
        } else {
            device = 'Desktop';
            features = ['Fixed Sidebar', 'Hover Effects', 'Keyboard Nav'];
        }
        
        console.log(`Device: ${device} (${width}px)`);
        console.log(`Features: ${features.join(', ')}`);
        
        // Check if bottom nav is visible
        const bottomNav = document.querySelector('.mobile-bottom-nav');
        if (bottomNav) {
            const isVisible = window.getComputedStyle(bottomNav).display !== 'none';
            console.log(`Bottom Navigation Visible: ${isVisible}`);
        }
        
        // Check if sidebar is visible
        const sidebar = document.querySelector('.admin-sidebar');
        if (sidebar) {
            const isVisible = sidebar.classList.contains('show');
            console.log(`Sidebar Visible: ${isVisible}`);
        }
    }
    
    logDeviceInfo();
    window.addEventListener('resize', logDeviceInfo);
    
    // Test haptic feedback
    const bottomNavItems = document.querySelectorAll('.bottom-nav-item');
    bottomNavItems.forEach(item => {
        item.addEventListener('touchstart', () => {
            console.log('Haptic feedback triggered');
        });
    });
});
</script>

<?php require_once 'includes/footer.php'; ?>
