<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/auth.php';
require_once 'includes/utilities.php';

// Initialize auth and database
$auth = new Auth();
$db = new Database();
$conn = $db->getConnection();

// Check if user has super admin role
if (!$auth->hasRole('super_admin')) {
    Utilities::setFlashMessage('error', 'You do not have permission to update staff permissions.');
    Utilities::redirect('index.php');
    exit;
}

// Check if form was submitted
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    Utilities::setFlashMessage('error', 'Invalid request method.');
    Utilities::redirect('staff_management.php');
    exit;
}

// Check if staff ID is provided
if (!isset($_POST['staff_id']) || !is_numeric($_POST['staff_id'])) {
    Utilities::setFlashMessage('error', 'Invalid staff ID.');
    Utilities::redirect('staff_management.php');
    exit;
}

$staffId = (int)$_POST['staff_id'];

// Verify staff exists and is a staff member
$staffQuery = "SELECT id, name FROM admin_users WHERE id = ? AND role = 'staff'";
$staffStmt = $conn->prepare($staffQuery);
$staffStmt->bind_param("i", $staffId);
$staffStmt->execute();
$staffResult = $staffStmt->get_result();

if ($staffResult->num_rows === 0) {
    Utilities::setFlashMessage('error', 'Staff member not found.');
    Utilities::redirect('staff_management.php');
    exit;
}

$staffInfo = $staffResult->fetch_assoc();

// Begin transaction
$conn->begin_transaction();

try {
    // Delete existing permissions
    $deleteQuery = "DELETE FROM admin_user_permissions WHERE admin_user_id = ?";
    $deleteStmt = $conn->prepare($deleteQuery);
    $deleteStmt->bind_param("i", $staffId);
    $deleteStmt->execute();
    
    // Insert new permissions if any were selected
    if (isset($_POST['permissions']) && is_array($_POST['permissions']) && !empty($_POST['permissions'])) {
        $insertQuery = "INSERT INTO admin_user_permissions (admin_user_id, permission_id) VALUES (?, ?)";
        $insertStmt = $conn->prepare($insertQuery);
        
        foreach ($_POST['permissions'] as $permissionId) {
            $permissionId = (int)$permissionId;
            $insertStmt->bind_param("ii", $staffId, $permissionId);
            $insertStmt->execute();
        }
    }
    
    // Commit transaction
    $conn->commit();
    
    Utilities::setFlashMessage('success', 'Permissions updated successfully for ' . htmlspecialchars($staffInfo['name']));
} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollback();
    Utilities::setFlashMessage('error', 'Failed to update permissions: ' . $e->getMessage());
}

// Redirect back to staff management page
Utilities::redirect('staff_management.php');
?>
