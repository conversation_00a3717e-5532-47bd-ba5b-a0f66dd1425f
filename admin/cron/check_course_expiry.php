<?php
require_once '../includes/config.php';
require_once '../includes/database.php';
require_once '../includes/utilities.php';
require_once '../includes/audit_logger.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Get all active enrollments that have expired
$query = "SELECT uce.id, uce.user_id, uce.course_id, uce.end_date, u.name as user_name, c.title as course_title
          FROM user_course_enrollments uce
          JOIN users u ON uce.user_id = u.id
          JOIN courses c ON uce.course_id = c.id
          WHERE uce.status = 'active' 
          AND uce.end_date < CURDATE()";

$result = $conn->query($query);

if ($result && $result->num_rows > 0) {
    while ($enrollment = $result->fetch_assoc()) {
        // Start transaction
        $conn->begin_transaction();

        try {
            // Update enrollment status to expired
            $updateQuery = "UPDATE user_course_enrollments 
                           SET status = 'expired', 
                               updated_at = NOW() 
                           WHERE id = ?";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->bind_param("i", $enrollment['id']);
            $updateStmt->execute();

            // Lock all videos for this course
            $lockQuery = "UPDATE user_video_progress uvp
                         JOIN course_videos cv ON uvp.video_id = cv.id
                         SET uvp.is_unlocked = 0,
                             uvp.updated_at = NOW()
                         WHERE cv.course_id = ? AND uvp.user_id = ?";
            $lockStmt = $conn->prepare($lockQuery);
            $lockStmt->bind_param("ii", $enrollment['course_id'], $enrollment['user_id']);
            $lockStmt->execute();

            // Log the expiration
            $details = "Course '{$enrollment['course_title']}' expired for user {$enrollment['user_name']} on {$enrollment['end_date']}";
            logSystemActivity($conn, 'course_expired', $enrollment['user_id'], $details);

            // Commit transaction
            $conn->commit();

            // Send notification to user (you can implement this based on your notification system)
            // sendExpirationNotification($enrollment['user_id'], $enrollment['course_title']);

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            error_log("Error processing course expiration: " . $e->getMessage());
        }
    }
}

// Function to log system activities
function logSystemActivity($conn, $action_type, $user_id, $details) {
    $query = "INSERT INTO system_activity_logs (action_type, user_id, details, created_at)
              VALUES (?, ?, ?, NOW())";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("sis", $action_type, $user_id, $details);
    $stmt->execute();
}

// Close database connection
$conn->close(); 