<?php
require_once 'includes/header.php';
require_once 'includes/user_helpers.php';

// Custom styles for the enhanced courses page
?>
<style>
    /* WhatsApp number styles */
    .whatsapp-number-container {
        min-width: 180px;
    }

    .whatsapp-value {
        max-width: 140px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* Enhanced course card styles */
    .course-card {
        transition: all 0.2s ease;
        border-left: 4px solid transparent;
    }

    .course-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    }

    .course-card.active {
        border-left-color: #28a745;
    }

    .course-card.inactive {
        border-left-color: #6c757d;
    }

    /* Status badge styles */
    .status-badge {
        transition: all 0.2s ease;
    }

    .status-badge:hover {
        transform: scale(1.05);
    }

    .course-title-container {
        position: relative;
    }

    /* Filter card styles */
    .filter-card {
        border-top: 4px solid #0d6efd;
    }

    /* Essential stats card styles */
    .stats-card {
        transition: all 0.2s ease;
        border-radius: 6px;
        border-left: 3px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .stats-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
    }

    .stats-card.primary {
        border-left-color: #0d6efd;
    }

    .stats-card.success {
        border-left-color: #198754;
    }

    .stats-card.info {
        border-left-color: #0dcaf0;
    }

    .stats-card.warning {
        border-left-color: #ffc107;
    }

    .stats-card .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 6px;
        transition: all 0.2s ease;
    }

    .stats-card:hover .icon-wrapper {
        transform: scale(1.05);
    }

    .stats-card .icon {
        font-size: 1.1rem;
    }

    .stats-card .stat-value {
        font-size: 1.75rem;
        font-weight: 600;
        line-height: 1.2;
        margin-bottom: 0;
        transition: all 0.2s ease;
    }

    .stats-card:hover .stat-value {
        transform: scale(1.02);
    }

    .stats-card .stat-label {
        font-size: 0.8rem;
        font-weight: 500;
        color: #6c757d;
        margin-bottom: 0;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .stats-card .stat-progress {
        height: 4px;
        margin: 0.75rem 0;
        background-color: rgba(0,0,0,0.05);
        border-radius: 2px;
        overflow: hidden;
    }

    .stats-card .stat-progress-bar {
        height: 100%;
        border-radius: 2px;
        transition: width 0.8s ease;
    }

    .stats-card .stat-detail {
        font-size: 0.75rem;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .stats-card .stat-detail i {
        margin-right: 0.25rem;
    }

    .stats-card .stat-trend {
        display: inline-flex;
        align-items: center;
        padding: 0.15rem 0.5rem;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
    }

    .stats-card .stat-trend.up {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }

    .stats-card .stat-trend.down {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }

    .stats-card .stat-trend i {
        font-size: 0.65rem;
        margin-right: 0.25rem;
    }

    /* Mini chart styles - simplified but preserved */
    .mini-chart {
        height: 30px;
        margin-top: 0.5rem;
        display: flex;
        align-items: flex-end;
        gap: 1px;
    }

    .mini-chart .bar {
        flex: 1;
        background-color: rgba(13, 110, 253, 0.15);
        border-radius: 1px 1px 0 0;
        transition: all 0.3s ease;
    }

    .mini-chart .bar.active {
        background-color: rgba(13, 110, 253, 0.4);
    }

    /* Pagination styles */
    .pagination .page-item.active .page-link {
        background-color: #0d6efd;
        border-color: #0d6efd;
    }

    /* Table responsive fixes */
    @media (max-width: 992px) {
        .table-responsive {
            overflow-x: auto;
        }

        .mobile-stack {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }
    }

    /* Loading indicator */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: visibility 0s, opacity 0.3s linear;
    }

    .loading-overlay.show {
        visibility: visible;
        opacity: 1;
    }

    .loading-spinner {
        width: 3rem;
        height: 3rem;
    }

    /* Make table header and Course List heading white for dark backgrounds */
    .card.bg-dark .card-header,
    .card.bg-black .card-header,
    .card.bg-dark .card-header *,
    .card.bg-black .card-header * {
        color: #fff !important;
    }

    .card.bg-dark .table thead,
    .card.bg-black .table thead,
    .card.bg-dark .table thead th,
    .card.bg-black .table thead th {
        color: #fff !important;
        background: transparent !important;
    }

    /* Videos badge: white text for dark backgrounds */
    .bg-info.bg-opacity-10.text-info,
    .bg-info.text-info {
        color: #fff !important;
        background-color: #17a2b8 !important;
    }

    /* Responsive adjustments for course list page */
    @media (max-width: 991px) {
        .card-header, .card-body, .filter-card .card-body {
            padding-left: 0.75rem !important;
            padding-right: 0.75rem !important;
        }
        .action-buttons {
            gap: 0.15rem;
        }
        .course-title-container h6 {
            max-width: 120px !important;
            font-size: 0.98rem;
        }
        .whatsapp-value {
            max-width: 60px !important;
        }
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }
        .responsive-table {
            min-width: 700px;
        }
    }
    @media (max-width: 767px) {
        .card-header, .card-body, .filter-card .card-body {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }
        .filter-card .row.g-2 > [class^='col-'],
        .filter-card .row.g-2 > [class*=' col-'] {
            flex: 0 0 100%;
            max-width: 100%;
        }
        .filter-card form.row.g-2 {
            flex-direction: column;
            gap: 0.5rem;
        }
        .action-buttons .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.85rem;
        }
        .course-title-container h6 {
            max-width: 90px !important;
            font-size: 0.95rem;
        }
        .whatsapp-value {
            max-width: 40px !important;
        }
        .responsive-table {
            min-width: 600px;
        }
    }
    @media (max-width: 575px) {
        .card-header, .card-body, .filter-card .card-body {
            padding-left: 0.25rem !important;
            padding-right: 0.25rem !important;
        }
        .action-buttons .btn {
            font-size: 0.8rem;
        }
        .responsive-table {
            min-width: 500px;
        }
    }
</style>
<?php

// Initialize database
try {
    $db = new Database();
    $conn = $db->getConnection();

    if (!$conn) {
        throw new Exception("Database connection failed");
    }
} catch (Exception $e) {
    Utilities::setFlashMessage('error', "Database error: " . $e->getMessage());
    $conn = null;
}

// Get current admin role and ID
$currentAdminRole = $auth->getUserRole();
$currentAdminId = $auth->getUserId();
$isStaff = $auth->hasRole('staff');
$isAdmin = $auth->hasRole('admin') || $auth->hasRole('super_admin');
$isSuperAdmin = $auth->hasRole('super_admin');

// Define a function to log actions for audit purposes
function logAdminAction($conn, $adminId, $action, $details) {
    try {
        $query = "INSERT INTO admin_activity_log (admin_id, action, details, ip_address) VALUES (?, ?, ?, ?)";
        $stmt = $conn->prepare($query);
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $stmt->bind_param("isss", $adminId, $action, $details, $ipAddress);
        $stmt->execute();
    } catch (Exception $e) {
        // Silently fail - logging should not interrupt the main flow
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}

// Handle bulk actions
if (isset($_POST['bulk_action']) && isset($_POST['course_ids']) && $conn) {
    $action = $_POST['bulk_action'];
    $courseIds = $_POST['course_ids'];

    if (!empty($courseIds) && !empty($action)) {
        $successCount = 0;
        $errorCount = 0;
        $errorMessages = [];

        // Start transaction for bulk operations
        $conn->begin_transaction();

        try {
            foreach ($courseIds as $courseId) {
                if (!is_numeric($courseId)) continue;

                // Verify course exists
                $checkQuery = "SELECT id, title FROM courses WHERE id = ?";
                $checkStmt = $conn->prepare($checkQuery);
                $checkStmt->bind_param("i", $courseId);
                $checkStmt->execute();
                $checkResult = $checkStmt->get_result();

                if ($checkResult->num_rows === 0) {
                    $errorCount++;
                    $errorMessages[] = "Course ID $courseId does not exist.";
                    continue;
                }

                $courseData = $checkResult->fetch_assoc();

                switch ($action) {
                    case 'delete':
                        // Check for enrollments first
                        $enrollmentCheck = "SELECT COUNT(*) as count FROM user_course_enrollments WHERE course_id = ?";
                        $enrollmentStmt = $conn->prepare($enrollmentCheck);
                        $enrollmentStmt->bind_param("i", $courseId);
                        $enrollmentStmt->execute();
                        $enrollmentResult = $enrollmentStmt->get_result();
                        $enrollmentCount = $enrollmentResult->fetch_assoc()['count'];

                        if ($enrollmentCount > 0 && !$isSuperAdmin) {
                            $errorCount++;
                            $errorMessages[] = "Cannot delete course '{$courseData['title']}' because it has active enrollments.";
                            continue 2; // Skip to next course ID
                        }

                        $query = "DELETE FROM courses WHERE id = ?";
                        $actionLog = "Deleted course: {$courseData['title']} (ID: $courseId)";
                        break;
                    case 'activate':
                        $query = "UPDATE courses SET is_active = 1 WHERE id = ?";
                        $actionLog = "Activated course: {$courseData['title']} (ID: $courseId)";
                        break;
                    case 'deactivate':
                        $query = "UPDATE courses SET is_active = 0 WHERE id = ?";
                        $actionLog = "Deactivated course: {$courseData['title']} (ID: $courseId)";
                        break;
                    default:
                        continue 2; // Skip to next course ID
                }

                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $courseId);

                if ($stmt->execute()) {
                    $successCount++;
                    // Log the action
                    logAdminAction($conn, $currentAdminId, "BULK_$action", $actionLog);
                } else {
                    $errorCount++;
                    $errorMessages[] = "Failed to $action course '{$courseData['title']}': " . $conn->error;
                }
            }

            // Commit transaction if we have any successes
            if ($successCount > 0) {
                $conn->commit();
            } else {
                $conn->rollback();
            }

            if ($successCount > 0) {
                $actionText = ucfirst(str_replace(['_', '-'], ' ', $action));
                Utilities::setFlashMessage('success', "$actionText action completed successfully for $successCount course(s).");
            }

            if ($errorCount > 0) {
                $errorMsg = "Failed to process $errorCount course(s).";
                if (!empty($errorMessages)) {
                    $errorMsg .= " Details: " . implode(" ", $errorMessages);
                }
                Utilities::setFlashMessage('error', $errorMsg);
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            Utilities::setFlashMessage('error', "Error processing bulk action: " . $e->getMessage());
        }

        Utilities::redirect('courses.php');
    } else {
        Utilities::setFlashMessage('warning', "No courses selected or no action specified.");
        Utilities::redirect('courses.php');
    }
}

// Handle individual course actions
if (isset($_GET['action']) && isset($_GET['id']) && is_numeric($_GET['id']) && $conn) {
    $action = $_GET['action'];
    $courseId = $_GET['id'];

    try {
        // Verify course exists and get its title
        $checkQuery = "SELECT id, title FROM courses WHERE id = ?";
        $checkStmt = $conn->prepare($checkQuery);
        $checkStmt->bind_param("i", $courseId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();

        if ($checkResult->num_rows === 0) {
            Utilities::setFlashMessage('error', "Course not found.");
            Utilities::redirect('courses.php');
            exit;
        }

        $courseData = $checkResult->fetch_assoc();
        $courseTitle = $courseData['title'];

        switch ($action) {
            case 'delete':
                // Check for enrollments first
                $enrollmentCheck = "SELECT COUNT(*) as count FROM user_course_enrollments WHERE course_id = ?";
                $enrollmentStmt = $conn->prepare($enrollmentCheck);
                $enrollmentStmt->bind_param("i", $courseId);
                $enrollmentStmt->execute();
                $enrollmentResult = $enrollmentStmt->get_result();
                $enrollmentCount = $enrollmentResult->fetch_assoc()['count'];

                if ($enrollmentCount > 0 && !$isSuperAdmin) {
                    Utilities::setFlashMessage('error', "Cannot delete course '$courseTitle' because it has active enrollments.");
                    Utilities::redirect('courses.php');
                    exit;
                }

                $query = "DELETE FROM courses WHERE id = ?";
                $successMessage = "Course '$courseTitle' deleted successfully.";
                $errorMessage = "Failed to delete course '$courseTitle'.";
                $actionLog = "Deleted course: $courseTitle (ID: $courseId)";
                break;
            case 'activate':
                $query = "UPDATE courses SET is_active = 1 WHERE id = ?";
                $successMessage = "Course '$courseTitle' activated successfully.";
                $errorMessage = "Failed to activate course '$courseTitle'.";
                $actionLog = "Activated course: $courseTitle (ID: $courseId)";
                break;
            case 'deactivate':
                $query = "UPDATE courses SET is_active = 0 WHERE id = ?";
                $successMessage = "Course '$courseTitle' deactivated successfully.";
                $errorMessage = "Failed to deactivate course '$courseTitle'.";
                $actionLog = "Deactivated course: $courseTitle (ID: $courseId)";
                break;
            default:
                Utilities::setFlashMessage('warning', "Invalid action specified.");
                Utilities::redirect('courses.php');
                exit;
        }

        // Start transaction
        $conn->begin_transaction();

        $stmt = $conn->prepare($query);
        $stmt->bind_param("i", $courseId);

        if ($stmt->execute()) {
            // Log the action
            logAdminAction($conn, $currentAdminId, $action, $actionLog);

            // Commit transaction
            $conn->commit();

            Utilities::setFlashMessage('success', $successMessage);
        } else {
            // Rollback transaction
            $conn->rollback();

            Utilities::setFlashMessage('error', $errorMessage . " Error: " . $conn->error);
        }
    } catch (Exception $e) {
        // Rollback transaction if active
        if ($conn->inTransaction()) {
            $conn->rollback();
        }

        Utilities::setFlashMessage('error', "Error processing action: " . $e->getMessage());
    }

    Utilities::redirect('courses.php');
}

// Get filter parameters
$category = isset($_GET['category']) ? trim($_GET['category']) : '';
$status = isset($_GET['status']) ? trim($_GET['status']) : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'created_at';
$order = isset($_GET['order']) ? strtoupper(trim($_GET['order'])) : 'DESC';
$page = isset($_GET['page']) && is_numeric($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$itemsPerPage = 10; // Number of courses per page

// Validate sort field to prevent SQL injection
$allowedSortFields = ['title', 'category', 'price', 'duration_weeks', 'created_at', 'enrollment_count', 'video_count'];
if (!in_array($sort, $allowedSortFields)) {
    $sort = 'created_at';
}

// Validate order direction
if ($order !== 'ASC' && $order !== 'DESC') {
    $order = 'DESC';
}

// Calculate pagination offset
$offset = ($page - 1) * $itemsPerPage;

// Get courses that have users assigned to this staff member (if staff)
$staffAssignedCourses = [];
if ($isStaff && $conn) {
    $staffAssignedCourses = getStaffAssignedCourses($conn, $auth);
}

// Initialize variables for course statistics
$totalCourses = 0;
$activeCourses = 0;
$totalEnrollments = 0;
$totalVideos = 0;

if ($conn) {
    try {
        // Build query with filters for counting total records
        $countQuery = "SELECT
                        COUNT(*) as total_count,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_count
                      FROM courses c WHERE 1=1";

        // Show all courses for both admins and staff
        // We'll handle staff-specific filtering in the UI instead

        $countParams = [];
        $countTypes = "";

        // No need to add course IDs for filtering anymore
        // We're showing all courses for both admins and staff

        if (!empty($category)) {
            $countQuery .= " AND c.category = ?";
            $countParams[] = $category;
            $countTypes .= "s";
        }

        if ($status !== '') {
            $isActive = ($status === 'active') ? 1 : 0;
            $countQuery .= " AND c.is_active = ?";
            $countParams[] = $isActive;
            $countTypes .= "i";
        }

        if (!empty($search)) {
            $searchTerm = "%$search%";
            $countQuery .= " AND (c.title LIKE ? OR c.description LIKE ? OR c.category LIKE ?)";
            $countParams[] = $searchTerm;
            $countParams[] = $searchTerm;
            $countParams[] = $searchTerm;
            $countTypes .= "sss";
        }

        // Prepare and execute the count query
        $countStmt = $conn->prepare($countQuery);

        if (!empty($countParams)) {
            $countStmt->bind_param($countTypes, ...$countParams);
        }

        $countStmt->execute();
        $countResult = $countStmt->get_result();
        $countData = $countResult->fetch_assoc();

        $totalFilteredCourses = $countData['total_count'];
        $activeFilteredCourses = $countData['active_count'];

        // Calculate total pages
        $totalPages = ceil($totalFilteredCourses / $itemsPerPage);

        // Adjust current page if it's out of range
        if ($totalPages > 0 && $page > $totalPages) {
            $page = $totalPages;
            $offset = ($page - 1) * $itemsPerPage;
        }

        // Get overall course statistics (regardless of filters)
        $statsQuery = "SELECT
                        COUNT(*) as total_courses,
                        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_courses,
                        (SELECT COUNT(*) FROM user_course_enrollments) as total_enrollments,
                        (SELECT COUNT(*) FROM course_videos) as total_videos
                      FROM courses";

        // Show all courses stats for both admins and staff
        $statsStmt = $conn->prepare($statsQuery);

        // Only execute if we have a statement
        if (isset($statsStmt)) {
            $statsStmt->execute();
            $statsResult = $statsStmt->get_result();
            $statsData = $statsResult->fetch_assoc();

            $totalCourses = $statsData['total_courses'];
            $activeCourses = $statsData['active_courses'];
            $totalEnrollments = $statsData['total_enrollments'];
            $totalVideos = $statsData['total_videos'];
        }

        // Build main query with filters
        $query = "SELECT c.*,
                  (SELECT COUNT(*) FROM course_videos WHERE course_id = c.id) as video_count,
                  (SELECT COUNT(*) FROM user_course_enrollments WHERE course_id = c.id) as enrollment_count";

        // For staff members, add a count of their assigned users enrolled in each course
        if ($isStaff) {
            $query .= ", (SELECT COUNT(*) FROM user_course_enrollments uce
                         JOIN users u ON uce.user_id = u.id
                         WHERE uce.course_id = c.id AND u.assigned_staff_id = ?) as staff_enrollment_count";
        }

        $query .= " FROM courses c WHERE 1=1";

        // Show all courses for both admins and staff
        // We'll handle staff-specific filtering in the UI instead

        $params = [];
        $types = "";

        // Add staff ID to params if staff member (for staff_enrollment_count)
        if ($isStaff) {
            $params[] = $currentAdminId;
            $types .= "i";
        }

        if (!empty($category)) {
            $query .= " AND c.category = ?";
            $params[] = $category;
            $types .= "s";
        }

        if ($status !== '') {
            $isActive = ($status === 'active') ? 1 : 0;
            $query .= " AND c.is_active = ?";
            $params[] = $isActive;
            $types .= "i";
        }

        if (!empty($search)) {
            $searchTerm = "%$search%";
            $query .= " AND (c.title LIKE ? OR c.description LIKE ? OR c.category LIKE ?)";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $types .= "sss";
        }

        // Add sorting
        if ($sort === 'enrollment_count' || $sort === 'video_count') {
            // These are calculated fields, so we sort by them directly
            $query .= " ORDER BY $sort $order";
        } else {
            $query .= " ORDER BY c.$sort $order";
        }

        // Add pagination
        $query .= " LIMIT ? OFFSET ?";
        $params[] = $itemsPerPage;
        $params[] = $offset;
        $types .= "ii";

        // Prepare and execute the query
        $stmt = $conn->prepare($query);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();

        // Get all categories for filter dropdown
        $categoryQuery = "SELECT DISTINCT category FROM courses ORDER BY category";
        $categoryResult = $conn->query($categoryQuery);
        $categories = [];

        if ($categoryResult && $categoryResult->num_rows > 0) {
            while ($row = $categoryResult->fetch_assoc()) {
                if (!empty($row['category'])) {
                    $categories[] = $row['category'];
                }
            }
        }
    } catch (Exception $e) {
        Utilities::setFlashMessage('error', "Error loading courses: " . $e->getMessage());
        $result = null;
    }
} else {
    $result = null;
}
?>

<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="spinner-border text-primary loading-spinner" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
</div>

<!-- Page Header -->
<div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-800">Courses</h1>
    <a href="add_course.php" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
        Add New Course
        </a>
</div>

<?php Utilities::displayFlashMessages(); ?>

<!-- Courses Table -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Course
                            </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Students
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Videos
                    </th>
                    <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Created
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                        </tr>
                    </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                        <?php if ($result && $result->num_rows > 0): ?>
                            <?php while ($course = $result->fetch_assoc()): ?>
                                <tr class="course-row <?php echo $course['is_active'] ? 'active' : 'inactive'; ?>" style="transition: background 0.2s; border-radius: 8px;">
                            <td class="align-middle" data-label="Course">
                                        <div class="d-flex align-items-center gap-2">
                                            <?php if (!empty($course['image_url'])): ?>
                                                <img src="<?php echo htmlspecialchars($course['image_url']); ?>" alt="<?php echo htmlspecialchars($course['title']); ?>" class="rounded" width="40" height="40" style="object-fit: cover;">
                                            <?php else: ?>
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; min-width: 40px;">
                                                    <i class="fas fa-book-open text-secondary"></i>
                                                </div>
                                            <?php endif; ?>
                                            <div class="course-title-container text-truncate">
                                                <h6 class="mb-0 fw-semibold text-truncate" style="max-width: 180px; font-size: 1rem;">
                                                    <a href="course_edit.php?id=<?php echo $course['id']; ?>" class="text-decoration-none text-dark" title="<?php echo htmlspecialchars($course['title']); ?>">
                                                        <?php echo htmlspecialchars($course['title']); ?>
                                                    </a>
                                                </h6>
                                                <small class="text-muted d-flex align-items-center">
                                                    <i class="far fa-calendar-alt me-1 fa-sm"></i>
                                                    <?php echo date('M d, Y', strtotime($course['created_at'])); ?>
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                            <td class="align-middle" data-label="Status">
                                <?php if ($course['is_active']): ?>
                                    <a href="courses.php?action=deactivate&id=<?php echo $course['id']; ?>" class="badge bg-success status-badge px-2 py-1" title="Click to deactivate">Active</a>
                                        <?php else: ?>
                                    <a href="courses.php?action=activate&id=<?php echo $course['id']; ?>" class="badge bg-secondary status-badge px-2 py-1" title="Click to activate">Inactive</a>
                                        <?php endif; ?>
                                    </td>
                            <td class="hidden md:table-cell align-middle" data-label="Students">
                                        <a href="course_enrollments.php?course_id=<?php echo $course['id']; ?>" class="text-decoration-none">
                                            <?php if ($isStaff): ?>
                                                <div class="d-flex flex-column">
                                                    <span class="fw-medium"><?php echo number_format($course['staff_enrollment_count']); ?></span>
                                                    <small class="text-muted">(<?php echo number_format($course['enrollment_count']); ?> total)</small>
                                                </div>
                                            <?php else: ?>
                                                <span class="fw-medium"><?php echo number_format($course['enrollment_count']); ?></span>
                                            <?php endif; ?>
                                        </a>
                                    </td>
                            <td class="align-middle" data-label="Videos">
                                <a href="course_videos.php?course_id=<?php echo $course['id']; ?>" class="badge bg-info bg-opacity-10 text-info px-2 py-1 text-decoration-none">
                                    <i class="fas fa-video me-1 text-info"></i>
                                    <?php echo $course['video_count']; ?>
                                </a>
                                    </td>
                            <td class="hidden md:table-cell align-middle" data-label="Created">
                                <?php echo date('M d, Y', strtotime($course['created_at'])); ?>
                                    </td>
                            <td class="align-middle text-end" data-label="Actions">
                                        <div class="d-flex align-items-center gap-1 justify-content-end action-buttons">
                                            <!-- Edit button -->
                                            <a href="course_edit.php?id=<?php echo $course['id']; ?>" class="btn btn-light btn-sm rounded-circle p-2 shadow-none" title="Edit Course">
                                                <i class="fas fa-edit text-success"></i>
                                            </a>
                                            <!-- Videos button -->
                                            <a href="course_videos.php?course_id=<?php echo $course['id']; ?>" class="btn btn-light btn-sm rounded-circle p-2 shadow-none" title="Manage Videos">
                                                <i class="fas fa-video text-info"></i>
                                            </a>
                                            <!-- Toggle active status button -->
                                            <?php if ($course['is_active']): ?>
                                                <a href="courses.php?action=deactivate&id=<?php echo $course['id']; ?>" class="btn btn-light btn-sm rounded-circle p-2 shadow-none" title="Deactivate Course">
                                                    <i class="fas fa-toggle-on text-success"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="courses.php?action=activate&id=<?php echo $course['id']; ?>" class="btn btn-light btn-sm rounded-circle p-2 shadow-none" title="Activate Course">
                                                    <i class="fas fa-toggle-off text-secondary"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endwhile; ?>
                        <?php else: ?>
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <div class="py-5">
                                        <div class="mb-3">
                                            <i class="fas fa-edit fa-4x text-primary opacity-50"></i>
                                        </div>
                                        <h5 class="mb-2">No courses found</h5>
                                        <p class="text-muted mb-3">
                                            <?php if (!empty($search) || !empty($status)): ?>
                                                Try adjusting your search or filter criteria
                                            <?php else: ?>
                                                Get started by adding your first course
                                            <?php endif; ?>
                                        </p>
                                        <?php if (!empty($search) || !empty($status)): ?>
                                            <a href="courses.php" class="btn btn-outline-secondary btn-sm me-2">
                                                <i class="fas fa-times me-1"></i> Clear Filters
                                            </a>
                                        <?php endif; ?>
                                        <a href="course_add.php" class="btn btn-success btn-sm">
                                            <i class="fas fa-plus me-1"></i> Add New Course
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <?php if (isset($totalPages) && $totalPages > 1): ?>
        <div class="card-footer d-flex justify-content-between align-items-center">
            <div class="text-muted small">
                Showing <?php echo ($offset + 1); ?> to <?php echo min($offset + $itemsPerPage, $totalFilteredCourses); ?> of <?php echo $totalFilteredCourses; ?> courses
            </div>
            <nav aria-label="Course pagination">
                <ul class="pagination pagination-sm mb-0">
                    <?php if ($page > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo Utilities::updateQueryParam($_SERVER['REQUEST_URI'], 'page', $page - 1); ?>" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>

                    <?php
                    // Calculate range of pages to show
                    $startPage = max(1, $page - 2);
                    $endPage = min($totalPages, $page + 2);

                    // Always show first page
                    if ($startPage > 1) {
                        echo '<li class="page-item"><a class="page-link" href="' . Utilities::updateQueryParam($_SERVER['REQUEST_URI'], 'page', 1) . '">1</a></li>';
                        if ($startPage > 2) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                    }

                    // Show page links
                    for ($i = $startPage; $i <= $endPage; $i++) {
                        echo '<li class="page-item ' . ($page == $i ? 'active' : '') . '"><a class="page-link" href="' . Utilities::updateQueryParam($_SERVER['REQUEST_URI'], 'page', $i) . '">' . $i . '</a></li>';
                    }

                    // Always show last page
                    if ($endPage < $totalPages) {
                        if ($endPage < $totalPages - 1) {
                            echo '<li class="page-item disabled"><span class="page-link">...</span></li>';
                        }
                        echo '<li class="page-item"><a class="page-link" href="' . Utilities::updateQueryParam($_SERVER['REQUEST_URI'], 'page', $totalPages) . '">' . $totalPages . '</a></li>';
                    }
                    ?>

                    <?php if ($page < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo Utilities::updateQueryParam($_SERVER['REQUEST_URI'], 'page', $page + 1); ?>" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
        <?php endif; ?>

<!-- JavaScript for Enhanced Course Management -->
<script>
// Global function to toggle action menu
function toggleActionMenu(button, event) {
    // Get the event object
    event = event || window.event;

    // Get the menu container and menu
    const container = button.closest('.action-menu-container');
    const menu = container.querySelector('.clean-action-menu');

    // Close any other open menus
    document.querySelectorAll('.clean-action-menu.show').forEach(openMenu => {
        if (openMenu !== menu) {
            openMenu.classList.remove('show');
        }
    });

    // Toggle menu visibility
    menu.classList.toggle('show');

    // Prevent event propagation
    if (event) {
        event.preventDefault();
        event.stopPropagation();
    }
    return false;
}

// Function to toggle sort order
function toggleSortOrder() {
    const orderInput = document.getElementById('orderInput');
    const toggleButton = document.getElementById('toggleOrder');
    const icon = toggleButton.querySelector('i');

    if (orderInput.value === 'DESC') {
        orderInput.value = 'ASC';
        icon.className = 'fas fa-sort-amount-up';
    } else {
        orderInput.value = 'DESC';
        icon.className = 'fas fa-sort-amount-down';
    }

    // Submit the form
    document.getElementById('filterForm').submit();
}

// Function to filter courses for staff view
function filterCoursesForStaff() {
    const staffView = document.getElementById('staff_view');
    const isMyCoursesView = staffView && staffView.value === 'my';

    if (isMyCoursesView) {
        // Hide courses with zero staff enrollments
        document.querySelectorAll('.course-row').forEach(row => {
            const staffEnrollmentCell = row.querySelector('td:nth-child(7)');
            if (staffEnrollmentCell) {
                // Look for the first number in the cell (staff enrollment count)
                const staffEnrollmentText = staffEnrollmentCell.textContent.trim();
                const staffEnrollmentMatch = staffEnrollmentText.match(/^(\d+)/);
                const staffEnrollmentCount = staffEnrollmentMatch ? parseInt(staffEnrollmentMatch[1]) : 0;

                if (isNaN(staffEnrollmentCount) || staffEnrollmentCount === 0) {
                    row.style.display = 'none';
                } else {
                    row.style.display = '';
                }
            }
        });

        // Update the course count display
        updateFilteredCourseCount();
    } else {
        // Show all courses
        document.querySelectorAll('.course-row').forEach(row => {
            row.style.display = '';
        });

        // Reset the course count display
        updateFilteredCourseCount();
    }
}

// Function to update the filtered course count display
function updateFilteredCourseCount() {
    const visibleCourses = document.querySelectorAll('.course-row:not([style*="display: none"])').length;
    const totalCourses = document.querySelectorAll('.course-row').length;
    const countDisplay = document.querySelector('.card-header small.text-muted');

    if (countDisplay) {
        if (visibleCourses !== totalCourses) {
            countDisplay.textContent = `${visibleCourses} courses found (filtered from ${totalCourses} total)`;
        } else {
            countDisplay.textContent = `${totalCourses} courses found`;
        }
    }
}

// Show loading overlay
function showLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('show');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // Apply staff view filtering if needed
    filterCoursesForStaff();

    // Handle staff view changes
    const staffViewSelect = document.getElementById('staff_view');
    if (staffViewSelect) {
        staffViewSelect.addEventListener('change', function() {
            filterCoursesForStaff();
        });
    }

    // Animate stats cards
    animateStatsElements();

    // Function to animate stats elements with a clean, subtle approach
    function animateStatsElements() {
        // Animate progress bars
        const progressBars = document.querySelectorAll('.stat-progress-bar');
        progressBars.forEach((bar, index) => {
            const width = bar.style.width;
            bar.style.width = '0%';
            setTimeout(() => {
                bar.style.width = width;
            }, 300 + (index * 50));
        });

        // Animate mini charts
        const charts = document.querySelectorAll('.mini-chart');
        charts.forEach((chart, chartIndex) => {
            const bars = chart.querySelectorAll('.bar');
            bars.forEach((bar, barIndex) => {
                const height = bar.style.height;
                bar.style.height = '0%';
                setTimeout(() => {
                    bar.style.height = height;
                }, 600 + (chartIndex * 100) + (barIndex * 30));
            });
        });

        // Animate stat values with subtle fade-in
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach((value, index) => {
            value.style.opacity = '0';
            value.style.transform = 'translateY(10px)';
            value.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

            setTimeout(() => {
                value.style.opacity = '1';
                value.style.transform = 'translateY(0)';
            }, 200 + (index * 100));
        });
    }

    // Select All Checkbox functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const courseCheckboxes = document.querySelectorAll('.course-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            courseCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }

    // Update "Select All" checkbox state based on individual checkboxes
    courseCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(courseCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(courseCheckboxes).some(cb => cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = someChecked && !allChecked;
            }
        });
    });

    // Bulk action button handler
    const applyBulkActionBtn = document.getElementById('applyBulkAction');
    if (applyBulkActionBtn) {
        applyBulkActionBtn.addEventListener('click', function() {
            const bulkActionSelect = document.getElementById('bulkActionSelect');
            const selectedAction = bulkActionSelect.value;
            const selectedCourses = document.querySelectorAll('.course-checkbox:checked');

            if (!selectedAction) {
                alert('Please select an action to perform.');
                return;
            }

            if (selectedCourses.length === 0) {
                alert('Please select at least one course.');
                return;
            }

            let confirmMessage = 'Are you sure you want to perform this action on the selected courses?';

            if (selectedAction === 'delete') {
                confirmMessage = 'WARNING: You are about to delete ' + selectedCourses.length + ' course(s). This action cannot be undone. Continue?';
            }

            if (confirm(confirmMessage)) {
                showLoading();
                document.getElementById('coursesForm').submit();
            }
        });
    }

    // Add fallback click handlers to all action buttons
    document.querySelectorAll('.action-btn.clean-action-btn').forEach(button => {
        button.addEventListener('click', function(e) {
            toggleActionMenu(this, e);
        });

        // Also add handler to the icon
        const icon = button.querySelector('i');
        if (icon) {
            icon.addEventListener('click', function(e) {
                toggleActionMenu(this.parentNode, e);
            });
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.action-menu-container')) {
            document.querySelectorAll('.clean-action-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Close menu on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.clean-action-menu.show').forEach(menu => {
                menu.classList.remove('show');
            });
        }
    });

    // Add loading overlay to all links and forms
    document.querySelectorAll('a[href*="action="], form').forEach(el => {
        if (el.tagName === 'A' && el.getAttribute('href').includes('action=')) {
            el.addEventListener('click', function() {
                // Don't show loading for confirmation dialogs that might be cancelled
                if (this.getAttribute('onclick') && this.getAttribute('onclick').includes('confirm')) {
                    // Only show loading if confirmed
                    const originalOnClick = this.getAttribute('onclick');
                    this.setAttribute('onclick', originalOnClick.replace('return confirm', 'var confirmed = confirm; if(confirmed) showLoading(); return confirmed'));
                } else {
                    showLoading();
                }
            });
        } else if (el.tagName === 'FORM' && el.id !== 'filterForm') {
            el.addEventListener('submit', showLoading);
        }
    });

    // Preserve scroll position across page reloads
    if (window.localStorage && localStorage.getItem('coursesScrollTop')) {
        window.scrollTo(0, parseInt(localStorage.getItem('coursesScrollTop'), 10));
        localStorage.removeItem('coursesScrollTop');
    }

    // Save scroll position before navigation or form submit
    function saveScroll() {
        if (window.localStorage) {
            localStorage.setItem('coursesScrollTop', window.scrollY);
        }
    }

    // Save on all links and form submits
    document.querySelectorAll('a, form').forEach(function(el) {
        if (el.tagName === 'A') {
            el.addEventListener('click', saveScroll);
        } else if (el.tagName === 'FORM') {
            el.addEventListener('submit', saveScroll);
        }
    });

    // Add CSS for responsive table and action buttons
    const style = document.createElement('style');
    style.textContent = `
        /* Responsive table styles */
        .table-responsive {
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        .responsive-table {
            width: 100%;
            table-layout: fixed;
        }

        /* Column widths for different screen sizes */
        @media (min-width: 1200px) {
            .title-column { width: 25%; }
            .price-column { width: 8%; }
            .duration-column { width: 8%; }
            .videos-column { width: 8%; }
            .enrollments-column { width: 10%; }
            .status-column { width: 8%; }
            .whatsapp-column { width: 12%; }
            .actions-column { width: 15%; }
        }

        @media (max-width: 1199px) {
            .title-column { width: 20%; }
            .price-column { width: 8%; }
            .duration-column { width: 8%; }
            .videos-column { width: 8%; }
            .enrollments-column { width: 10%; }
            .status-column { width: 8%; }
            .whatsapp-column { width: 12%; }
            .actions-column { width: 15%; }
        }

        @media (max-width: 991px) {
            .title-column { width: 20%; }
            .price-column { width: 8%; }
            .duration-column { width: 8%; }
            .videos-column { width: 8%; }
            .enrollments-column { width: 10%; }
            .status-column { width: 8%; }
            .whatsapp-column { width: 12%; }
            .actions-column { width: 15%; }

            .course-title-container h6 {
                max-width: 150px !important;
            }

            .whatsapp-value {
                max-width: 80px !important;
            }
        }

        /* Truncate text with ellipsis */
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Action buttons container */
        .action-buttons {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            flex-wrap: nowrap;
        }

        .action-buttons .btn {
            padding: 0.25rem 0.5rem;
            line-height: 1;
            transition: all 0.2s ease;
            background-color: #28a745;
            border-color: #28a745;
        }

        .action-buttons .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            background-color: #218838;
            border-color: #1e7e34;
        }

        .action-buttons .btn:focus {
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        /* Make buttons more compact on smaller screens */
        @media (max-width: 768px) {
            .action-buttons {
                gap: 0.15rem;
            }

            .action-buttons .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .course-title-container h6 {
                max-width: 120px !important;
            }

            .whatsapp-value {
                max-width: 60px !important;
            }
        }

        /* Action menu container */
        .action-menu-container {
            position: relative;
            display: inline-block;
        }

        .action-menu {
            position: absolute;
            top: 100%;
            right: 0;
            z-index: 1000;
            display: none;
            min-width: 180px;
            padding: 0.5rem 0;
            margin: 0.125rem 0 0;
            font-size: 0.875rem;
            color: #212529;
            text-align: left;
            list-style: none;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .action-menu.show {
            display: block;
            animation: fadeIn 0.2s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-5px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .action-menu-header {
            display: block;
            padding: 0.25rem 1rem;
            margin-bottom: 0;
            font-size: 0.75rem;
            color: #6c757d;
            white-space: nowrap;
            text-transform: uppercase;
            font-weight: 600;
        }

        .action-menu-item {
            display: block;
            width: 100%;
            padding: 0.5rem 1rem;
            clear: both;
            font-weight: 400;
            color: #212529;
            text-align: inherit;
            text-decoration: none;
            white-space: nowrap;
            background-color: transparent;
            border: 0;
            cursor: pointer;
            transition: background-color 0.15s ease;
        }

        .action-menu-item:hover, .action-menu-item:focus {
            color: #16181b;
            text-decoration: none;
            background-color: #f8f9fa;
        }

        .action-menu-item.danger {
            color: #dc3545;
        }

        .action-menu-item.danger:hover, .action-menu-item.danger:focus {
            background-color: #f8d7da;
        }

        .action-menu-item i {
            margin-right: 0.5rem;
            width: 1rem;
            text-align: center;
        }

        .action-menu-divider {
            height: 0;
            margin: 0.5rem 0;
            overflow: hidden;
            border-top: 1px solid #e9ecef;
        }
    `;
    document.head.appendChild(style);
});
</script>

<?php require_once 'includes/footer.php'; ?>
