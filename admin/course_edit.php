<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('error', 'Invalid course ID.');
    Utilities::redirect('courses.php');
}

$courseId = $_GET['id'];

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $title = Utilities::sanitizeInput($_POST['title'] ?? '');
    $category = Utilities::sanitizeInput($_POST['category'] ?? 'General');
    $description = Utilities::sanitizeInput($_POST['description'] ?? '');
    $thumbnailUrl = Utilities::sanitizeInput($_POST['thumbnail_url'] ?? '');
    $durationWeeks = intval($_POST['duration_weeks'] ?? 4);
    $price = floatval($_POST['price'] ?? 0);
    $discountPercentage = intval($_POST['discount_percentage'] ?? 0);
    $whatsappNumber = Utilities::sanitizeInput($_POST['whatsapp_number'] ?? '');
    $whatsappMessagePrefix = Utilities::sanitizeInput($_POST['whatsapp_message_prefix'] ?? '');
    $isActive = isset($_POST['is_active']) ? 1 : 0;
    $isFeatured = isset($_POST['is_featured']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if (empty($title)) {
        $errors[] = 'Course title is required.';
    }

    if ($durationWeeks < 1) {
        $errors[] = 'Duration must be at least 1 week.';
    }

    if ($price < 0) {
        $errors[] = 'Price cannot be negative.';
    }

    if ($discountPercentage < 0 || $discountPercentage > 100) {
        $errors[] = 'Discount percentage must be between 0 and 100.';
    }

    // If no errors, update the course
    if (empty($errors)) {
        $updateQuery = "UPDATE courses
                        SET title = ?, category = ?, description = ?, thumbnail_url = ?,
                            duration_weeks = ?, price = ?, discount_percentage = ?,
                            whatsapp_number = ?, whatsapp_message_prefix = ?,
                            is_active = ?, is_featured = ?
                        WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("ssssiiissiii", $title, $category, $description, $thumbnailUrl,
                         $durationWeeks, $price, $discountPercentage, $whatsappNumber, $whatsappMessagePrefix,
                         $isActive, $isFeatured, $courseId);

        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Course updated successfully.');
            Utilities::redirect('courses.php');
        } else {
            $errors[] = 'Failed to update course: ' . $conn->error;
        }
    }
} else {
    // Fetch course data
    $query = "SELECT * FROM courses WHERE id = ?";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("i", $courseId);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        Utilities::setFlashMessage('error', 'Course not found.');
        Utilities::redirect('courses.php');
    }

    $course = $result->fetch_assoc();

    // Set variables for form
    $title = $course['title'];
    $category = $course['category'] ?? 'General';
    $description = $course['description'];
    $thumbnailUrl = $course['thumbnail_url'];
    $durationWeeks = $course['duration_weeks'];
    $price = $course['price'] ?? 0;
    $discountPercentage = $course['discount_percentage'] ?? 0;
    $whatsappNumber = $course['whatsapp_number'] ?? '';
    $whatsappMessagePrefix = $course['whatsapp_message_prefix'] ?? '';
    $isActive = $course['is_active'];
    $isFeatured = $course['is_featured'] ?? 0;
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Course</h1>
    <div>
        <a href="course_videos.php?course_id=<?php echo $courseId; ?>" class="btn btn-outline-primary me-2">
            <i class="fas fa-video me-2"></i> Manage Videos
        </a>
        <a href="courses.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i> Back to Courses
        </a>
    </div>
</div>

<?php Utilities::displayFlashMessages(); ?>

<?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
<?php endif; ?>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <form method="post">
            <div class="row">
                <div class="col-md-8 mb-3">
                    <label for="title" class="form-label">Course Title</label>
                    <input type="text" class="form-control" id="title" name="title" value="<?php echo htmlspecialchars($title); ?>" required>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="category" class="form-label">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="General" <?php echo $category === 'General' ? 'selected' : ''; ?>>General</option>
                        <option value="Fitness" <?php echo $category === 'Fitness' ? 'selected' : ''; ?>>Fitness</option>
                        <option value="Yoga" <?php echo $category === 'Yoga' ? 'selected' : ''; ?>>Yoga</option>
                        <option value="Strength" <?php echo $category === 'Strength' ? 'selected' : ''; ?>>Strength</option>
                        <option value="Cardio" <?php echo $category === 'Cardio' ? 'selected' : ''; ?>>Cardio</option>
                        <option value="Nutrition" <?php echo $category === 'Nutrition' ? 'selected' : ''; ?>>Nutrition</option>
                        <option value="Wellness" <?php echo $category === 'Wellness' ? 'selected' : ''; ?>>Wellness</option>
                    </select>
                </div>
            </div>

            <div class="mb-3">
                <label for="description" class="form-label">Description</label>
                <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($description); ?></textarea>
            </div>

            <div class="mb-3">
                <label for="thumbnail_url" class="form-label">Thumbnail URL</label>
                <input type="url" class="form-control" id="thumbnail_url" name="thumbnail_url" value="<?php echo htmlspecialchars($thumbnailUrl); ?>">
                <div class="form-text">Enter a URL for the course thumbnail image.</div>
            </div>

            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="duration_weeks" class="form-label">Duration (Weeks)</label>
                    <input type="number" class="form-control" id="duration_weeks" name="duration_weeks" value="<?php echo $durationWeeks; ?>" min="1" required>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="price" class="form-label">Price (₹)</label>
                    <input type="number" class="form-control" id="price" name="price" value="<?php echo $price; ?>" min="0" step="0.01">
                </div>

                <div class="col-md-4 mb-3">
                    <label for="discount_percentage" class="form-label">Discount (%)</label>
                    <input type="number" class="form-control" id="discount_percentage" name="discount_percentage" value="<?php echo $discountPercentage; ?>" min="0" max="100">
                </div>
            </div>

            <div class="card mb-4 bg-light">
                <div class="card-header">
                    <h5 class="mb-0">WhatsApp Enrollment Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="whatsapp_number" class="form-label">WhatsApp Number</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fab fa-whatsapp"></i></span>
                                <input type="text" class="form-control" id="whatsapp_number" name="whatsapp_number"
                                    value="<?php echo htmlspecialchars($whatsappNumber); ?>" placeholder="e.g., +919876543210">
                            </div>
                            <div class="form-text">Enter the WhatsApp number with country code for enrollment inquiries.</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="whatsapp_message_prefix" class="form-label">Custom Message Prefix</label>
                            <textarea class="form-control" id="whatsapp_message_prefix" name="whatsapp_message_prefix"
                                rows="3" placeholder="Hi, I'm interested in enrolling in the course:"><?php echo htmlspecialchars($whatsappMessagePrefix); ?></textarea>
                            <div class="form-text">Custom message that will appear before the course details in WhatsApp.</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_active" name="is_active" <?php echo $isActive ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_active">Active</label>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input" id="is_featured" name="is_featured" <?php echo $isFeatured ? 'checked' : ''; ?>>
                        <label class="form-check-label" for="is_featured">Featured on Homepage</label>
                    </div>
                </div>
            </div>

            <div class="d-grid gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Update Course
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
