<?php
require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Create motivational_quotes table
$createQuotesTable = "
CREATE TABLE IF NOT EXISTS `motivational_quotes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `quote` text NOT NULL,
  `author` varchar(255) DEFAULT NULL,
  `category` varchar(100) DEFAULT NULL,
  `is_ai_generated` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `category` (`category`),
  KEY `is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Create user_quote_preferences table
$createPreferencesTable = "
CREATE TABLE IF NOT EXISTS `user_quote_preferences` (
  `user_id` int(11) NOT NULL,
  `preferred_categories` text DEFAULT NULL,
  `personalization_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `last_quote_id` int(11) DEFAULT NULL,
  `last_quote_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_quote_preferences_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Create quote_settings table for admin configuration
$createSettingsTable = "
CREATE TABLE IF NOT EXISTS `quote_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
";

// Execute the queries
try {
    if ($conn->query($createQuotesTable)) {
        echo "Table 'motivational_quotes' created successfully.<br>";
    } else {
        echo "Error creating table 'motivational_quotes': " . $conn->error . "<br>";
    }

    if ($conn->query($createPreferencesTable)) {
        echo "Table 'user_quote_preferences' created successfully.<br>";
    } else {
        echo "Error creating table 'user_quote_preferences': " . $conn->error . "<br>";
    }

    if ($conn->query($createSettingsTable)) {
        echo "Table 'quote_settings' created successfully.<br>";
    } else {
        echo "Error creating table 'quote_settings': " . $conn->error . "<br>";
    }

    // Insert default settings
    $insertDefaultSettings = "
    INSERT INTO `quote_settings` (`setting_key`, `setting_value`) VALUES
    ('ai_provider', 'deepseek'),
    ('deepseek_api_key', ''),
    ('ai_model', 'deepseek-chat'),
    ('personalization_enabled', '1'),
    ('quotes_enabled', '1'),
    ('personalization_factors', 'workout_history,fitness_goals,streak_days,bmi_progress'),
    ('personalization_prompt', 'Generate a motivational fitness quote for a user who {user_context}. The quote should be encouraging, positive, and tailored to their current fitness journey. Make the quote concise and impactful.'),
    ('quote_refresh_frequency', 'daily'),
    ('default_categories', 'fitness,motivation,health,mindfulness'),
    ('max_ai_quotes_per_day', '1')
    ON DUPLICATE KEY UPDATE `setting_value` = VALUES(`setting_value`);
    ";

    if ($conn->query($insertDefaultSettings)) {
        echo "Default quote settings inserted successfully.<br>";
    } else {
        echo "Error inserting default quote settings: " . $conn->error . "<br>";
    }

    // Insert sample quotes
    $insertSampleQuotes = "
    INSERT INTO `motivational_quotes` (`quote`, `author`, `category`, `is_active`) VALUES
    ('The only bad workout is the one that did not happen.', 'Unknown', 'fitness', 1),
    ('Your body can stand almost anything. It is your mind that you have to convince.', 'Andrew Murphy', 'motivation', 1),
    ('Take care of your body. It is the only place you have to live.', 'Jim Rohn', 'health', 1),
    ('The difference between try and triumph is just a little umph!', 'Marvin Phillips', 'motivation', 1),
    ('The only way to define your limits is by going beyond them.', 'Arthur C. Clarke', 'motivation', 1),
    ('You do not have to be great to start, but you have to start to be great.', 'Zig Ziglar', 'motivation', 1),
    ('Strength does not come from physical capacity. It comes from an indomitable will.', 'Mahatma Gandhi', 'fitness', 1),
    ('The greatest wealth is health.', 'Virgil', 'health', 1),
    ('The hardest lift of all is lifting your butt off the couch.', 'Unknown', 'fitness', 1),
    ('Mindfulness is not difficult. We just need to remember to do it.', 'Sharon Salzberg', 'mindfulness', 1),
    ('Your health is an investment, not an expense.', 'Unknown', 'health', 1),
    ('The body achieves what the mind believes.', 'Napoleon Hill', 'motivation', 1),
    ('Strive for progress, not perfection.', 'Unknown', 'motivation', 1),
    ('The pain you feel today will be the strength you feel tomorrow.', 'Arnold Schwarzenegger', 'fitness', 1),
    ('Be present in all things and thankful for all things.', 'Maya Angelou', 'mindfulness', 1)
    ";

    if ($conn->query($insertSampleQuotes)) {
        echo "Sample quotes inserted successfully.<br>";
    } else {
        echo "Error inserting sample quotes: " . $conn->error . "<br>";
    }

    echo "Motivational quotes tables setup completed.";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage();
}
