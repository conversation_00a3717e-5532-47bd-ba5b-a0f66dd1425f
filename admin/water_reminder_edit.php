<?php
require_once 'includes/header.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if reminder ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    Utilities::setFlashMessage('danger', 'Invalid reminder ID.');
    Utilities::redirect('water_reminders.php');
}

$reminderId = (int)$_GET['id'];

// Get reminder details
$reminderQuery = "SELECT wr.*, u.name as user_name, u.username
                 FROM water_reminders wr
                 JOIN users u ON wr.user_id = u.id
                 WHERE wr.id = ?";
$stmt = $conn->prepare($reminderQuery);
$stmt->bind_param("i", $reminderId);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    Utilities::setFlashMessage('danger', 'Water reminder not found.');
    Utilities::redirect('water_reminders.php');
}

$reminder = $result->fetch_assoc();

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize inputs
    $intervalHours = isset($_POST['interval_hours']) ? (int)$_POST['interval_hours'] : 2;
    $startTime = $_POST['start_time'] ?? '08:00';
    $endTime = $_POST['end_time'] ?? '22:00';
    $isActive = isset($_POST['is_active']) ? 1 : 0;

    // Validate inputs
    $errors = [];

    if ($intervalHours < 1 || $intervalHours > 12) {
        $errors[] = 'Interval must be between 1 and 12 hours.';
    }

    if (empty($startTime)) {
        $errors[] = 'Start time is required.';
    }

    if (empty($endTime)) {
        $errors[] = 'End time is required.';
    }

    if (!empty($startTime) && !empty($endTime)) {
        $startTimestamp = Utilities::safeStrtotime($startTime);
        $endTimestamp = Utilities::safeStrtotime($endTime);

        if ($startTimestamp !== false && $endTimestamp !== false && $startTimestamp >= $endTimestamp) {
            $errors[] = 'End time must be after start time.';
        }
    }

    if (empty($errors)) {
        // Update water reminder
        $updateQuery = "UPDATE water_reminders
                       SET interval_hours = ?, start_time = ?, end_time = ?, is_active = ?
                       WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->bind_param("issii", $intervalHours, $startTime, $endTime, $isActive, $reminderId);

        if ($stmt->execute()) {
            Utilities::setFlashMessage('success', 'Water reminder has been updated successfully.');
            Utilities::redirect('water_reminders.php');
        } else {
            Utilities::setFlashMessage('danger', 'Failed to update water reminder: ' . $conn->error);
        }
    } else {
        // Display errors
        foreach ($errors as $error) {
            Utilities::setFlashMessage('danger', $error);
        }
    }
}
?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Water Reminder</h1>
    <a href="water_reminders.php" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left me-2"></i> Back to Water Reminders
    </a>
</div>

<div class="card border-0 shadow-sm">
    <div class="card-body">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i> You are editing water reminder settings for <strong><?php echo htmlspecialchars($reminder['user_name']); ?></strong> (Username: <strong><?php echo htmlspecialchars($reminder['username']); ?></strong>).
        </div>

        <form method="post" action="water_reminder_edit.php?id=<?php echo $reminderId; ?>">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label for="interval_hours" class="form-label">Reminder Interval (hours) <span class="text-danger">*</span></label>
                    <input type="number" class="form-control" id="interval_hours" name="interval_hours" min="1" max="12" value="<?php echo $reminder['interval_hours']; ?>" required>
                    <div class="form-text">How often to remind the user to drink water (in hours).</div>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="start_time" class="form-label">Start Time <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" id="start_time" name="start_time" value="<?php echo $reminder['start_time']; ?>" required>
                    <div class="form-text">When to start sending reminders during the day.</div>
                </div>

                <div class="col-md-4 mb-3">
                    <label for="end_time" class="form-label">End Time <span class="text-danger">*</span></label>
                    <input type="time" class="form-control" id="end_time" name="end_time" value="<?php echo $reminder['end_time']; ?>" required>
                    <div class="form-text">When to stop sending reminders during the day.</div>
                </div>
            </div>

            <div class="mb-3">
                <div class="form-check form-switch">
                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" <?php echo $reminder['is_active'] ? 'checked' : ''; ?>>
                    <label class="form-check-label" for="is_active">Active Reminder</label>
                </div>
                <div class="form-text">Toggle to enable or disable water reminders for this user.</div>
            </div>

            <div class="mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i> Save Changes
                </button>
                <a href="water_reminders.php" class="btn btn-outline-secondary ms-2">Cancel</a>
            </div>
        </form>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
