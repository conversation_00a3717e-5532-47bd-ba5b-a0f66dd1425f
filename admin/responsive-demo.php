<?php
/**
 * Responsive Dashboard Demo Page
 * Demonstrates the new responsive dashboard features
 */

// Include authentication and configuration
require_once 'includes/config.php';
require_once 'includes/auth.php';
require_once 'includes/database.php';
require_once 'includes/utilities.php';

// Initialize authentication
$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

// Include header
include 'includes/header.php';
?>

<!-- Demo Content -->
<div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center mb-4 gap-3">
    <div>
        <h1 class="h3 font-weight-bold text-dark">Responsive Dashboard Demo</h1>
        <p class="text-muted">
            This page demonstrates the new responsive dashboard features including collapsible sidebar, 
            bottom navigation, and mobile-first design.
        </p>
    </div>
</div>

<!-- Feature Cards -->
<div class="row g-4 mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="premium-card p-4">
            <div class="d-flex align-items-center gap-3 mb-3">
                <div class="dashboard-card-icon bg-primary text-white">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <div>
                    <h5 class="mb-1">Mobile First</h5>
                    <p class="text-muted mb-0">Optimized for mobile devices</p>
                </div>
            </div>
            <p class="text-sm">
                The dashboard is built with a mobile-first approach, ensuring optimal 
                experience on smartphones and tablets.
            </p>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="premium-card p-4">
            <div class="d-flex align-items-center gap-3 mb-3">
                <div class="dashboard-card-icon bg-success text-white">
                    <i class="fas fa-bars"></i>
                </div>
                <div>
                    <h5 class="mb-1">Collapsible Sidebar</h5>
                    <p class="text-muted mb-0">Space-saving navigation</p>
                </div>
            </div>
            <p class="text-sm">
                The sidebar can be collapsed on desktop to provide more space for content 
                while maintaining easy access to navigation.
            </p>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="premium-card p-4">
            <div class="d-flex align-items-center gap-3 mb-3">
                <div class="dashboard-card-icon bg-info text-white">
                    <i class="fas fa-compass"></i>
                </div>
                <div>
                    <h5 class="mb-1">Bottom Navigation</h5>
                    <p class="text-muted mb-0">Native mobile app feel</p>
                </div>
            </div>
            <p class="text-sm">
                On mobile devices, a bottom navigation bar provides quick access to 
                the most important admin functions.
            </p>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6">
        <div class="premium-card p-4">
            <div class="d-flex align-items-center gap-3 mb-3">
                <div class="dashboard-card-icon bg-warning text-white">
                    <i class="fas fa-magic"></i>
                </div>
                <div>
                    <h5 class="mb-1">Smooth Animations</h5>
                    <p class="text-muted mb-0">Enhanced user experience</p>
                </div>
            </div>
            <p class="text-sm">
                All interactions include smooth animations and transitions that enhance 
                the overall user experience.
            </p>
        </div>
    </div>
</div>

<!-- Responsive Grid Demo -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="premium-card p-4">
            <h5 class="mb-3">Responsive Grid System</h5>
            <p class="text-muted mb-4">
                The dashboard uses a responsive grid system that adapts to different screen sizes:
            </p>
            
            <div class="row g-3">
                <div class="col-lg-4 col-md-6">
                    <div class="bg-light p-3 rounded text-center">
                        <i class="fas fa-desktop fa-2x text-primary mb-2"></i>
                        <h6>Desktop</h6>
                        <small class="text-muted">Multi-column layout</small>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="bg-light p-3 rounded text-center">
                        <i class="fas fa-tablet-alt fa-2x text-success mb-2"></i>
                        <h6>Tablet</h6>
                        <small class="text-muted">2-column layout</small>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="bg-light p-3 rounded text-center">
                        <i class="fas fa-mobile-alt fa-2x text-info mb-2"></i>
                        <h6>Mobile</h6>
                        <small class="text-muted">Single-column layout</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="row g-4">
    <div class="col-12">
        <div class="premium-card p-4">
            <h5 class="mb-3">How to Test</h5>
            <div class="row g-3">
                <div class="col-md-6">
                    <h6><i class="fas fa-desktop me-2"></i>Desktop Features</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Click the collapse button in the sidebar to minimize it</li>
                        <li><i class="fas fa-check text-success me-2"></i>Use Alt+S to toggle the sidebar</li>
                        <li><i class="fas fa-check text-success me-2"></i>Hover over sidebar items for smooth animations</li>
                        <li><i class="fas fa-check text-success me-2"></i>Resize the browser window to see responsive behavior</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-mobile-alt me-2"></i>Mobile Features</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Use the hamburger menu to open the sidebar</li>
                        <li><i class="fas fa-check text-success me-2"></i>Navigate using the bottom navigation bar</li>
                        <li><i class="fas fa-check text-success me-2"></i>Tap outside the sidebar to close it</li>
                        <li><i class="fas fa-check text-success me-2"></i>Press ESC to close the sidebar</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
