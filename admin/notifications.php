<?php
require_once 'includes/header.php';
require_once 'includes/notification_helper.php';

// Check if user has appropriate permissions
if (!$auth->hasRole('super_admin') && !$auth->hasRole('manager') && !$auth->hasPermission('view_notifications')) {
    Utilities::setFlashMessage('error', 'You do not have permission to view notifications.');
    Utilities::redirect('index.php');
    exit;
}

// Initialize database and notification helper
$db = new Database();
$conn = $db->getConnection();
$notificationHelper = new NotificationHelper($conn);

// Check if the notifications table exists
$tableExistsQuery = "SHOW TABLES LIKE 'notifications'";
$result = $conn->query($tableExistsQuery);
$tableExists = $result->num_rows > 0;

// If the table doesn't exist, show a message to create it
if (!$tableExists) {
    Utilities::setFlashMessage('warning', 'The notifications table does not exist. <a href="create_notifications_table.php">Click here to create it</a>.');
}

// Handle mark as read action
if (isset($_POST['action']) && $_POST['action'] === 'mark_read' && isset($_POST['notification_id'])) {
    $notificationId = (int)$_POST['notification_id'];
    if ($notificationHelper->markAsRead($notificationId)) {
        Utilities::setFlashMessage('success', 'Notification marked as read.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to mark notification as read.');
    }
    Utilities::redirect('notifications.php');
}

// Handle mark all as read action
if (isset($_POST['action']) && $_POST['action'] === 'mark_all_read') {
    if ($notificationHelper->markAllAsRead()) {
        Utilities::setFlashMessage('success', 'All notifications marked as read.');
    } else {
        Utilities::setFlashMessage('error', 'Failed to mark all notifications as read.');
    }
    Utilities::redirect('notifications.php');
}

// Get notifications
$filters = [
    'type' => isset($_GET['type']) ? $_GET['type'] : '',
    'is_read' => isset($_GET['is_read']) ? (int)$_GET['is_read'] : null
];

$limit = 10;
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$offset = ($page - 1) * $limit;

$result = $notificationHelper->getNotifications($filters, $limit, $offset);
$notifications = $result['notifications'];
$totalNotifications = $result['total'];
$totalPages = ceil($totalNotifications / $limit);

// Get notification statistics
$stats = $notificationHelper->getStatistics();
?>

<div class="d-sm-flex align-items-center justify-content-between mb-4">
    <div>
        <h1 class="h3 mb-0 text-dark">Notifications Panel</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-transparent p-0 mb-0">
                <li class="breadcrumb-item"><a href="index.php">Dashboard</a></li>
                <li class="breadcrumb-item active" aria-current="page">Notifications</li>
            </ol>
        </nav>
    </div>
    <div>
        <form method="post" action="notifications.php" class="d-inline">
            <input type="hidden" name="action" value="mark_all_read">
            <button type="submit" class="btn btn-sm btn-outline-dark">
                <i class="fas fa-check-double me-2"></i> Mark All as Read
            </button>
        </form>
        <a href="notifications.php" class="btn btn-sm btn-outline-dark ms-2">
            <i class="fas fa-sync me-2"></i> Refresh
        </a>
    </div>
</div>

<?php Utilities::displayFlashMessages(); ?>

<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-0 mb-4" style="background: #fff; border: 1px solid #e0e0e0;">
            <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center" style="border-bottom: 1px solid #e0e0e0;">
                <h6 class="m-0 font-weight-bold text-dark">Recent Notifications</h6>
                <div class="d-flex">
                    <div class="dropdown me-2">
                        <button class="btn btn-sm btn-outline-dark dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-filter me-1"></i> Filter
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end shadow-sm" aria-labelledby="filterDropdown">
                            <li><h6 class="dropdown-header">Filter by Type</h6></li>
                            <li><a class="dropdown-item <?php echo empty($_GET['type']) ? 'active' : ''; ?>" href="notifications.php">All Types</a></li>
                            <li><a class="dropdown-item <?php echo isset($_GET['type']) && $_GET['type'] === 'user' ? 'active' : ''; ?>" href="notifications.php?type=user">User</a></li>
                            <li><a class="dropdown-item <?php echo isset($_GET['type']) && $_GET['type'] === 'system' ? 'active' : ''; ?>" href="notifications.php?type=system">System</a></li>
                            <li><a class="dropdown-item <?php echo isset($_GET['type']) && $_GET['type'] === 'course' ? 'active' : ''; ?>" href="notifications.php?type=course">Course</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><h6 class="dropdown-header">Filter by Status</h6></li>
                            <li><a class="dropdown-item <?php echo !isset($_GET['is_read']) ? 'active' : ''; ?>" href="notifications.php<?php echo !empty($_GET['type']) ? '?type=' . $_GET['type'] : ''; ?>">All</a></li>
                            <li><a class="dropdown-item <?php echo isset($_GET['is_read']) && $_GET['is_read'] === '0' ? 'active' : ''; ?>" href="notifications.php?<?php echo !empty($_GET['type']) ? 'type=' . $_GET['type'] . '&' : ''; ?>is_read=0">Unread</a></li>
                            <li><a class="dropdown-item <?php echo isset($_GET['is_read']) && $_GET['is_read'] === '1' ? 'active' : ''; ?>" href="notifications.php?<?php echo !empty($_GET['type']) ? 'type=' . $_GET['type'] . '&' : ''; ?>is_read=1">Read</a></li>
                        </ul>
                    </div>
                    <?php if ($tableExists && !empty($notifications)): ?>
                    <span class="badge rounded-pill align-self-center" style="background: #111; color: #fff; font-weight: 500;"><?php echo $totalNotifications; ?> notifications</span>
                    <?php endif; ?>
                </div>
            </div>
            <div class="card-body">
                <?php if (!$tableExists): ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    The notifications table does not exist in the database.
                    <a href="create_notifications_table.php" class="alert-link">Click here to create it</a>.
                </div>
                <?php else: ?>
                <div class="notification-list">
                    <?php if (!empty($notifications)): ?>
                        <?php foreach ($notifications as $notification): ?>
                            <div class="notification-item <?php echo $notification['is_read'] ? 'read' : 'unread'; ?>" style="border-bottom: 1px solid #e0e0e0; padding: 1rem 0; display: flex; align-items: center; gap: 1rem; background: #fff; color: #111;">
                                <div class="notification-icon" style="font-size: 1.5rem;">
                                    <?php if ($notification['type'] === 'user'): ?>
                                        <i class="fas fa-user-circle" style="color: #111;"></i>
                                    <?php elseif ($notification['type'] === 'system'): ?>
                                        <i class="fas fa-cog" style="color: #888;"></i>
                                    <?php elseif ($notification['type'] === 'course'): ?>
                                        <i class="fas fa-graduation-cap" style="color: #222;"></i>
                                    <?php else: ?>
                                        <i class="fas fa-bell" style="color: #666;"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="notification-content" style="flex: 1;">
                                    <div class="notification-message" style="font-weight: 500; color: #111;"><?php echo htmlspecialchars($notification['message']); ?></div>
                                    <div class="notification-time" style="font-size: 0.9rem; color: #888;"><?php echo Utilities::timeAgo($notification['created_at']); ?></div>
                                </div>
                                <div class="notification-actions">
                                    <?php if (!$notification['is_read']): ?>
                                    <form method="post" action="notifications.php" class="d-inline">
                                        <input type="hidden" name="action" value="mark_read">
                                        <input type="hidden" name="notification_id" value="<?php echo $notification['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-outline-dark" title="Mark as read">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    <?php else: ?>
                                    <span class="text-muted small">
                                        <i class="fas fa-check-double"></i> Read
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if ($totalPages > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Notification pagination">
                                <ul class="pagination pagination-sm">
                                    <li class="page-item <?php echo $page <= 1 ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="notifications.php?page=<?php echo $page - 1; ?><?php echo !empty($_GET['type']) ? '&type=' . $_GET['type'] : ''; ?><?php echo isset($_GET['is_read']) ? '&is_read=' . $_GET['is_read'] : ''; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                                    <li class="page-item <?php echo $page == $i ? 'active' : ''; ?>">
                                        <a class="page-link" href="notifications.php?page=<?php echo $i; ?><?php echo !empty($_GET['type']) ? '&type=' . $_GET['type'] : ''; ?><?php echo isset($_GET['is_read']) ? '&is_read=' . $_GET['is_read'] : ''; ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                    <?php endfor; ?>
                                    <li class="page-item <?php echo $page >= $totalPages ? 'disabled' : ''; ?>">
                                        <a class="page-link" href="notifications.php?page=<?php echo $page + 1; ?><?php echo !empty($_GET['type']) ? '&type=' . $_GET['type'] : ''; ?><?php echo isset($_GET['is_read']) ? '&is_read=' . $_GET['is_read'] : ''; ?>" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        <?php endif; ?>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="fas fa-bell-slash fa-3x text-muted mb-3"></i>
                            <p class="mb-0">No notifications to display</p>
                            <?php if (!empty($_GET)): ?>
                            <a href="notifications.php" class="btn btn-sm btn-outline-dark mt-3">
                                <i class="fas fa-redo me-1"></i> Clear Filters
                            </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<style>
.notification-list {
    max-height: 400px;
    overflow-y: auto;
}
.notification-item {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid #f1f1f1;
    transition: background-color 0.3s;
}
.notification-item:hover {
    background-color: #f8f9fc;
}
.notification-item.unread {
    background-color: #f0f7ff;
}
.notification-icon {
    font-size: 24px;
    margin-right: 15px;
    display: flex;
    align-items: center;
}
.notification-content {
    flex: 1;
}
.notification-message {
    font-weight: 500;
    margin-bottom: 5px;
}
.notification-time {
    font-size: 12px;
    color: #6c757d;
}
.notification-actions {
    display: flex;
    align-items: center;
}
.minimalist-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111;
    line-height: 1.1;
}
.minimalist-label {
    font-size: 0.85rem;
    color: #888;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
    margin-bottom: 0.15rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Notification chart
    <?php if ($tableExists): ?>
    var ctx = document.getElementById('notificationChart').getContext('2d');
    var notificationChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [
                <?php foreach ($stats['chart_data'] as $data): ?>
                '<?php echo $data['date']; ?>',
                <?php endforeach; ?>
            ],
            datasets: [{
                label: 'Notifications',
                data: [
                    <?php foreach ($stats['chart_data'] as $data): ?>
                    <?php echo $data['count']; ?>,
                    <?php endforeach; ?>
                ],
                backgroundColor: 'rgba(0,0,0,0.05)',
                borderColor: '#111',
                borderWidth: 2,
                pointBackgroundColor: '#111',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                tension: 0.3
            }]
        },
        options: {
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        precision: 0,
                        color: '#222'
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.08)',
                        drawBorder: false
                    }
                },
                x: {
                    ticks: { color: '#222' },
                    grid: { color: 'rgba(0,0,0,0.08)', drawBorder: false }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        title: function(tooltipItems) {
                            return tooltipItems[0].label;
                        },
                        label: function(context) {
                            return context.parsed.y + ' notifications';
                        }
                    },
                    backgroundColor: '#fff',
                    titleColor: '#111',
                    bodyColor: '#222',
                    borderColor: '#eee',
                    borderWidth: 1,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true
                }
            }
        }
    });
    <?php endif; ?>
});
</script>

<?php require_once 'includes/footer.php'; ?>
