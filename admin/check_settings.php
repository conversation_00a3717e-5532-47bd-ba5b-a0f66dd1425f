<?php
// Enable error reporting
ini_set('display_errors', 1);
error_reporting(E_ALL);

require_once 'includes/config.php';
require_once 'includes/database.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Check if settings table exists
$query = "SHOW TABLES LIKE 'settings'";
$result = $conn->query($query);

echo "<h1>Settings Check</h1>";

if ($result->num_rows > 0) {
    echo "<p style='color:green'>✓ Table 'settings' exists</p>";

    // Check if is_dev_mode setting exists
    $query = "SELECT * FROM settings WHERE `key` = 'is_dev_mode'";
    $result = $conn->query($query);

    if ($result->num_rows > 0) {
        $setting = $result->fetch_assoc();
        echo "<p style='color:green'>✓ Setting 'is_dev_mode' exists with value: " . $setting['value'] . "</p>";
    } else {
        echo "<p style='color:red'>✗ Setting 'is_dev_mode' does not exist</p>";

        // Add the setting
        echo "<p>Adding 'is_dev_mode' setting...</p>";
        $query = "INSERT INTO settings (`key`, `value`, `group`, is_active) VALUES ('is_dev_mode', 'true', 'system', 1)";
        if ($conn->query($query)) {
            echo "<p style='color:green'>✓ Added 'is_dev_mode' setting</p>";
        } else {
            echo "<p style='color:red'>✗ Failed to add 'is_dev_mode' setting: " . $conn->error . "</p>";
        }
    }

    // Check if DEV_MODE constant is defined
    echo "<p>DEV_MODE constant is " . (defined('DEV_MODE') ? "defined as " . (DEV_MODE ? "true" : "false") : "not defined") . "</p>";
} else {
    echo "<p style='color:red'>✗ Table 'settings' does not exist</p>";

    // Create settings table
    echo "<p>Creating 'settings' table...</p>";
    $query = "CREATE TABLE `settings` (
        `id` bigint unsigned NOT NULL AUTO_INCREMENT,
        `key` varchar(255) NOT NULL,
        `value` text DEFAULT NULL,
        `group` varchar(255) DEFAULT NULL,
        `is_active` tinyint(1) NOT NULL DEFAULT 1,
        `created_at` timestamp NULL DEFAULT NULL,
        `updated_at` timestamp NULL DEFAULT NULL,
        PRIMARY KEY (`id`),
        UNIQUE KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if ($conn->query($query)) {
        echo "<p style='color:green'>✓ Created 'settings' table</p>";

        // Add is_dev_mode setting
        echo "<p>Adding 'is_dev_mode' setting...</p>";
        $query = "INSERT INTO settings (`key`, `value`, `group`, is_active) VALUES ('is_dev_mode', 'true', 'system', 1)";
        if ($conn->query($query)) {
            echo "<p style='color:green'>✓ Added 'is_dev_mode' setting</p>";
        } else {
            echo "<p style='color:red'>✗ Failed to add 'is_dev_mode' setting: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color:red'>✗ Failed to create 'settings' table: " . $conn->error . "</p>";
    }
}

// Force DEV_MODE to be true
echo "<p>Setting DEV_MODE to true...</p>";
define('DEV_MODE', true);
echo "<p>DEV_MODE is now " . (DEV_MODE ? "true" : "false") . "</p>";

// Check if quotes.php is accessible
echo "<h2>Quotes.php Access Check</h2>";
echo "<p>You should be able to access <a href='quotes.php'>quotes.php</a> now.</p>";
