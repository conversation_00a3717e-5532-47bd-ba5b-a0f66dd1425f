# KFT Fitness Database Setup Guide

This guide explains how to set up the database for the KFT Fitness application using the provided database backup file.

## Overview

The KFT Fitness application uses a MySQL database with the following main components:

- **User Management**: User accounts, authentication, and profiles
- **Course System**: Video courses with progress tracking
- **Streak Tracking**: Daily completion tracking and motivation
- **Health Metrics**: BMI records and fitness data
- **Nutrition**: Calorie tracking and food database
- **Admin Panel**: Administrative interface and settings
- **Motivational System**: Quotes and user engagement

## Database Files

### Primary Database File
- **`kft_db_backup_20250527_200550.sql`** - Complete database schema with sample data
  - Contains all table structures
  - Includes default admin user (username: `admin`, password: `admin123`, PIN: `1234`)
  - Sample motivational quotes, courses, and food items
  - All necessary indexes and foreign key constraints

### Additional Schema Files
- **`kft_complete_database_schema.sql`** - Comprehensive schema file (identical to backup)
- **`admin/kft_fitness.sql`** - Basic schema (legacy)
- **`admin/kft_fitness_updated.sql`** - Enhanced schema (legacy)
- **`database/migrations/create_streak_tables.sql`** - Streak system tables

## Quick Setup (Recommended)

### Option 1: Automated Setup Script

1. **Run the setup script:**
   ```bash
   ./setup_database.sh
   ```

2. **Follow the prompts:**
   - Enter your MySQL credentials
   - The script will automatically:
     - Test database connection
     - Import the complete schema
     - Verify table creation
     - Update configuration files
     - Display setup summary

### Option 2: Manual Setup

1. **Import the database:**
   ```bash
   mysql -u your_username -p < kft_db_backup_20250527_200550.sql
   ```

2. **Update configuration files:**
   
   Edit `admin/includes/config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'myclo4dz_new_kftdb');
   define('DB_PASS', 'U.q.!)hDK+gR');
   define('DB_NAME', 'myclo4dz_new_kftdb');
   ```
   
   Edit `admin/api/config.php` with the same credentials.

## Database Structure

### Core Tables

| Table | Purpose |
|-------|---------|
| `users` | User accounts and profiles |
| `admin_users` | Admin panel users |
| `api_tokens` | Mobile app authentication |
| `courses` | Video course information |
| `course_videos` | Individual course videos |
| `user_video_progress` | Video watching progress |
| `user_course_enrollments` | Course enrollment tracking |

### Health & Fitness Tables

| Table | Purpose |
|-------|---------|
| `bmi_records` | BMI tracking history |
| `workout_records` | Workout session logs |
| `workout_programs` | Available workout programs |
| `program_enrollments` | User program participation |
| `user_streaks` | Streak summary data |
| `streak_completions` | Daily completion records |

### Nutrition Tables

| Table | Purpose |
|-------|---------|
| `food_items` | Food database |
| `calorie_logs` | User food intake logs |
| `calorie_goals` | User calorie targets |

### System Tables

| Table | Purpose |
|-------|---------|
| `settings` | Application configuration |
| `motivational_quotes` | Motivational content |
| `user_quote_preferences` | User quote settings |
| `quote_settings` | Quote system configuration |
| `water_reminders` | Water reminder settings |
| `session_access` | Session management |

## Default Data

### Admin User
- **Username:** `admin`
- **Password:** `admin123`
- **PIN:** `1234`
- **Email:** `<EMAIL>`

⚠️ **Important:** Change the default password immediately after first login!

### Sample Content
- 8 motivational quotes across different categories
- 4 sample courses (Beginner to Advanced)
- 8 common food items with nutritional data
- Default system settings for development

## Configuration

### Database Connection Settings

The application uses these configuration files:

1. **`admin/includes/config.php`** - Main admin panel configuration
2. **`admin/api/config.php`** - API endpoint configuration

Both files should have identical database credentials:

```php
define('DB_HOST', 'localhost');
define('DB_USER', 'myclo4dz_new_kftdb');
define('DB_PASS', 'U.q.!)hDK+gR');
define('DB_NAME', 'myclo4dz_new_kftdb');
```

### Development Mode

The database is set up with development mode enabled by default:
- Error logging is active
- Debug information is available
- API responses include detailed error messages

To disable development mode, update the `settings` table:
```sql
UPDATE settings SET value = 'false' WHERE `key` = 'is_dev_mode';
```

## Verification

After setup, verify the installation:

1. **Check table count:**
   ```sql
   USE kft_fitness;
   SHOW TABLES;
   ```
   Should show approximately 20+ tables.

2. **Verify admin user:**
   ```sql
   SELECT username, email, role FROM admin_users;
   ```

3. **Check sample data:**
   ```sql
   SELECT COUNT(*) FROM motivational_quotes;
   SELECT COUNT(*) FROM courses;
   SELECT COUNT(*) FROM food_items;
   ```

## Troubleshooting

### Common Issues

1. **Connection Failed:**
   - Verify MySQL is running
   - Check credentials
   - Ensure database user has proper permissions

2. **Import Errors:**
   - Check MySQL version compatibility (5.7+ recommended)
   - Ensure sufficient disk space
   - Verify file permissions

3. **Missing Tables:**
   - Re-run the import process
   - Check for SQL syntax errors in logs
   - Verify MySQL user has CREATE privileges

### Backup and Recovery

1. **Create backup:**
   ```bash
   ./backup_database.sh
   ```

2. **Restore from backup:**
   ```bash
   mysql -u username -p kft_fitness < backup_file.sql
   ```

## Security Considerations

1. **Change default passwords** immediately
2. **Use strong database passwords**
3. **Limit database user permissions** to only necessary operations
4. **Enable SSL** for database connections in production
5. **Regular backups** are essential
6. **Keep MySQL updated** to latest stable version

## Support

For database-related issues:

1. Check the application logs in `admin/logs/`
2. Verify database connection in admin panel
3. Review MySQL error logs
4. Ensure all required PHP extensions are installed (mysqli, pdo_mysql)

---

**Note:** This database setup includes all features of the KFT Fitness application including user management, course system, streak tracking, nutrition logging, and administrative tools.
