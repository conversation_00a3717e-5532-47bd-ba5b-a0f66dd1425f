<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web Video Player Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%; /* 16:9 aspect ratio */
            margin-bottom: 20px;
        }
        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
        }
        .error {
            background-color: #ffebee;
            border-color: #f44336;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Web Video Player Test</h1>
        
        <div class="test-info">
            <h3>Test Information:</h3>
            <ul>
                <li><strong>Video ID:</strong> 1087487482 (Known working Vimeo video)</li>
                <li><strong>Domain:</strong> mycloudforge.com (Whitelisted in Vimeo)</li>
                <li><strong>Private Hash:</strong> ae75b6e329 (For private video access)</li>
                <li><strong>Test Purpose:</strong> Verify Vimeo iframe embedding works in browser</li>
            </ul>
        </div>

        <div id="status" class="status">
            Loading video player...
        </div>

        <div class="video-container">
            <iframe 
                id="vimeo-player"
                src="https://player.vimeo.com/video/1087487482?h=ae75b6e329&autoplay=1&muted=1&title=0&byline=0&portrait=0&responsive=1&controls=1&playsinline=1&referrer=https://mycloudforge.com/&background=0"
                allow="autoplay; fullscreen; picture-in-picture; encrypted-media"
                allowfullscreen>
            </iframe>
        </div>

        <div class="test-info">
            <h3>Expected Behavior:</h3>
            <ul>
                <li>✅ Video should load and start playing automatically</li>
                <li>✅ Video should be muted initially (required for autoplay)</li>
                <li>✅ Controls should be functional (pause, seek, volume)</li>
                <li>✅ Fullscreen button should work</li>
                <li>✅ User can unmute to hear audio</li>
                <li>❌ If you see "This video is private" or domain errors, there's an issue</li>
            </ul>
        </div>

        <div class="test-info">
            <h3>Troubleshooting:</h3>
            <ul>
                <li><strong>Domain Error:</strong> Check if mycloudforge.com is whitelisted in Vimeo</li>
                <li><strong>Private Video Error:</strong> Verify the hash parameter is correct</li>
                <li><strong>Loading Issues:</strong> Check browser console for errors</li>
                <li><strong>Autoplay Issues:</strong> Video starts muted (browser requirement), click unmute to hear audio</li>
                <li><strong>Grey Screen:</strong> Video should start playing automatically, if not check browser autoplay settings</li>
            </ul>
        </div>
    </div>

    <script>
        // Test script to monitor video player status
        const iframe = document.getElementById('vimeo-player');
        const statusDiv = document.getElementById('status');

        // Monitor iframe load events
        iframe.onload = function() {
            statusDiv.textContent = '✅ Video player loaded successfully';
            statusDiv.className = 'status';
        };

        iframe.onerror = function() {
            statusDiv.textContent = '❌ Failed to load video player';
            statusDiv.className = 'status error';
        };

        // Check for common issues
        setTimeout(() => {
            if (statusDiv.textContent === 'Loading video player...') {
                statusDiv.textContent = '⚠️ Video player taking longer than expected to load';
                statusDiv.className = 'status error';
            }
        }, 5000);

        // Log to console for debugging
        console.log('🔍 Web Video Player Test initialized');
        console.log('📺 Testing Vimeo video: 1087487482');
        console.log('🌐 Domain: mycloudforge.com');
        console.log('🔐 Private hash: ae75b6e329');
    </script>
</body>
</html> 