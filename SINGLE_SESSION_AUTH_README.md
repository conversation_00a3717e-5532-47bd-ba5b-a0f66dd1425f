# Single-Session Authentication System

This implementation provides a comprehensive single-session authentication system for the Flutter app that ensures only one active session per user account across all devices.

## Features

### ✅ Implemented Features

1. **Device ID Management**
   - Unique device ID generation and storage
   - Automatic device ID replacement on new login
   - Secure storage using Flutter Secure Storage

2. **Automatic Session Termination**
   - Invalidates authentication tokens on old device when new login occurs
   - Clears local user data and preferences on session invalidation
   - Automatic redirection to login screen with user-friendly message

3. **User Notification**
   - Clear, user-friendly logout messages
   - Animated dialog with professional design
   - Informative messages explaining the logout reason

4. **Offline Handling**
   - Defers logout process when device is offline
   - Detects session invalidation on reconnect
   - Prevents data loss during offline periods
   - Smart reconnection handling

5. **Session Validation**
   - Periodic session validation checks (every 5 minutes)
   - Device ID verification with server
   - Automatic token refresh and validation
   - Background validation without interrupting user experience

6. **Error Handling**
   - Comprehensive error handling for network failures
   - Graceful degradation when services are unavailable
   - Retry mechanisms with exponential backoff
   - Self-healing capabilities

## Architecture

### Core Components

1. **SingleSessionAuthService** (`lib/services/single_session_auth_service.dart`)
   - Manages device IDs and session validation
   - Handles offline logout scenarios
   - Provides session event streams

2. **SessionManager** (`lib/services/session_manager.dart`)
   - Coordinates between auth services
   - Manages user notifications and dialogs
   - Handles session lifecycle events

3. **SessionLogoutDialog** (`lib/widgets/session_logout_dialog.dart`)
   - User-friendly logout notification dialogs
   - Animated and professional design
   - Customizable messages and actions

4. **Backend Integration**
   - Enhanced login endpoint with device management
   - New session validation endpoint
   - Comprehensive logging and monitoring

### Integration Points

- **PersistentAuthService**: Enhanced with session manager integration
- **Main App**: Session manager initialization and context management
- **AuthWrapper**: Session validation during app lifecycle
- **Login Flow**: Automatic session management on successful login

## Usage

### Automatic Integration

The single-session authentication system is automatically integrated into the existing authentication flow. No changes are required to existing login/logout code.

### Manual Session Validation

```dart
// Get session manager instance
final sessionManager = SessionManager();

// Force session validation
final result = await sessionManager.forceValidation();

// Check validation result
switch (result) {
  case SessionValidationResult.valid:
    print('Session is valid');
    break;
  case SessionValidationResult.invalidated:
    print('Session was invalidated');
    break;
  case SessionValidationResult.offline:
    print('Device is offline');
    break;
  // Handle other cases...
}
```

### Listening to Session Events

```dart
// Listen to session events
SessionManager().singleSessionService.sessionEventStream.listen((event) {
  switch (event.type) {
    case SessionEventType.deviceReplaced:
      print('Device was replaced: ${event.message}');
      break;
    case SessionEventType.sessionInvalidated:
      print('Session invalidated: ${event.message}');
      break;
    // Handle other events...
  }
});
```

### Custom Logout Dialog

```dart
// Show custom logout dialog
await SessionLogoutDialog.show(
  context,
  reason: 'Custom logout reason',
  onConfirm: () {
    // Handle logout confirmation
    Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
  },
);
```

## Backend Requirements

### Database Schema

The existing user table should have a `device_id` column:

```sql
ALTER TABLE users ADD COLUMN device_id VARCHAR(255) DEFAULT NULL;
```

### API Endpoints

1. **Enhanced Login** (`admin/api/login.php`)
   - Handles device ID management
   - Returns forced logout status
   - Invalidates old device tokens

2. **Session Validation** (`admin/api/session_validate.php`)
   - Validates device ID against stored value
   - Checks token expiry and validity
   - Returns session status and user information

## Configuration

### Validation Intervals

```dart
// In SingleSessionAuthService
static const Duration _validationInterval = Duration(minutes: 5);
static const Duration _offlineCheckInterval = Duration(seconds: 30);
```

### Storage Keys

All sensitive data is stored using Flutter Secure Storage with the following keys:
- `single_session_device_id`: Current device ID
- `single_session_id`: Current session ID
- `last_session_validation`: Last validation timestamp
- `logout_reason`: Reason for logout (for user notification)

## Security Features

1. **Encrypted Storage**: All session data stored using Flutter Secure Storage
2. **Device Verification**: Server-side device ID validation
3. **Token Invalidation**: Automatic token cleanup on device change
4. **Session Timeout**: Configurable session timeout periods
5. **Audit Logging**: Comprehensive logging of session events

## Error Handling

The system includes comprehensive error handling for:
- Network connectivity issues
- Server unavailability
- Storage access failures
- Token parsing errors
- Session validation failures

All errors are logged and handled gracefully without breaking the user experience.

## Testing

### Manual Testing

1. **Device Change Test**:
   - Login on Device A
   - Login with same account on Device B
   - Verify Device A shows logout dialog
   - Verify Device A redirects to login

2. **Offline Test**:
   - Login on Device A
   - Disconnect Device A from internet
   - Login with same account on Device B
   - Reconnect Device A
   - Verify Device A shows logout dialog when it reconnects

3. **Session Validation Test**:
   - Login and wait for periodic validation
   - Check logs for validation success
   - Manually invalidate session on server
   - Verify app detects invalidation

### Automated Testing

Unit tests can be added for:
- Device ID generation and storage
- Session validation logic
- Offline handling scenarios
- Error handling paths

## Monitoring

The system provides extensive logging for monitoring:
- Session creation and invalidation events
- Device ID changes and conflicts
- Validation success/failure rates
- Offline handling scenarios
- Error occurrences and recovery

All logs are prefixed with emojis for easy identification:
- 🔐 Authentication events
- 🆔 Device ID operations
- 🔍 Session validation
- 📱 Offline handling
- ✅ Success operations
- ❌ Error conditions
- ⚠️ Warning conditions

## Future Enhancements

Potential future improvements:
1. **Multi-device Support**: Allow limited number of concurrent sessions
2. **Device Management UI**: Allow users to see and manage their devices
3. **Session Analytics**: Track session patterns and usage
4. **Advanced Security**: Implement device fingerprinting
5. **Push Notifications**: Notify users of new device logins
