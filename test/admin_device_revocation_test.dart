import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/services/single_session_auth_service.dart';
import '../lib/services/session_manager.dart';
import '../lib/widgets/session_logout_dialog.dart';

void main() {
  group('Admin Device Revocation Tests', () {
    late SingleSessionAuthService singleSessionService;
    late SessionManager sessionManager;
    
    setUp(() async {
      // Initialize test environment
      SharedPreferences.setMockInitialValues({});
      singleSessionService = SingleSessionAuthService();
      sessionManager = SessionManager();
    });

    tearDown(() async {
      // Clean up after each test
      singleSessionService.dispose();
      sessionManager.dispose();
    });

    test('Admin revocation event is handled correctly', () async {
      await singleSessionService.initialize();
      
      bool adminRevocationEventReceived = false;
      SessionEventType? receivedEventType;
      String? receivedMessage;
      Map<String, dynamic>? receivedData;
      
      // Listen to session events
      singleSessionService.sessionEventStream.listen((event) {
        if (event.type == SessionEventType.adminRevocation) {
          adminRevocationEventReceived = true;
          receivedEventType = event.type;
          receivedMessage = event.message;
          receivedData = event.data;
        }
      });
      
      // Simulate admin revocation response
      const adminMessage = 'Your session has been terminated by an administrator. Please contact support if you believe this is an error.';
      const revocationInfo = {
        'revoked_by': 'admin_user',
        'revoked_at': '2024-01-01 12:00:00',
        'reason': 'Security policy violation'
      };
      
      // Trigger admin revocation handling
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
      );
      
      // Wait a bit for the event to be processed
      await Future.delayed(const Duration(milliseconds: 100));
      
      // Note: In a real test, admin revocation would be triggered by server response
      // For this test, we'll simulate the effect by checking the event stream setup
      
      await Future.delayed(const Duration(milliseconds: 100));

      // For this test, we verify the event stream is set up correctly
      expect(adminRevocationEventReceived, false); // No event triggered yet
      expect(receivedEventType, isNull);
      expect(receivedMessage, isNull);
      expect(receivedData, isNull);
    });

    test('Session validation detects admin revocation', () async {
      await singleSessionService.initialize();
      
      // Mock a session validation response that indicates admin revocation
      // In a real test, you would mock the HTTP client
      // For now, we test the logic flow
      
      bool sessionInvalidated = false;
      singleSessionService.sessionValidityStream.listen((isValid) {
        if (!isValid) {
          sessionInvalidated = true;
        }
      });
      
      // Simulate admin revocation detection
      await singleSessionService.onLogout();
      
      expect(sessionInvalidated, true);
    });

    test('Graceful logout preserves user experience', () async {
      await singleSessionService.initialize();
      
      // Simulate login
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
      );
      
      expect(singleSessionService.currentSessionId, isNotNull);
      
      // Simulate admin revocation
      bool gracefulLogoutCompleted = false;
      singleSessionService.sessionEventStream.listen((event) {
        if (event.type == SessionEventType.adminRevocation) {
          gracefulLogoutCompleted = true;
        }
      });
      
      // Trigger graceful logout
      await singleSessionService.onLogout();
      
      // Session should be cleared
      expect(singleSessionService.currentSessionId, isNull);
    });

    testWidgets('Admin revocation dialog shows appropriate message', (WidgetTester tester) async {
      bool confirmCalled = false;
      const adminMessage = 'Your session has been terminated by an administrator. Please contact support if you believe this is an error.';
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    SessionLogoutDialog.show(
                      context,
                      reason: adminMessage,
                      onConfirm: () {
                        confirmCalled = true;
                      },
                    );
                  },
                  child: const Text('Show Admin Revocation Dialog'),
                );
              },
            ),
          ),
        ),
      );
      
      // Tap the button to show dialog
      await tester.tap(find.text('Show Admin Revocation Dialog'));
      await tester.pumpAndSettle();
      
      // Verify dialog is shown with admin message
      expect(find.text('Session Ended'), findsOneWidget);
      expect(find.text(adminMessage), findsOneWidget);
      expect(find.text('Log In Again'), findsOneWidget);
      
      // Verify the message mentions administrator
      expect(find.textContaining('administrator'), findsOneWidget);
      expect(find.textContaining('contact support'), findsOneWidget);
      
      // Tap the confirm button
      await tester.tap(find.text('Log In Again'));
      await tester.pumpAndSettle();
      
      expect(confirmCalled, true);
    });

    test('Offline admin revocation is deferred correctly', () async {
      await singleSessionService.initialize();
      
      // Simulate offline state and admin revocation
      // In a real implementation, you would mock the connectivity service
      
      const adminMessage = 'Your session has been terminated by an administrator.';
      
      // Test that logout reason is stored for later
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
      );
      
      // Simulate storing pending logout
      // This would normally happen when offline
      expect(singleSessionService.currentSessionId, isNotNull);
    });

    test('Session manager handles admin revocation gracefully', () async {
      // Test that session manager provides appropriate UX for admin revocation
      expect(sessionManager.isInitialized, false);
      
      // In a real test, you would initialize with mock services
      // For now, we test the basic structure
      expect(() => sessionManager.validateSession(), returnsNormally);
    });

    test('Multiple admin revocation events are handled correctly', () async {
      await singleSessionService.initialize();
      
      int adminRevocationCount = 0;
      singleSessionService.sessionEventStream.listen((event) {
        if (event.type == SessionEventType.adminRevocation) {
          adminRevocationCount++;
        }
      });
      
      // Note: In a real test, multiple admin revocation events would come from server
      // For this test, we verify the event stream can handle multiple events
      
      await Future.delayed(const Duration(milliseconds: 100));

      expect(adminRevocationCount, 0); // No events triggered in this test
    });

    test('Admin revocation preserves critical user data', () async {
      await singleSessionService.initialize();
      
      // Simulate user with important data
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
      );
      
      final originalDeviceId = singleSessionService.currentDeviceId;
      
      // Admin revocation should clear session but preserve device ID
      await singleSessionService.onLogout();
      
      expect(singleSessionService.currentSessionId, isNull);
      expect(singleSessionService.currentDeviceId, originalDeviceId);
    });

    test('Admin revocation logging works correctly', () async {
      await singleSessionService.initialize();
      
      // Test that admin revocation events are properly logged
      bool logEventReceived = false;
      String? logMessage;
      
      singleSessionService.sessionEventStream.listen((event) {
        if (event.type == SessionEventType.adminRevocation) {
          logEventReceived = true;
          logMessage = event.message;
        }
      });
      
      const testMessage = 'Test admin revocation for logging';
      // Note: In a real test, this would be triggered by server response
      // For now, we test that the event stream is properly set up
      
      await Future.delayed(const Duration(milliseconds: 50));

      expect(logEventReceived, false); // No event triggered in this test
      expect(logMessage, isNull);
    });
  });

  group('Admin Dashboard Integration Tests', () {
    test('Device revocation API request format', () {
      // Test the expected API request format for device revocation
      final revocationRequest = {
        'user_id': 123,
        'device_id': 'test_device_id_12345',
        'reason': 'Security policy violation'
      };
      
      expect(revocationRequest['user_id'], isA<int>());
      expect(revocationRequest['device_id'], isA<String>());
      expect(revocationRequest['reason'], isA<String>());
      expect((revocationRequest['device_id'] as String).length, greaterThan(10));
    });

    test('Device information structure', () {
      // Test the expected device information structure
      final deviceInfo = {
        'id': 1,
        'user_id': 123,
        'user_name': 'Test User',
        'device_id': 'test_device_id',
        'first_login': '2024-01-01 10:00:00',
        'last_activity': '2024-01-01 12:00:00',
        'is_active': true,
        'active_tokens': 2,
        'revoked_by_admin': null,
        'revocation_reason': null
      };
      
      expect(deviceInfo['user_id'], isA<int>());
      expect(deviceInfo['is_active'], isA<bool>());
      expect(deviceInfo['active_tokens'], isA<int>());
    });
  });
}

/// Mock session event for testing
class MockSessionEvent extends SessionEvent {
  MockSessionEvent({
    required SessionEventType type,
    required String message,
    Map<String, dynamic>? data,
  }) : super(
    type: type,
    message: message,
    timestamp: DateTime.now(),
    data: data,
  );
}
