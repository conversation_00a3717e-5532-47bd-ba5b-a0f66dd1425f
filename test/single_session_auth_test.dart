import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../lib/services/single_session_auth_service.dart';
import '../lib/services/session_manager.dart';
import '../lib/widgets/session_logout_dialog.dart';

void main() {
  group('Single Session Authentication Tests', () {
    late SingleSessionAuthService singleSessionService;
    
    setUp(() async {
      // Initialize test environment
      SharedPreferences.setMockInitialValues({});
      singleSessionService = SingleSessionAuthService();
    });

    tearDown(() async {
      // Clean up after each test
      singleSessionService.dispose();
    });

    test('SingleSessionAuthService initializes correctly', () async {
      expect(singleSessionService.isInitialized, false);
      
      // Initialize the service
      await singleSessionService.initialize();
      
      expect(singleSessionService.isInitialized, true);
      expect(singleSessionService.currentDeviceId, isNotNull);
    });

    test('Device ID is generated and persisted', () async {
      await singleSessionService.initialize();
      
      final deviceId1 = singleSessionService.currentDeviceId;
      expect(deviceId1, isNotNull);
      expect(deviceId1!.isNotEmpty, true);
      
      // Create a new instance to test persistence
      final newService = SingleSessionAuthService();
      await newService.initialize();
      
      final deviceId2 = newService.currentDeviceId;
      expect(deviceId2, equals(deviceId1));
      
      newService.dispose();
    });

    test('Session validation returns correct results', () async {
      await singleSessionService.initialize();
      
      // Test with invalid token
      final result = await singleSessionService.validateSession('invalid_token');
      expect(result, isIn([
        SessionValidationResult.failed,
        SessionValidationResult.error,
        SessionValidationResult.offline,
      ]));
    });

    test('Session events are emitted correctly', () async {
      await singleSessionService.initialize();
      
      bool eventReceived = false;
      SessionEventType? receivedEventType;
      
      // Listen to session events
      singleSessionService.sessionEventStream.listen((event) {
        eventReceived = true;
        receivedEventType = event.type;
      });
      
      // Simulate a login success
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
        serverResponse: {'forced_logout': true},
      );
      
      // Wait a bit for the event to be processed
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(eventReceived, true);
      expect(receivedEventType, SessionEventType.deviceReplaced);
    });

    test('Logout clears session data', () async {
      await singleSessionService.initialize();
      
      // Simulate login
      await singleSessionService.onLoginSuccess(
        userId: 'test_user',
        token: 'test_token',
      );
      
      expect(singleSessionService.currentSessionId, isNotNull);
      
      // Perform logout
      await singleSessionService.onLogout();
      
      expect(singleSessionService.currentSessionId, isNull);
    });

    testWidgets('SessionLogoutDialog displays correctly', (WidgetTester tester) async {
      bool confirmCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    SessionLogoutDialog.show(
                      context,
                      reason: 'Test logout reason',
                      onConfirm: () {
                        confirmCalled = true;
                      },
                    );
                  },
                  child: const Text('Show Dialog'),
                );
              },
            ),
          ),
        ),
      );
      
      // Tap the button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();
      
      // Verify dialog is shown
      expect(find.text('Session Ended'), findsOneWidget);
      expect(find.text('Test logout reason'), findsOneWidget);
      expect(find.text('Log In Again'), findsOneWidget);
      
      // Tap the confirm button
      await tester.tap(find.text('Log In Again'));
      await tester.pumpAndSettle();
      
      expect(confirmCalled, true);
    });

    testWidgets('AnimatedSessionLogoutDialog displays with animation', (WidgetTester tester) async {
      bool confirmCalled = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    AnimatedSessionLogoutDialog.show(
                      context,
                      reason: 'Animated test logout reason',
                      onConfirm: () {
                        confirmCalled = true;
                      },
                    );
                  },
                  child: const Text('Show Animated Dialog'),
                );
              },
            ),
          ),
        ),
      );
      
      // Tap the button to show dialog
      await tester.tap(find.text('Show Animated Dialog'));
      await tester.pump(); // Start animation
      await tester.pump(const Duration(milliseconds: 150)); // Partial animation
      
      // Verify dialog is animating in
      expect(find.text('Session Ended'), findsOneWidget);
      
      await tester.pumpAndSettle(); // Complete animation
      
      // Verify dialog is fully shown
      expect(find.text('Animated test logout reason'), findsOneWidget);
      expect(find.text('Log In Again'), findsOneWidget);
    });
  });

  group('SessionManager Tests', () {
    late SessionManager sessionManager;
    
    setUp(() async {
      SharedPreferences.setMockInitialValues({});
      sessionManager = SessionManager();
    });

    tearDown(() async {
      sessionManager.dispose();
    });

    test('SessionManager initializes correctly', () async {
      expect(sessionManager.isInitialized, false);
      
      // Note: In a real test, you'd need to provide mock services
      // For now, we just test the basic structure
      expect(sessionManager.singleSessionService, isNotNull);
    });

    test('Session validation delegates to single session service', () async {
      // This test would require mocking the auth services
      // For now, we just verify the method exists and doesn't throw
      expect(() => sessionManager.validateSession(), returnsNormally);
    });
  });

  group('Integration Tests', () {
    test('Complete login flow with session management', () async {
      final singleSessionService = SingleSessionAuthService();
      await singleSessionService.initialize();
      
      // Simulate first login
      await singleSessionService.onLoginSuccess(
        userId: 'user123',
        token: 'token123',
        serverResponse: {'forced_logout': false},
      );
      
      expect(singleSessionService.currentSessionId, isNotNull);
      expect(singleSessionService.currentDeviceId, isNotNull);
      
      // Simulate second login from different device (forced logout)
      bool sessionInvalidated = false;
      singleSessionService.sessionEventStream.listen((event) {
        if (event.type == SessionEventType.deviceReplaced) {
          sessionInvalidated = true;
        }
      });
      
      await singleSessionService.onLoginSuccess(
        userId: 'user123',
        token: 'token456',
        serverResponse: {'forced_logout': true},
      );
      
      await Future.delayed(const Duration(milliseconds: 100));
      expect(sessionInvalidated, true);
      
      singleSessionService.dispose();
    });
  });
}

/// Mock classes for testing
class MockFlutterSecureStorage extends FlutterSecureStorage {
  final Map<String, String> _storage = {};
  
  @override
  Future<String?> read({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WindowsOptions? wOptions, WebOptions? webOptions, MacOsOptions? mOptions}) async {
    return _storage[key];
  }
  
  @override
  Future<void> write({required String key, required String? value, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WindowsOptions? wOptions, WebOptions? webOptions, MacOsOptions? mOptions}) async {
    if (value == null) {
      _storage.remove(key);
    } else {
      _storage[key] = value;
    }
  }
  
  @override
  Future<void> delete({required String key, IOSOptions? iOptions, AndroidOptions? aOptions, LinuxOptions? lOptions, WindowsOptions? wOptions, WebOptions? webOptions, MacOsOptions? mOptions}) async {
    _storage.remove(key);
  }
}
