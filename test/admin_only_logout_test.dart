import 'package:flutter_test/flutter_test.dart';
import 'package:http/http.dart' as http;
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:your_app/services/single_session_auth_service.dart';
import 'package:your_app/services/session_manager.dart';

// Generate mocks
@GenerateMocks([http.Client])
import 'admin_only_logout_test.mocks.dart';

void main() {
  group('Admin-Only Logout System Tests', () {
    late MockClient mockClient;
    late SingleSessionAuthService authService;
    late SessionManager sessionManager;

    setUp(() {
      mockClient = MockClient();
      authService = SingleSessionAuthService();
      sessionManager = SessionManager();
    });

    test('Should logout only for admin revocation', () async {
      // Mock admin revocation response
      when(mockClient.post(
        any,
        body: anyNamed('body'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        '{"success": false, "error": "Device access revoked by administrator: Security policy violation"}',
        403,
      ));

      // Test admin revocation detection
      final result = await authService.validateSession('test_token');
      
      expect(result, equals(SessionValidationResult.invalidated));
    });

    test('Should NOT logout for network timeout', () async {
      // Mock network timeout
      when(mockClient.post(
        any,
        body: anyNamed('body'),
        headers: anyNamed('headers'),
      )).thenThrow(Exception('Timeout'));

      // Test timeout handling
      final result = await authService.validateSession('test_token');
      
      expect(result, equals(SessionValidationResult.valid));
    });

    test('Should NOT logout for server error', () async {
      // Mock server error
      when(mockClient.post(
        any,
        body: anyNamed('body'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        '{"success": false, "error": "Internal server error"}',
        500,
      ));

      // Test server error handling
      final result = await authService.validateSession('test_token');
      
      expect(result, equals(SessionValidationResult.valid));
    });

    test('Should NOT logout for token expiration', () async {
      // Mock token expiration
      when(mockClient.post(
        any,
        body: anyNamed('body'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        '{"success": false, "error": "Token has expired"}',
        401,
      ));

      // Test token expiration handling
      final result = await authService.validateSession('test_token');
      
      expect(result, equals(SessionValidationResult.valid));
    });

    test('Should NOT logout for device mismatch', () async {
      // Mock device mismatch
      when(mockClient.post(
        any,
        body: anyNamed('body'),
        headers: anyNamed('headers'),
      )).thenAnswer((_) async => http.Response(
        '{"success": false, "error": "Device ID mismatch - session invalidated"}',
        403,
      ));

      // Test device mismatch handling
      final result = await authService.validateSession('test_token');
      
      expect(result, equals(SessionValidationResult.valid));
    });

    test('Should logout for admin revocation with different message formats', () async {
      final adminRevocationMessages = [
        'Device access revoked by administrator: Security policy violation',
        'Device access revoked by administrator: Account suspended',
        'Device access revoked by administrator: Unauthorized access',
      ];

      for (final message in adminRevocationMessages) {
        when(mockClient.post(
          any,
          body: anyNamed('body'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(
          '{"success": false, "error": "$message"}',
          403,
        ));

        final result = await authService.validateSession('test_token');
        expect(result, equals(SessionValidationResult.invalidated), 
               reason: 'Should logout for admin revocation: $message');
      }
    });

    test('Should NOT logout for non-admin 403 errors', () async {
      final nonAdminErrors = [
        'Access forbidden',
        'Insufficient permissions',
        'Resource not accessible',
      ];

      for (final error in nonAdminErrors) {
        when(mockClient.post(
          any,
          body: anyNamed('body'),
          headers: anyNamed('headers'),
        )).thenAnswer((_) async => http.Response(
          '{"success": false, "error": "$error"}',
          403,
        ));

        final result = await authService.validateSession('test_token');
        expect(result, equals(SessionValidationResult.valid), 
               reason: 'Should NOT logout for non-admin error: $error');
      }
    });
  });
} 