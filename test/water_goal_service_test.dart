import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../lib/services/water_goal_service.dart';

void main() {
  group('WaterGoalService Tests', () {
    late WaterGoalService service;

    setUp(() async {
      // Initialize SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
      service = WaterGoalService();
    });

    tearDown(() async {
      // Clear settings after each test
      await service.clearSettings();
    });

    test('should return default settings when no settings exist', () async {
      final settings = await service.getGoalSettings();
      
      expect(settings.goalInMl, equals(2000));
      expect(settings.preferredUnit, equals(WaterUnit.liters));
      expect(settings.isCustomGoal, equals(false));
    });

    test('should save and retrieve goal settings', () async {
      const testGoal = 2500;
      const testUnit = WaterUnit.milliliters;
      
      await service.updateDailyGoal(testGoal);
      await service.updatePreferredUnit(testUnit);
      
      final settings = await service.getGoalSettings();
      
      expect(settings.goalInMl, equals(testGoal));
      expect(settings.preferredUnit, equals(testUnit));
    });

    test('should convert between ml and liters correctly', () {
      expect(WaterGoalService.mlToLiters(1000), equals(1.0));
      expect(WaterGoalService.mlToLiters(2500), equals(2.5));
      expect(WaterGoalService.litersToMl(1.5), equals(1500));
      expect(WaterGoalService.litersToMl(2.0), equals(2000));
    });

    test('should format goal display correctly', () {
      expect(
        WaterGoalService.formatGoalDisplay(2000, WaterUnit.liters),
        equals('2L'),
      );
      expect(
        WaterGoalService.formatGoalDisplay(2500, WaterUnit.liters),
        equals('2.5L'),
      );
      expect(
        WaterGoalService.formatGoalDisplay(2000, WaterUnit.milliliters),
        equals('2000ml'),
      );
    });

    test('should validate goal ranges correctly', () {
      expect(WaterGoalService.isValidGoal(500), isTrue);
      expect(WaterGoalService.isValidGoal(2000), isTrue);
      expect(WaterGoalService.isValidGoal(5000), isTrue);
      expect(WaterGoalService.isValidGoal(400), isFalse);
      expect(WaterGoalService.isValidGoal(5100), isFalse);
    });

    test('should provide correct goal presets', () {
      final presets = WaterGoalService.getGoalPresets();
      
      expect(presets, contains(1000));
      expect(presets, contains(1500));
      expect(presets, contains(2000));
      expect(presets, contains(2500));
      expect(presets, contains(3000));
    });

    test('should provide quick adjustment amounts', () {
      final adjustments = WaterGoalService.getQuickAdjustments();
      
      expect(adjustments, contains(250));
      expect(adjustments, contains(500));
      expect(adjustments, contains(750));
    });

    test('should persist settings across service instances', () async {
      const testGoal = 3000;
      const testUnit = WaterUnit.milliliters;
      
      // Save with first instance
      await service.updateDailyGoal(testGoal);
      await service.updatePreferredUnit(testUnit);
      
      // Create new instance and verify persistence
      final newService = WaterGoalService();
      final settings = await newService.getGoalSettings();
      
      expect(settings.goalInMl, equals(testGoal));
      expect(settings.preferredUnit, equals(testUnit));
    });
  });

  group('WaterGoalSettings Tests', () {
    test('should create default settings correctly', () {
      final settings = WaterGoalSettings.defaultSettings();
      
      expect(settings.goalInMl, equals(2000));
      expect(settings.preferredUnit, equals(WaterUnit.liters));
      expect(settings.isCustomGoal, equals(false));
    });

    test('should convert to and from JSON correctly', () {
      final originalSettings = WaterGoalSettings(
        goalInMl: 2500,
        preferredUnit: WaterUnit.milliliters,
        lastUpdated: DateTime(2024, 1, 15),
        isCustomGoal: true,
      );
      
      final json = originalSettings.toJson();
      final restoredSettings = WaterGoalSettings.fromJson(json);
      
      expect(restoredSettings.goalInMl, equals(originalSettings.goalInMl));
      expect(restoredSettings.preferredUnit, equals(originalSettings.preferredUnit));
      expect(restoredSettings.isCustomGoal, equals(originalSettings.isCustomGoal));
    });

    test('should copy with modifications correctly', () {
      final originalSettings = WaterGoalSettings.defaultSettings();
      final modifiedSettings = originalSettings.copyWith(
        goalInMl: 3000,
        preferredUnit: WaterUnit.milliliters,
      );
      
      expect(modifiedSettings.goalInMl, equals(3000));
      expect(modifiedSettings.preferredUnit, equals(WaterUnit.milliliters));
      expect(modifiedSettings.isCustomGoal, equals(originalSettings.isCustomGoal));
    });

    test('should get goal in different units correctly', () {
      final settings = WaterGoalSettings(
        goalInMl: 2500,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      expect(settings.getGoalInUnit(WaterUnit.milliliters), equals(2500.0));
      expect(settings.getGoalInUnit(WaterUnit.liters), equals(2.5));
    });

    test('should identify preset goals correctly', () {
      final presetSettings = WaterGoalSettings(
        goalInMl: 2000,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      final customSettings = WaterGoalSettings(
        goalInMl: 2250,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      expect(presetSettings.isPresetGoal, isTrue);
      expect(customSettings.isPresetGoal, isFalse);
    });

    test('should format display text correctly', () {
      final literSettings = WaterGoalSettings(
        goalInMl: 2000,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      final mlSettings = WaterGoalSettings(
        goalInMl: 2000,
        preferredUnit: WaterUnit.milliliters,
        lastUpdated: DateTime.now(),
      );
      
      expect(literSettings.displayText, equals('2L'));
      expect(mlSettings.displayText, equals('2000ml'));
    });

    test('should handle equality correctly', () {
      final settings1 = WaterGoalSettings(
        goalInMl: 2000,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      final settings2 = WaterGoalSettings(
        goalInMl: 2000,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now().add(const Duration(hours: 1)),
      );
      
      final settings3 = WaterGoalSettings(
        goalInMl: 2500,
        preferredUnit: WaterUnit.liters,
        lastUpdated: DateTime.now(),
      );
      
      expect(settings1, equals(settings2)); // Same goal and unit
      expect(settings1, isNot(equals(settings3))); // Different goal
    });
  });

  group('WaterUnit Extension Tests', () {
    test('should provide correct display names', () {
      expect(WaterUnit.milliliters.displayName, equals('ml'));
      expect(WaterUnit.liters.displayName, equals('L'));
    });

    test('should provide correct full names', () {
      expect(WaterUnit.milliliters.fullName, equals('Milliliters'));
      expect(WaterUnit.liters.fullName, equals('Liters'));
    });
  });
}
