# Network Connection Fixes

This document outlines the fixes implemented to resolve SSL certificate and connection issues in the Flutter app.

## Issues Fixed

### 1. SSL Certificate Verification Failure
**Error**: `CERTIFICATE_VERIFY_FAILED: unable to get local issuer certificate`

**Solution**: 
- Added SSL certificate bypass for development mode
- Updated `network_security_config.xml` to trust user certificates
- Created custom HTTP client with `badCertificateCallback`

### 2. Connection Refused Error
**Error**: `SocketException: Connection refused (OS Error: Connection refused, errno = 111)`

**Solution**:
- Implemented automatic endpoint detection
- Added multiple fallback endpoints
- Created endpoint switching functionality for debug mode

## Files Modified

### Core Network Configuration
- `lib/config/network_config.dart` - New network configuration helper
- `lib/services/api_service.dart` - Updated with SSL bypass and better error handling
- `android/app/src/main/res/xml/network_security_config.xml` - Enhanced security config

### UI Components
- `lib/widgets/network_error_dialog.dart` - New error dialog with suggestions
- `lib/pages/debug_network_page.dart` - Debug tools for network testing
- `lib/pages/settings_page.dart` - Added debug network option
- `lib/pages/login_page.dart` - Updated error handling

### Configuration
- `lib/config/app_config.dart` - Updated to use NetworkConfig
- `lib/main.dart` - Added API service initialization

## Features Added

### 1. Automatic Endpoint Detection
The app now automatically tests multiple endpoints and selects the first working one:
- `https://mycloudforge.com/admin/api/`
- `http://***************:9001/admin/api/`
- `http://localhost:8080/admin/api/`
- `http://127.0.0.1:8080/admin/api/`

### 2. Debug Network Tools
In debug mode, users can access network debugging tools via Settings:
- Test all endpoints
- Switch between endpoints
- View connectivity status
- Get error-specific suggestions

### 3. Enhanced Error Handling
- Network errors now show helpful dialogs with suggestions
- SSL errors are automatically handled in debug mode
- Connection errors provide retry and endpoint switching options

### 4. SSL Certificate Bypass
For development environments:
- Automatically bypasses SSL verification in debug mode
- Trusts user-added certificates
- Allows self-signed certificates

## Usage

### For Developers
1. Run the app in debug mode
2. If network errors occur, go to Settings > Debug Tools > Network Debug
3. Test endpoints and switch to working ones
4. Use the retry functionality in error dialogs

### For Production
- SSL bypass is disabled in production builds
- Only the production endpoint is used
- Standard error handling applies

## Error Messages and Solutions

### "Connection refused"
1. Check if server is running
2. Verify IP address/port
3. Check firewall settings
4. For MIUI devices: Disable battery optimization

### "SSL Certificate error"
1. Common in development with self-signed certificates
2. App automatically bypasses in debug mode
3. Check network_security_config.xml settings

### "Network unreachable"
1. Check internet connection
2. Try different network
3. Verify server accessibility

## Testing

To test the network fixes:
1. Run the app in debug mode
2. Go to Settings > Debug Tools > Network Debug
3. Use "Test All Endpoints" to check connectivity
4. Try switching endpoints if current one fails
5. Test login/registration with different network conditions

## Configuration for Different Environments

### Development
- Multiple endpoints available
- SSL bypass enabled
- Debug tools accessible

### Production
- Single production endpoint
- Full SSL verification
- No debug tools

The app automatically detects the environment and applies appropriate settings.
