<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hosted Player</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .player-container {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .logs {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎬 KFT Hosted Player Test</h1>
        <p>Testing the hosted Vimeo player with seek throttling and stability improvements.</p>
        
        <div class="controls">
            <button onclick="loadPlayer()">Load Player</button>
            <button onclick="testSeek(30)">Seek to 30s</button>
            <button onclick="testSeek(60)">Seek to 60s</button>
            <button onclick="testSeek(120)">Seek to 120s</button>
            <button onclick="testRapidSeek()">Rapid Seek Test</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <div class="player-container">
            <iframe id="hosted-player" 
                    src="" 
                    width="100%" 
                    height="100%" 
                    frameborder="0"
                    allow="autoplay; fullscreen; picture-in-picture">
            </iframe>
        </div>
        
        <h3>📋 Test Logs</h3>
        <div id="logs" class="logs"></div>
    </div>

    <script>
        let logContainer = document.getElementById('logs');
        let playerFrame = document.getElementById('hosted-player');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }
        
        function clearLogs() {
            logContainer.innerHTML = '';
        }
        
        function loadPlayer() {
            log('🔄 Loading hosted player...');
            
            // Build hosted player URL with test parameters
            const params = new URLSearchParams({
                vimeo_id: '76979871', // Test video
                video_id: '999999',
                domain: 'mycloudforge.com',
                autoplay: '0',
                t: Date.now().toString()
            });
            
            const hostedUrl = `./hosted_vimeo_player.html?${params.toString()}`;
            log(`🔗 Loading URL: ${hostedUrl}`);
            
            playerFrame.src = hostedUrl;
            
            // Set up message listener for player events
            window.addEventListener('message', function(event) {
                if (event.origin !== window.location.origin) return;
                
                log(`📨 Received message: ${JSON.stringify(event.data)}`);
            });
        }
        
        function testSeek(position) {
            log(`🎯 Testing seek to ${position}s`);
            
            try {
                playerFrame.contentWindow.postMessage({
                    action: 'seek',
                    position: position
                }, '*');
                
                log(`✅ Seek command sent to ${position}s`);
            } catch (error) {
                log(`❌ Failed to send seek command: ${error.message}`);
            }
        }
        
        function testRapidSeek() {
            log('🚀 Starting rapid seek test...');
            
            const positions = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];
            let index = 0;
            
            function performNextSeek() {
                if (index < positions.length) {
                    const position = positions[index];
                    log(`🎛️ Rapid seek ${index + 1}/${positions.length}: ${position}s`);
                    testSeek(position);
                    index++;
                    
                    // Rapid seeking - 100ms intervals
                    setTimeout(performNextSeek, 100);
                } else {
                    log('🏁 Rapid seek test completed');
                }
            }
            
            performNextSeek();
        }
        
        // Initialize
        log('🎬 Hosted Player Test initialized');
        log('📝 Click "Load Player" to start testing');
    </script>
</body>
</html>
