<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Expose-Headers: Authorization');
header('Access-Control-Max-Age: 86400');

// <PERSON>le preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// Verify admin authentication
$auth = verifyAdminToken();
if (!$auth['valid']) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access',
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
    exit();
}

try {
    $search = $_GET['search'] ?? '';
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = min(100, max(10, (int)($_GET['limit'] ?? 20)));
    $offset = ($page - 1) * $limit;
    
    // Build search condition
    $search_condition = '';
    $search_params = [];
    
    if (!empty($search)) {
        $search_condition = "WHERE (u.name LIKE ? OR u.phone LIKE ? OR u.email LIKE ?)";
        $search_term = "%$search%";
        $search_params = [$search_term, $search_term, $search_term];
    }
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM users u $search_condition";
    $stmt = $pdo->prepare($count_sql);
    $stmt->execute($search_params);
    $total_count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Get users with water reminder status
    $sql = "
        SELECT 
            u.id,
            u.name,
            u.phone,
            u.email,
            u.created_at,
            u.last_login,
            CASE WHEN wr.id IS NOT NULL THEN 1 ELSE 0 END as has_water_reminder,
            wr.is_enabled as water_reminder_enabled,
            wr.daily_target,
            wr.admin_managed
        FROM users u
        LEFT JOIN water_reminders wr ON u.id = wr.user_id
        $search_condition
        ORDER BY u.name ASC
        LIMIT ? OFFSET ?
    ";
    
    $params = array_merge($search_params, [$limit, $offset]);
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Format users data
    $formatted_users = array_map(function($user) {
        return [
            'id' => (int)$user['id'],
            'name' => $user['name'],
            'phone' => $user['phone'],
            'email' => $user['email'],
            'created_at' => $user['created_at'],
            'last_login' => $user['last_login'],
            'has_water_reminder' => (bool)$user['has_water_reminder'],
            'water_reminder_enabled' => $user['water_reminder_enabled'] ? (bool)$user['water_reminder_enabled'] : null,
            'daily_target' => $user['daily_target'] ? (int)$user['daily_target'] : null,
            'admin_managed' => $user['admin_managed'] ? (bool)$user['admin_managed'] : false
        ];
    }, $users);
    
    echo json_encode([
        'success' => true,
        'users' => $formatted_users,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total_count' => (int)$total_count,
            'total_pages' => ceil($total_count / $limit),
            'has_next' => ($page * $limit) < $total_count,
            'has_prev' => $page > 1
        ],
        'search' => $search,
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
    
} catch (Exception $e) {
    error_log("Users List Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage(),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
}
?>
