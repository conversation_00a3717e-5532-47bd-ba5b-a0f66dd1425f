-- KFT Fitness Simple Database Setup
-- This file creates the essential database structure for development

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS kft_fitness;
USE kft_fitness;

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    pin CHAR(4) DEFAULT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'editor', 'viewer') NOT NULL DEFAULT 'editor',
    last_login TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    name VARCHAR(100) NOT NULL
);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VA<PERSON>HA<PERSON>(50) UNIQUE,
    password VARCHAR(255),
    pin CHAR(4) DEFAULT NULL,
    name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    gender ENUM('male', 'female', 'other'),
    fitness_goal VARCHAR(50),
    age INT,
    height DECIMAL(5,2) DEFAULT NULL,
    weight DECIMAL(5,2) DEFAULT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    verification_code VARCHAR(10),
    verification_expires_at TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- API tokens table
CREATE TABLE IF NOT EXISTS api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(100) NOT NULL,
    `value` TEXT DEFAULT NULL,
    `group` VARCHAR(50) DEFAULT 'general',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key (`key`)
);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnail_url VARCHAR(500),
    duration_weeks INT DEFAULT 1,
    difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Course videos table
CREATE TABLE IF NOT EXISTS course_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    vimeo_id VARCHAR(50),
    vimeo_password VARCHAR(255),
    duration_seconds INT DEFAULT 0,
    week_number INT DEFAULT 1,
    video_order INT DEFAULT 0,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- User video progress table
CREATE TABLE IF NOT EXISTS user_video_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    watch_time_seconds INT DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_video (user_id, video_id)
);

-- User streaks table
CREATE TABLE IF NOT EXISTS user_streaks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    current_streak INT DEFAULT 0,
    best_streak INT DEFAULT 0,
    total_completions INT DEFAULT 0,
    last_completion_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_streak (user_id)
);

-- Motivational quotes table
CREATE TABLE IF NOT EXISTS motivational_quotes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quote TEXT NOT NULL,
    author VARCHAR(255) DEFAULT NULL,
    category VARCHAR(100) DEFAULT NULL,
    is_ai_generated TINYINT(1) NOT NULL DEFAULT 0,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- BMI records table
CREATE TABLE IF NOT EXISTS bmi_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    weight DECIMAL(5,2) NOT NULL,
    height DECIMAL(5,2) NOT NULL,
    bmi DECIMAL(4,2) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123, pin: 1234)
INSERT INTO admin_users (username, password, pin, email, role, name) VALUES
('admin', '$2y$10$8zUkFXRsqFvgQa.Yl9VDOeQJPb0zrQGwXcaVCPqPHbCiGWYXwwRne', '1234', '<EMAIL>', 'admin', 'Admin')
ON DUPLICATE KEY UPDATE id=id;

-- Insert default system settings
INSERT INTO settings (`key`, `value`, `group`, is_active) VALUES
('is_dev_mode', 'true', 'system', 1),
('app_version', '1.0.0', 'system', 1),
('maintenance_mode', 'false', 'system', 1)
ON DUPLICATE KEY UPDATE `value`=VALUES(`value`);

-- Insert sample motivational quotes
INSERT INTO motivational_quotes (quote, author, category, is_active) VALUES
('The only bad workout is the one that didn\'t happen.', 'Unknown', 'fitness', 1),
('Your body can do it. It\'s your mind you have to convince.', 'Unknown', 'motivation', 1),
('Fitness is not about being better than someone else. It\'s about being better than you used to be.', 'Khloe Kardashian', 'fitness', 1),
('Take care of your body. It\'s the only place you have to live.', 'Jim Rohn', 'health', 1)
ON DUPLICATE KEY UPDATE quote=VALUES(quote);

-- Insert sample courses
INSERT INTO courses (title, description, duration_weeks, difficulty, is_premium, is_active, sort_order) VALUES
('Beginner Fitness Journey', 'Perfect for those starting their fitness journey. Learn the basics of exercise and build healthy habits.', 4, 'beginner', FALSE, TRUE, 1),
('Intermediate Strength Training', 'Take your fitness to the next level with structured strength training routines.', 6, 'intermediate', TRUE, TRUE, 2)
ON DUPLICATE KEY UPDATE title=VALUES(title);
