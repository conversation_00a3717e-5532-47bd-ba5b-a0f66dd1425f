# Custom Vimeo Video Player Integration Guide

## Overview

This implementation provides a secure, custom Vimeo video player integration for the Flutter mobile app with domain protection, user access validation, and comprehensive analytics tracking.

## Features Implemented

### 🔒 **Security & Domain Protection**
- **Domain Restrictions**: Videos can only be played within the app environment
- **User Access Validation**: Server-side verification of user enrollment and video access rights
- **Secure Token System**: One-time playback tokens with expiration and validation
- **Activity Logging**: Comprehensive tracking of all video access attempts for security monitoring
- **Suspicious Activity Detection**: Automatic detection and alerting for unusual access patterns

### 🎥 **Custom Video Player**
- **Native Vimeo Integration**: Uses `vimeo_video_player` package for optimal performance
- **Cross-Platform Support**: Works on both iOS and Android with WebView fallback for web
- **Custom Controls**: Maintains existing UI/UX while adding Vimeo-specific functionality
- **Progress Tracking**: Real-time video progress updates with 80% completion detection
- **Error Handling**: Graceful error handling with retry mechanisms

### 📊 **Analytics & Tracking**
- **Video Analytics**: Track views, completions, and engagement metrics
- **User Progress**: Detailed progress tracking with position saving and resume functionality
- **Security Logs**: Complete audit trail of video access attempts
- **Performance Monitoring**: Track player performance and error rates

## Implementation Details

### Frontend Components

#### 1. Custom Vimeo Player Widget (`lib/widgets/custom_vimeo_player.dart`)
```dart
CustomVimeoPlayer(
  video: courseVideo,
  autoPlay: false,
  showControls: true,
  onProgress: (position) => updateProgress(position),
  onCompleted: () => markAsCompleted(),
  onError: (error) => handleError(error),
)
```

**Key Features:**
- Secure video ID extraction and validation
- Real-time progress tracking every 10 seconds
- Automatic completion detection at 80% watched
- Cross-platform compatibility (iOS/Android/Web)
- Error handling with retry mechanisms

#### 2. Video Security Helper (`lib/utils/video_security_helper.dart`)
```dart
// Generate secure embed URL
final embedUrl = await VideoSecurityHelper.getSecureEmbedUrl(
  vimeoId: vimeoId,
  videoId: videoId,
  userId: userId,
);

// Validate user access
final hasAccess = await VideoSecurityHelper.validateUserAccess(videoId, userId);

// Log video activity
await VideoSecurityHelper.logVideoAccess(
  vimeoId: vimeoId,
  videoId: videoId,
  userId: userId,
  action: 'play',
);
```

**Security Features:**
- HMAC-SHA256 token generation with app domain binding
- Time-based token expiration (24 hours)
- User enrollment validation
- Video unlock status verification
- Comprehensive activity logging

### Backend API Endpoints

#### 1. Video Access Validation (`api/validate_video_access.php`)
- **Purpose**: Validates if a user has access to a specific video
- **Security**: Checks user enrollment, video unlock status, and subscription status
- **Logging**: Records all access attempts for security monitoring

#### 2. Vimeo Metadata API (`api/vimeo_metadata.php`)
- **Purpose**: Retrieves video metadata from Vimeo API
- **Features**: Supports both public (oEmbed) and private (API token) videos
- **Security**: Validates user access before returning metadata

#### 3. Playback Token Generation (`api/generate_playback_token.php`)
- **Purpose**: Generates secure, one-time playback tokens
- **Security**: HMAC-signed tokens with expiration and domain binding
- **Management**: Automatic cleanup of expired tokens

#### 4. Video Access Logging (`api/log_video_access.php`)
- **Purpose**: Comprehensive logging of all video interactions
- **Analytics**: Updates video view counts and completion statistics
- **Security**: Detects and alerts on suspicious activity patterns

### Database Schema

#### New Tables Created:
```sql
-- Video access logs for security monitoring
CREATE TABLE video_access_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vimeo_id VARCHAR(50),
    video_id INT NOT NULL,
    user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NULL,
    app_domain VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- One-time playback tokens
CREATE TABLE playback_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    token TEXT NOT NULL,
    video_id INT NOT NULL,
    user_id INT NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP NULL
);

-- Video analytics
CREATE TABLE video_analytics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    video_id INT NOT NULL UNIQUE,
    total_views INT DEFAULT 0,
    total_completions INT DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Security alerts
CREATE TABLE security_alerts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    alert_type VARCHAR(100) NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Configuration Requirements

### 1. Environment Variables
```bash
# Vimeo API Configuration
VIMEO_ACCESS_TOKEN=your_vimeo_access_token_here

# Security Configuration
VIDEO_SECRET_KEY=your_secure_secret_key_here
```

### 2. Vimeo Account Settings
- **Domain Whitelist**: Add your app's package name to Vimeo domain restrictions
- **Privacy Settings**: Configure video privacy settings for your use case
- **API Access**: Ensure your Vimeo account has API access enabled

### 3. App Configuration
Update `lib/utils/video_security_helper.dart`:
```dart
static const String _appDomain = 'com.yourapp.package'; // Your app's package name
static const String _secretKey = 'your-secret-key-here'; // Should match backend
```

## Usage Examples

### 1. Playing a Vimeo Video
```dart
// In your video player page
Widget _buildSecureVimeoPlayer() {
  return AspectRatio(
    aspectRatio: 16 / 9,
    child: CustomVimeoPlayer(
      video: widget.video,
      autoPlay: false,
      showControls: true,
      onProgress: (position) {
        _updateVideoProgressSecure(position);
      },
      onCompleted: () {
        _markAsCompleted();
        VideoSecurityHelper.logVideoAccess(
          vimeoId: VideoSecurityHelper.extractVimeoId(widget.video.videoUrl) ?? '',
          videoId: widget.video.id,
          userId: currentUserId,
          action: 'complete',
        );
      },
      onError: (error) {
        setState(() {
          _errorMessage = error;
        });
      },
    ),
  );
}
```

### 2. Admin Panel Integration
When adding Vimeo videos through the admin panel:
```php
// Store Vimeo URL in the database
$vimeoUrl = "https://vimeo.com/123456789";
$vimeoId = extractVimeoId($vimeoUrl); // Extract ID: 123456789

// Store in course_videos table
INSERT INTO course_videos (
    course_id, title, description, video_url, video_id, video_provider
) VALUES (
    ?, ?, ?, ?, ?, 'vimeo'
);
```

## Security Best Practices

### 1. Token Management
- Tokens expire after 24 hours
- One-time use tokens for sensitive operations
- Automatic cleanup of expired tokens
- HMAC-SHA256 signing for integrity

### 2. Access Control
- Server-side validation of all video access
- User enrollment verification
- Video unlock status checking
- Subscription status validation

### 3. Monitoring & Alerts
- Log all video access attempts
- Detect suspicious activity patterns
- Alert on excessive access attempts
- Track failed authentication attempts

### 4. Domain Protection
- Restrict video playback to app domain only
- Validate referrer headers
- Use secure embed URLs with domain binding
- Prevent direct video URL access

## Testing & Validation

### 1. Security Testing
- Verify domain restrictions work
- Test token expiration and validation
- Confirm user access controls
- Validate suspicious activity detection

### 2. Functionality Testing
- Test video playback on different devices
- Verify progress tracking accuracy
- Confirm completion detection works
- Test error handling and recovery

### 3. Performance Testing
- Monitor video loading times
- Track memory usage during playback
- Verify smooth playback performance
- Test network error handling

## Troubleshooting

### Common Issues:
1. **Video won't play**: Check user enrollment and video unlock status
2. **Domain errors**: Verify Vimeo domain whitelist settings
3. **Token errors**: Check secret key configuration and token expiration
4. **Progress not saving**: Verify API connectivity and authentication

### Debug Steps:
1. Check browser/app console for errors
2. Verify API endpoint responses
3. Check database logs for access attempts
4. Validate Vimeo account settings

## Maintenance

### Regular Tasks:
- Clean up expired tokens (automated)
- Monitor security alerts
- Review video analytics
- Update Vimeo API tokens as needed

### Performance Optimization:
- Monitor video loading performance
- Optimize token generation
- Cache video metadata when possible
- Implement CDN for better performance

This implementation provides a robust, secure foundation for Vimeo video integration with comprehensive protection against unauthorized access while maintaining excellent user experience.
