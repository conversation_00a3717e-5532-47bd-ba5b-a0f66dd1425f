<?php
/**
 * FCM Notification Scheduler for KFT Fitness App
 * 
 * This script handles server-side notification scheduling and sending
 * for reliable push notifications using Firebase Cloud Messaging.
 */

require_once 'vendor/autoload.php';
require_once 'config/database.php';

use Google\Auth\Credentials\ServiceAccountCredentials;
use GuzzleHttp\Client;

class FCMNotificationScheduler {
    private $db;
    private $fcmServerKey;
    private $projectId;
    private $httpClient;
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->fcmServerKey = $_ENV['FCM_SERVER_KEY'] ?? '';
        $this->projectId = $_ENV['FIREBASE_PROJECT_ID'] ?? '';
        $this->httpClient = new Client();
    }
    
    /**
     * Register FCM token for a user
     */
    public function registerToken($userId, $token, $platform) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO fcm_tokens (user_id, token, platform, created_at, updated_at) 
                VALUES (?, ?, ?, NOW(), NOW())
                ON DUPLICATE KEY UPDATE 
                token = VALUES(token), 
                platform = VALUES(platform), 
                updated_at = NOW()
            ");
            
            $stmt->execute([$userId, $token, $platform]);
            
            return ['success' => true, 'message' => 'Token registered successfully'];
        } catch (Exception $e) {
            error_log("Error registering FCM token: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to register token'];
        }
    }
    
    /**
     * Update workout reminder time for a user
     */
    public function updateWorkoutTime($userId, $workoutTime, $timezone) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO user_notification_preferences (user_id, workout_time, timezone, updated_at) 
                VALUES (?, ?, ?, NOW())
                ON DUPLICATE KEY UPDATE 
                workout_time = VALUES(workout_time), 
                timezone = VALUES(timezone), 
                updated_at = NOW()
            ");
            
            $stmt->execute([$userId, $workoutTime, $timezone]);
            
            return ['success' => true, 'message' => 'Workout time updated successfully'];
        } catch (Exception $e) {
            error_log("Error updating workout time: " . $e->getMessage());
            return ['success' => false, 'message' => 'Failed to update workout time'];
        }
    }
    
    /**
     * Send notification to specific token
     */
    public function sendToToken($token, $title, $body, $data = []) {
        try {
            $message = [
                'message' => [
                    'token' => $token,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                    'data' => $data,
                    'android' => [
                        'notification' => [
                            'icon' => 'ic_launcher',
                            'color' => '#6366F1',
                            'sound' => 'default',
                            'channel_id' => $data['type'] ?? 'general_notifications',
                        ],
                        'priority' => 'high',
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $title,
                                    'body' => $body,
                                ],
                                'badge' => 1,
                                'sound' => 'default',
                            ],
                        ],
                    ],
                ],
            ];
            
            $response = $this->httpClient->post(
                "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send",
                [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->getAccessToken(),
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $message,
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            
            return ['success' => true, 'message_id' => $result['name'] ?? null];
        } catch (Exception $e) {
            error_log("Error sending FCM notification: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Send notification to topic
     */
    public function sendToTopic($topic, $title, $body, $data = []) {
        try {
            $message = [
                'message' => [
                    'topic' => $topic,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                    'data' => $data,
                    'android' => [
                        'notification' => [
                            'icon' => 'ic_launcher',
                            'color' => '#6366F1',
                            'sound' => 'default',
                            'channel_id' => $data['type'] ?? 'general_notifications',
                        ],
                        'priority' => 'high',
                    ],
                    'apns' => [
                        'payload' => [
                            'aps' => [
                                'alert' => [
                                    'title' => $title,
                                    'body' => $body,
                                ],
                                'badge' => 1,
                                'sound' => 'default',
                            ],
                        ],
                    ],
                ],
            ];
            
            $response = $this->httpClient->post(
                "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send",
                [
                    'headers' => [
                        'Authorization' => 'Bearer ' . $this->getAccessToken(),
                        'Content-Type' => 'application/json',
                    ],
                    'json' => $message,
                ]
            );
            
            $result = json_decode($response->getBody(), true);
            
            return ['success' => true, 'message_id' => $result['name'] ?? null];
        } catch (Exception $e) {
            error_log("Error sending FCM topic notification: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Schedule workout reminders for all users
     */
    public function scheduleWorkoutReminders() {
        try {
            // Get all users with workout notifications enabled
            $stmt = $this->db->prepare("
                SELECT u.id, u.name, p.workout_time, p.timezone, t.token 
                FROM users u
                JOIN user_notification_preferences p ON u.id = p.user_id
                JOIN fcm_tokens t ON u.id = t.user_id
                WHERE p.workout_enabled = 1 
                AND t.token IS NOT NULL
                AND p.workout_time IS NOT NULL
            ");
            
            $stmt->execute();
            $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $successCount = 0;
            $errorCount = 0;
            
            foreach ($users as $user) {
                $currentTime = new DateTime('now', new DateTimeZone($user['timezone'] ?? 'UTC'));
                $workoutTime = DateTime::createFromFormat('H:i', $user['workout_time'], new DateTimeZone($user['timezone'] ?? 'UTC'));
                
                // Check if it's time for workout reminder (within 1 minute window)
                $timeDiff = abs($currentTime->getTimestamp() - $workoutTime->getTimestamp());
                
                if ($timeDiff <= 60) { // Within 1 minute
                    $result = $this->sendToToken(
                        $user['token'],
                        'Workout Reminder 💪',
                        "Hi {$user['name']}! Time for your daily workout! Let's get moving and stay fit!",
                        [
                            'type' => 'workout',
                            'action' => 'open_workouts',
                            'user_id' => $user['id'],
                        ]
                    );
                    
                    if ($result['success']) {
                        $successCount++;
                    } else {
                        $errorCount++;
                    }
                }
            }
            
            return [
                'success' => true,
                'sent' => $successCount,
                'errors' => $errorCount,
                'total_users' => count($users),
            ];
        } catch (Exception $e) {
            error_log("Error scheduling workout reminders: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Schedule water reminders
     */
    public function scheduleWaterReminders() {
        try {
            $currentHour = (int)date('H');
            $waterReminderHours = [9, 12, 15, 18]; // 9 AM, 12 PM, 3 PM, 6 PM
            
            if (!in_array($currentHour, $waterReminderHours)) {
                return ['success' => true, 'message' => 'Not a water reminder hour'];
            }
            
            // Send to water reminders topic
            $result = $this->sendToTopic(
                'water_reminders',
                'Stay Hydrated! 💧',
                'Time for a refreshing glass of water. Your body will thank you!',
                [
                    'type' => 'water',
                    'action' => 'log_water',
                    'hour' => $currentHour,
                ]
            );
            
            return $result;
        } catch (Exception $e) {
            error_log("Error scheduling water reminders: " . $e->getMessage());
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * Get OAuth2 access token for FCM
     */
    private function getAccessToken() {
        // This would typically use a service account key file
        // For now, return the server key (legacy method)
        return $this->fcmServerKey;
    }
}

// API endpoints
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $scheduler = new FCMNotificationScheduler();
    $input = json_decode(file_get_contents('php://input'), true);
    $endpoint = $_GET['endpoint'] ?? '';
    
    header('Content-Type: application/json');
    
    switch ($endpoint) {
        case 'register-token':
            $result = $scheduler->registerToken(
                $input['user_id'] ?? null,
                $input['token'] ?? null,
                $input['platform'] ?? null
            );
            echo json_encode($result);
            break;
            
        case 'update-workout-time':
            $result = $scheduler->updateWorkoutTime(
                $input['user_id'] ?? null,
                $input['workout_time'] ?? null,
                $input['timezone'] ?? null
            );
            echo json_encode($result);
            break;
            
        case 'send-test':
            $result = $scheduler->sendToToken(
                $input['token'] ?? null,
                $input['title'] ?? 'Test Notification',
                $input['body'] ?? 'This is a test notification',
                $input['data'] ?? []
            );
            echo json_encode($result);
            break;
            
        case 'schedule-workout':
            $result = $scheduler->scheduleWorkoutReminders();
            echo json_encode($result);
            break;
            
        case 'schedule-water':
            $result = $scheduler->scheduleWaterReminders();
            echo json_encode($result);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => 'Invalid endpoint']);
            break;
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Only POST requests allowed']);
}
?>
