-- FCM Tokens table to store user device tokens
CREATE TABLE IF NOT EXISTS fcm_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL,
    platform ENUM('android', 'ios') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_token (user_id, token),
    INDEX idx_user_id (user_id),
    INDEX idx_token (token),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User notification preferences table
CREATE TABLE IF NOT EXISTS user_notification_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    workout_enabled BOOLEAN DEFAULT TRUE,
    water_enabled BOOLEAN DEFAULT TRUE,
    workout_time TIME DEFAULT '07:00:00',
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_prefs (user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_workout_time (workout_time),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Notification logs table to track sent notifications
CREATE TABLE IF NOT EXISTS notification_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    token VARCHAR(255),
    notification_type ENUM('workout', 'water', 'progress', 'general') NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSON,
    fcm_message_id VARCHAR(255),
    status ENUM('sent', 'failed', 'delivered') DEFAULT 'sent',
    error_message TEXT,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_type (notification_type),
    INDEX idx_sent_at (sent_at),
    INDEX idx_status (status),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Scheduled notifications table for future notifications
CREATE TABLE IF NOT EXISTS scheduled_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    notification_type ENUM('workout', 'water', 'progress', 'general') NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    data JSON,
    scheduled_time DATETIME NOT NULL,
    timezone VARCHAR(50) DEFAULT 'UTC',
    status ENUM('pending', 'sent', 'cancelled', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_scheduled_time (scheduled_time),
    INDEX idx_status (status),
    INDEX idx_type (notification_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- FCM topic subscriptions table
CREATE TABLE IF NOT EXISTS fcm_topic_subscriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    topic VARCHAR(100) NOT NULL,
    subscribed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_user_topic (user_id, topic),
    INDEX idx_user_id (user_id),
    INDEX idx_topic (topic),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default notification preferences for existing users
INSERT IGNORE INTO user_notification_preferences (user_id, workout_enabled, water_enabled, workout_time, timezone)
SELECT id, TRUE, TRUE, '07:00:00', 'UTC' FROM users;

-- Insert default topic subscriptions for existing users
INSERT IGNORE INTO fcm_topic_subscriptions (user_id, topic)
SELECT id, 'general_notifications' FROM users
UNION ALL
SELECT id, 'progress_updates' FROM users;

-- Add indexes for better performance
ALTER TABLE fcm_tokens ADD INDEX idx_platform (platform);
ALTER TABLE fcm_tokens ADD INDEX idx_updated_at (updated_at);
ALTER TABLE user_notification_preferences ADD INDEX idx_workout_enabled (workout_enabled);
ALTER TABLE user_notification_preferences ADD INDEX idx_water_enabled (water_enabled);
ALTER TABLE notification_logs ADD INDEX idx_fcm_message_id (fcm_message_id);

-- Create a view for active user tokens with preferences
CREATE OR REPLACE VIEW active_user_tokens AS
SELECT 
    u.id as user_id,
    u.name,
    u.email,
    t.token,
    t.platform,
    p.workout_enabled,
    p.water_enabled,
    p.workout_time,
    p.timezone,
    t.updated_at as token_updated_at
FROM users u
JOIN fcm_tokens t ON u.id = t.user_id
LEFT JOIN user_notification_preferences p ON u.id = p.user_id
WHERE t.token IS NOT NULL
AND t.updated_at > DATE_SUB(NOW(), INTERVAL 30 DAY); -- Only tokens updated in last 30 days

-- Create a stored procedure for cleaning up old tokens
DELIMITER //
CREATE PROCEDURE CleanupOldTokens()
BEGIN
    -- Delete tokens older than 90 days
    DELETE FROM fcm_tokens 
    WHERE updated_at < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- Delete old notification logs (keep last 30 days)
    DELETE FROM notification_logs 
    WHERE sent_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Delete old scheduled notifications that are completed
    DELETE FROM scheduled_notifications 
    WHERE status IN ('sent', 'cancelled', 'failed') 
    AND updated_at < DATE_SUB(NOW(), INTERVAL 7 DAY);
    
    SELECT ROW_COUNT() as cleaned_records;
END //
DELIMITER ;

-- Create a stored procedure for getting users due for workout reminders
DELIMITER //
CREATE PROCEDURE GetWorkoutReminderUsers()
BEGIN
    SELECT 
        u.id as user_id,
        u.name,
        t.token,
        p.workout_time,
        p.timezone
    FROM users u
    JOIN fcm_tokens t ON u.id = t.user_id
    JOIN user_notification_preferences p ON u.id = p.user_id
    WHERE p.workout_enabled = TRUE
    AND t.token IS NOT NULL
    AND p.workout_time IS NOT NULL
    AND t.updated_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
    ORDER BY p.workout_time;
END //
DELIMITER ;

-- Create a function to check if user should receive notification
DELIMITER //
CREATE FUNCTION ShouldReceiveNotification(
    user_id INT,
    notification_type VARCHAR(20),
    hours_since_last INT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE last_sent TIMESTAMP;
    DECLARE should_send BOOLEAN DEFAULT TRUE;
    
    -- Get the last notification of this type sent to the user
    SELECT MAX(sent_at) INTO last_sent
    FROM notification_logs
    WHERE user_id = user_id
    AND notification_type = notification_type
    AND status = 'sent';
    
    -- If no previous notification, send it
    IF last_sent IS NULL THEN
        RETURN TRUE;
    END IF;
    
    -- Check if enough time has passed since last notification
    IF TIMESTAMPDIFF(HOUR, last_sent, NOW()) >= hours_since_last THEN
        RETURN TRUE;
    END IF;
    
    RETURN FALSE;
END //
DELIMITER ;

-- Create event scheduler for automatic cleanup (runs daily at 2 AM)
CREATE EVENT IF NOT EXISTS daily_cleanup
ON SCHEDULE EVERY 1 DAY
STARTS TIMESTAMP(CURDATE() + INTERVAL 1 DAY, '02:00:00')
DO
CALL CleanupOldTokens();
