<?php
/**
 * Helper functions for video management
 */

/**
 * Extract Vimeo video ID from various Vimeo URL formats
 * 
 * @param string $url The Vimeo URL
 * @return string|null The Vimeo video ID or null if not a valid Vimeo URL
 */
function extractVimeoId($url) {
    if (empty($url)) {
        return null;
    }
    
    // Regular expression to match Vimeo URLs and extract the video ID
    $patterns = [
        // Standard Vimeo URL: https://vimeo.com/123456789
        '/vimeo\.com\/([0-9]+)/',
        
        // Player URL: https://player.vimeo.com/video/123456789
        '/player\.vimeo\.com\/video\/([0-9]+)/',
        
        // Private link: https://vimeo.com/123456789/abcdef1234
        '/vimeo\.com\/([0-9]+)\/[a-zA-Z0-9]+/',
        
        // Embedded URL: https://player.vimeo.com/video/123456789?h=abcdef1234
        '/player\.vimeo\.com\/video\/([0-9]+)\?h=[a-zA-Z0-9]+/'
    ];
    
    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $url, $matches)) {
            return $matches[1];
        }
    }
    
    return null;
}

/**
 * Check if a URL is a valid Vimeo URL
 * 
 * @param string $url The URL to check
 * @return bool Whether the URL is a valid Vimeo URL
 */
function isVimeoUrl($url) {
    return extractVimeoId($url) !== null;
}

/**
 * Convert a Vimeo URL to an embeddable URL
 * 
 * @param string $url The Vimeo URL
 * @return string|null The embeddable URL or null if not a valid Vimeo URL
 */
function getVimeoEmbedUrl($url) {
    $videoId = extractVimeoId($url);
    
    if ($videoId) {
        return "https://player.vimeo.com/video/{$videoId}";
    }
    
    return null;
}

/**
 * Get Vimeo video thumbnail URL
 * 
 * Note: This is a simplified version. In a production environment,
 * you would use Vimeo's API to get the actual thumbnail URL.
 * 
 * @param string $url The Vimeo URL
 * @return string|null The thumbnail URL or null if not a valid Vimeo URL
 */
function getVimeoThumbnailUrl($url) {
    $videoId = extractVimeoId($url);
    
    if ($videoId) {
        // This is a placeholder. In a real implementation, you would use Vimeo's API
        // to get the actual thumbnail URL for the video.
        return "https://vumbnail.com/{$videoId}.jpg";
    }
    
    return null;
}

/**
 * Determine if a URL is a video URL (currently only checks for Vimeo)
 * 
 * @param string $url The URL to check
 * @return bool Whether the URL is a video URL
 */
function isVideoUrl($url) {
    return isVimeoUrl($url);
}

/**
 * Get video provider from URL
 * 
 * @param string $url The video URL
 * @return string The video provider ('vimeo', 'youtube', 'unknown')
 */
function getVideoProvider($url) {
    if (isVimeoUrl($url)) {
        return 'vimeo';
    }
    
    // Add support for other providers here
    
    return 'unknown';
}
