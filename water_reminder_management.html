<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Reminder Management - KFT Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #3B82F6;
            --secondary-color: #6366F1;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --danger-color: #EF4444;
            --dark-bg: #1F2937;
            --card-bg: #FFFFFF;
            --border-color: #E5E7EB;
        }

        body {
            background-color: #F9FAFB;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: #F8FAFC;
            border: none;
            font-weight: 600;
            color: #374151;
            padding: 16px;
        }

        .table tbody td {
            border: none;
            padding: 16px;
            vertical-align: middle;
        }

        .table tbody tr {
            border-bottom: 1px solid #F3F4F6;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .badge-success {
            background-color: #D1FAE5;
            color: #065F46;
        }

        .badge-warning {
            background-color: #FEF3C7;
            color: #92400E;
        }

        .badge-danger {
            background-color: #FEE2E2;
            color: #991B1B;
        }

        .form-control, .form-select {
            border: 1px solid #D1D5DB;
            border-radius: 8px;
            padding: 10px 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .modal-content {
            border: none;
            border-radius: 16px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        }

        .modal-header {
            border-bottom: 1px solid #F3F4F6;
            padding: 20px 24px;
        }

        .modal-body {
            padding: 24px;
        }

        .modal-footer {
            border-top: 1px solid #F3F4F6;
            padding: 16px 24px;
        }

        .search-box {
            position: relative;
        }

        .search-box .form-control {
            padding-left: 40px;
        }

        .search-box .search-icon {
            position: absolute;
            left: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
        }

        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6B7280;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 16px;
            opacity: 0.5;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.75rem;
            border-radius: 6px;
        }

        .time-input {
            max-width: 120px;
        }

        .target-input {
            max-width: 100px;
        }

        .interval-select {
            max-width: 140px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="fas fa-tint me-2"></i>
                KFT Admin - Water Reminders
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid py-4">
        <!-- Stats Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-75">Total Users</h6>
                            <h3 class="mb-0" id="totalUsers">-</h3>
                        </div>
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-75">Active Reminders</h6>
                            <h3 class="mb-0" id="activeReminders">-</h3>
                        </div>
                        <i class="fas fa-bell fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-75">Admin Managed</h6>
                            <h3 class="mb-0" id="adminManaged">-</h3>
                        </div>
                        <i class="fas fa-cog fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1 opacity-75">Avg Target</h6>
                            <h3 class="mb-0" id="avgTarget">-</h3>
                        </div>
                        <i class="fas fa-tint fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-white border-0 py-3">
                        <div class="row align-items-center">
                            <div class="col">
                                <h5 class="mb-0 fw-bold">
                                    <i class="fas fa-tint text-primary me-2"></i>
                                    Water Reminder Management
                                </h5>
                                <p class="text-muted mb-0 small">Configure hydration settings for users</p>
                            </div>
                            <div class="col-auto">
                                <button class="btn btn-primary" onclick="showCreateModal()">
                                    <i class="fas fa-plus me-1"></i>
                                    Add Reminder
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Search and Filters -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="search-box">
                                    <i class="fas fa-search search-icon"></i>
                                    <input type="text" class="form-control" id="searchInput"
                                           placeholder="Search users by name, phone, or email...">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="statusFilter">
                                    <option value="">All Status</option>
                                    <option value="enabled">Enabled</option>
                                    <option value="disabled">Disabled</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="managementFilter">
                                    <option value="">All Users</option>
                                    <option value="admin">Admin Managed</option>
                                    <option value="user">User Managed</option>
                                    <option value="none">No Reminder</option>
                                </select>
                            </div>
                        </div>

                        <!-- Loading State -->
                        <div class="loading-spinner" id="loadingSpinner">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2 text-muted">Loading users...</p>
                        </div>

                        <!-- Users Table -->
                        <div class="table-responsive" id="usersTableContainer" style="display: none;">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Contact</th>
                                        <th>Daily Target</th>
                                        <th>Interval</th>
                                        <th>Active Hours</th>
                                        <th>Status</th>
                                        <th>Management</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Users will be loaded here -->
                                </tbody>
                            </table>
                        </div>

                        <!-- Empty State -->
                        <div class="empty-state" id="emptyState" style="display: none;">
                            <i class="fas fa-tint"></i>
                            <h5>No Users Found</h5>
                            <p>No users match your current search criteria.</p>
                        </div>

                        <!-- Pagination -->
                        <nav aria-label="Users pagination" id="paginationContainer" style="display: none;">
                            <ul class="pagination justify-content-center" id="pagination">
                                <!-- Pagination will be generated here -->
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Create/Edit Water Reminder Modal -->
    <div class="modal fade" id="waterReminderModal" tabindex="-1" aria-labelledby="waterReminderModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="waterReminderModalLabel">
                        <i class="fas fa-tint text-primary me-2"></i>
                        <span id="modalTitle">Add Water Reminder</span>
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="waterReminderForm">
                        <input type="hidden" id="userId" name="user_id">
                        <input type="hidden" id="isEdit" value="false">

                        <!-- User Selection (only for create) -->
                        <div class="mb-3" id="userSelectionGroup">
                            <label for="userSelect" class="form-label fw-semibold">
                                <i class="fas fa-user me-1"></i>
                                Select User
                            </label>
                            <select class="form-select" id="userSelect" name="user_id" required>
                                <option value="">Choose a user...</option>
                            </select>
                            <div class="form-text">Select the user for whom you want to configure water reminders.</div>
                        </div>

                        <!-- User Info (only for edit) -->
                        <div class="mb-3" id="userInfoGroup" style="display: none;">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-user me-1"></i>
                                User Information
                            </label>
                            <div class="p-3 bg-light rounded">
                                <div class="d-flex align-items-center">
                                    <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                         style="width: 40px; height: 40px; font-weight: bold;" id="userAvatar">
                                        U
                                    </div>
                                    <div>
                                        <h6 class="mb-1" id="userName">User Name</h6>
                                        <small class="text-muted" id="userContact"><EMAIL></small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Daily Target -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dailyTarget" class="form-label fw-semibold">
                                        <i class="fas fa-bullseye me-1"></i>
                                        Daily Target (ml)
                                    </label>
                                    <input type="number" class="form-control target-input" id="dailyTarget"
                                           name="daily_target" min="500" max="5000" step="100" value="2000" required>
                                    <div class="form-text">Recommended: 2000-3000ml per day</div>
                                </div>
                            </div>

                            <!-- Reminder Interval -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="reminderInterval" class="form-label fw-semibold">
                                        <i class="fas fa-clock me-1"></i>
                                        Reminder Interval
                                    </label>
                                    <select class="form-select interval-select" id="reminderInterval" name="reminder_interval" required>
                                        <option value="30">Every 30 minutes</option>
                                        <option value="60">Every 1 hour</option>
                                        <option value="90">Every 1.5 hours</option>
                                        <option value="120" selected>Every 2 hours</option>
                                        <option value="180">Every 3 hours</option>
                                    </select>
                                    <div class="form-text">How often to send reminders</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <!-- Start Time -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="startTime" class="form-label fw-semibold">
                                        <i class="fas fa-sun me-1"></i>
                                        Start Time
                                    </label>
                                    <input type="time" class="form-control time-input" id="startTime"
                                           name="start_time" value="08:00" required>
                                    <div class="form-text">When to start sending reminders</div>
                                </div>
                            </div>

                            <!-- End Time -->
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="endTime" class="form-label fw-semibold">
                                        <i class="fas fa-moon me-1"></i>
                                        End Time
                                    </label>
                                    <input type="time" class="form-control time-input" id="endTime"
                                           name="end_time" value="22:00" required>
                                    <div class="form-text">When to stop sending reminders</div>
                                </div>
                            </div>
                        </div>

                        <!-- Enable/Disable -->
                        <div class="mb-3">
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="isEnabled" name="is_enabled" checked>
                                <label class="form-check-label fw-semibold" for="isEnabled">
                                    <i class="fas fa-toggle-on me-1"></i>
                                    Enable Water Reminders
                                </label>
                                <div class="form-text">Turn on/off water reminder notifications for this user</div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveWaterReminder()">
                        <i class="fas fa-save me-1"></i>
                        <span id="saveButtonText">Save Reminder</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">
                        <i class="fas fa-exclamation-triangle text-danger me-2"></i>
                        Confirm Deletion
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the water reminder for <strong id="deleteUserName">this user</strong>?</p>
                    <p class="text-muted small">This action cannot be undone. The user will no longer receive water reminder notifications.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash me-1"></i>
                        Delete Reminder
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Global variables
        let currentUsers = [];
        let allUsers = [];
        let currentPage = 1;
        let totalPages = 1;
        let deleteUserId = null;

        // API Configuration
        const API_BASE_URL = 'api';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadUsers();
            loadUsersForSelect();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            // Search functionality
            document.getElementById('searchInput').addEventListener('input', debounce(filterUsers, 300));

            // Filter functionality
            document.getElementById('statusFilter').addEventListener('change', filterUsers);
            document.getElementById('managementFilter').addEventListener('change', filterUsers);
        }

        // Debounce function for search
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Load users with water reminder status
        async function loadUsers(page = 1) {
            try {
                showLoading(true);

                const response = await fetch(`${API_BASE_URL}/users_list.php?page=${page}&limit=20`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    allUsers = data.users;
                    currentUsers = [...allUsers];
                    currentPage = data.pagination.current_page;
                    totalPages = data.pagination.total_pages;

                    renderUsersTable();
                    renderPagination();
                    updateStats();
                } else {
                    throw new Error(data.message || 'Failed to load users');
                }
            } catch (error) {
                console.error('Error loading users:', error);
                showError('Failed to load users. Please try again.');
            } finally {
                showLoading(false);
            }
        }

        // Load users for select dropdown
        async function loadUsersForSelect() {
            try {
                const response = await fetch(`${API_BASE_URL}/users_list.php?limit=1000`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success) {
                    const userSelect = document.getElementById('userSelect');
                    userSelect.innerHTML = '<option value="">Choose a user...</option>';

                    // Only show users without water reminders
                    const usersWithoutReminders = data.users.filter(user => !user.has_water_reminder);

                    usersWithoutReminders.forEach(user => {
                        const option = document.createElement('option');
                        option.value = user.id;
                        option.textContent = `${user.name} (${user.phone})`;
                        userSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Error loading users for select:', error);
            }
        }

        // Render users table
        function renderUsersTable() {
            const tbody = document.getElementById('usersTableBody');
            const tableContainer = document.getElementById('usersTableContainer');
            const emptyState = document.getElementById('emptyState');

            if (currentUsers.length === 0) {
                tableContainer.style.display = 'none';
                emptyState.style.display = 'block';
                return;
            }

            tableContainer.style.display = 'block';
            emptyState.style.display = 'none';

            tbody.innerHTML = currentUsers.map(user => {
                const statusBadge = user.has_water_reminder
                    ? (user.water_reminder_enabled
                        ? '<span class="badge badge-success">Enabled</span>'
                        : '<span class="badge badge-warning">Disabled</span>')
                    : '<span class="badge badge-danger">No Reminder</span>';

                const managementBadge = user.has_water_reminder
                    ? (user.admin_managed
                        ? '<span class="badge bg-primary">Admin Managed</span>'
                        : '<span class="badge bg-secondary">User Managed</span>')
                    : '<span class="badge bg-light text-dark">None</span>';

                const dailyTarget = user.daily_target ? `${user.daily_target}ml` : '-';
                const interval = user.has_water_reminder ? 'Every 2h' : '-'; // Default interval display
                const activeHours = user.has_water_reminder ? '08:00 - 22:00' : '-'; // Default hours display

                const actions = user.has_water_reminder && user.admin_managed
                    ? `<div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="editWaterReminder(${user.id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteWaterReminder(${user.id}, '${user.name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                       </div>`
                    : user.has_water_reminder
                    ? '<span class="text-muted small">User Managed</span>'
                    : `<button class="btn btn-sm btn-primary" onclick="createWaterReminder(${user.id})">
                        <i class="fas fa-plus me-1"></i>Add
                       </button>`;

                return `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                                     style="width: 32px; height: 32px; font-size: 12px; font-weight: bold;">
                                    ${user.name.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                    <div class="fw-semibold">${user.name}</div>
                                    <small class="text-muted">ID: ${user.id}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div>${user.phone}</div>
                            ${user.email ? `<small class="text-muted">${user.email}</small>` : ''}
                        </td>
                        <td>${dailyTarget}</td>
                        <td>${interval}</td>
                        <td>${activeHours}</td>
                        <td>${statusBadge}</td>
                        <td>${managementBadge}</td>
                        <td>${actions}</td>
                    </tr>
                `;
            }).join('');
        }

        // Filter users based on search and filters
        function filterUsers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const managementFilter = document.getElementById('managementFilter').value;

            currentUsers = allUsers.filter(user => {
                // Search filter
                const matchesSearch = !searchTerm ||
                    user.name.toLowerCase().includes(searchTerm) ||
                    user.phone.includes(searchTerm) ||
                    (user.email && user.email.toLowerCase().includes(searchTerm));

                // Status filter
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'enabled' && user.has_water_reminder && user.water_reminder_enabled) ||
                    (statusFilter === 'disabled' && user.has_water_reminder && !user.water_reminder_enabled);

                // Management filter
                const matchesManagement = !managementFilter ||
                    (managementFilter === 'admin' && user.has_water_reminder && user.admin_managed) ||
                    (managementFilter === 'user' && user.has_water_reminder && !user.admin_managed) ||
                    (managementFilter === 'none' && !user.has_water_reminder);

                return matchesSearch && matchesStatus && matchesManagement;
            });

            renderUsersTable();
        }

        // Update statistics
        function updateStats() {
            const totalUsers = allUsers.length;
            const activeReminders = allUsers.filter(u => u.has_water_reminder && u.water_reminder_enabled).length;
            const adminManaged = allUsers.filter(u => u.has_water_reminder && u.admin_managed).length;
            const avgTarget = allUsers.filter(u => u.daily_target).reduce((sum, u) => sum + u.daily_target, 0) /
                             Math.max(1, allUsers.filter(u => u.daily_target).length);

            document.getElementById('totalUsers').textContent = totalUsers;
            document.getElementById('activeReminders').textContent = activeReminders;
            document.getElementById('adminManaged').textContent = adminManaged;
            document.getElementById('avgTarget').textContent = avgTarget > 0 ? `${Math.round(avgTarget)}ml` : '-';
        }

        // Show create modal
        function showCreateModal() {
            document.getElementById('modalTitle').textContent = 'Add Water Reminder';
            document.getElementById('saveButtonText').textContent = 'Save Reminder';
            document.getElementById('isEdit').value = 'false';
            document.getElementById('userSelectionGroup').style.display = 'block';
            document.getElementById('userInfoGroup').style.display = 'none';

            // Reset form
            document.getElementById('waterReminderForm').reset();
            document.getElementById('dailyTarget').value = '2000';
            document.getElementById('reminderInterval').value = '120';
            document.getElementById('startTime').value = '08:00';
            document.getElementById('endTime').value = '22:00';
            document.getElementById('isEnabled').checked = true;

            new bootstrap.Modal(document.getElementById('waterReminderModal')).show();
        }

        // Create water reminder for specific user
        function createWaterReminder(userId) {
            const user = allUsers.find(u => u.id === userId);
            if (!user) return;

            document.getElementById('modalTitle').textContent = `Add Water Reminder for ${user.name}`;
            document.getElementById('saveButtonText').textContent = 'Save Reminder';
            document.getElementById('isEdit').value = 'false';
            document.getElementById('userId').value = userId;
            document.getElementById('userSelect').value = userId;
            document.getElementById('userSelectionGroup').style.display = 'none';
            document.getElementById('userInfoGroup').style.display = 'block';

            // Set user info
            document.getElementById('userAvatar').textContent = user.name.charAt(0).toUpperCase();
            document.getElementById('userName').textContent = user.name;
            document.getElementById('userContact').textContent = user.phone + (user.email ? ` • ${user.email}` : '');

            // Reset form
            document.getElementById('waterReminderForm').reset();
            document.getElementById('dailyTarget').value = '2000';
            document.getElementById('reminderInterval').value = '120';
            document.getElementById('startTime').value = '08:00';
            document.getElementById('endTime').value = '22:00';
            document.getElementById('isEnabled').checked = true;

            new bootstrap.Modal(document.getElementById('waterReminderModal')).show();
        }

        // Edit water reminder
        async function editWaterReminder(userId) {
            try {
                const response = await fetch(`${API_BASE_URL}/water_reminder_admin.php?user_id=${userId}`, {
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();

                if (data.success && data.water_reminder) {
                    const reminder = data.water_reminder;

                    document.getElementById('modalTitle').textContent = `Edit Water Reminder for ${reminder.user_name}`;
                    document.getElementById('saveButtonText').textContent = 'Update Reminder';
                    document.getElementById('isEdit').value = 'true';
                    document.getElementById('userId').value = userId;
                    document.getElementById('userSelectionGroup').style.display = 'none';
                    document.getElementById('userInfoGroup').style.display = 'block';

                    // Set user info
                    document.getElementById('userAvatar').textContent = reminder.user_name.charAt(0).toUpperCase();
                    document.getElementById('userName').textContent = reminder.user_name;
                    document.getElementById('userContact').textContent = reminder.user_phone;

                    // Set form values
                    document.getElementById('dailyTarget').value = reminder.daily_target;
                    document.getElementById('reminderInterval').value = reminder.reminder_interval;
                    document.getElementById('startTime').value = reminder.start_time;
                    document.getElementById('endTime').value = reminder.end_time;
                    document.getElementById('isEnabled').checked = reminder.is_enabled;

                    new bootstrap.Modal(document.getElementById('waterReminderModal')).show();
                } else {
                    throw new Error(data.message || 'Failed to load water reminder');
                }
            } catch (error) {
                console.error('Error loading water reminder:', error);
                showError('Failed to load water reminder details.');
            }
        }

        // Save water reminder
        async function saveWaterReminder() {
            try {
                const form = document.getElementById('waterReminderForm');
                const formData = new FormData(form);
                const isEdit = document.getElementById('isEdit').value === 'true';

                // Get form values
                const data = {
                    user_id: parseInt(formData.get('user_id') || document.getElementById('userId').value),
                    daily_target: parseInt(formData.get('daily_target')),
                    reminder_interval: parseInt(formData.get('reminder_interval')),
                    start_time: formData.get('start_time'),
                    end_time: formData.get('end_time'),
                    is_enabled: document.getElementById('isEnabled').checked
                };

                const method = isEdit ? 'PUT' : 'POST';
                const response = await fetch(`${API_BASE_URL}/water_reminder_admin.php`, {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    showSuccess(isEdit ? 'Water reminder updated successfully!' : 'Water reminder created successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('waterReminderModal')).hide();
                    loadUsers(currentPage);
                    loadUsersForSelect();
                } else {
                    throw new Error(result.message || 'Failed to save water reminder');
                }
            } catch (error) {
                console.error('Error saving water reminder:', error);
                showError('Failed to save water reminder. Please try again.');
            }
        }

        // Delete water reminder
        function deleteWaterReminder(userId, userName) {
            deleteUserId = userId;
            document.getElementById('deleteUserName').textContent = userName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Confirm delete
        async function confirmDelete() {
            if (!deleteUserId) return;

            try {
                const response = await fetch(`${API_BASE_URL}/water_reminder_admin.php?user_id=${deleteUserId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${getAuthToken()}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    showSuccess('Water reminder deleted successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
                    loadUsers(currentPage);
                    loadUsersForSelect();
                } else {
                    throw new Error(result.message || 'Failed to delete water reminder');
                }
            } catch (error) {
                console.error('Error deleting water reminder:', error);
                showError('Failed to delete water reminder. Please try again.');
            } finally {
                deleteUserId = null;
            }
        }

        // Utility functions
        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
        }

        function showError(message) {
            // You can implement a toast notification system here
            alert('Error: ' + message);
        }

        function showSuccess(message) {
            // You can implement a toast notification system here
            alert('Success: ' + message);
        }

        function getAuthToken() {
            return localStorage.getItem('admin_token') || '';
        }

        function renderPagination() {
            // Implement pagination if needed
            const paginationContainer = document.getElementById('paginationContainer');
            if (totalPages > 1) {
                paginationContainer.style.display = 'block';
                // Add pagination logic here
            } else {
                paginationContainer.style.display = 'none';
            }
        }
    </script>
</body>
</html>
