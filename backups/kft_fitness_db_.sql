-- MySQL dump 10.13  Distrib 9.3.0, for macos15.2 (arm64)
--
-- Host: localhost    Database: kft_fitness
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_action_logs`
--

DROP TABLE IF EXISTS `admin_action_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_action_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_user_id` int NOT NULL,
  `admin_username` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `action_type` enum('device_revoke','user_deactivate','user_activate','token_invalidate','session_terminate') CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `target_user_id` int NOT NULL,
  `target_username` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `target_device_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `action_details` json DEFAULT NULL,
  `reason` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_admin_user` (`admin_user_id`),
  KEY `idx_target_user` (`target_user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_action_logs`
--

LOCK TABLES `admin_action_logs` WRITE;
/*!40000 ALTER TABLE `admin_action_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_action_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_permissions`
--

DROP TABLE IF EXISTS `admin_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `slug` (`slug`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_permissions`
--

LOCK TABLES `admin_permissions` WRITE;
/*!40000 ALTER TABLE `admin_permissions` DISABLE KEYS */;
INSERT INTO `admin_permissions` VALUES (1,'Manage Users','manage_users','Create, edit, and delete users','2025-05-11 17:59:19','2025-05-11 17:59:19'),(2,'Manage Courses','manage_courses','Create, edit, and delete courses','2025-05-11 17:59:19','2025-05-11 17:59:19'),(3,'Manage Workouts','manage_workouts','Create, edit, and delete workouts','2025-05-11 17:59:19','2025-05-11 17:59:19'),(4,'Manage Quotes','manage_quotes','Create, edit, and delete motivational quotes','2025-05-11 17:59:19','2025-05-11 17:59:19'),(5,'View Reports','view_reports','View system reports and analytics','2025-05-11 17:59:19','2025-05-11 17:59:19'),(6,'Manage Settings','manage_settings','Modify system settings','2025-05-11 17:59:19','2025-05-11 17:59:19'),(7,'Manage Staff','manage_staff','Create, edit, and delete staff accounts','2025-05-11 17:59:19','2025-05-11 17:59:19'),(9,'Manage Food Database','manage_food_database','Add, edit, and delete food items in the calorie tracker database','2025-05-11 17:59:19','2025-05-11 17:59:19'),(10,'Assign Users','assign_users','Assign users to staff members','2025-05-11 17:59:19','2025-05-11 17:59:19');
/*!40000 ALTER TABLE `admin_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_user_permissions`
--

DROP TABLE IF EXISTS `admin_user_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_user_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` bigint unsigned NOT NULL,
  `permission_id` bigint unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_user_permission` (`admin_user_id`,`permission_id`),
  KEY `fk_admin_user_permissions_permission_id` (`permission_id`),
  CONSTRAINT `fk_admin_user_permissions_admin_user_id` FOREIGN KEY (`admin_user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_admin_user_permissions_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `admin_permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=76 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_user_permissions`
--

LOCK TABLES `admin_user_permissions` WRITE;
/*!40000 ALTER TABLE `admin_user_permissions` DISABLE KEYS */;
INSERT INTO `admin_user_permissions` VALUES (1,1,10,'2025-05-11 17:59:19'),(3,1,2,'2025-05-11 17:59:19'),(4,1,9,'2025-05-11 17:59:19'),(5,1,4,'2025-05-11 17:59:19'),(6,1,6,'2025-05-11 17:59:19'),(7,1,7,'2025-05-11 17:59:19'),(8,1,1,'2025-05-11 17:59:19'),(9,1,3,'2025-05-11 17:59:19'),(10,1,5,'2025-05-11 17:59:19'),(11,4,10,'2025-05-11 18:54:56'),(12,4,9,'2025-05-11 18:54:56'),(13,4,1,'2025-05-11 18:54:56'),(14,4,3,'2025-05-11 18:54:56'),(24,6,10,'2025-05-12 05:36:05'),(25,6,7,'2025-05-12 05:36:05'),(26,6,1,'2025-05-12 05:36:05'),(60,7,1,'2025-05-12 14:39:09'),(61,7,3,'2025-05-12 14:39:09'),(62,7,5,'2025-05-12 14:39:09'),(69,8,1,'2025-05-20 16:56:09'),(70,8,3,'2025-05-20 16:56:09'),(71,8,5,'2025-05-20 16:56:09'),(72,5,10,'2025-05-29 03:28:00'),(73,5,1,'2025-05-29 03:28:00'),(74,5,2,'2025-05-29 03:28:00'),(75,5,9,'2025-05-29 03:28:00');
/*!40000 ALTER TABLE `admin_user_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` enum('admin','editor','viewer','super_admin','staff') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'staff',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `phone` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parent_admin_id` bigint unsigned DEFAULT NULL,
  `permissions_locked` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_users_username_unique` (`username`),
  UNIQUE KEY `admin_users_email_unique` (`email`),
  KEY `parent_admin_id` (`parent_admin_id`),
  KEY `parent_admin_id_2` (`parent_admin_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_role` (`role`),
  KEY `idx_username` (`username`),
  CONSTRAINT `fk_parent_admin` FOREIGN KEY (`parent_admin_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_users`
--

LOCK TABLES `admin_users` WRITE;
/*!40000 ALTER TABLE `admin_users` DISABLE KEYS */;
INSERT INTO `admin_users` VALUES (1,'admin','$2y$12$w21cilpio7DT12RjIbAOkOVHvlOnlIKAvo0.tpO/YmhoIW7DMU7X2','<EMAIL>','Administrator','super_admin',1,'2025-05-21 03:57:25',NULL,'2025-05-09 06:54:42','2025-05-09 07:40:41','+919876543210',NULL,0),(2,'editor','$2y$12$gwgclGe6oCeQjQz23ZpiWe3NumHro8Es0MQMlC0qav.fogJO5.ScG','<EMAIL>','Editor User','editor',1,NULL,NULL,'2025-05-09 06:54:42','2025-05-09 06:54:42',NULL,NULL,0),(3,'viewer','$2y$12$isAz1GdUkKNXmsCPyYgW1ulPZnpzSK0SQ8wV.WQv1klsrYqmxnNUO','<EMAIL>','Viewer User','viewer',1,NULL,NULL,'2025-05-09 06:54:43','2025-05-09 06:54:43',NULL,NULL,0),(4,'staff1','$2y$12$Gmgz9tpyZZQumy3xhn2dguhicndSiiQEZYisNBHMnMbb5n2UA9wSi','<EMAIL>','StaffOne','staff',1,'2025-05-12 04:29:52',NULL,NULL,NULL,'1234567890',1,0),(5,'STAFF2','$2y$12$l7lgcfSwlU6x4MkYkP4IfePF8n.L9/x6XK3y3wux/eOZAVsgsnWCm','<EMAIL>','staff2','staff',1,'2025-05-13 17:24:38',NULL,NULL,NULL,'987654321',1,0),(6,'staff3','$2y$12$Pk/stfD.DsxlKRosQGGHLecFBWNs78kh4viEJ5Xj.7EKMErpqSpVa','<EMAIL>','staff3','staff',1,'2025-05-12 08:45:32',NULL,NULL,NULL,'987654321',1,0),(7,'staffsdf','$2y$12$UnwyQPS4x.96HeppQQ4CiOdp98MCke659I29v/u4JpxNyEYepPSMC','<EMAIL>','staff01','staff',1,'2025-05-12 06:40:16',NULL,NULL,NULL,'9876543212',1,0),(8,'dfsfds','$2y$12$VROKkuTkUqHLa02Yk3uTF.ovcyPneZtYmjLqzrbAgU1.Ek4zKZ39K','<EMAIL>','abc','staff',0,NULL,NULL,NULL,NULL,'1234567890',1,0);
/*!40000 ALTER TABLE `admin_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `api_tokens`
--

DROP TABLE IF EXISTS `api_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `api_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `last_used` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_revoked` tinyint(1) DEFAULT '0',
  `revoked_by_admin` int DEFAULT NULL,
  `revoked_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `api_tokens_token_unique` (`token`),
  KEY `api_tokens_user_id_foreign` (`user_id`),
  KEY `idx_device_id_api_tokens` (`device_id`),
  KEY `idx_is_revoked_api_tokens` (`is_revoked`),
  KEY `idx_last_used_api_tokens` (`last_used`),
  KEY `idx_api_tokens_user_device` (`user_id`,`device_id`,`is_revoked`),
  CONSTRAINT `api_tokens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=218 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `api_tokens`
--

LOCK TABLES `api_tokens` WRITE;
/*!40000 ALTER TABLE `api_tokens` DISABLE KEYS */;
INSERT INTO `api_tokens` VALUES (171,24,'4f63f89b8b6b51231b1396acabbf2e5a9a4ab30b108db704dc2b75c5c5f960b0',NULL,NULL,'2025-07-13 11:28:15',NULL,NULL,NULL,'2025-06-13 16:58:15',0,NULL,NULL),(179,29,'143f0b436212c26f79f811bd476d955e79d91927b7dcca55802f9a461254f127',NULL,NULL,'2025-07-13 23:23:56',NULL,NULL,NULL,'2025-06-14 04:53:56',0,NULL,NULL),(184,34,'cbe329be8f4a922d15091da416063185aaf55df1e43d733ece8be4d1b4cf6e98',NULL,NULL,'2025-07-14 00:01:42',NULL,NULL,NULL,'2025-06-14 05:31:42',0,NULL,NULL),(188,30,'ba85b155858e1bf08cf782ca444935c338b39e2ce859e15b02d166b6dfbc785a',NULL,NULL,'2025-07-14 00:33:41',NULL,NULL,NULL,'2025-06-14 06:03:41',0,NULL,NULL),(191,32,'06345ed7a12b4be6ab4521ac0c6ae04192c96f945dd301a80cf9f280455ba5ef',NULL,NULL,'2025-07-14 04:18:42',NULL,NULL,NULL,'2025-06-14 09:48:42',0,NULL,NULL),(196,40,'61ed9206a2bc8e5b70cd61d711cbd5c8a222969aa9625287c2e5c6e9d16d7f3e',NULL,NULL,'2025-07-14 10:21:25',NULL,NULL,NULL,'2025-06-14 15:51:25',0,NULL,NULL),(197,39,'28d96ab6a265872bf848e7a53ba790fbf265a4a7a1c595a0468e5454215768c4',NULL,NULL,'2025-07-14 10:22:05',NULL,NULL,NULL,'2025-06-14 15:52:05',0,NULL,NULL),(198,41,'526f64b474efbb8f26b0163cd769eed0605c4f355774b650398f018c433bfc35',NULL,NULL,'2025-07-14 10:26:11',NULL,NULL,NULL,'2025-06-14 15:56:11',0,NULL,NULL),(200,33,'4c19c47a99db6b1d3401d47d5daf83599d18994fdbd22184c3f7490696a0614f',NULL,NULL,'2025-07-14 11:27:37',NULL,NULL,NULL,'2025-06-14 16:57:37',0,NULL,NULL),(208,25,'65db4dae2ea324bd775e905dbe1b1b75ba4f845cc1cda67353add535b74dcfb4',NULL,NULL,'2025-07-15 08:42:22',NULL,NULL,NULL,'2025-06-15 14:12:22',0,NULL,NULL),(211,38,'867921507949000c0f2b7cddf1d7ce54db63701a376d4fd11ac72fb67be83362',NULL,NULL,'2025-07-15 09:49:30',NULL,NULL,NULL,'2025-06-15 15:19:30',0,NULL,NULL),(213,35,'74b9297975484801935cf3fab814d46d4cfcf04de925acaeaf8d60dcb7017c2b',NULL,NULL,'2025-07-15 09:52:25',NULL,NULL,NULL,'2025-06-15 15:22:25',0,NULL,NULL),(214,37,'13436445210630b844075bc143b96a520ff66f0cbeba0eebca7541d8dd2411ad',NULL,NULL,'2025-07-15 09:53:35',NULL,NULL,NULL,'2025-06-15 15:23:35',0,NULL,NULL),(216,36,'118eb51953ce52e476656dfac23aceefcb9ca36aa3bbfa0276b192604bcb1af8',NULL,NULL,'2025-07-15 09:55:46',NULL,NULL,NULL,'2025-06-15 15:25:46',0,NULL,NULL),(217,27,'1943d0a1a28c2d445397ecbf61acd43b2892530691921ba25fbba94873f213cc',NULL,NULL,'2025-07-15 10:37:06',NULL,NULL,NULL,'2025-06-15 16:07:06',0,NULL,NULL);
/*!40000 ALTER TABLE `api_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `audit_logs`
--

DROP TABLE IF EXISTS `audit_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `audit_logs` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` bigint unsigned NOT NULL,
  `action_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `timestamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `affected_user_id` bigint unsigned DEFAULT NULL,
  `details` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `staff_id` (`staff_id`),
  KEY `affected_user_id` (`affected_user_id`),
  KEY `idx_audit_logs_action_type` (`action_type`),
  KEY `idx_audit_logs_timestamp` (`timestamp`),
  CONSTRAINT `audit_logs_ibfk_1` FOREIGN KEY (`staff_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `audit_logs_ibfk_2` FOREIGN KEY (`affected_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `audit_logs`
--

LOCK TABLES `audit_logs` WRITE;
/*!40000 ALTER TABLE `audit_logs` DISABLE KEYS */;
INSERT INTO `audit_logs` VALUES (1,1,'user_edit','2025-05-12 12:43:31',NULL,'0','***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(2,1,'user_edit','2025-05-12 12:43:46',NULL,'0','***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(3,1,'user_edit','2025-05-12 19:23:18',NULL,'0','192.168.1.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(4,1,'user_edit','2025-05-12 19:24:44',NULL,'0','192.168.1.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(5,1,'user_edit','2025-05-12 19:25:11',NULL,'0','192.168.1.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(6,1,'user_edit','2025-05-12 19:25:45',NULL,'0','192.168.1.10','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(7,1,'user_edit','2025-05-13 12:06:40',NULL,'0','***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(8,1,'user_edit','2025-05-13 13:11:22',NULL,'0','***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(9,1,'user_edit','2025-05-13 15:06:08',NULL,'0','***********','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36'),(10,1,'user_edit','2025-06-10 19:54:35',NULL,'0','157.51.226.107','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),(11,1,'user_edit','2025-06-14 10:53:09',NULL,'0','103.153.105.64','Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),(12,1,'course_enroll','2025-06-14 23:59:44',27,'0','157.51.213.235','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'),(13,1,'user_edit','2025-06-15 14:27:15',27,'0','157.51.226.179','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36');
/*!40000 ALTER TABLE `audit_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `bmi_records`
--

DROP TABLE IF EXISTS `bmi_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `bmi_records` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `height` double NOT NULL,
  `weight` double NOT NULL,
  `bmi` double NOT NULL,
  `recorded_at` datetime NOT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `bmi_records_user_id_foreign` (`user_id`),
  CONSTRAINT `bmi_records_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=198 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `bmi_records`
--

LOCK TABLES `bmi_records` WRITE;
/*!40000 ALTER TABLE `bmi_records` DISABLE KEYS */;
INSERT INTO `bmi_records` VALUES (26,25,152,52,22.506925207756233,'2025-05-28 17:26:07',NULL,NULL,NULL),(31,25,152,52,22.506925207756233,'2025-05-28 21:26:26',NULL,NULL,NULL),(32,25,152,52,22.506925207756233,'2025-05-28 21:31:33',NULL,NULL,NULL),(33,25,152,52,22.506925207756233,'2025-05-28 21:33:47',NULL,NULL,NULL),(40,25,152,52,22.506925207756233,'2025-05-29 20:04:15',NULL,NULL,NULL),(56,25,152,52,22.506925207756233,'2025-06-02 12:17:21',NULL,NULL,NULL),(91,25,152,52,22.506925207756233,'2025-06-05 22:58:45',NULL,NULL,NULL),(118,25,152,52,22.506925207756233,'2025-06-13 22:01:14',NULL,NULL,NULL),(119,24,188,76,21.50294250792214,'2025-06-13 22:28:18',NULL,NULL,NULL),(120,29,157,57,23.124670372023203,'2025-06-14 10:24:01',NULL,NULL,NULL),(121,30,163,44,16.560653393052053,'2025-06-14 10:36:16',NULL,NULL,NULL),(123,32,154,44,18.55287569573284,'2025-06-14 10:52:16',NULL,NULL,NULL),(124,30,163,44,16.560653393052053,'2025-06-14 10:54:25',NULL,NULL,NULL),(125,30,160,44,17.187499999999996,'2025-06-14 10:54:27',NULL,NULL,NULL),(126,30,160,44,17.187499999999996,'2025-06-14 10:54:31',NULL,NULL,NULL),(127,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(128,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(129,30,161,44,16.97465375564214,'2025-06-14 10:55:01',NULL,NULL,NULL),(130,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(131,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(132,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(133,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(134,30,160,44,17.187499999999996,'2025-06-14 10:55:01',NULL,NULL,NULL),(135,29,157,57,23.124670372023203,'2025-06-14 10:57:15',NULL,NULL,NULL),(136,29,157,57,23.124670372023203,'2025-06-14 10:57:16',NULL,NULL,NULL),(137,29,157,57,23.124670372023203,'2025-06-14 10:57:18',NULL,NULL,NULL),(138,29,157,57,23.124670372023203,'2025-06-14 10:57:19',NULL,NULL,NULL),(139,29,157,57,23.124670372023203,'2025-06-14 10:57:20',NULL,NULL,NULL),(140,29,157,57,23.124670372023203,'2025-06-14 10:57:22',NULL,NULL,NULL),(141,33,163,43,16.184274906846326,'2025-06-14 10:58:28',NULL,NULL,NULL),(142,34,150,47,20.88888888888889,'2025-06-14 11:01:45',NULL,NULL,NULL),(143,35,151,40,17.543090215341433,'2025-06-14 11:05:06',NULL,NULL,NULL),(144,36,150,49,21.77777777777778,'2025-06-14 11:07:32',NULL,NULL,NULL),(145,37,160,50,19.531249999999996,'2025-06-14 11:19:31',NULL,NULL,NULL),(146,30,160,44,17.187499999999996,'2025-06-14 11:33:45',NULL,NULL,NULL),(147,30,160,44,17.187499999999996,'2025-06-14 11:34:14',NULL,NULL,NULL),(148,30,161,44,16.97465375564214,'2025-06-14 11:34:14',NULL,NULL,NULL),(149,30,161,44,16.97465375564214,'2025-06-14 11:34:15',NULL,NULL,NULL),(150,30,161,44,16.97465375564214,'2025-06-14 11:34:16',NULL,NULL,NULL),(151,30,161,44,16.97465375564214,'2025-06-14 11:34:17',NULL,NULL,NULL),(152,30,161,44,16.97465375564214,'2025-06-14 11:34:17',NULL,NULL,NULL),(153,30,161,44,16.97465375564214,'2025-06-14 11:34:18',NULL,NULL,NULL),(154,30,161,44,16.97465375564214,'2025-06-14 11:34:18',NULL,NULL,NULL),(155,30,161,44,16.97465375564214,'2025-06-14 11:34:19',NULL,NULL,NULL),(156,30,161,44,16.97465375564214,'2025-06-14 11:34:20',NULL,NULL,NULL),(157,30,161,44,16.97465375564214,'2025-06-14 11:34:21',NULL,NULL,NULL),(158,30,161,44,16.97465375564214,'2025-06-14 11:34:22',NULL,NULL,NULL),(159,32,154,44,18.55287569573284,'2025-06-14 11:34:56',NULL,NULL,NULL),(160,32,153,44,18.79618949976505,'2025-06-14 11:34:57',NULL,NULL,NULL),(161,32,153,44,18.79618949976505,'2025-06-14 11:34:57',NULL,NULL,NULL),(162,32,153,44,18.79618949976505,'2025-06-14 11:34:58',NULL,NULL,NULL),(163,32,153,44,18.79618949976505,'2025-06-14 11:34:59',NULL,NULL,NULL),(164,32,153,44,18.79618949976505,'2025-06-14 11:34:59',NULL,NULL,NULL),(165,32,153,44,18.79618949976505,'2025-06-14 11:34:59',NULL,NULL,NULL),(166,32,153,44,18.79618949976505,'2025-06-14 11:35:00',NULL,NULL,NULL),(167,32,153,44,18.79618949976505,'2025-06-14 11:35:00',NULL,NULL,NULL),(168,32,153,44,18.79618949976505,'2025-06-14 11:35:00',NULL,NULL,NULL),(169,32,153,44,18.79618949976505,'2025-06-14 11:35:00',NULL,NULL,NULL),(170,32,153,44,18.79618949976505,'2025-06-14 11:35:01',NULL,NULL,NULL),(171,32,153,44,18.79618949976505,'2025-06-14 11:35:01',NULL,NULL,NULL),(172,32,153,44,18.79618949976505,'2025-06-14 11:35:01',NULL,NULL,NULL),(173,32,153,44,18.79618949976505,'2025-06-14 11:35:02',NULL,NULL,NULL),(174,32,153,44,18.79618949976505,'2025-06-14 11:35:02',NULL,NULL,NULL),(175,32,153,44,18.79618949976505,'2025-06-14 11:35:03',NULL,NULL,NULL),(176,32,153,44,18.79618949976505,'2025-06-14 11:35:03',NULL,NULL,NULL),(177,32,153,44,18.79618949976505,'2025-06-14 11:35:20',NULL,NULL,NULL),(178,32,154,44,18.55287569573284,'2025-06-14 11:35:21',NULL,NULL,NULL),(179,32,154,44,18.55287569573284,'2025-06-14 11:35:22',NULL,NULL,NULL),(180,32,154,44,18.55287569573284,'2025-06-14 11:35:23',NULL,NULL,NULL),(181,32,154,44,18.55287569573284,'2025-06-14 11:35:24',NULL,NULL,NULL),(182,32,154,44,18.55287569573284,'2025-06-14 11:35:24',NULL,NULL,NULL),(183,32,154,44,18.55287569573284,'2025-06-14 11:35:25',NULL,NULL,NULL),(184,32,154,44,18.55287569573284,'2025-06-14 11:35:25',NULL,NULL,NULL),(185,32,154,44,18.55287569573284,'2025-06-14 11:35:27',NULL,NULL,NULL),(186,38,167,61,21.872422819032593,'2025-06-14 11:43:01',NULL,NULL,NULL),(187,32,154,44,18.55287569573284,'2025-06-14 15:18:50',NULL,NULL,NULL),(188,40,157,50,20.28479857195018,'2025-06-14 21:21:28',NULL,NULL,NULL),(189,39,150,42,18.666666666666668,'2025-06-14 21:22:08',NULL,NULL,NULL),(190,41,150,50,22.22222222222222,'2025-06-14 21:26:16',NULL,NULL,NULL),(191,33,163,43,16.184274906846326,'2025-06-14 22:27:49',NULL,NULL,NULL),(192,25,152,52,22.506925207756233,'2025-06-15 19:40:45',NULL,NULL,NULL),(193,35,151,40,17.543090215341433,'2025-06-15 20:48:39',NULL,NULL,NULL),(194,38,167,61,21.872422819032593,'2025-06-15 20:49:34',NULL,NULL,NULL),(195,35,151,40,17.543090215341433,'2025-06-15 20:52:30',NULL,NULL,NULL),(196,37,160,50,19.531249999999996,'2025-06-15 20:53:39',NULL,NULL,NULL),(197,36,150,49,21.77777777777778,'2025-06-15 20:53:48',NULL,NULL,NULL);
/*!40000 ALTER TABLE `bmi_records` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache`
--

DROP TABLE IF EXISTS `cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache`
--

LOCK TABLES `cache` WRITE;
/*!40000 ALTER TABLE `cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `cache_locks`
--

DROP TABLE IF EXISTS `cache_locks`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `cache_locks`
--

LOCK TABLES `cache_locks` WRITE;
/*!40000 ALTER TABLE `cache_locks` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache_locks` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `calorie_goals`
--

DROP TABLE IF EXISTS `calorie_goals`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `calorie_goals` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `daily_calories` int NOT NULL,
  `protein_goal` decimal(5,2) DEFAULT NULL,
  `carbs_goal` decimal(5,2) DEFAULT NULL,
  `fat_goal` decimal(5,2) DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `calorie_goals_user_id_foreign` (`user_id`),
  CONSTRAINT `calorie_goals_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `calorie_goals`
--

LOCK TABLES `calorie_goals` WRITE;
/*!40000 ALTER TABLE `calorie_goals` DISABLE KEYS */;
/*!40000 ALTER TABLE `calorie_goals` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `calorie_logs`
--

DROP TABLE IF EXISTS `calorie_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `calorie_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `log_date` date NOT NULL,
  `meal_type` enum('breakfast','lunch','dinner','snack') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `food_item_id` bigint unsigned DEFAULT NULL,
  `food_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `quantity` decimal(5,2) NOT NULL DEFAULT '1.00',
  `serving_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `calorie_logs_user_id_foreign` (`user_id`),
  KEY `calorie_logs_log_date_index` (`log_date`),
  CONSTRAINT `calorie_logs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `calorie_logs`
--

LOCK TABLES `calorie_logs` WRITE;
/*!40000 ALTER TABLE `calorie_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `calorie_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `chats`
--

DROP TABLE IF EXISTS `chats`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `chats` (
  `id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned NOT NULL,
  `admin_id` bigint unsigned DEFAULT '0',
  `last_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_message_time` timestamp NULL DEFAULT NULL,
  `unread_user` int DEFAULT '0',
  `unread_admin` int DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `admin_id` (`admin_id`),
  CONSTRAINT `chats_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `chats`
--

LOCK TABLES `chats` WRITE;
/*!40000 ALTER TABLE `chats` DISABLE KEYS */;
/*!40000 ALTER TABLE `chats` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `course_purchases`
--

DROP TABLE IF EXISTS `course_purchases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_purchases` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `course_id` bigint unsigned NOT NULL,
  `purchase_date` datetime NOT NULL,
  `amount_paid` decimal(10,2) NOT NULL,
  `payment_method` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `transaction_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `course_id` (`course_id`),
  CONSTRAINT `course_purchases_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `course_purchases_ibfk_2` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `course_purchases`
--

LOCK TABLES `course_purchases` WRITE;
/*!40000 ALTER TABLE `course_purchases` DISABLE KEYS */;
INSERT INTO `course_purchases` VALUES (1,27,2,'2025-06-13 03:31:29',49.00,'upi','121221222','completed'),(2,28,2,'2025-06-13 11:33:12',49.00,'upi','232132313123','completed'),(3,29,2,'2025-06-14 10:23:17',1499.00,'upi','76532235678','completed'),(4,30,2,'2025-06-14 10:33:57',1499.00,'upi','3456789','completed'),(5,32,2,'2025-06-14 10:51:23',1499.00,'upi','12345','completed'),(6,33,2,'2025-06-14 10:57:26',1499.00,'upi','3589000','completed'),(7,34,2,'2025-06-14 11:00:55',1499.00,'upi','12356','completed'),(8,35,2,'2025-06-14 11:04:10',1499.00,'upi','6545625','completed'),(9,36,2,'2025-06-14 11:06:44',1499.00,'upi','178039','completed'),(10,37,2,'2025-06-14 11:18:39',1499.00,'upi','12345','completed'),(11,38,2,'2025-06-14 11:41:54',1499.00,'upi','521667','completed'),(12,39,2,'2025-06-14 20:54:07',1499.00,'upi','2443','completed'),(13,40,2,'2025-06-14 21:07:21',1499.00,'upi','23456','completed'),(14,41,2,'2025-06-14 21:18:40',1500.00,'upi','8765','completed');
/*!40000 ALTER TABLE `course_purchases` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `course_videos`
--

DROP TABLE IF EXISTS `course_videos`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `course_videos` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `course_id` bigint unsigned NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `video_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_minutes` int DEFAULT NULL,
  `week_number` int NOT NULL DEFAULT '1',
  `sequence_number` int NOT NULL DEFAULT '0',
  `video_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `video_embed_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `course_videos_course_id_foreign` (`course_id`),
  CONSTRAINT `course_videos_course_id_foreign` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `course_videos`
--

LOCK TABLES `course_videos` WRITE;
/*!40000 ALTER TABLE `course_videos` DISABLE KEYS */;
INSERT INTO `course_videos` VALUES (1,2,'EPISODE 1','','https://vimeo.com/**********','https://shorturl.at/wZ5uG',14,1,1,NULL,NULL,NULL,1,NULL,NULL),(2,2,'EPISODE 2','','https://vimeo.com/1092216165?share=copy','https://shorturl.at/jcnpl',25,2,2,NULL,NULL,NULL,1,NULL,NULL),(4,2,'EPISODE 3','sdfs','https://vimeo.com/1092232498','https://shorturl.at/IBoGT',28,3,3,NULL,NULL,NULL,1,NULL,NULL),(7,2,'EPISODE 4','','https://vimeo.com/1092242468','https://shorturl.at/BXKn5',31,4,1,NULL,NULL,NULL,1,NULL,NULL),(8,2,'EPISODE 5','','https://vimeo.com/1092249935?share=copy','',37,5,1,NULL,NULL,NULL,1,NULL,NULL);
/*!40000 ALTER TABLE `course_videos` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `courses`
--

DROP TABLE IF EXISTS `courses`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `courses` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `thumbnail_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_weeks` int NOT NULL DEFAULT '1',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `is_premium` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `discount_percentage` int NOT NULL DEFAULT '0',
  `is_featured` tinyint(1) NOT NULL DEFAULT '0',
  `whatsapp_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `whatsapp_message_prefix` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `courses`
--

LOCK TABLES `courses` WRITE;
/*!40000 ALTER TABLE `courses` DISABLE KEYS */;
INSERT INTO `courses` VALUES (2,'Weight Loss','','https://www.spartan.com/cdn/shop/articles/at-home-workout-2_1200x.jpg?v=1595895885',6,1500.00,1,1,'2025-05-09 18:51:24','2025-05-09 18:51:24','General',0,0,'',''),(3,'Skinny Fat Loss','','',6,1500.00,0,1,'2025-05-09 18:51:24','2025-05-09 18:51:24','General',0,0,'',''),(13,'NO JUMPING CARDIO','','',6,1500.00,0,0,NULL,NULL,'General',0,0,'',''),(14,'Weight Gain','','',6,1500.00,0,0,NULL,NULL,'General',0,0,'','');
/*!40000 ALTER TABLE `courses` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `device_sessions`
--

DROP TABLE IF EXISTS `device_sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `device_sessions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `device_id` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `device_info` json DEFAULT NULL,
  `first_login` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_activity` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_active` tinyint(1) DEFAULT '1',
  `revoked_by_admin` int DEFAULT NULL,
  `revoked_at` timestamp NULL DEFAULT NULL,
  `revocation_reason` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_device` (`user_id`,`device_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_last_activity` (`last_activity`),
  KEY `idx_device_sessions_user_active` (`user_id`,`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `device_sessions`
--

LOCK TABLES `device_sessions` WRITE;
/*!40000 ALTER TABLE `device_sessions` DISABLE KEYS */;
INSERT INTO `device_sessions` VALUES (1,25,'QP1A.190711.020','{\"platform\": \"unknown\", \"first_seen\": \"2025-05-28 12:37:10.000000\"}','2025-05-28 07:07:10','2025-05-28 07:07:10',1,NULL,NULL,NULL,'2025-06-05 16:39:18','2025-06-05 16:39:18');
/*!40000 ALTER TABLE `device_sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `failed_jobs`
--

DROP TABLE IF EXISTS `failed_jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `failed_jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `failed_jobs`
--

LOCK TABLES `failed_jobs` WRITE;
/*!40000 ALTER TABLE `failed_jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `failed_jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `food_items`
--

DROP TABLE IF EXISTS `food_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `food_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `food_items_name_index` (`name`),
  KEY `food_items_category_index` (`category`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `food_items`
--

LOCK TABLES `food_items` WRITE;
/*!40000 ALTER TABLE `food_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `food_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `job_batches`
--

DROP TABLE IF EXISTS `job_batches`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `job_batches`
--

LOCK TABLES `job_batches` WRITE;
/*!40000 ALTER TABLE `job_batches` DISABLE KEYS */;
/*!40000 ALTER TABLE `job_batches` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `jobs`
--

DROP TABLE IF EXISTS `jobs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `jobs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint unsigned NOT NULL,
  `reserved_at` int unsigned DEFAULT NULL,
  `available_at` int unsigned NOT NULL,
  `created_at` int unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `jobs`
--

LOCK TABLES `jobs` WRITE;
/*!40000 ALTER TABLE `jobs` DISABLE KEYS */;
/*!40000 ALTER TABLE `jobs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `messages`
--

DROP TABLE IF EXISTS `messages`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `messages` (
  `id` int NOT NULL AUTO_INCREMENT,
  `chat_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `sender_id` bigint unsigned NOT NULL,
  `receiver_id` bigint unsigned NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT '0',
  `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'sent',
  `sent_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `chat_id` (`chat_id`),
  KEY `sender_id` (`sender_id`),
  KEY `receiver_id` (`receiver_id`),
  KEY `status` (`status`),
  CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`chat_id`) REFERENCES `chats` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=165 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `messages`
--

LOCK TABLES `messages` WRITE;
/*!40000 ALTER TABLE `messages` DISABLE KEYS */;
/*!40000 ALTER TABLE `messages` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `migrations` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'0001_01_01_000000_create_users_table',1),(2,'0001_01_01_000001_create_cache_table',1),(3,'0001_01_01_000002_create_jobs_table',1),(4,'2025_05_09_091152_create_users_table',1),(5,'2025_05_09_091206_create_admin_users_table',1),(6,'2025_05_09_091206_create_api_tokens_table',1),(7,'2025_05_09_091206_create_courses_table',1),(8,'2025_05_09_091206_create_pending_users_table',1),(9,'2025_05_09_091207_create_calorie_logs_table',1),(10,'2025_05_09_091207_create_course_videos_table',1),(11,'2025_05_09_091207_create_food_items_table',1),(12,'2025_05_09_091207_create_motivational_quotes_table',1),(13,'2025_05_09_091207_create_quote_settings_table',1),(14,'2025_05_09_091207_create_user_course_enrollments_table',1),(15,'2025_05_09_091207_create_user_food_items_table',1),(16,'2025_05_09_091207_create_user_quote_preferences_table',1),(17,'2025_05_09_091207_create_user_video_progress_table',1),(18,'2025_05_09_091208_create_bmi_records_table',1),(19,'2025_05_09_091208_create_calorie_goals_table',1),(20,'2025_05_09_091208_create_settings_table',1),(21,'2025_05_09_091208_create_streak_days_table',1),(22,'2025_05_09_091208_create_user_activity_log_table',1),(23,'2025_05_09_091208_create_workout_history_table',1),(24,'2025_05_09_100019_create_sessions_table',1),(25,'2025_05_09_101231_create_personal_access_tokens_table',1),(26,'2025_05_09_104919_add_missing_columns_to_courses_table',1),(27,'2025_05_09_111956_add_group_column_to_settings_table',1);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `motivational_quotes`
--

DROP TABLE IF EXISTS `motivational_quotes`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `motivational_quotes` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `quote` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_ai_generated` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `motivational_quotes_category_index` (`category`),
  KEY `motivational_quotes_is_active_index` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `motivational_quotes`
--

LOCK TABLES `motivational_quotes` WRITE;
/*!40000 ALTER TABLE `motivational_quotes` DISABLE KEYS */;
/*!40000 ALTER TABLE `motivational_quotes` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notification_settings`
--

DROP TABLE IF EXISTS `notification_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notification_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `admin_id` int NOT NULL,
  `email_notifications` tinyint(1) DEFAULT '1',
  `notification_types` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_id` (`admin_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notification_settings`
--

LOCK TABLES `notification_settings` WRITE;
/*!40000 ALTER TABLE `notification_settings` DISABLE KEYS */;
INSERT INTO `notification_settings` VALUES (1,1,1,'[\"user\",\"system\",\"course\"]','2025-05-12 07:28:29','2025-05-12 07:28:29');
/*!40000 ALTER TABLE `notification_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `notifications`
--

DROP TABLE IF EXISTS `notifications`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `notifications` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `target_id` int DEFAULT NULL,
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `is_read` tinyint(1) DEFAULT '0',
  `read_at` timestamp NULL DEFAULT NULL,
  `recipient_id` int DEFAULT NULL,
  `recipient_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'admin',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `is_read` (`is_read`),
  KEY `recipient_id` (`recipient_id`),
  KEY `recipient_type` (`recipient_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `notifications`
--

LOCK TABLES `notifications` WRITE;
/*!40000 ALTER TABLE `notifications` DISABLE KEYS */;
INSERT INTO `notifications` VALUES (1,'user','New user registration: John Doe',1,'user','2025-05-12 00:54:41',1,'2025-05-12 07:24:56',NULL,'admin'),(2,'system','System update completed successfully',NULL,NULL,'2025-05-11 22:54:41',1,NULL,NULL,'admin'),(3,'course','New course \"Advanced Fitness\" has been created',1,'course','2025-05-11 01:54:41',1,'2025-05-12 07:24:56',NULL,'admin');
/*!40000 ALTER TABLE `notifications` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_reset_tokens`
--

DROP TABLE IF EXISTS `password_reset_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_reset_tokens`
--

LOCK TABLES `password_reset_tokens` WRITE;
/*!40000 ALTER TABLE `password_reset_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_reset_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pending_users`
--

DROP TABLE IF EXISTS `pending_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pending_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `phone_number` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('pending','approved','not_interested') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'pending',
  `status_updated_at` timestamp NULL DEFAULT NULL,
  `status_updated_by` bigint unsigned DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `pending_users_phone_number_unique` (`phone_number`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pending_users`
--

LOCK TABLES `pending_users` WRITE;
/*!40000 ALTER TABLE `pending_users` DISABLE KEYS */;
INSERT INTO `pending_users` VALUES (1,'','9656444498',NULL,'approved','2025-05-09 21:26:41',NULL,NULL,NULL),(2,'','9565656562',NULL,'approved','2025-05-09 21:28:58',NULL,NULL,NULL),(3,'','65653256565',NULL,'approved','2025-05-09 21:38:40',NULL,NULL,NULL),(4,'','9494646464',NULL,'not_interested','2025-05-10 03:59:36',NULL,NULL,NULL),(7,'','9876543213',NULL,'approved','2025-05-12 06:30:06',NULL,NULL,NULL),(8,'','95599599865',NULL,'not_interested','2025-05-29 08:47:53',NULL,NULL,NULL),(9,'hshee','9656666664','TP1A.220905.001','not_interested','2025-05-29 08:47:53',NULL,'2025-05-28 02:25:59',NULL),(10,'Mahira','8714151952','QP1A.190711.020','approved','2025-05-28 06:31:45',NULL,'2025-05-28 06:31:02',NULL);
/*!40000 ALTER TABLE `pending_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `personal_access_tokens`
--

DROP TABLE IF EXISTS `personal_access_tokens`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `personal_access_tokens` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `personal_access_tokens`
--

LOCK TABLES `personal_access_tokens` WRITE;
/*!40000 ALTER TABLE `personal_access_tokens` DISABLE KEYS */;
/*!40000 ALTER TABLE `personal_access_tokens` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `pin_usage_history`
--

DROP TABLE IF EXISTS `pin_usage_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `pin_usage_history` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `pin` char(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `used_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_pin` (`user_id`,`pin`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `pin_usage_history`
--

LOCK TABLES `pin_usage_history` WRITE;
/*!40000 ALTER TABLE `pin_usage_history` DISABLE KEYS */;
INSERT INTO `pin_usage_history` VALUES (1,27,'2850','2025-06-14 19:56:56');
/*!40000 ALTER TABLE `pin_usage_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `program_enrollments`
--

DROP TABLE IF EXISTS `program_enrollments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `program_enrollments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `program_id` int NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_program_id` (`program_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `program_enrollments`
--

LOCK TABLES `program_enrollments` WRITE;
/*!40000 ALTER TABLE `program_enrollments` DISABLE KEYS */;
/*!40000 ALTER TABLE `program_enrollments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `quote_settings`
--

DROP TABLE IF EXISTS `quote_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `quote_settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `quote_settings_setting_key_unique` (`setting_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `quote_settings`
--

LOCK TABLES `quote_settings` WRITE;
/*!40000 ALTER TABLE `quote_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `quote_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `security_alerts`
--

DROP TABLE IF EXISTS `security_alerts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `security_alerts` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `alert_type` varchar(100) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `details` json DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_alert_type` (`alert_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `security_alerts`
--

LOCK TABLES `security_alerts` WRITE;
/*!40000 ALTER TABLE `security_alerts` DISABLE KEYS */;
/*!40000 ALTER TABLE `security_alerts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sessions`
--

DROP TABLE IF EXISTS `sessions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint unsigned DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sessions`
--

LOCK TABLES `sessions` WRITE;
/*!40000 ALTER TABLE `sessions` DISABLE KEYS */;
INSERT INTO `sessions` VALUES ('7quEAx9eO7JmS2YVkeRdMX9983Fp3rjEaa8cUh1K',1,'127.0.0.1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTo1OntzOjY6Il90b2tlbiI7czo0MDoidjV5cFlaY1FPSUREc3M2WnVFa3YyUlo3OWNQQUsyTnlsUVprS2hTaiI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjc6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMC9hZG1pbiI7fXM6MzoidXJsIjthOjA6e31zOjUyOiJsb2dpbl9hZG1pbl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjE7fQ==',1746796275),('c8kAM6vPONCKd5Kt5OujD9tgz8luZ0HfLc7PdPAj',1,'127.0.0.1','Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','YTo1OntzOjY6Il90b2tlbiI7czo0MDoiQzZ2Wkl1ZUd6WVpUWTFnOHVXcXFxb2h1UG1CV1J3UmRKdEdiNktvNCI7czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319czozOiJ1cmwiO2E6MDp7fXM6NTI6ImxvZ2luX2FkbWluXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czoyNzoiaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FkbWluIjt9fQ==',1746796241);
/*!40000 ALTER TABLE `sessions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `settings`
--

DROP TABLE IF EXISTS `settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `settings` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `group` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `settings_key_unique` (`key`),
  KEY `settings_group_index` (`group`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `settings`
--

LOCK TABLES `settings` WRITE;
/*!40000 ALTER TABLE `settings` DISABLE KEYS */;
INSERT INTO `settings` VALUES (1,'site_name','KFT Fitness','general',1,NULL,NULL),(2,'site_description','Fitness tracking and workout management','general',1,NULL,NULL),(3,'is_dev_mode','false','system',1,NULL,NULL),(4,'maintenance_mode','1','system',1,NULL,NULL),(5,'allow_registrations','true','users',1,NULL,NULL),(6,'default_user_role','viewer','users',1,NULL,NULL),(7,'contact_email','<EMAIL>','contact',1,NULL,NULL),(8,'max_login_attempts','5','security',1,NULL,NULL),(9,'lockout_time','30','security',1,NULL,NULL),(10,'session_lifetime','1440','security',1,NULL,NULL),(12,'deepseek_enabled','1',NULL,1,NULL,NULL),(15,'deepseek_api_key','***********************************',NULL,1,NULL,NULL),(16,'quote_categories','motivation, workout, home workout,',NULL,1,NULL,NULL),(19,'moderate_quotes','1',NULL,1,NULL,NULL),(21,'quote_fallback_source','external_api',NULL,1,NULL,NULL),(26,'platform_name','KFT Fitness',NULL,1,NULL,NULL),(27,'default_language','en',NULL,1,NULL,NULL),(28,'timezone','UTC',NULL,1,NULL,NULL),(29,'enable_registration','1',NULL,1,NULL,NULL),(31,'support_email','<EMAIL>',NULL,1,NULL,NULL),(47,'password_min_length','8',NULL,1,NULL,NULL),(48,'require_special','1',NULL,1,NULL,NULL),(49,'require_number','1',NULL,1,NULL,NULL),(50,'session_timeout','30',NULL,1,NULL,NULL),(51,'allow_password_reset','1',NULL,1,NULL,NULL),(52,'lockout_attempts','5',NULL,1,NULL,NULL),(53,'enable_2fa','0',NULL,1,NULL,NULL);
/*!40000 ALTER TABLE `settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `streak_days`
--

DROP TABLE IF EXISTS `streak_days`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `streak_days` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `date` date NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `streak_days_user_id_date_unique` (`user_id`,`date`),
  CONSTRAINT `streak_days_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `streak_days`
--

LOCK TABLES `streak_days` WRITE;
/*!40000 ALTER TABLE `streak_days` DISABLE KEYS */;
/*!40000 ALTER TABLE `streak_days` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_activity_log`
--

DROP TABLE IF EXISTS `user_activity_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_activity_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `activity_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `related_id` int DEFAULT NULL,
  `details` json DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_activity_log_user_id_foreign` (`user_id`),
  KEY `idx_user_activity_log_user_type_related` (`user_id`,`activity_type`,`related_id`),
  CONSTRAINT `user_activity_log_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=75 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_activity_log`
--

LOCK TABLES `user_activity_log` WRITE;
/*!40000 ALTER TABLE `user_activity_log` DISABLE KEYS */;
INSERT INTO `user_activity_log` VALUES (55,27,'admin_unlock',2,'{\"admin_id\": 1, \"video_id\": 2, \"course_id\": 2}','2025-06-14 06:19:50',NULL),(56,27,'video_progress',1,'{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}','2025-06-14 15:34:18',NULL),(57,27,'video_progress',1,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}','2025-06-14 13:34:18',NULL),(58,27,'video_progress',1,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}','2025-06-14 11:34:18',NULL),(59,27,'video_progress',1,'{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}','2025-06-14 09:34:18',NULL),(60,27,'video_progress',2,'{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}','2025-06-14 15:34:18',NULL),(61,27,'video_progress',2,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}','2025-06-14 13:34:18',NULL),(62,27,'video_progress',2,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}','2025-06-14 11:34:18',NULL),(63,27,'video_progress',2,'{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}','2025-06-14 09:34:18',NULL),(64,27,'video_progress',4,'{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}','2025-06-14 15:34:18',NULL),(65,27,'video_progress',4,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}','2025-06-14 13:34:18',NULL),(66,27,'video_progress',4,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}','2025-06-14 11:34:18',NULL),(67,27,'video_progress',4,'{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}','2025-06-14 09:34:18',NULL),(68,27,'video_progress',7,'{\"action\": \"play\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 30, \"watch_duration\": 30}','2025-06-14 15:34:18',NULL),(69,27,'video_progress',7,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 120, \"watch_duration\": 120}','2025-06-14 13:34:18',NULL),(70,27,'video_progress',7,'{\"action\": \"progress_update\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": false, \"last_position\": 300, \"watch_duration\": 300}','2025-06-14 11:34:18',NULL),(71,27,'video_progress',7,'{\"action\": \"complete\", \"timestamp\": \"2025-06-14 17:34:18\", \"sample_data\": true, \"is_completed\": true, \"last_position\": 450, \"watch_duration\": 450}','2025-06-14 09:34:18',NULL),(72,27,'video_progress',1,'{\"action\": \"complete\", \"timestamp\": \"2025-06-15 17:58:15\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}','2025-06-15 17:58:15',NULL),(73,27,'video_progress',1,'{\"action\": \"complete\", \"timestamp\": \"2025-06-15 18:04:05\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}','2025-06-15 18:04:05',NULL),(74,27,'video_progress',1,'{\"action\": \"complete\", \"timestamp\": \"2025-06-15 18:14:07\", \"is_completed\": true, \"last_position\": 180, \"watch_duration\": 180}',NULL,NULL);
/*!40000 ALTER TABLE `user_activity_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_course_enrollments`
--

DROP TABLE IF EXISTS `user_course_enrollments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_course_enrollments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `course_id` bigint unsigned NOT NULL,
  `enrollment_date` date DEFAULT NULL,
  `start_date` date NOT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','completed','cancelled') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
  `is_paid` tinyint(1) NOT NULL DEFAULT '0',
  `amount_paid` decimal(10,2) NOT NULL DEFAULT '0.00',
  `payment_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `transaction_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_course_enrollments_user_id_course_id_unique` (`user_id`,`course_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_course_id` (`course_id`),
  CONSTRAINT `user_course_enrollments_course_id_foreign` FOREIGN KEY (`course_id`) REFERENCES `courses` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_course_enrollments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=40 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_course_enrollments`
--

LOCK TABLES `user_course_enrollments` WRITE;
/*!40000 ALTER TABLE `user_course_enrollments` DISABLE KEYS */;
INSERT INTO `user_course_enrollments` VALUES (19,24,2,'2025-05-28','2025-05-28','2025-11-24','active',0,0.00,NULL,NULL,NULL,NULL),(20,25,2,'2025-05-28','2025-05-28','2025-11-24','active',0,0.00,NULL,NULL,NULL,NULL),(22,27,2,'2025-06-12','2025-06-12','2025-12-09','active',0,0.00,NULL,NULL,NULL,NULL),(23,28,2,'2025-06-13','2025-06-13','2025-12-10','active',0,0.00,NULL,NULL,NULL,NULL),(24,29,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(25,30,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(26,32,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(28,33,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(29,34,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(30,35,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(31,36,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(32,37,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(33,38,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(34,39,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(35,40,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL),(36,41,2,'2025-06-14','2025-06-14','2025-12-11','active',0,0.00,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `user_course_enrollments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_food_items`
--

DROP TABLE IF EXISTS `user_food_items`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_food_items` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `calories` int NOT NULL,
  `protein` decimal(5,2) DEFAULT NULL,
  `carbs` decimal(5,2) DEFAULT NULL,
  `fat` decimal(5,2) DEFAULT NULL,
  `serving_size` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_food_items_user_id_foreign` (`user_id`),
  CONSTRAINT `user_food_items_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_food_items`
--

LOCK TABLES `user_food_items` WRITE;
/*!40000 ALTER TABLE `user_food_items` DISABLE KEYS */;
/*!40000 ALTER TABLE `user_food_items` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_quote_preferences`
--

DROP TABLE IF EXISTS `user_quote_preferences`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_quote_preferences` (
  `user_id` bigint unsigned NOT NULL,
  `preferred_categories` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `personalization_enabled` tinyint(1) NOT NULL DEFAULT '1',
  `last_quote_id` bigint unsigned DEFAULT NULL,
  `last_quote_date` date DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`),
  CONSTRAINT `user_quote_preferences_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_quote_preferences`
--

LOCK TABLES `user_quote_preferences` WRITE;
/*!40000 ALTER TABLE `user_quote_preferences` DISABLE KEYS */;
INSERT INTO `user_quote_preferences` VALUES (24,'',1,NULL,NULL,NULL,NULL),(25,'',1,NULL,NULL,NULL,NULL),(27,'',1,NULL,NULL,NULL,NULL),(29,'',1,NULL,NULL,NULL,NULL),(30,'',1,NULL,NULL,NULL,NULL),(32,'',1,NULL,NULL,NULL,NULL),(33,'',1,NULL,NULL,NULL,NULL),(34,'',1,NULL,NULL,NULL,NULL),(35,'',1,NULL,NULL,NULL,NULL),(36,'',1,NULL,NULL,NULL,NULL),(37,'',1,NULL,NULL,NULL,NULL),(38,'',1,NULL,NULL,NULL,NULL),(39,'',1,NULL,NULL,NULL,NULL),(40,'',1,NULL,NULL,NULL,NULL),(41,'',1,NULL,NULL,NULL,NULL);
/*!40000 ALTER TABLE `user_quote_preferences` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `user_video_progress`
--

DROP TABLE IF EXISTS `user_video_progress`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `user_video_progress` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `video_id` bigint unsigned NOT NULL,
  `is_unlocked` tinyint(1) NOT NULL DEFAULT '0',
  `is_completed` tinyint(1) NOT NULL DEFAULT '0',
  `watch_duration_seconds` int NOT NULL DEFAULT '0',
  `last_position_seconds` int NOT NULL DEFAULT '0',
  `unlock_date` date DEFAULT NULL,
  `completion_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_video_progress_user_id_video_id_unique` (`user_id`,`video_id`),
  KEY `user_video_progress_video_id_foreign` (`video_id`),
  KEY `idx_user_video_progress_user_completed` (`user_id`,`is_completed`),
  KEY `idx_user_video_progress_video_completed` (`video_id`,`is_completed`),
  CONSTRAINT `user_video_progress_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `user_video_progress_video_id_foreign` FOREIGN KEY (`video_id`) REFERENCES `course_videos` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=30 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `user_video_progress`
--

LOCK TABLES `user_video_progress` WRITE;
/*!40000 ALTER TABLE `user_video_progress` DISABLE KEYS */;
INSERT INTO `user_video_progress` VALUES (6,25,1,1,0,0,0,'2025-05-28',NULL,NULL,NULL),(8,25,2,1,0,0,0,'2025-06-05',NULL,NULL,NULL),(10,27,1,0,1,180,180,'2025-06-13',NULL,NULL,'2025-06-15 18:14:07'),(11,24,1,1,0,0,0,'2025-06-13',NULL,NULL,NULL),(12,24,2,1,0,0,0,'2025-06-13',NULL,NULL,NULL),(13,29,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(14,30,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(15,32,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(16,33,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(17,34,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(18,35,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(19,36,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(20,37,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(21,38,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(22,27,2,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(23,25,4,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(24,40,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(25,39,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL),(26,41,1,1,0,0,0,'2025-06-14',NULL,NULL,NULL);
/*!40000 ALTER TABLE `user_video_progress` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `height` double DEFAULT NULL,
  `weight` double DEFAULT NULL,
  `is_premium` tinyint(1) NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `last_login` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT NULL,
  `device_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `gender` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fitness_goal` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `age` int DEFAULT NULL,
  `verification_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `verification_expires_at` datetime DEFAULT NULL,
  `profile_image_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `assigned_staff_id` bigint unsigned DEFAULT NULL,
  `pin` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `pin_expires_at` datetime DEFAULT NULL,
  `pin_used` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_username_unique` (`username`),
  UNIQUE KEY `users_phone_number_unique` (`phone_number`),
  UNIQUE KEY `users_email_unique` (`email`),
  KEY `assigned_staff_id` (`assigned_staff_id`),
  KEY `idx_username` (`username`),
  KEY `idx_phone_number` (`phone_number`),
  KEY `idx_assigned_staff_id` (`assigned_staff_id`),
  KEY `idx_device_id` (`device_id`),
  KEY `idx_users_device_id_active` (`device_id`,`is_active`),
  CONSTRAINT `fk_assigned_staff` FOREIGN KEY (`assigned_staff_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'John Doe','john.doe','<EMAIL>','',NULL,'$2y$10$TMthQoXyW6Ho62cORnHviebv9vVRNkmKXeBOz6hLF8CZ/aML5hfdq',NULL,NULL,0,1,NULL,NULL,'2025-06-15 18:55:09',NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,NULL,0),(24,'Farook','farook4110',NULL,'+919895035757',NULL,NULL,188,76,1,1,'2025-06-13 16:58:15',NULL,'2025-05-28 02:29:04','2025-06-13 16:58:18','AP3A.240905.015.A2','+919895035757','male','weight_loss',33,'492059','2025-05-29 02:29:04',NULL,7,'9947',NULL,1),(25,'Mahira','mahira9993',NULL,'+918714151952',NULL,NULL,152,52,1,1,'2025-06-15 14:12:22',NULL,'2025-05-28 07:07:10','2025-06-15 14:10:45','QP1A.190711.020','+918714151952','female','weight_loss',27,'480236','2025-05-29 07:07:10',NULL,7,'6209','2025-06-15 14:40:38',1),(27,'jafer sadik','sdfwe32',NULL,'+919656444498',NULL,NULL,331,122,1,1,'2025-06-15 16:07:06',NULL,'2025-06-12 22:01:29',NULL,'TP1A.220905.001','+919656444498','male','weight_loss',33,'756313','2025-06-13 22:01:29',NULL,7,'6343','2025-06-15 16:37:03',1),(28,'test','test5335',NULL,'+919876543210',NULL,NULL,160,100,1,1,NULL,NULL,'2025-06-13 06:03:12',NULL,NULL,'+919876543210','female','weight_loss',20,'458471','2025-06-14 06:03:12',NULL,7,'3283',NULL,0),(29,'soorya','soorya@123',NULL,'+919746613507',NULL,NULL,157,57,1,1,'2025-06-14 04:53:56',NULL,'2025-06-14 04:53:17','2025-06-14 05:27:22','S3RWBS32.125-29-2-4-3','+919746613507','female','weight_loss',22,'253407','2025-06-15 04:53:17',NULL,7,'7492',NULL,0),(30,'Shabnam','shabnam6523',NULL,'+918137087075',NULL,NULL,161,44,1,1,'2025-06-14 06:03:41',NULL,'2025-06-14 05:03:57','2025-06-14 06:04:22','AP3A.240905.015.A2_NC','+918137087075','female','weight_loss',24,'127432','2025-06-15 05:03:57',NULL,7,'7109',NULL,1),(32,'Rajeshwari','rajeshwari9423',NULL,'+918848597612',NULL,NULL,154,44,1,1,'2025-06-14 09:48:42',NULL,'2025-06-14 05:21:23','2025-06-14 09:48:50','AP3A.240905.015.A2','+918848597612','female','weight_loss',25,'973841','2025-06-15 05:21:23',NULL,7,'6794',NULL,1),(33,'Sannidhi K Shetty','sannidhikshetty9736',NULL,'+916361356281',NULL,NULL,163,43,1,1,'2025-06-14 16:57:37',NULL,'2025-06-14 05:27:26','2025-06-14 16:57:49','UP1A.231005.007','+916361356281','female','weight_loss',24,'513282','2025-06-15 05:27:26',NULL,5,'9389',NULL,1),(34,'Sabah','sabah3982',NULL,'+919633111952',NULL,NULL,150,47,1,1,'2025-06-14 05:31:42',NULL,'2025-06-14 05:30:55','2025-06-14 05:31:45','RKQ1.211001.001','+919633111952','female','weight_loss',24,'238962','2025-06-15 05:30:55',NULL,7,'6343',NULL,1),(35,'Kadeejath Rahina Nasreen','kadeejathrahinanasreen9067',NULL,'+919972198900',NULL,NULL,151,40,1,1,'2025-06-15 15:22:25',NULL,'2025-06-14 05:34:10','2025-06-15 15:22:30','AP3A.240905.015.A2_NC','+919972198900','female','weight_loss',24,'936324','2025-06-15 05:34:10',NULL,7,'1830','2025-06-15 15:48:10',1),(36,'Ramya.T','ramyat1088',NULL,'+918590996832',NULL,NULL,150,49,1,1,'2025-06-15 15:25:46',NULL,'2025-06-14 05:36:44','2025-06-15 15:23:48','AP3A.240905.015.A2','+918590996832','female','weight_loss',23,'987394','2025-06-15 05:36:44',NULL,7,'9024','2025-06-15 15:52:05',1),(37,'maariya','maariya5510',NULL,'+919526007767',NULL,NULL,160,50,1,1,'2025-06-15 15:23:35',NULL,'2025-06-14 05:48:39','2025-06-15 15:23:39','T2SRS33.72-22-4-11','+919526007767','female','weight_loss',21,'310794','2025-06-15 05:48:39',NULL,NULL,'7300','2025-06-15 15:48:48',1),(38,'Thawhida','thawhida2508',NULL,'+919633216952',NULL,NULL,167,61,1,1,'2025-06-15 15:19:30',NULL,'2025-06-14 06:11:54','2025-06-15 15:19:34','S3RWBS32.125-29-2-4-3','+919633216952','female','weight_loss',24,'903196','2025-06-15 06:11:54',NULL,7,'3471','2025-06-15 15:41:57',1),(39,'Shifa','shifa3481',NULL,'+918129283507',NULL,NULL,150,42,1,1,'2025-06-14 15:52:05',NULL,'2025-06-14 15:24:07','2025-06-14 15:52:08','AP3A.240905.015.A2_NC','+918129283507','female','weight_loss',23,'428011','2025-06-15 15:24:07',NULL,7,'2545',NULL,1),(40,'Nahla','nahla1352',NULL,'+918714840774',NULL,NULL,157,50,1,1,'2025-06-14 15:51:25',NULL,'2025-06-14 15:37:21','2025-06-14 15:51:28','SKQ1.211103.001','+918714840774','female','weight_loss',24,'625897','2025-06-15 15:37:21',NULL,NULL,'4068',NULL,1),(41,'Nusrin','nusrin7442',NULL,'+919061053507',NULL,NULL,150,50,1,1,'2025-06-14 15:56:11',NULL,'2025-06-14 15:48:40','2025-06-14 15:56:16','UP1A.231005.007','+919061053507','','weight_loss',25,'756365','2025-06-15 15:48:40',NULL,7,'8432','2026-06-16 14:14:32',0),(42,'dfasfs','dfasfs4714',NULL,'+912321312321',NULL,NULL,NULL,NULL,1,1,NULL,NULL,'2025-06-16 16:02:08',NULL,NULL,'+912321312321','','',NULL,'735244','2025-06-17 16:02:08',NULL,NULL,'0255',NULL,0);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_access_logs`
--

DROP TABLE IF EXISTS `video_access_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_access_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `vimeo_id` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `video_id` int NOT NULL,
  `user_id` int NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci NOT NULL,
  `timestamp` timestamp NULL DEFAULT NULL,
  `app_domain` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb3 COLLATE utf8mb3_unicode_ci,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `watch_duration_seconds` int DEFAULT '0',
  `last_position_seconds` int DEFAULT '0',
  `is_completed` tinyint(1) DEFAULT '0',
  `device_info` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_video_user` (`video_id`,`user_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_access_logs`
--

LOCK TABLES `video_access_logs` WRITE;
/*!40000 ALTER TABLE `video_access_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_access_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `video_analytics`
--

DROP TABLE IF EXISTS `video_analytics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `video_analytics` (
  `id` int NOT NULL AUTO_INCREMENT,
  `video_id` int NOT NULL,
  `total_views` int DEFAULT '0',
  `total_completions` int DEFAULT '0',
  `total_watch_duration` int DEFAULT '0',
  `average_watch_duration` int DEFAULT '0',
  `last_updated` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `video_id` (`video_id`),
  KEY `idx_video_id` (`video_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `video_analytics`
--

LOCK TABLES `video_analytics` WRITE;
/*!40000 ALTER TABLE `video_analytics` DISABLE KEYS */;
/*!40000 ALTER TABLE `video_analytics` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `water_reminders`
--

DROP TABLE IF EXISTS `water_reminders`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `water_reminders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `interval_hours` int NOT NULL DEFAULT '2',
  `start_time` time NOT NULL DEFAULT '08:00:00',
  `end_time` time NOT NULL DEFAULT '22:00:00',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `is_verified` tinyint(1) NOT NULL DEFAULT '0',
  `verified_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=35 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `water_reminders`
--

LOCK TABLES `water_reminders` WRITE;
/*!40000 ALTER TABLE `water_reminders` DISABLE KEYS */;
INSERT INTO `water_reminders` VALUES (1,6,2,'08:00:00','22:00:00',1,'2025-05-09 21:38:40','2025-05-09 21:38:40',0,NULL),(2,7,2,'08:00:00','22:00:00',1,'2025-05-09 21:42:10','2025-05-09 21:42:10',0,NULL),(3,8,2,'08:00:00','22:00:00',1,'2025-05-09 21:45:05','2025-05-09 21:45:05',0,NULL),(4,9,2,'08:00:00','22:00:00',1,'2025-05-10 04:27:52','2025-05-10 04:27:52',0,NULL),(5,10,2,'08:00:00','22:00:00',1,'2025-05-11 05:59:38','2025-05-11 05:59:38',0,NULL),(6,11,2,'08:00:00','22:00:00',0,'2025-05-12 05:44:57','2025-05-12 09:10:03',0,NULL),(7,12,2,'08:00:00','22:00:00',1,'2025-05-12 05:56:21','2025-05-12 13:46:00',0,NULL),(8,13,2,'08:00:00','22:00:00',1,'2025-05-12 20:57:16','2025-05-12 20:57:16',0,NULL),(9,14,2,'08:00:00','22:00:00',1,'2025-05-12 20:59:37','2025-05-12 20:59:37',0,NULL),(10,18,2,'08:00:00','22:00:00',1,'2025-05-13 12:48:11','2025-05-13 12:48:11',0,NULL),(11,19,2,'08:00:00','22:00:00',1,'2025-05-13 13:11:45','2025-05-13 13:11:45',0,NULL),(12,20,2,'08:00:00','22:00:00',1,'2025-05-13 13:36:16','2025-05-13 13:36:16',0,NULL),(13,21,2,'08:00:00','22:00:00',1,'2025-05-13 13:41:04','2025-05-13 13:41:04',0,NULL),(14,22,2,'08:00:00','22:00:00',0,'2025-05-13 13:43:12','2025-05-13 13:43:57',0,NULL),(15,23,2,'08:00:00','22:00:00',1,'2025-05-13 17:26:03','2025-05-13 17:26:03',0,NULL),(16,24,2,'08:00:00','22:00:00',1,'2025-05-28 02:29:05','2025-05-28 02:29:05',0,NULL),(17,25,2,'08:00:00','22:00:00',1,'2025-05-28 07:07:10','2025-05-29 14:33:32',0,NULL),(18,26,1,'08:00:00','22:00:00',1,'2025-05-30 13:32:30','2025-06-01 17:02:38',0,NULL),(19,27,2,'08:00:00','22:00:00',1,'2025-06-12 22:01:29','2025-06-12 22:01:29',0,NULL),(20,28,2,'08:00:00','22:00:00',1,'2025-06-13 06:03:12','2025-06-13 06:03:12',0,NULL),(21,29,2,'08:00:00','22:00:00',1,'2025-06-14 04:53:17','2025-06-14 04:53:17',0,NULL),(22,30,2,'08:00:00','22:00:00',1,'2025-06-14 05:03:57','2025-06-14 05:03:57',0,NULL),(23,31,2,'08:00:00','22:00:00',1,'2025-06-14 05:17:35','2025-06-14 05:17:35',0,NULL),(24,32,2,'08:00:00','22:00:00',1,'2025-06-14 05:21:23','2025-06-14 05:21:23',0,NULL),(25,33,2,'08:00:00','22:00:00',1,'2025-06-14 05:27:26','2025-06-14 05:27:26',0,NULL),(26,34,2,'08:00:00','22:00:00',1,'2025-06-14 05:30:55','2025-06-14 05:30:55',0,NULL),(27,35,2,'08:00:00','22:00:00',1,'2025-06-14 05:34:10','2025-06-14 05:34:10',0,NULL),(28,36,2,'08:00:00','22:00:00',1,'2025-06-14 05:36:44','2025-06-14 05:36:44',0,NULL),(29,37,2,'08:00:00','22:00:00',1,'2025-06-14 05:48:39','2025-06-14 05:48:39',0,NULL),(30,38,2,'08:00:00','22:00:00',1,'2025-06-14 06:11:54','2025-06-14 06:11:54',0,NULL),(31,39,2,'08:00:00','22:00:00',1,'2025-06-14 15:24:07','2025-06-14 15:24:07',0,NULL),(32,40,2,'08:00:00','22:00:00',1,'2025-06-14 15:37:21','2025-06-14 15:37:21',0,NULL),(33,41,2,'08:00:00','22:00:00',1,'2025-06-14 15:48:40','2025-06-14 15:48:40',0,NULL),(34,42,2,'08:00:00','22:00:00',1,'2025-06-16 16:02:08','2025-06-16 16:02:08',0,NULL);
/*!40000 ALTER TABLE `water_reminders` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workout_history`
--

DROP TABLE IF EXISTS `workout_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workout_history` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint unsigned NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `duration_minutes` int NOT NULL DEFAULT '0',
  `calories_burned` int DEFAULT NULL,
  `date` datetime NOT NULL,
  `workout_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `intensity` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `workout_history_user_id_foreign` (`user_id`),
  CONSTRAINT `workout_history_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workout_history`
--

LOCK TABLES `workout_history` WRITE;
/*!40000 ALTER TABLE `workout_history` DISABLE KEYS */;
/*!40000 ALTER TABLE `workout_history` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workout_programs`
--

DROP TABLE IF EXISTS `workout_programs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workout_programs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `difficulty` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `duration_weeks` int DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workout_programs`
--

LOCK TABLES `workout_programs` WRITE;
/*!40000 ALTER TABLE `workout_programs` DISABLE KEYS */;
/*!40000 ALTER TABLE `workout_programs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `workout_records`
--

DROP TABLE IF EXISTS `workout_records`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `workout_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `workout_id` int DEFAULT NULL,
  `duration` int NOT NULL COMMENT 'in minutes',
  `calories_burned` int DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `recorded_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `duration_minutes` int DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `workout_id` (`workout_id`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `workout_records`
--

LOCK TABLES `workout_records` WRITE;
/*!40000 ALTER TABLE `workout_records` DISABLE KEYS */;
INSERT INTO `workout_records` VALUES (1,1,1,30,250,'Morning cardio session','2025-05-08 18:50:18',0),(2,2,2,45,350,'Evening strength training','2025-05-07 18:50:18',0),(3,3,3,60,500,'Full body workout','2025-05-06 18:50:18',0),(4,4,4,20,180,'Quick HIIT session','2025-05-05 18:50:18',0),(5,5,5,90,700,'Long distance running','2025-05-04 18:50:18',0),(6,1,6,40,300,'Yoga and stretching','2025-05-03 18:50:18',0),(7,2,7,50,400,'Weight lifting','2025-05-02 18:50:18',0),(8,3,1,35,280,'Morning run','2025-05-09 18:50:18',0),(16,15,NULL,30,NULL,NULL,'2025-05-13 06:25:34',0),(17,16,NULL,45,NULL,NULL,'2025-05-13 06:27:33',0),(18,17,NULL,60,NULL,NULL,'2025-05-13 06:27:37',0);
/*!40000 ALTER TABLE `workout_records` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-06-16 22:10:07
