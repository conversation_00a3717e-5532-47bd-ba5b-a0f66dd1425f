#!/bin/bash
echo "Starting KFT Fitness App and Admin Dashboard..."

# Function to check if a port is in use
is_port_in_use() {
  lsof -i :$1 >/dev/null 2>&1
  return $?
}

# Check if ports are already in use
if is_port_in_use 9001; then
  echo "Error: Port 9001 is already in use. Please close the application using this port."
  exit 1
fi

# Start PHP server for admin dashboard in the background
echo "Starting PHP server for admin dashboard on port 9001..."
(cd admin && php -S 0.0.0.0:9001) &
PHP_PID=$!

# Give the PHP server a moment to start
sleep 2

# Check if PHP server started successfully
if ! is_port_in_use 9001; then
  echo "Error: Failed to start PHP server. Please check if PHP is installed correctly."
  exit 1
fi

echo "Admin dashboard is running at http://***************:9001"
echo "App is running at http://***************:9001"
echo "---------------------------------------------------"

# Start Flutter app
echo "Starting Flutter app..."

# Local IP address
LOCAL_IP="***************"
APP_PORT=9001
SERVER_PORT=9001

# Check Flutter installation
echo "Checking Flutter installation..."
FLUTTER_PATH=$(which flutter)
if [ -z "$FLUTTER_PATH" ]; then
  echo "Flutter not found in PATH. Trying direct path..."
  FLUTTER_PATH="/Users/<USER>/flutter/bin/flutter"
fi

# Check available devices
echo "Checking available devices..."
$FLUTTER_PATH devices

# Run the app
echo "Running the Flutter app..."

# Try to run on Android device if available
ANDROID_DEVICE=$($FLUTTER_PATH devices | grep -i "android" | head -1 | awk '{print $1}')
if [ -n "$ANDROID_DEVICE" ]; then
  echo "Android device found: $ANDROID_DEVICE, running on Android..."
  $FLUTTER_PATH run -d "$ANDROID_DEVICE" --web-port=$APP_PORT
else
  # Fall back to Chrome if no Android device is available
  echo "No Android device found, running on Chrome..."
  $FLUTTER_PATH run -d chrome --web-port=$APP_PORT
fi

# If the Flutter app exits, kill the PHP server
kill $PHP_PID

echo "Both servers have been stopped."
