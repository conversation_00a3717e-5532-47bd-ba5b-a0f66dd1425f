#!/usr/bin/env python3
"""
Upload the hosted Vimeo player HTML file to mycloudforge.com
This script uploads the hosted_vimeo_player.html file to the server
"""

import requests
import os
import sys

def upload_hosted_player():
    """Upload the hosted player HTML file to the server"""
    
    # Read the hosted player HTML file
    html_file_path = 'hosted_vimeo_player.html'
    
    if not os.path.exists(html_file_path):
        print(f"❌ Error: {html_file_path} not found")
        return False
    
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"📄 Read {len(html_content)} characters from {html_file_path}")
        
        # Upload via FTP or HTTP (simulated here)
        # In a real scenario, you would use FTP, SFTP, or a file upload API
        
        # For now, let's create a simple HTTP upload simulation
        upload_url = 'https://mycloudforge.com/upload_hosted_player.php'
        
        files = {
            'file': ('kft_vimeo_player.html', html_content, 'text/html')
        }
        
        data = {
            'action': 'upload_hosted_player',
            'filename': 'kft_vimeo_player.html'
        }
        
        print(f"🔄 Uploading to {upload_url}...")
        
        # Note: This is a simulation - in practice you'd need proper server-side upload handling
        response = requests.post(upload_url, files=files, data=data, timeout=30)
        
        if response.status_code == 200:
            print("✅ Hosted player uploaded successfully!")
            return True
        else:
            print(f"❌ Upload failed with status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except FileNotFoundError:
        print(f"❌ Error: Could not find {html_file_path}")
        return False
    except requests.RequestException as e:
        print(f"❌ Network error during upload: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def verify_hosted_player():
    """Verify that the hosted player is accessible on the server"""
    
    test_url = 'https://mycloudforge.com/kft_vimeo_player.html'
    
    try:
        print(f"🔍 Verifying hosted player at {test_url}...")
        
        response = requests.get(test_url, timeout=10)
        
        if response.status_code == 200:
            content = response.text
            
            # Check for key components
            if 'KFT Vimeo Player' in content and 'throttledSeek' in content:
                print("✅ Hosted player is accessible and contains expected content")
                return True
            else:
                print("⚠️ Hosted player is accessible but may be missing key components")
                return False
        else:
            print(f"❌ Hosted player not accessible (status: {response.status_code})")
            return False
            
    except requests.RequestException as e:
        print(f"❌ Error verifying hosted player: {e}")
        return False

def main():
    """Main function"""
    print("🎬 KFT Hosted Player Upload Script")
    print("=" * 50)
    
    # First, try to verify if it already exists
    if verify_hosted_player():
        print("✅ Hosted player already exists and is working")
        return
    
    # Upload the hosted player
    if upload_hosted_player():
        # Verify the upload
        if verify_hosted_player():
            print("🎉 Hosted player upload and verification successful!")
        else:
            print("⚠️ Upload completed but verification failed")
    else:
        print("❌ Failed to upload hosted player")

if __name__ == "__main__":
    main()
