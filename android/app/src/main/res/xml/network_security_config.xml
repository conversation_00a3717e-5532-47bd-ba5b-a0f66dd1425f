<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <base-config cleartextTrafficPermitted="true">
        <trust-anchors>
            <!-- Trust system certificates -->
            <certificates src="system"/>
            <!-- Trust user-added certificates for development -->
            <certificates src="user"/>
        </trust-anchors>
    </base-config>

    <!-- Allow all certificates for development domains -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***************</domain>
        <domain includeSubdomains="true">mycloudforge.com</domain>
        <domain includeSubdomains="true">ngrok-free.app</domain>
        <domain includeSubdomains="true">ngrok.io</domain>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>

    <!-- Debug overrides for development -->
    <debug-overrides>
        <trust-anchors>
            <certificates src="system"/>
            <certificates src="user"/>
        </trust-anchors>
    </debug-overrides>
</network-security-config>