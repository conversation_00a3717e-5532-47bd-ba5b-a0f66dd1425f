package com.example.kft

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.os.Debug
import android.os.StatFs
import android.os.Environment
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.io.File
import java.io.RandomAccessFile

/**
 * Production-grade device info plugin for performance monitoring
 * Provides detailed device capabilities and memory information
 */
class DeviceInfoPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.kft.fitness/device_info")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: Method<PERSON>all, result: Result) {
        when (call.method) {
            "getMemoryInfo" -> {
                try {
                    val memoryInfo = getMemoryInfo()
                    result.success(memoryInfo)
                } catch (e: Exception) {
                    result.error("MEMORY_ERROR", "Failed to get memory info: ${e.message}", null)
                }
            }
            "getDeviceInfo" -> {
                try {
                    val deviceInfo = getDeviceInfo()
                    result.success(deviceInfo)
                } catch (e: Exception) {
                    result.error("DEVICE_ERROR", "Failed to get device info: ${e.message}", null)
                }
            }
            "getStorageInfo" -> {
                try {
                    val storageInfo = getStorageInfo()
                    result.success(storageInfo)
                } catch (e: Exception) {
                    result.error("STORAGE_ERROR", "Failed to get storage info: ${e.message}", null)
                }
            }
            "getCpuInfo" -> {
                try {
                    val cpuInfo = getCpuInfo()
                    result.success(cpuInfo)
                } catch (e: Exception) {
                    result.error("CPU_ERROR", "Failed to get CPU info: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * Get comprehensive memory information
     */
    private fun getMemoryInfo(): Map<String, Any> {
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        // Get total RAM
        val totalMemory = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
            memoryInfo.totalMem / (1024 * 1024) // Convert to MB
        } else {
            getTotalMemoryLegacy()
        }

        // Get available memory
        val availableMemory = memoryInfo.availMem / (1024 * 1024) // Convert to MB

        // Get current app memory usage
        val runtime = Runtime.getRuntime()
        val usedMemory = (runtime.totalMemory() - runtime.freeMemory()) / (1024 * 1024)
        val maxMemory = runtime.maxMemory() / (1024 * 1024)

        // Get native heap info
        val nativeHeapSize = Debug.getNativeHeapSize() / (1024 * 1024)
        val nativeHeapAllocated = Debug.getNativeHeapAllocatedSize() / (1024 * 1024)
        val nativeHeapFree = Debug.getNativeHeapFreeSize() / (1024 * 1024)

        return mapOf(
            "totalMemory" to totalMemory,
            "availableMemory" to availableMemory,
            "usedMemory" to usedMemory,
            "maxMemory" to maxMemory,
            "isLowMemory" to memoryInfo.lowMemory,
            "threshold" to (memoryInfo.threshold / (1024 * 1024)),
            "nativeHeapSize" to nativeHeapSize,
            "nativeHeapAllocated" to nativeHeapAllocated,
            "nativeHeapFree" to nativeHeapFree,
            "memoryClass" to activityManager.memoryClass,
            "largeMemoryClass" to activityManager.largeMemoryClass
        )
    }

    /**
     * Get total memory for older Android versions
     */
    private fun getTotalMemoryLegacy(): Long {
        return try {
            val reader = RandomAccessFile("/proc/meminfo", "r")
            val line = reader.readLine()
            reader.close()
            
            val tokens = line.split("\\s+".toRegex())
            if (tokens.size >= 2) {
                tokens[1].toLong() / 1024 // Convert KB to MB
            } else {
                2048 // Default 2GB
            }
        } catch (e: Exception) {
            2048 // Default 2GB
        }
    }

    /**
     * Get comprehensive device information
     */
    private fun getDeviceInfo(): Map<String, Any> {
        return mapOf(
            "manufacturer" to Build.MANUFACTURER,
            "model" to Build.MODEL,
            "device" to Build.DEVICE,
            "product" to Build.PRODUCT,
            "brand" to Build.BRAND,
            "hardware" to Build.HARDWARE,
            "board" to Build.BOARD,
            "androidVersion" to Build.VERSION.RELEASE,
            "sdkInt" to Build.VERSION.SDK_INT,
            "codename" to Build.VERSION.CODENAME,
            "incremental" to Build.VERSION.INCREMENTAL,
            "fingerprint" to Build.FINGERPRINT,
            "supportedAbis" to Build.SUPPORTED_ABIS.toList(),
            "supported32BitAbis" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Build.SUPPORTED_32_BIT_ABIS.toList()
            } else {
                emptyList<String>()
            },
            "supported64BitAbis" to if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                Build.SUPPORTED_64_BIT_ABIS.toList()
            } else {
                emptyList<String>()
            }
        )
    }

    /**
     * Get storage information
     */
    private fun getStorageInfo(): Map<String, Any> {
        val internalStorage = getStorageStats(Environment.getDataDirectory())
        val externalStorage = if (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED) {
            getStorageStats(Environment.getExternalStorageDirectory())
        } else {
            mapOf("total" to 0L, "free" to 0L, "used" to 0L)
        }

        return mapOf(
            "internal" to internalStorage,
            "external" to externalStorage,
            "hasExternalStorage" to (Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED)
        )
    }

    /**
     * Get storage statistics for a directory
     */
    private fun getStorageStats(directory: File): Map<String, Long> {
        val stat = StatFs(directory.path)
        val blockSize = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            stat.blockSizeLong
        } else {
            stat.blockSize.toLong()
        }
        
        val totalBlocks = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            stat.blockCountLong
        } else {
            stat.blockCount.toLong()
        }
        
        val freeBlocks = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN_MR2) {
            stat.freeBlocksLong
        } else {
            stat.freeBlocks.toLong()
        }

        val totalBytes = totalBlocks * blockSize
        val freeBytes = freeBlocks * blockSize
        val usedBytes = totalBytes - freeBytes

        return mapOf(
            "total" to (totalBytes / (1024 * 1024)), // Convert to MB
            "free" to (freeBytes / (1024 * 1024)),
            "used" to (usedBytes / (1024 * 1024))
        )
    }

    /**
     * Get CPU information
     */
    private fun getCpuInfo(): Map<String, Any> {
        val cpuInfo = mutableMapOf<String, Any>()
        
        try {
            // Get CPU cores
            cpuInfo["cores"] = Runtime.getRuntime().availableProcessors()
            
            // Get CPU architecture
            cpuInfo["architecture"] = System.getProperty("os.arch") ?: "unknown"
            
            // Try to get CPU frequency and other info from /proc/cpuinfo
            val cpuInfoFile = File("/proc/cpuinfo")
            if (cpuInfoFile.exists()) {
                val cpuInfoText = cpuInfoFile.readText()
                
                // Extract processor name
                val processorRegex = Regex("model name\\s*:\\s*(.+)")
                val processorMatch = processorRegex.find(cpuInfoText)
                if (processorMatch != null) {
                    cpuInfo["processor"] = processorMatch.groupValues[1].trim()
                }
                
                // Extract CPU frequency
                val frequencyRegex = Regex("cpu MHz\\s*:\\s*([0-9.]+)")
                val frequencyMatch = frequencyRegex.find(cpuInfoText)
                if (frequencyMatch != null) {
                    cpuInfo["frequencyMHz"] = frequencyMatch.groupValues[1].toDoubleOrNull() ?: 0.0
                }
                
                // Extract features
                val featuresRegex = Regex("Features\\s*:\\s*(.+)")
                val featuresMatch = featuresRegex.find(cpuInfoText)
                if (featuresMatch != null) {
                    cpuInfo["features"] = featuresMatch.groupValues[1].trim().split(" ")
                }
            }
            
        } catch (e: Exception) {
            cpuInfo["error"] = "Failed to read CPU info: ${e.message}"
        }
        
        return cpuInfo
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
    }
}
