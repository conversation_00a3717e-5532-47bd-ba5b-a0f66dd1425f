package com.example.kft

import android.app.ActivityManager
import android.content.Context
import android.os.Debug
import android.os.Handler
import android.os.Looper
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result
import java.lang.ref.WeakReference

/**
 * Production-grade performance monitoring plugin
 * Provides real-time performance metrics and memory management
 */
class PerformancePlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private val handler = Handler(Looper.getMainLooper())
    private var memoryMonitoringRunnable: Runnable? = null
    private var isMonitoring = false

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        context = flutterPluginBinding.applicationContext
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "com.kft.fitness/performance")
        channel.setMethodCallHandler(this)
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "getMemoryUsage" -> {
                try {
                    val memoryUsage = getCurrentMemoryUsage()
                    result.success(memoryUsage)
                } catch (e: Exception) {
                    result.error("MEMORY_ERROR", "Failed to get memory usage: ${e.message}", null)
                }
            }
            "triggerGC" -> {
                try {
                    triggerGarbageCollection()
                    result.success(true)
                } catch (e: Exception) {
                    result.error("GC_ERROR", "Failed to trigger GC: ${e.message}", null)
                }
            }
            "startMemoryMonitoring" -> {
                try {
                    val intervalMs = call.argument<Int>("intervalMs") ?: 30000
                    startMemoryMonitoring(intervalMs)
                    result.success(true)
                } catch (e: Exception) {
                    result.error("MONITOR_ERROR", "Failed to start monitoring: ${e.message}", null)
                }
            }
            "stopMemoryMonitoring" -> {
                try {
                    stopMemoryMonitoring()
                    result.success(true)
                } catch (e: Exception) {
                    result.error("MONITOR_ERROR", "Failed to stop monitoring: ${e.message}", null)
                }
            }
            "getPerformanceMetrics" -> {
                try {
                    val metrics = getPerformanceMetrics()
                    result.success(metrics)
                } catch (e: Exception) {
                    result.error("METRICS_ERROR", "Failed to get metrics: ${e.message}", null)
                }
            }
            "optimizeMemory" -> {
                try {
                    optimizeMemory()
                    result.success(true)
                } catch (e: Exception) {
                    result.error("OPTIMIZE_ERROR", "Failed to optimize memory: ${e.message}", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    /**
     * Get current memory usage
     */
    private fun getCurrentMemoryUsage(): Map<String, Any> {
        val runtime = Runtime.getRuntime()
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        // App memory usage
        val totalMemory = runtime.totalMemory()
        val freeMemory = runtime.freeMemory()
        val usedMemory = totalMemory - freeMemory
        val maxMemory = runtime.maxMemory()

        // System memory
        val availableMemory = memoryInfo.availMem
        val totalSystemMemory = memoryInfo.totalMem
        val usedSystemMemory = totalSystemMemory - availableMemory

        // Native heap
        val nativeHeapSize = Debug.getNativeHeapSize()
        val nativeHeapAllocated = Debug.getNativeHeapAllocatedSize()
        val nativeHeapFree = Debug.getNativeHeapFreeSize()

        // Memory pressure indicators
        val memoryPressure = calculateMemoryPressure(usedMemory, maxMemory, memoryInfo.lowMemory)

        return mapOf(
            "currentMemory" to usedMemory,
            "maxMemory" to maxMemory,
            "freeMemory" to freeMemory,
            "totalMemory" to totalMemory,
            "availableSystemMemory" to availableMemory,
            "totalSystemMemory" to totalSystemMemory,
            "usedSystemMemory" to usedSystemMemory,
            "nativeHeapSize" to nativeHeapSize,
            "nativeHeapAllocated" to nativeHeapAllocated,
            "nativeHeapFree" to nativeHeapFree,
            "isLowMemory" to memoryInfo.lowMemory,
            "memoryPressure" to memoryPressure,
            "memoryUsagePercent" to ((usedMemory.toDouble() / maxMemory.toDouble()) * 100).toInt()
        )
    }

    /**
     * Calculate memory pressure level
     */
    private fun calculateMemoryPressure(usedMemory: Long, maxMemory: Long, isLowMemory: Boolean): String {
        val usagePercent = (usedMemory.toDouble() / maxMemory.toDouble()) * 100
        
        return when {
            isLowMemory || usagePercent > 90 -> "critical"
            usagePercent > 75 -> "high"
            usagePercent > 50 -> "medium"
            else -> "low"
        }
    }

    /**
     * Trigger garbage collection
     */
    private fun triggerGarbageCollection() {
        // Suggest garbage collection
        System.gc()
        
        // Give it a moment to complete
        Thread.sleep(100)
        
        // Run finalizers
        System.runFinalization()
    }

    /**
     * Start memory monitoring
     */
    private fun startMemoryMonitoring(intervalMs: Int) {
        if (isMonitoring) {
            stopMemoryMonitoring()
        }

        isMonitoring = true
        memoryMonitoringRunnable = object : Runnable {
            override fun run() {
                if (isMonitoring) {
                    try {
                        val memoryUsage = getCurrentMemoryUsage()
                        channel.invokeMethod("onMemoryUpdate", memoryUsage)
                        
                        // Check for memory pressure and auto-optimize if needed
                        val pressure = memoryUsage["memoryPressure"] as String
                        if (pressure == "critical" || pressure == "high") {
                            optimizeMemory()
                        }
                        
                    } catch (e: Exception) {
                        // Log error but continue monitoring
                        println("Memory monitoring error: ${e.message}")
                    }
                    
                    handler.postDelayed(this, intervalMs.toLong())
                }
            }
        }
        
        handler.post(memoryMonitoringRunnable!!)
    }

    /**
     * Stop memory monitoring
     */
    private fun stopMemoryMonitoring() {
        isMonitoring = false
        memoryMonitoringRunnable?.let { handler.removeCallbacks(it) }
        memoryMonitoringRunnable = null
    }

    /**
     * Get comprehensive performance metrics
     */
    private fun getPerformanceMetrics(): Map<String, Any> {
        val memoryUsage = getCurrentMemoryUsage()
        val runtime = Runtime.getRuntime()
        
        // CPU and thread info
        val availableProcessors = runtime.availableProcessors()
        val activeThreads = Thread.activeCount()
        
        // Timing info
        val currentTime = System.currentTimeMillis()
        val nanoTime = System.nanoTime()
        
        return mapOf(
            "memory" to memoryUsage,
            "cpu" to mapOf(
                "availableProcessors" to availableProcessors,
                "activeThreads" to activeThreads
            ),
            "timing" to mapOf(
                "currentTimeMs" to currentTime,
                "nanoTime" to nanoTime
            ),
            "vm" to mapOf(
                "name" to System.getProperty("java.vm.name"),
                "version" to System.getProperty("java.vm.version"),
                "vendor" to System.getProperty("java.vm.vendor")
            )
        )
    }

    /**
     * Optimize memory usage
     */
    private fun optimizeMemory() {
        // Trigger garbage collection
        triggerGarbageCollection()
        
        // Clear weak references
        clearWeakReferences()
        
        // Trim memory if possible
        trimMemory()
    }

    /**
     * Clear weak references
     */
    private fun clearWeakReferences() {
        try {
            // Create and immediately discard a weak reference to trigger cleanup
            val weakRef = WeakReference(Object())
            System.gc()
            System.runFinalization()
        } catch (e: Exception) {
            // Ignore errors in cleanup
        }
    }

    /**
     * Trim memory usage
     */
    private fun trimMemory() {
        try {
            // This would typically be called by the system, but we can suggest it
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            
            // Get current memory info to determine trim level
            val memoryInfo = ActivityManager.MemoryInfo()
            activityManager.getMemoryInfo(memoryInfo)
            
            if (memoryInfo.lowMemory) {
                // Suggest aggressive memory trimming
                System.gc()
                System.runFinalization()
            }
        } catch (e: Exception) {
            // Ignore errors in trimming
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        stopMemoryMonitoring()
        channel.setMethodCallHandler(null)
    }
}
