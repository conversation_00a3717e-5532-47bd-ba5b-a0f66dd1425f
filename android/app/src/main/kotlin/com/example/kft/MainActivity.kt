package com.example.kft

import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel
import android.content.pm.ActivityInfo
import android.view.WindowManager
import android.os.Build
import android.webkit.WebSettings

class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.example.kft/orientation"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register performance monitoring plugins
        flutterEngine.plugins.add(DeviceInfoPlugin())
        flutterEngine.plugins.add(PerformancePlugin())

        // Configure WebView settings for better video support
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            WebSettings.getDefaultUserAgent(this)
        }

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "setLandscape" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
                    // Enable fullscreen for landscape mode
                    window.addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
                    result.success(null)
                }
                "setPortrait" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_PORTRAIT
                    // Remove fullscreen flags for portrait mode
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    result.success(null)
                }
                "setAuto" -> {
                    requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_UNSPECIFIED
                    // Remove fullscreen flags
                    window.clearFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN)
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
