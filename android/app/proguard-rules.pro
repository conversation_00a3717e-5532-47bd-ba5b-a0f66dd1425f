# ===== NOTIFICATION SYSTEM PROTECTION =====
# Prevent ProGuard/R8 from minifying notification-related classes

# Flutter Local Notifications - keep generic type signatures
-keepattributes Signature
-keep class com.dexterous.** { *; }
-keep class com.dexterous.flutterlocalnotifications.** { *; }
-keep class com.google.gson.reflect.TypeToken { *; }

# AwesomeNotifications - comprehensive protection
-keep class me.carda.awesome_notifications.** { *; }
-keep class me.carda.awesome_notifications.core.** { *; }
-keep class me.carda.awesome_notifications.core.models.** { *; }
-keep class me.carda.awesome_notifications.core.services.** { *; }
-keep class me.carda.awesome_notifications.core.managers.** { *; }
-keep class me.carda.awesome_notifications.core.broadcasters.** { *; }
-keep class me.carda.awesome_notifications.core.listeners.** { *; }

# Android Notification System - core classes
-keep class android.app.Notification** { *; }
-keep class android.app.NotificationManager** { *; }
-keep class android.app.NotificationChannel** { *; }
-keep class android.app.PendingIntent** { *; }
-keep class android.content.Intent** { *; }
-keep class android.content.BroadcastReceiver** { *; }
-keep class android.os.Bundle** { *; }

# Firebase Cloud Messaging - notification support
-keep class com.google.firebase.messaging.** { *; }
-keep class com.google.firebase.iid.** { *; }

# Gson - for notification data serialization
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep all notification-related enums and their values
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep notification data models and their fields
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

# Prevent obfuscation of notification callback methods
-keepclassmembers class * {
    public void onReceive(android.content.Context, android.content.Intent);
    public void onNotificationReceived(android.content.Context, me.carda.awesome_notifications.core.models.ReceivedNotification);
    public void onActionReceived(android.content.Context, me.carda.awesome_notifications.core.models.ReceivedAction);
}

# Keep Flutter plugin registration methods
-keep class io.flutter.plugins.** { *; }
-keep class io.flutter.plugin.common.** { *; }
-keep class io.flutter.embedding.engine.plugins.** { *; }

# Additional safety for reflection-based operations
-keepattributes *Annotation*
-keepattributes InnerClasses
-keepattributes EnclosingMethod