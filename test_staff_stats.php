<?php
require_once 'includes/config.php';
require_once 'includes/database.php';
require_once 'includes/user_helpers.php';

// Initialize database
$db = new Database();
$conn = $db->getConnection();

// Test getStaffStatistics function
try {
    echo "Testing getStaffStatistics function...\n";
    $staffStats = getStaffStatistics($conn);
    echo "Success! Got " . count($staffStats) . " staff members.\n";
    
    // Print the first staff member's stats
    if (!empty($staffStats)) {
        echo "First staff member stats:\n";
        print_r($staffStats[0]);
    }
} catch (Exception $e) {
    echo "Error in getStaffStatistics: " . $e->getMessage() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

// Test getOverallStaffMetrics function
try {
    echo "\nTesting getOverallStaffMetrics function...\n";
    $overallMetrics = getOverallStaffMetrics($conn);
    echo "Success! Got overall staff metrics.\n";
    print_r($overallMetrics);
} catch (Exception $e) {
    echo "Error in getOverallStaffMetrics: " . $e->getMessage() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}
