import 'package:flutter/material.dart';
import 'dashboard_new.dart';

class DashboardDemo extends StatelessWidget {
  const DashboardDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard Demo'),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const DashboardPage()),
            );
          },
          child: const Text('Open New Dashboard'),
        ),
      ),
    );
  }
}
