import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../config/app_config.dart';
import '../services/api_service.dart';
import '../widgets/kft_app_bar.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = true;
  ThemeMode _currentThemeMode = ThemeMode.system;

  @override
  void initState() {
    super.initState();
    _loadCurrentTheme();
  }

  Future<void> _loadCurrentTheme() async {
    setState(() {
      _currentThemeMode = AppConfig.themeMode;
      _isLoading = false;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _saveThemeMode(ThemeMode mode) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Save to AppConfig and SharedPreferences
      await AppConfig.saveThemeMode(mode);

      setState(() {
        _currentThemeMode = mode;
        _isLoading = false;
      });

      // Force rebuild of the app to apply theme changes
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              mode == ThemeMode.system
                  ? 'Using system theme'
                  : mode == ThemeMode.dark
                      ? 'Dark mode enabled'
                      : 'Light mode enabled',
            ),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('Error saving theme mode: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error saving theme mode: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: KFTAppBar(
        title: 'Settings',
        showBackButton: true,
        showThemeToggle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Appearance Settings Section
                    Card(
                      margin: const EdgeInsets.only(bottom: 16.0),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.palette, color: theme.primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  'Appearance',
                                  style: theme.textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Theme Mode Selection
                            const Text('Theme Mode'),
                            const SizedBox(height: 8),

                            // System Theme Option
                            RadioListTile<ThemeMode>(
                              title: const Text('System Theme'),
                              subtitle: const Text('Follow system dark/light mode settings'),
                              value: ThemeMode.system,
                              groupValue: _currentThemeMode,
                              onChanged: (ThemeMode? value) {
                                if (value != null) {
                                  _saveThemeMode(value);
                                }
                              },
                              secondary: const Icon(Icons.brightness_auto),
                            ),

                            // Light Theme Option
                            RadioListTile<ThemeMode>(
                              title: const Text('Light Theme'),
                              subtitle: const Text('Always use light mode'),
                              value: ThemeMode.light,
                              groupValue: _currentThemeMode,
                              onChanged: (ThemeMode? value) {
                                if (value != null) {
                                  _saveThemeMode(value);
                                }
                              },
                              secondary: const Icon(Icons.brightness_high),
                            ),

                            // Dark Theme Option
                            RadioListTile<ThemeMode>(
                              title: const Text('Dark Theme'),
                              subtitle: const Text('Always use dark mode'),
                              value: ThemeMode.dark,
                              groupValue: _currentThemeMode,
                              onChanged: (ThemeMode? value) {
                                if (value != null) {
                                  _saveThemeMode(value);
                                }
                              },
                              secondary: const Icon(Icons.brightness_4),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Notifications Section
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.notifications_outlined, color: theme.primaryColor),
                                const SizedBox(width: 8),
                                Text(
                                  'Notifications',
                                  style: theme.textTheme.titleLarge,
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            ListTile(
                              leading: const Icon(Icons.notifications_active),
                              title: const Text('Notification Settings'),
                              subtitle: const Text('Manage workout reminders and notifications'),
                              trailing: const Icon(Icons.chevron_right),
                              onTap: () {
                                Navigator.pushNamed(context, '/notification-settings');
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
    );
  }
}
