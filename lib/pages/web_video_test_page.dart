import 'package:flutter/material.dart';
import '../models/course_video.dart';
import '../widgets/platform_video_player.dart';

/// Test page for web video player functionality
class WebVideoTestPage extends StatefulWidget {
  const WebVideoTestPage({Key? key}) : super(key: key);

  @override
  State<WebVideoTestPage> createState() => _WebVideoTestPageState();
}

class _WebVideoTestPageState extends State<WebVideoTestPage> {
  String _status = 'Ready';
  String _error = '';
  int _selectedVideoIndex = 0;

  // Test videos with different configurations
  final List<CourseVideo> _testVideos = [
    CourseVideo(
      id: 1,
      title: 'Test Video 1 (Known Working)',
      description: 'This is the known working Vimeo video',
      videoUrl: 'https://vimeo.com/**********',
      thumbnailUrl: null,
      durationMinutes: 5,
      weekNumber: 1,
      sequenceNumber: 1,
      isUnlocked: true,
      isCompleted: false,
      unlockDate: null,
      completionDate: null,
      watchDurationSeconds: 0,
      lastPositionSeconds: 0,
      videoProvider: 'vimeo',
      videoEmbedUrl: null,
      videoId: '**********',
    ),
    CourseVideo(
      id: 2,
      title: 'Test Video 2 (Public Vimeo)',
      description: 'This is a public Vimeo video for testing',
      videoUrl: 'https://vimeo.com/148751763',
      thumbnailUrl: null,
      durationMinutes: 3,
      weekNumber: 1,
      sequenceNumber: 2,
      isUnlocked: true,
      isCompleted: false,
      unlockDate: null,
      completionDate: null,
      watchDurationSeconds: 0,
      lastPositionSeconds: 0,
      videoProvider: 'vimeo',
      videoEmbedUrl: null,
      videoId: '148751763',
    ),
    CourseVideo(
      id: 3,
      title: 'Test Video 3 (Direct URL)',
      description: 'This is a direct video URL for testing',
      videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      thumbnailUrl: null,
      durationMinutes: 10,
      weekNumber: 1,
      sequenceNumber: 3,
      isUnlocked: true,
      isCompleted: false,
      unlockDate: null,
      completionDate: null,
      watchDurationSeconds: 0,
      lastPositionSeconds: 0,
      videoProvider: 'direct',
      videoEmbedUrl: null,
      videoId: null,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final currentVideo = _testVideos[_selectedVideoIndex];

    return Scaffold(
      appBar: AppBar(
        title: const Text('Web Video Player Test'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<int>(
            onSelected: (index) {
              setState(() {
                _selectedVideoIndex = index;
                _status = 'Switched to video ${index + 1}';
                _error = '';
              });
            },
            itemBuilder: (context) => _testVideos.asMap().entries.map((entry) {
              return PopupMenuItem(
                value: entry.key,
                child: Text('Video ${entry.key + 1}: ${entry.value.title}'),
              );
            }).toList(),
            child: const Padding(
              padding: EdgeInsets.all(16.0),
              child: Icon(Icons.more_vert),
            ),
          ),
        ],
      ),
      body: Container(
        color: Colors.black,
        child: Column(
          children: [
            // Video player
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    // Video info
                    Container(
                      padding: const EdgeInsets.all(8.0),
                      margin: const EdgeInsets.only(bottom: 16.0),
                      decoration: BoxDecoration(
                        color: Colors.grey[800],
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            currentVideo.title,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'URL: ${currentVideo.videoUrl}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            'Provider: ${currentVideo.videoProvider}',
                            style: const TextStyle(
                              color: Colors.white70,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Video player
                    Expanded(
                      child: PlatformVideoPlayer(
                        video: currentVideo,
                        autoPlay: true,
                        onReady: () {
                          setState(() {
                            _status = 'Player Ready';
                            _error = '';
                          });
                        },
                        onPlay: () {
                          setState(() {
                            _status = 'Playing';
                          });
                        },
                        onPause: () {
                          setState(() {
                            _status = 'Paused';
                          });
                        },
                        onCompleted: () {
                          setState(() {
                            _status = 'Completed';
                          });
                        },
                        onProgress: (position) {
                          setState(() {
                            _status = 'Playing at ${position}s';
                          });
                        },
                        onError: (error) {
                          setState(() {
                            _status = 'Error';
                            _error = error;
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Status display
            Container(
              padding: const EdgeInsets.all(16.0),
              color: Colors.grey[900],
              child: Column(
                children: [
                  Text(
                    'Status: $_status',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_error.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Error: $_error',
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                  const SizedBox(height: 16),
                  const Text(
                    'Instructions:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. The video should load automatically\n'
                    '2. Click the play button to start playback\n'
                    '3. Use the controls to pause, seek, and adjust volume\n'
                    '4. Check the status updates below the player\n'
                    '5. Use the menu to switch between test videos',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Troubleshooting:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• If video shows grey screen: Check browser autoplay settings\n'
                    '• If video shows "private" error: Domain verification issue\n'
                    '• If video doesn\'t load: Check browser console for errors\n'
                    '• Try different test videos to isolate the issue',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
} 