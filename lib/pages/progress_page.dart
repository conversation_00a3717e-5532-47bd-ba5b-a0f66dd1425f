import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import '../widgets/progress_card.dart';
import '../widgets/fitness_stats_widget.dart';
import '../services/user_service.dart';

class ProgressPage extends StatefulWidget {
  const ProgressPage({Key? key}) : super(key: key);

  @override
  State<ProgressPage> createState() => _ProgressPageState();
}

class _ProgressPageState extends State<ProgressPage> {
  UserProfile? _user;
  bool _loading = true;
  String? _error;
  bool _syncing = false;

  @override
  void initState() {
    super.initState();
    _loadCachedProfileAndSync();
  }

  Future<void> _loadCachedProfileAndSync() async {
    setState(() {
      _loading = true;
      _error = null;
      _syncing = false;
    });
    try {
      // 1. Load cached/local profile instantly
      final userService = UserService();
      final cachedProfile = await userService.getCachedProfile();
      if (cachedProfile != null) {
        setState(() {
          _user = cachedProfile;
          _loading = false;
        });
      }
      // 2. Fetch latest profile from API in background
      setState(() { _syncing = true; });
      try {
        final latestProfile = await userService.getUserProfile();
        if (latestProfile != null && mounted) {
          if (_user == null || !_profilesEqual(_user!, latestProfile)) {
            setState(() {
              _user = latestProfile;
            });
          }
        }
      } catch (e) {
        // Ignore API errors, keep showing cached/local data
      } finally {
        if (mounted) setState(() { _syncing = false; });
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to load user data';
        _loading = false;
        _syncing = false;
      });
    }
  }

  bool _profilesEqual(UserProfile a, UserProfile b) {
    return a.toJson().toString() == b.toJson().toString();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    if (_loading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Your Progress'), centerTitle: true),
        body: const Center(child: CircularProgressIndicator()),
      );
    }
    if (_error != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Your Progress'), centerTitle: true),
        body: Center(child: Text(_error!, style: theme.textTheme.bodyLarge)),
      );
    }
    final user = _user!;
    final today = DateTime.now();
    final todayWorkouts = user.workoutHistory.where((w) => _isSameDay(w.date, today)).toList();
    final todayMinutes = todayWorkouts.fold(0, (sum, w) => sum + w.durationMinutes);
    final longestStreak = _getLongestStreak(user.streakDays);
    final mostWorkoutsWeek = _getMostWorkoutsInAWeek(user.workoutHistory);
    final longestWorkout = _getLongestWorkout(user.workoutHistory);
    final latestBMI = user.bmiHistory.isNotEmpty ? user.bmiHistory.last : null;
    final quote = '"${user.name}, every day is a new chance to get stronger!"';

    return Scaffold(
      appBar: AppBar(
        title: const Text('Your Progress'),
        centerTitle: true,
        actions: [
          if (_syncing)
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: SizedBox(
                width: 18,
                height: 18,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadCachedProfileAndSync,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 28),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Motivational Quote
              Card(
                color: theme.colorScheme.primary.withOpacity(0.08),
                elevation: 0,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(Icons.format_quote, color: theme.colorScheme.primary, size: 32),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          quote,
                          style: theme.textTheme.bodyLarge?.copyWith(fontStyle: FontStyle.italic),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 18),
              // Daily Activity Summary
              Text('Daily Activity Summary', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Card(
                elevation: 1,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildStatCard(theme, 'Workouts', '${todayWorkouts.length}', Icons.fitness_center, color: Colors.blue),
                      _buildStatCard(theme, 'Minutes', '$todayMinutes', Icons.timer, color: Colors.green),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 18),
              // Workout Progress Tracker (weekly chart)
              Text('Workout Progress Tracker', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Card(
                elevation: 1,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
                child: Padding(
                  padding: const EdgeInsets.all(12.0),
                  child: WeeklyActivityChart(user: user),
                ),
              ),
              const SizedBox(height: 18),
              // Milestones & Achievements
              Row(
                children: [
                  Text('Milestones & Achievements', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                  const SizedBox(width: 8),
                  Expanded(child: Divider(thickness: 1, color: theme.dividerColor)),
                ],
              ),
              const SizedBox(height: 12),
              GridView.count(
                crossAxisCount: 3,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                mainAxisSpacing: 16,
                crossAxisSpacing: 8,
                childAspectRatio: 0.85,
                children: [
                  AchievementBadge(
                    label: 'First Workout',
                    icon: Icons.emoji_events,
                    achieved: (user.totalWorkouts ?? 0) > 0,
                    description: 'Complete your first workout',
                  ),
                  AchievementBadge(
                    label: '10 Days Streak',
                    icon: Icons.star,
                    achieved: (user.currentStreak ?? 0) >= 10,
                    description: 'Workout 10 days in a row',
                  ),
                  AchievementBadge(
                    label: '500 min',
                    icon: Icons.timer,
                    achieved: (user.totalWorkoutTime ?? 0) >= 500,
                    description: 'Accumulate 500 minutes of workouts',
                  ),
                  AchievementBadge(
                    label: 'Longest Streak',
                    icon: Icons.whatshot,
                    achieved: longestStreak >= 15,
                    description: 'Reach a 15-day workout streak',
                  ),
                  AchievementBadge(
                    label: 'Most Workouts/Week',
                    icon: Icons.calendar_today,
                    achieved: mostWorkoutsWeek >= 5,
                    description: 'Do 5+ workouts in a week',
                  ),
                  AchievementBadge(
                    label: 'Longest Workout',
                    icon: Icons.access_time,
                    achieved: longestWorkout >= 90,
                    description: 'Complete a 90+ minute workout',
                  ),
                ],
              ),
              const SizedBox(height: 18),
              // Fitness Stats Section (merged from profile page)
              Text('Fitness Stats', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              FitnessStatsWidget(
                userProfile: user,
                onUpdateWeight: () {
                  // Show weight update dialog
                  _showUpdateWeightDialog(context);
                },
              ),
              const SizedBox(height: 24),
              // Essential options
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildProgressAction(
                    theme,
                    'Share',
                    Icons.share,
                    () => _showShareProgressDialog(context),
                  ),
                  _buildProgressAction(theme, 'Set Goal', Icons.flag, () {/* TODO: Implement set goal */}),
                  _buildProgressAction(theme, 'History', Icons.history, () {/* TODO: Implement history */}),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showShareProgressDialog(BuildContext context) {
    final cardKey = GlobalKey();
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(8),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
          content: SizedBox(
            width: 370,
            child: RepaintBoundary(
              key: cardKey,
              child: ProgressCard(
                userProfile: _user!,
                achievements: _getAchievements(_user!),
                onShare: () async {
                  Navigator.of(context).pop();
                  await _shareProgressCard(cardKey);
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Future<void> _shareProgressCard(GlobalKey cardKey) async {
    try {
      final image = await exportProgressCardToImage(cardKey);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final pngBytes = byteData!.buffer.asUint8List();
      final tempDir = await getTemporaryDirectory();
      final file = await File('${tempDir.path}/progress_card.png').create();
      await file.writeAsBytes(pngBytes);
      await Share.shareXFiles([XFile(file.path)], text: 'Check out my fitness progress!');
    } catch (e) {
      debugPrint('Error sharing progress card: $e');
    }
  }

  List<String> _getAchievements(UserProfile user) {
    final List<String> achievements = [];
    if (user.totalWorkouts > 0) achievements.add('Completed first workout');
    if (user.currentStreak >= 10) achievements.add('10-day workout streak');
    if (user.totalWorkoutTime >= 500) achievements.add('500+ workout minutes');
    if (_getLongestStreak(user.streakDays) >= 15) achievements.add('15-day longest streak');
    if (_getMostWorkoutsInAWeek(user.workoutHistory) >= 5) achievements.add('5+ workouts in a week');
    if (_getLongestWorkout(user.workoutHistory) >= 90) achievements.add('90+ minute workout');
    return achievements;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  int _getLongestStreak(List<DateTime> streakDays) {
    if (streakDays.isEmpty) return 0;
    final sorted = List<DateTime>.from(streakDays)..sort();
    int longest = 1, current = 1;
    for (int i = 1; i < sorted.length; i++) {
      if (sorted[i].difference(sorted[i - 1]).inDays == 1) {
        current++;
        if (current > longest) longest = current;
      } else {
        current = 1;
      }
    }
    return longest;
  }

  int _getMostWorkoutsInAWeek(List workouts) {
    if (workouts.isEmpty) return 0;
    Map<String, int> weekCounts = {};
    for (var w in workouts) {
      final week = '${w.date.year}-W${((w.date.dayOfYear - w.date.weekday + 10) / 7).floor()}';
      weekCounts[week] = (weekCounts[week] ?? 0) + 1;
    }
    return weekCounts.values.isEmpty ? 0 : weekCounts.values.reduce((a, b) => a > b ? a : b);
  }

  int _getLongestWorkout(List workouts) {
    if (workouts.isEmpty) return 0;
    return workouts.map((w) => w.durationMinutes).reduce((a, b) => a > b ? a : b);
  }

  void _showUpdateWeightDialog(BuildContext context) {
    if (_user == null) return;

    final TextEditingController weightController = TextEditingController(text: _user!.weight.toString());
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Weight'),
        content: Form(
          key: formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: weightController,
                decoration: const InputDecoration(
                  labelText: 'Weight (kg)',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your weight';
                  }
                  final weight = double.tryParse(value);
                  if (weight == null || weight <= 0 || weight > 300) {
                    return 'Please enter a valid weight';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 8),
              Text(
                'Your BMI will be recalculated based on your new weight.',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (formKey.currentState!.validate()) {
                final weight = double.parse(weightController.text);

                // Here you would update the weight in your backend
                // For now, we'll just show a success message
                Navigator.of(context).pop();

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Weight updated successfully'),
                    behavior: SnackBarBehavior.floating,
                  ),
                );

                // In a real app, you would update the user profile and refresh the UI
                // For example:
                // final updatedUser = await userService.updateWeight(weight);
                // setState(() {
                //   user = updatedUser;
                // });
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(ThemeData theme, String label, String value, IconData icon, {Color? color}) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(14)),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color ?? theme.colorScheme.primary, size: 28),
            const SizedBox(height: 6),
            Text(value, style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
            Text(label, style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressAction(ThemeData theme, String label, IconData icon, VoidCallback onTap) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: CircleAvatar(
            backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
            child: Icon(icon, color: theme.colorScheme.primary),
          ),
        ),
        const SizedBox(height: 4),
        Text(label, style: theme.textTheme.bodySmall),
      ],
    );
  }
}

class WeeklyActivityChart extends StatelessWidget {
  final UserProfile? user;
  const WeeklyActivityChart({Key? key, this.user}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    // Prepare data for the last 7 days
    List<DateTime> days = List.generate(7, (i) => now.subtract(Duration(days: 6 - i)));
    List<int> workoutsPerDay = List.generate(7, (i) {
      return user?.workoutHistory.where((w) => _isSameDay(w.date, days[i])).length ?? 0;
    });

    return Column(
      children: [
        SizedBox(
          height: 180,
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: (workoutsPerDay.reduce((a, b) => a > b ? a : b).toDouble() + 2).clamp(4, 20),
              barTouchData: BarTouchData(enabled: true),
              titlesData: FlTitlesData(
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: true, reservedSize: 28),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      int idx = value.toInt();
                      if (idx < 0 || idx > 6) return const SizedBox.shrink();
                      return Text(DateFormat('E').format(days[idx]), style: theme.textTheme.bodySmall);
                    },
                    reservedSize: 24,
                  ),
                ),
                rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
              ),
              gridData: FlGridData(show: true, horizontalInterval: 1),
              borderData: FlBorderData(show: false),
              barGroups: List.generate(7, (i) {
                return BarChartGroupData(
                  x: i,
                  barRods: [
                    BarChartRodData(
                      toY: workoutsPerDay[i].toDouble(),
                      color: theme.colorScheme.primary,
                      width: 14,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                  showingTooltipIndicators: [0],
                );
              }),
            ),
          ),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(width: 16, height: 8, color: theme.colorScheme.primary, margin: const EdgeInsets.only(right: 6)),
            Text('Workouts per day', style: theme.textTheme.bodySmall),
          ],
        ),
      ],
    );
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
}

class AchievementBadge extends StatelessWidget {
  final String label;
  final IconData icon;
  final bool achieved;
  final String description;
  const AchievementBadge({
    Key? key,
    required this.label,
    required this.icon,
    required this.achieved,
    required this.description,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Tooltip(
      message: description,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 250),
        curve: Curves.easeOut,
        decoration: BoxDecoration(
          color: achieved ? theme.colorScheme.primary.withOpacity(0.12) : theme.dividerColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: achieved ? theme.colorScheme.primary : theme.dividerColor,
            width: achieved ? 2 : 1,
          ),
          boxShadow: achieved
              ? [
                  BoxShadow(
                    color: theme.colorScheme.primary.withOpacity(0.18),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [],
        ),
        padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 6),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Icon(
                  icon,
                  size: 36,
                  color: achieved
                      ? theme.colorScheme.primary
                      : theme.disabledColor,
                ),
                if (!achieved)
                  Positioned(
                    right: 0,
                    bottom: 0,
                    child: Icon(Icons.lock, size: 16, color: theme.disabledColor.withOpacity(0.7)),
                  ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: achieved ? theme.colorScheme.primary : theme.disabledColor,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}