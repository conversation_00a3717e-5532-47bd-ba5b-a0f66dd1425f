import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';
import 'package:provider/provider.dart';

import '../models/course.dart';
import '../models/purchasable_course.dart';
import '../models/course_category.dart';
import '../models/user_profile.dart';
import '../models/course_settings.dart';
import '../services/api_service.dart';
import '../providers/course_settings_provider.dart';
import '../widgets/course_videos_page.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/kft_button.dart';
import '../utils/animations.dart';
import '../widgets/theme_toggle_button.dart';

class CoursesPageNew extends StatefulWidget {
  const CoursesPageNew({Key? key}) : super(key: key);

  @override
  _CoursesPageNewState createState() => _CoursesPageNewState();
}

class _CoursesPageNewState extends State<CoursesPageNew> with SingleTickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  bool _isLoading = true;
  List<Course> _enrolledCourses = [];
  List<PurchasableCourse> _availableCourses = [];
  String _errorMessage = '';
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isScreenBlocked = false;

  // Selected category filter
  String? _selectedCategory;

  // List of course categories
  List<CourseCategory> _categories = [];

  // Map to organize courses by category
  Map<String, List<PurchasableCourse>> _coursesByCategory = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadAllCourses();
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadAllCourses() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      // Load both enrolled and available courses in parallel
      final futures = await Future.wait([
        _apiService.getUserCourses(),
        _apiService.getAvailableCourses(),
      ]);

      final enrolledCoursesData = futures[0] as Map<String, dynamic>;
      final availableCoursesData = futures[1] as Map<String, dynamic>;

      // Parse enrolled courses safely
      final List<Course> enrolledCourses = [];
      if (enrolledCoursesData['success'] == true && enrolledCoursesData['courses'] != null) {
        final coursesData = enrolledCoursesData['courses'] as List;
        for (var courseData in coursesData) {
          try {
            if (courseData is Map<String, dynamic>) {
              enrolledCourses.add(Course.fromJson(courseData));
            } else if (courseData is Map) {
              enrolledCourses.add(Course.fromJson(Map<String, dynamic>.from(courseData)));
            }
          } catch (e) {
            print('Error parsing enrolled course: $e');
            // Skip invalid course data instead of failing completely
          }
        }
      }

      // Get categories from the API response
      final List<String> apiCategories = [];
      if (availableCoursesData['categories'] != null) {
        final categoriesData = availableCoursesData['categories'] as List;
        for (var category in categoriesData) {
          if (category is String) {
            apiCategories.add(category);
          } else {
            apiCategories.add(category.toString());
          }
        }
      }

      // Create category objects
      final categories = apiCategories.map((name) => CourseCategory.fromString(name)).toList();

      // If no categories returned from API, use predefined ones
      if (categories.isEmpty) {
        categories.addAll(CourseCategory.getPredefinedCategories());
      }

      // Add "All Courses" category at the beginning
      categories.insert(0, CourseCategory(
        name: 'all',
        displayName: 'All Courses',
        description: 'View all available courses',
        iconName: 'view_list',
      ));

      // Parse available courses safely
      final List<PurchasableCourse> availableCourses = [];
      if (availableCoursesData['success'] == true && availableCoursesData['courses'] != null) {
        final coursesData = availableCoursesData['courses'] as List;
        for (var courseData in coursesData) {
          try {
            if (courseData is Map<String, dynamic>) {
              availableCourses.add(PurchasableCourse.fromJson(courseData));
            } else if (courseData is Map) {
              availableCourses.add(PurchasableCourse.fromJson(Map<String, dynamic>.from(courseData)));
            }
          } catch (e) {
            print('Error parsing available course: $e');
            // Skip invalid course data instead of failing completely
          }
        }
      }

      // Organize courses by category
      final coursesByCategory = <String, List<PurchasableCourse>>{};

      // Initialize categories with empty lists
      for (var category in categories) {
        coursesByCategory[category.name] = [];
      }

      // Add courses to their respective categories
      for (var course in availableCourses) {
        final categoryName = course.category.toLowerCase();

        // Add to specific category
        if (coursesByCategory.containsKey(categoryName)) {
          coursesByCategory[categoryName]!.add(course);
        } else {
          // If category doesn't exist, create it
          coursesByCategory[categoryName] = [course];

          // Also add a new category object if it doesn't exist
          if (!categories.any((c) => c.name.toLowerCase() == categoryName)) {
            categories.add(CourseCategory.fromString(categoryName));
          }
        }

        // Also add to "all" category
        coursesByCategory['all']!.add(course);
      }

      // Sort categories by name (keeping "all" at the top)
      categories.sort((a, b) {
        if (a.name == 'all') return -1;
        if (b.name == 'all') return 1;
        return a.displayName.compareTo(b.displayName);
      });

      setState(() {
        _enrolledCourses = enrolledCourses;
        _availableCourses = availableCourses;
        _categories = categories;
        _coursesByCategory = coursesByCategory;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });
    }
  }

  // Launch WhatsApp with course details
  Future<void> _launchWhatsAppWithCourse(PurchasableCourse course) async {
    final settingsProvider = Provider.of<CourseSettingsProvider>(context, listen: false);
    final settings = settingsProvider.settings;
    final apiService = ApiService();

    UserProfile? userProfile;
    try {
      final profileProvider = Provider.of<UserProfileProvider>(context, listen: false);
      userProfile = profileProvider.profile;
    } catch (e) {
      print('User profile not available: $e');
    }

    String deviceInfo = '';
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceInfo = '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceInfo = '${iosInfo.name} ${iosInfo.systemVersion}';
      }
    } catch (e) {
      print('Device info not available: $e');
    }

    // Determine WhatsApp number: course > staff > default
    String phone = '';
    if (settings.useCustomWhatsappNumbers && course.whatsappNumber.isNotEmpty) {
      phone = course.whatsappNumber;
      print('Using course-specific WhatsApp number: $phone');
    } else if (course.assignedStaffId != null && course.assignedStaffId! > 0) {
      // Try to fetch staff WhatsApp number
      final staffInfo = await apiService.getStaffInfo(course.assignedStaffId!);
      phone = staffInfo?['phone'] ?? '';
      if (phone.isNotEmpty) {
        print('Using assigned staff WhatsApp number: $phone');
      } else {
        phone = settings.defaultWhatsappNumber;
        print('Using default WhatsApp number: $phone');
      }
    } else {
      phone = settings.defaultWhatsappNumber;
      print('Using default WhatsApp number: $phone');
    }

    try {
      phone = phone.replaceAll(RegExp(r'[^\d+]'), '');
      if (!phone.startsWith('+')) {
        phone = '+$phone';
      }
      print('Cleaned phone number: $phone');

      String message;
      if (course.isPurchased) {
        message = 'Hi, I\'m already enrolled in the course: *${course.title}* (ID: ${course.id})\n'
            'I need assistance with this course.\n'
            'Course duration: ${course.durationWeeks} weeks\n'
            'Please help me with my query.';
      } else {
        final messagePrefix = course.whatsappMessagePrefix ?? settings.defaultMessagePrefix;
        message = '$messagePrefix *${course.title}* (ID: ${course.id})\n'
            'Price: ₹${course.price.final_.toStringAsFixed(2)}\n'
            'Duration: ${course.durationWeeks} weeks\n'
            'Please provide more information about enrollment.';
      }

      if (userProfile != null) {
        message += '\n\nMy Details:\n'
            'Name: ${userProfile.name}\n'
            'Phone: ${userProfile.phoneNumber}';
      }
      if (deviceInfo.isNotEmpty) {
        message += '\nDevice: $deviceInfo';
      }
      final formattedPhone = phone.replaceAll('+', '');
      print('Formatted phone number for WhatsApp: $formattedPhone');
      String whatsappUrl;
      whatsappUrl = 'https://wa.me/$formattedPhone?text=${Uri.encodeComponent(message)}';
      if (await canLaunchUrlString(whatsappUrl)) {
        await launchUrlString(whatsappUrl, mode: LaunchMode.externalApplication);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Could not open WhatsApp.')),
          );
        }
      }
    } catch (e) {
      print('Error launching WhatsApp: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: KFTDesignSystem.getBackgroundColor(context),
      appBar: AppBar(
        title: const Text('Courses', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // Theme Toggle Button
          const ThemeToggleButton(iconOnly: true),
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {
              showSearch(
                context: context,
                delegate: CourseSearchDelegate(
                  enrolledCourses: _enrolledCourses,
                  availableCourses: _availableCourses,
                  onEnrolledCourseSelected: (course) {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => CourseVideosPage(courseId: course.id),
                      ),
                    ).then((_) => _loadAllCourses());
                  },
                  onAvailableCourseSelected: _launchWhatsAppWithCourse,
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadAllCourses,
          ),
        ],
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(48),
          child: Container(
            alignment: Alignment.centerLeft,
            child: TabBar(
              controller: _tabController,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2.5,
                ),
                insets: const EdgeInsets.symmetric(horizontal: 24),
              ),
              labelColor: theme.colorScheme.primary,
              unselectedLabelColor: theme.colorScheme.onSurface.withOpacity(0.6),
              labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              unselectedLabelStyle: const TextStyle(fontWeight: FontWeight.normal, fontSize: 16),
              overlayColor: MaterialStateProperty.all(Colors.transparent),
              tabs: const [
                Tab(text: 'My Courses'),
                Tab(text: 'All Courses'),
              ],
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          _buildBody(),
          if (_isScreenBlocked)
            Positioned.fill(
              child: Container(
                color: Colors.black,
                child: const Center(
                  child: Text(
                    'Screen capture is blocked',
                    style: TextStyle(color: Colors.white, fontSize: 20),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_errorMessage.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text('Oops! $_errorMessage', style: const TextStyle(color: Colors.red, fontSize: 16)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAllCourses,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
    return TabBarView(
      controller: _tabController,
      children: [
        _buildEnrolledCoursesTab(),
        _buildAvailableCoursesTab(),
      ],
    );
  }

  Widget _buildEnrolledCoursesTab() {
    final filtered = _enrolledCourses.where((c) => c.title.toLowerCase().contains(_searchQuery.toLowerCase())).toList();
    if (filtered.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            const Text('No enrolled courses found', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            KFTButton(
              label: 'Browse All Courses',
              onPressed: () => _tabController.animateTo(1),
              type: KFTButtonType.primary,
            ),
          ],
        ),
      );
    }
    return RefreshIndicator(
      onRefresh: _loadAllCourses,
      child: ListView.separated(
        padding: const EdgeInsets.all(20),
        itemCount: filtered.length,
        separatorBuilder: (_, __) => const SizedBox(height: 18),
        itemBuilder: (context, index) {
          final course = filtered[index];
          return _buildEnrolledCourseCard(course);
        },
      ),
    );
  }

  Widget _buildAvailableCoursesTab() {
    if (_availableCourses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            const Text('No available courses found', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 16),
            KFTButton(
              label: 'Refresh',
              onPressed: _loadAllCourses,
              type: KFTButtonType.primary,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAllCourses,
      child: _buildAllCoursesGrid(),
    );
  }

  Widget _buildCategoryFilter() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = _selectedCategory == category.name;

          return Padding(
            padding: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (category.iconName != null) ...[
                    Icon(
                      _getCategoryIcon(category.iconName!),
                      size: 16,
                      color: isSelected ? Colors.white : Theme.of(context).colorScheme.primary,
                    ),
                    const SizedBox(width: 4),
                  ],
                  Text(category.displayName),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = selected ? category.name : null;
                });
              },
              backgroundColor: Theme.of(context).colorScheme.surface,
              selectedColor: Theme.of(context).colorScheme.primary,
              checkmarkColor: Colors.white,
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Theme.of(context).colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          );
        },
      ),
    );
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'fitness_center':
        return Icons.fitness_center;
      case 'directions_run':
        return Icons.directions_run;
      case 'sports_martial_arts':
        return Icons.sports_martial_arts;
      case 'star':
        return Icons.star;
      case 'restaurant':
        return Icons.restaurant;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'view_list':
        return Icons.view_list;
      default:
        return Icons.school;
    }
  }

  Widget _buildAllCoursesGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine the number of columns based on screen width
        final width = constraints.maxWidth;
        final crossAxisCount = width > 600 ? 3 : 2;

        // Adjust aspect ratio based on screen size
        final childAspectRatio = width > 600 ? 0.8 : 0.65;

        return GridView.builder(
          padding: const EdgeInsets.all(16),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
          ),
          itemCount: _availableCourses.length,
          itemBuilder: (context, index) {
            final course = _availableCourses[index];
            return _buildAvailableCourseCard(course);
          },
        );
      },
    );
  }

  Widget _buildCategoryCoursesGrid(String category) {
    final courses = _coursesByCategory[category] ?? [];

    if (courses.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.school_outlined, size: 64, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text('No courses found in this category', style: const TextStyle(fontSize: 16)),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: courses.length,
      itemBuilder: (context, index) {
        final course = courses[index];
        return _buildAvailableCourseCard(course);
      },
    );
  }

  Widget _buildEnrolledCourseCard(Course course) {
    final theme = Theme.of(context);

    return Card(
      elevation: 3,
      shadowColor: Colors.black.withOpacity(0.15),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => CourseVideosPage(courseId: course.id),
            ),
          ).then((_) => _loadAllCourses());
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course image
            Stack(
              children: [
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: course.imageUrl.isNotEmpty
                        ? Image.network(
                            course.imageUrl,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return Container(
                                color: theme.colorScheme.primary.withOpacity(0.1),
                                child: Center(
                                  child: Icon(
                                    Icons.fitness_center,
                                    color: theme.colorScheme.primary.withOpacity(0.5),
                                    size: 36,
                                  ),
                                ),
                              );
                            },
                          )
                        : Container(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            child: Center(
                              child: Icon(
                                Icons.fitness_center,
                                color: theme.colorScheme.primary.withOpacity(0.5),
                                size: 36,
                              ),
                            ),
                          ),
                  ),
                ),

                // Status badge
                Positioned(
                  top: 12,
                  right: 12,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Enrolled',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // Course info
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Course title
                  Text(
                    course.title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 12),

                  // Course details
                  Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${course.durationWeeks} weeks',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        Icons.video_library,
                        size: 16,
                        color: theme.colorScheme.onSurface.withOpacity(0.6),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${course.unlockedVideos}/${course.totalVideos} videos',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Course progress
                  if (course.progressPercentage > 0) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Progress',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          '${course.progressPercentage}%',
                          style: theme.textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    ClipRRect(
                      borderRadius: BorderRadius.circular(4),
                      child: LinearProgressIndicator(
                        value: course.progressPercentage / 100,
                        backgroundColor: theme.colorScheme.primary.withOpacity(0.1),
                        valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                        minHeight: 6,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Continue button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => CourseVideosPage(courseId: course.id),
                          ),
                        ).then((_) => _loadAllCourses());
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: theme.colorScheme.onPrimary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                      child: const Text('Continue'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailableCourseCard(PurchasableCourse course) {
    final theme = Theme.of(context);

    return Card(
      elevation: 3,
      shadowColor: Colors.black.withOpacity(0.15),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: () {
          if (course.isPurchased) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => CourseVideosPage(courseId: course.id),
              ),
            ).then((_) => _loadAllCourses());
          } else {
            _launchWhatsAppWithCourse(course);
          }
        },
        borderRadius: BorderRadius.circular(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Course image
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              child: AspectRatio(
                aspectRatio: 1.2,
                child: course.imageUrl.isNotEmpty
                    ? Image.network(
                        course.imageUrl,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: theme.colorScheme.primary.withOpacity(0.1),
                            child: Center(
                              child: Icon(
                                Icons.fitness_center,
                                color: theme.colorScheme.primary.withOpacity(0.5),
                                size: 36,
                              ),
                            ),
                          );
                        },
                      )
                    : Container(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        child: Center(
                          child: Icon(
                            Icons.fitness_center,
                            color: theme.colorScheme.primary.withOpacity(0.5),
                            size: 36,
                          ),
                        ),
                      ),
              ),
            ),

            // Course info
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Course title
                    Expanded(
                      child: Text(
                        course.title,
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),

                    // Price and duration row
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Duration
                        Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              size: 14,
                              color: theme.colorScheme.onSurface.withOpacity(0.6),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${course.durationWeeks} weeks',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                          ],
                        ),

                        // Price
                        if (!course.isPurchased)
                          Text(
                            '₹${course.price.final_.toStringAsFixed(0)}',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.secondary,
                            ),
                          ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Enroll button - Redesigned for a more professional and minimalistic look
                    SizedBox(
                      width: double.infinity,
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            if (course.isPurchased) {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => CourseVideosPage(courseId: course.id),
                                ),
                              ).then((_) => _loadAllCourses());
                            } else {
                              _launchWhatsAppWithCourse(course);
                            }
                          },
                          borderRadius: BorderRadius.circular(8),
                          child: Ink(
                            decoration: BoxDecoration(
                              gradient: course.isPurchased
                                ? LinearGradient(
                                    colors: [
                                      theme.colorScheme.primary,
                                      theme.colorScheme.primary.withOpacity(0.9),
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  )
                                : LinearGradient(
                                    colors: [
                                      const Color(0xFF25D366), // WhatsApp green
                                      const Color(0xFF128C7E), // Darker WhatsApp green
                                    ],
                                    begin: Alignment.topLeft,
                                    end: Alignment.bottomRight,
                                  ),
                              borderRadius: BorderRadius.circular(8),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.08),
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  // Icon with subtle styling
                                  Icon(
                                    course.isPurchased ? Icons.play_circle_outline : Icons.message_outlined,
                                    size: 16,
                                    color: Colors.white,
                                  ),
                                  const SizedBox(width: 8),
                                  // Text with refined typography
                                  Text(
                                    course.isPurchased ? 'View Course' : 'Enroll Now',
                                    style: const TextStyle(
                                      fontSize: 13,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.2,
                                      color: Colors.white,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Search delegate for courses
class CourseSearchDelegate extends SearchDelegate<dynamic> {
  final List<Course> enrolledCourses;
  final List<PurchasableCourse> availableCourses;
  final Function(Course) onEnrolledCourseSelected;
  final Function(PurchasableCourse) onAvailableCourseSelected;

  CourseSearchDelegate({
    required this.enrolledCourses,
    required this.availableCourses,
    required this.onEnrolledCourseSelected,
    required this.onAvailableCourseSelected,
  });

  @override
  List<Widget> buildActions(BuildContext context) {
    return [
      IconButton(
        icon: const Icon(Icons.clear),
        onPressed: () {
          query = '';
        },
      ),
    ];
  }

  @override
  Widget buildLeading(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.arrow_back),
      onPressed: () {
        close(context, null);
      },
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    return _buildSearchResults(context);
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return _buildSearchResults(context);
  }

  Widget _buildSearchResults(BuildContext context) {
    final theme = Theme.of(context);

    if (query.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 64, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              'Search for courses',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    final filteredEnrolled = enrolledCourses
        .where((course) => course.title.toLowerCase().contains(query.toLowerCase()))
        .toList();

    final filteredAvailable = availableCourses
        .where((course) => course.title.toLowerCase().contains(query.toLowerCase()))
        .toList();

    if (filteredEnrolled.isEmpty && filteredAvailable.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              'No courses found for "$query"',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        if (filteredEnrolled.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              'My Courses',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...filteredEnrolled.map((course) => _buildEnrolledCourseItem(context, course)),
          const SizedBox(height: 16),
        ],
        if (filteredAvailable.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: Text(
              'Available Courses',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ...filteredAvailable.map((course) => _buildAvailableCourseItem(context, course)),
        ],
      ],
    );
  }

  Widget _buildEnrolledCourseItem(BuildContext context, Course course) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: 56,
            height: 56,
            child: course.imageUrl.isNotEmpty
                ? Image.network(
                    course.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        child: Icon(
                          Icons.fitness_center,
                          color: theme.colorScheme.primary,
                        ),
                      );
                    },
                  )
                : Container(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    child: Icon(
                      Icons.fitness_center,
                      color: theme.colorScheme.primary,
                    ),
                  ),
          ),
        ),
        title: Text(
          course.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('${course.durationWeeks} weeks'),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          close(context, null);
          onEnrolledCourseSelected(course);
        },
      ),
    );
  }

  Widget _buildAvailableCourseItem(BuildContext context, PurchasableCourse course) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: SizedBox(
            width: 56,
            height: 56,
            child: course.imageUrl.isNotEmpty
                ? Image.network(
                    course.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: theme.colorScheme.primary.withOpacity(0.1),
                        child: Icon(
                          Icons.fitness_center,
                          color: theme.colorScheme.primary,
                        ),
                      );
                    },
                  )
                : Container(
                    color: theme.colorScheme.primary.withOpacity(0.1),
                    child: Icon(
                      Icons.fitness_center,
                      color: theme.colorScheme.primary,
                    ),
                  ),
          ),
        ),
        title: Text(
          course.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${course.durationWeeks} weeks'),
            if (!course.isPurchased)
              Text(
                '₹${course.price.final_.toStringAsFixed(0)}',
                style: TextStyle(
                  color: theme.colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              ),
          ],
        ),
        trailing: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () {
              close(context, null);
              onAvailableCourseSelected(course);
            },
            borderRadius: BorderRadius.circular(8),
            child: Ink(
              decoration: BoxDecoration(
                gradient: course.isPurchased
                  ? LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.primary.withOpacity(0.9),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : LinearGradient(
                      colors: [
                        const Color(0xFF25D366), // WhatsApp green
                        const Color(0xFF128C7E), // Darker WhatsApp green
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                child: Text(
                  course.isPurchased ? 'View' : 'Enroll',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                    letterSpacing: 0.2,
                  ),
                ),
              ),
            ),
          ),
        ),
        onTap: () {
          close(context, null);
          onAvailableCourseSelected(course);
        },
      ),
    );
  }
}