import 'package:flutter/material.dart';
import '../design_system/kft_design_system.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';
import '../models/workout.dart';
import '../services/api_service.dart';
import '../services/quote_service.dart';
import '../models/motivational_quote.dart';
import 'package:provider/provider.dart';
import '../widgets/profile_avatar_enhanced.dart';
import '../utils/responsive_utils.dart';

class DashboardPage extends StatefulWidget {
  const DashboardPage({Key? key}) : super(key: key);

  @override
  _DashboardPageState createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  final UserService _userService = UserService();
  final ApiService _apiService = ApiService();

  late Future<UserProfile> _profileFuture;
  late Future<List<Workout>> _workoutsFuture;

  bool _isMyWorkoutsSelected = true;
  bool _deepSeekEnabled = false;
  MotivationalQuote? _quote;
  bool _isLoadingQuote = true;

  @override
  void initState() {
    super.initState();
    _profileFuture = _userService.getUserProfile();
    _workoutsFuture = _apiService.getWorkouts();
    _loadDeepSeekSettingAndQuote();
  }

  Future<void> _loadDeepSeekSettingAndQuote() async {
    final enabled = await QuoteService().getDeepSeekEnabledForUser();
    MotivationalQuote? quote;
    if (enabled) {
      quote = await QuoteService().generateAiQuote();
    }
    if (!mounted) return;
    setState(() {
      _deepSeekEnabled = enabled;
      _quote = quote;
      _isLoadingQuote = false;
    });
  }

  Future<void> _toggleDeepSeek(bool value) async {
    setState(() => _deepSeekEnabled = value);
    await QuoteService().setDeepSeekEnabledForUser(value);
    await _loadDeepSeekSettingAndQuote();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          backgroundColor: isDarkMode ? const Color(0xFF121212) : const Color(0xFFF5F7FA),
          appBar: _buildResponsiveAppBar(context, theme, isDarkMode, deviceType),
          body: SingleChildScrollView(
            padding: ResponsiveUtils.getResponsivePadding(
              context,
              mobile: const EdgeInsets.all(16),
              largeMobile: const EdgeInsets.all(20),
              tablet: const EdgeInsets.all(24),
              desktop: const EdgeInsets.all(32),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                _buildWelcomeSection(context, theme, isDarkMode, deviceType),

                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 16,
                  tablet: 24,
                  desktop: 32,
                )),

                // DeepSeek AI Quote Section
                _buildQuoteToggleSection(context, theme, isDarkMode, deviceType),

                if (_deepSeekEnabled && _quote != null)
                  _buildMotivationalQuoteCard(context, theme, isDarkMode, deviceType),

                // Workout Summary Cards
                _buildResponsiveWorkoutSummaryCards(context, theme, isDarkMode, deviceType),

                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 20,
                  tablet: 24,
                  desktop: 32,
                )),

                // Tabs
                _buildResponsiveTabs(context, theme, isDarkMode, deviceType),

                const SizedBox(height: 16),

                // Filter Row
                _buildResponsiveFilterRow(context, theme, isDarkMode, deviceType),

                const SizedBox(height: 16),

                // Workout List
                _buildResponsiveWorkoutList(context, theme, isDarkMode, deviceType),
              ],
            ),
          ),
          drawer: ResponsiveUtils.isMobile(context) ? _buildDrawer(context, theme, isDarkMode) : null,
        );
      },
    );
  }

  PreferredSizeWidget _buildResponsiveAppBar(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return AppBar(
      backgroundColor: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
      elevation: 0,
      title: ResponsiveText(
        'My Dashboard',
        mobileFontSize: 18,
        tabletFontSize: 20,
        desktopFontSize: 22,
        fontWeight: FontWeight.w600,
        color: isDarkMode ? Colors.white : Colors.black,
      ),
      actions: [
        if (!ResponsiveUtils.isMobile(context)) ...[
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {},
          ),
          const SizedBox(width: 8),
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Consumer<UserProfileProvider>(
              builder: (context, profileProvider, _) {
                final userProfile = profileProvider.profile;
                return ProfileAvatarEnhanced(
                  userProfile: userProfile,
                  radius: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    mobile: 14,
                    tablet: 16,
                    desktop: 18,
                  ),
                  showDropdown: false,
                );
              },
            ),
          ),
        ] else ...[
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () {},
          ),
        ],
      ],
    );
  }

  Widget _buildWelcomeSection(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return ResponsiveContainer(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(20),
        desktop: const EdgeInsets.all(24),
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  'Good ${_getTimeOfDay()}, ${_user?.name ?? 'User'}!',
                  mobileFontSize: 20,
                  tabletFontSize: 24,
                  desktopFontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: isDarkMode ? Colors.white : Colors.black,
                ),
                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 8,
                  tablet: 12,
                  desktop: 16,
                )),
                ResponsiveText(
                  'Ready to crush your fitness goals today?',
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ],
            ),
          ),
          if (!ResponsiveUtils.isMobile(context))
            Container(
              width: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 50,
                tablet: 60,
                desktop: 70,
              ),
              height: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 50,
                tablet: 60,
                desktop: 70,
              ),
              decoration: BoxDecoration(
                color: KFTDesignSystem.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(35),
              ),
              child: Icon(
                Icons.fitness_center,
                color: KFTDesignSystem.primaryColor,
                size: ResponsiveUtils.getResponsiveValue<double>(
                  context,
                  mobile: 25,
                  tablet: 30,
                  desktop: 35,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildQuoteToggleSection(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        ResponsiveText(
          'Show DeepSeek AI Quote',
          mobileFontSize: 16,
          tabletFontSize: 18,
          desktopFontSize: 20,
          fontWeight: FontWeight.w500,
          color: isDarkMode ? Colors.white : Colors.black,
        ),
        Switch(
          value: _deepSeekEnabled,
          onChanged: (value) => _toggleDeepSeek(value),
        ),
      ],
    );
  }

  Widget _buildDrawer(BuildContext context, ThemeData theme, bool isDarkMode) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
            ),
            child: Text(
              'Menu',
              style: TextStyle(
                color: Colors.white,
                fontSize: 24,
              ),
            ),
          ),
          ListTile(
            leading: Icon(Icons.format_quote),
            title: Text('Quote Settings'),
            onTap: () {
              Navigator.pop(context); // Close the drawer
              showDialog(
                context: context,
                builder: (context) => AlertDialog(
                  title: Text('Quote Settings'),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Enable or disable DeepSeek AI quotes for everyone.'),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('Enable DeepSeek AI'),
                          Switch(
                            value: _deepSeekEnabled,
                            onChanged: (value) async {
                              Navigator.pop(context);
                              await _toggleDeepSeek(value);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('Close'),
                    ),
                  ],
                ),
              );
            },
          ),
          // ... other menu items ...
        ],
      ),
    );
  }

  String _getTimeOfDay() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'morning';
    } else if (hour < 17) {
      return 'afternoon';
    } else {
      return 'evening';
    }
  }

  Widget _buildResponsiveWorkoutSummaryCards(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return ResponsiveGrid(
      mobileColumns: 1,
      largeMobileColumns: 2,
      tabletColumns: 2,
      desktopColumns: 4,
      largeDesktopColumns: 4,
      spacing: ResponsiveUtils.getResponsiveSpacing(
        context,
        mobile: 12,
        tablet: 16,
        desktop: 20,
      ),
      runSpacing: ResponsiveUtils.getResponsiveSpacing(
        context,
        mobile: 12,
        tablet: 16,
        desktop: 20,
      ),
      childAspectRatio: ResponsiveUtils.getResponsiveAspectRatio(
        context,
        mobile: 1.2,
        largeMobile: 1.0,
        tablet: 0.9,
        desktop: 0.8,
      ),
      children: [
        _buildResponsiveSummaryCard(
          context,
          theme,
          isDarkMode,
          deviceType,
          title: 'Basic Workout',
          count: 25,
          total: 30,
          status: 'TO PLAN',
          iconBgColor: const Color(0xFFE3F2FD),
          iconColor: const Color(0xFF2196F3),
          icon: Icons.fitness_center,
        ),
        _buildResponsiveSummaryCard(
          context,
          theme,
          isDarkMode,
          deviceType,
          title: 'Study Workout',
          count: 16,
          total: 30,
          status: 'TO PLAN',
          iconBgColor: const Color(0xFFFFF8E1),
          iconColor: const Color(0xFFFFC107),
          icon: Icons.school,
        ),
        _buildResponsiveSummaryCard(
          context,
          theme,
          isDarkMode,
          deviceType,
          title: 'Parental Workout',
          count: 12,
          total: null,
          status: 'TAKEN',
          iconBgColor: const Color(0xFFF3E5F5),
          iconColor: const Color(0xFF9C27B0),
          icon: Icons.family_restroom,
        ),
        _buildResponsiveSummaryCard(
          context,
          theme,
          isDarkMode,
          deviceType,
          title: 'Military Service',
          count: 0,
          total: null,
          status: 'TAKEN',
          iconBgColor: const Color(0xFFE8F5E9),
          iconColor: const Color(0xFF4CAF50),
          icon: Icons.military_tech,
        ),
      ],
    );
  }

  Widget _buildMotivationalQuoteCard(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return Container(
      margin: EdgeInsets.only(
        bottom: ResponsiveUtils.getResponsiveSpacing(
          context,
          mobile: 20,
          tablet: 24,
          desktop: 32,
        ),
      ),
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(20),
        desktop: const EdgeInsets.all(24),
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            KFTDesignSystem.primaryColor.withOpacity(0.8),
            KFTDesignSystem.primaryColor,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveValue<double>(
            context,
            mobile: 12,
            tablet: 16,
            desktop: 20,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: KFTDesignSystem.primaryColor.withOpacity(0.3),
            blurRadius: ResponsiveUtils.getResponsiveValue<double>(
              context,
              mobile: 15,
              tablet: 20,
              desktop: 25,
            ),
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: _isLoadingQuote
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText(
                  'Motivational Quote',
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 8,
                  tablet: 12,
                  desktop: 16,
                )),
                ResponsiveText(
                  '"${_quote!.quote}"',
                  mobileFontSize: 16,
                  tabletFontSize: 18,
                  desktopFontSize: 20,
                  color: Colors.white,
                ),
                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 8,
                  tablet: 12,
                  desktop: 16,
                )),
                Align(
                  alignment: Alignment.centerRight,
                  child: ResponsiveText(
                    '- ${_quote!.displayAuthor}',
                    mobileFontSize: 12,
                    tabletFontSize: 14,
                    desktopFontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildResponsiveSummaryCard(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType, {
    required String title,
    required int count,
    required String status,
    int? total,
    required Color iconBgColor,
    required Color iconColor,
    required IconData icon,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveValue<double>(
            context,
            mobile: 12,
            tablet: 16,
            desktop: 20,
          ),
        ),
        boxShadow: [
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 8,
                tablet: 10,
                desktop: 12,
              ),
              offset: const Offset(0, 4),
            ),
        ],
      ),
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(18),
        desktop: const EdgeInsets.all(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: ResponsiveUtils.getResponsiveValue<double>(
                  context,
                  mobile: 36,
                  tablet: 40,
                  desktop: 44,
                ),
                height: ResponsiveUtils.getResponsiveValue<double>(
                  context,
                  mobile: 36,
                  tablet: 40,
                  desktop: 44,
                ),
                decoration: BoxDecoration(
                  color: iconBgColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    mobile: 18,
                    tablet: 20,
                    desktop: 22,
                  ),
                ),
              ),
              SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
                context,
                mobile: 10,
                tablet: 12,
                desktop: 14,
              )),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      title,
                      mobileFontSize: 12,
                      tabletFontSize: 14,
                      desktopFontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white : Colors.black87,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                      context,
                      mobile: 4,
                      tablet: 6,
                      desktop: 8,
                    )),
                    Row(
                      children: [
                        ResponsiveText(
                          count.toString(),
                          mobileFontSize: 18,
                          tabletFontSize: 20,
                          desktopFontSize: 22,
                          fontWeight: FontWeight.bold,
                          color: isDarkMode ? Colors.white : Colors.black,
                        ),
                        if (total != null)
                          ResponsiveText(
                            '/$total',
                            mobileFontSize: 12,
                            tabletFontSize: 14,
                            desktopFontSize: 16,
                            fontWeight: FontWeight.w400,
                            color: isDarkMode ? Colors.white60 : Colors.black45,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Spacer(),
          Center(
            child: ResponsiveText(
              status,
              mobileFontSize: 10,
              tabletFontSize: 12,
              desktopFontSize: 14,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? Colors.white60 : Colors.black45,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveTabs(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return ResponsiveWrap(
      spacing: ResponsiveUtils.getResponsiveSpacing(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      children: [
        _buildResponsiveTab(
          context,
          'My workouts',
          isSelected: _isMyWorkoutsSelected,
          onTap: () {
            setState(() {
              _isMyWorkoutsSelected = true;
            });
          },
          deviceType: deviceType,
        ),
        _buildResponsiveTab(
          context,
          'Applications (1)',
          isSelected: !_isMyWorkoutsSelected,
          onTap: () {
            setState(() {
              _isMyWorkoutsSelected = false;
            });
          },
          deviceType: deviceType,
        ),
      ],
    );
  }

  Widget _buildResponsiveTab(
    BuildContext context,
    String text, {
    required bool isSelected,
    required VoidCallback onTap,
    required DeviceType deviceType,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          ResponsiveText(
            text,
            mobileFontSize: 14,
            tabletFontSize: 16,
            desktopFontSize: 18,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
            color: isSelected ? Colors.black87 : Colors.black45,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
            context,
            mobile: 6,
            tablet: 8,
            desktop: 10,
          )),
          if (isSelected)
            Container(
              height: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 2,
                tablet: 3,
                desktop: 4,
              ),
              width: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 30,
                tablet: 40,
                desktop: 50,
              ),
              decoration: BoxDecoration(
                color: KFTDesignSystem.primaryColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildResponsiveFilterRow(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    if (ResponsiveUtils.isMobile(context)) {
      // Stack filters vertically on mobile
      return Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          _buildFilterDropdown(
            context,
            'Most Recent',
            ['Most Recent', 'Oldest', 'Alphabetical'],
            deviceType,
          ),
          SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
            context,
            mobile: 12,
          )),
          _buildFilterDropdown(
            context,
            '2022',
            ['2023', '2022', '2021'],
            deviceType,
          ),
        ],
      );
    } else {
      // Keep filters in a row for larger screens
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          _buildFilterDropdown(
            context,
            'Most Recent',
            ['Most Recent', 'Oldest', 'Alphabetical'],
            deviceType,
          ),
          SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
            context,
            tablet: 16,
            desktop: 20,
          )),
          _buildFilterDropdown(
            context,
            '2022',
            ['2023', '2022', '2021'],
            deviceType,
          ),
        ],
      );
    }
  }

  Widget _buildFilterDropdown(
    BuildContext context,
    String value,
    List<String> items,
    DeviceType deviceType,
  ) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        tablet: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        desktop: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      ),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<String>(
        value: value,
        underline: const SizedBox(),
        icon: Icon(
          Icons.keyboard_arrow_down,
          size: ResponsiveUtils.getResponsiveValue<double>(
            context,
            mobile: 16,
            tablet: 18,
            desktop: 20,
          ),
        ),
        items: items.map((String item) {
          return DropdownMenuItem<String>(
            value: item,
            child: ResponsiveText(
              item,
              mobileFontSize: 14,
              tabletFontSize: 16,
              desktopFontSize: 18,
            ),
          );
        }).toList(),
        onChanged: (String? newValue) {},
      ),
    );
  }

  Widget _buildResponsiveWorkoutList(
    BuildContext context,
    ThemeData theme,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return FutureBuilder<List<Workout>>(
      future: _workoutsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: ResponsiveText(
              'Error loading workouts: ${snapshot.error}',
              mobileFontSize: 14,
              tabletFontSize: 16,
              desktopFontSize: 18,
              color: theme.colorScheme.error,
            ),
          );
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: ResponsiveText(
              'No workouts found',
              mobileFontSize: 16,
              tabletFontSize: 18,
              desktopFontSize: 20,
            ),
          );
        }

        // Use dummy data for demonstration
        final workouts = [
          {
            'type': 'Basic workout',
            'iconBg': const Color(0xFFE3F2FD),
            'iconColor': const Color(0xFF2196F3),
            'icon': Icons.fitness_center,
            'dateRange': '27.08.2022 - 15.09.2022',
            'days': 16,
            'status': 'Approved',
            'statusColor': Colors.green,
          },
          {
            'type': 'Basic workout',
            'iconBg': const Color(0xFFE3F2FD),
            'iconColor': const Color(0xFF2196F3),
            'icon': Icons.fitness_center,
            'dateRange': '10.07.2022 - 12.07.2022',
            'days': 2,
            'status': 'Pending',
            'statusColor': Colors.amber,
          },
          {
            'type': 'Study workout',
            'iconBg': const Color(0xFFFFF8E1),
            'iconColor': const Color(0xFFFFC107),
            'icon': Icons.school,
            'dateRange': '25.06.2022 - 28.06.2022',
            'days': 4,
            'status': 'Approved',
            'statusColor': Colors.green,
          },
          {
            'type': 'Study workout',
            'iconBg': const Color(0xFFFFF8E1),
            'iconColor': const Color(0xFFFFC107),
            'icon': Icons.school,
            'dateRange': '02.03.2022 - 07.03.2022',
            'days': 5,
            'status': 'Rejected',
            'statusColor': Colors.red,
          },
          {
            'type': 'Parental workout',
            'iconBg': const Color(0xFFF3E5F5),
            'iconColor': const Color(0xFF9C27B0),
            'icon': Icons.family_restroom,
            'dateRange': '14.02.2022 - 24.02.2022',
            'days': 13,
            'status': 'Approved',
            'statusColor': Colors.green,
          },
        ];

        if (ResponsiveUtils.isMobile(context)) {
          // Card layout for mobile
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: workouts.length,
            separatorBuilder: (context, index) => SizedBox(
              height: ResponsiveUtils.getResponsiveSpacing(context, mobile: 12),
            ),
            itemBuilder: (context, index) {
              final workout = workouts[index];
              return _buildMobileWorkoutCard(context, workout, isDarkMode, deviceType);
            },
          );
        } else {
          // Table layout for larger screens
          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: workouts.length,
            separatorBuilder: (context, index) => Divider(
              color: isDarkMode ? Colors.white12 : Colors.black12,
              height: 1,
            ),
            itemBuilder: (context, index) {
              final workout = workouts[index];
              return _buildDesktopWorkoutRow(context, workout, isDarkMode, deviceType);
            },
          );
        }
      },
    );
  }

  Widget _buildMobileWorkoutCard(
    BuildContext context,
    Map<String, dynamic> workout,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return Container(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        largeMobile: const EdgeInsets.all(18),
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: workout['iconBg'] as Color,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  workout['icon'] as IconData,
                  color: workout['iconColor'] as Color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      workout['type'] as String,
                      mobileFontSize: 16,
                      largeMobileFontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDarkMode ? Colors.white : Colors.black87,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 14,
                          color: isDarkMode ? Colors.white60 : Colors.black45,
                        ),
                        const SizedBox(width: 4),
                        ResponsiveText(
                          '${workout['days']} Days',
                          mobileFontSize: 12,
                          largeMobileFontSize: 14,
                          color: isDarkMode ? Colors.white70 : Colors.black54,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (workout['statusColor'] as Color).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ResponsiveText(
                  workout['status'] as String,
                  mobileFontSize: 10,
                  largeMobileFontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: workout['statusColor'] as Color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 14,
                color: isDarkMode ? Colors.white60 : Colors.black45,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ResponsiveText(
                  workout['dateRange'] as String,
                  mobileFontSize: 12,
                  largeMobileFontSize: 14,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ),
              Icon(
                workout['status'] == 'Approved'
                    ? Icons.check_circle
                    : (workout['status'] == 'Pending'
                        ? Icons.access_time
                        : Icons.cancel),
                color: workout['statusColor'] as Color,
                size: 18,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopWorkoutRow(
    BuildContext context,
    Map<String, dynamic> workout,
    bool isDarkMode,
    DeviceType deviceType,
  ) {
    return Padding(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        tablet: const EdgeInsets.symmetric(vertical: 12),
        desktop: const EdgeInsets.symmetric(vertical: 16),
      ),
      child: Row(
        children: [
          // Icon
          Container(
            width: ResponsiveUtils.getResponsiveValue<double>(
              context,
              tablet: 36,
              desktop: 40,
            ),
            height: ResponsiveUtils.getResponsiveValue<double>(
              context,
              tablet: 36,
              desktop: 40,
            ),
            decoration: BoxDecoration(
              color: workout['iconBg'] as Color,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              workout['icon'] as IconData,
              color: workout['iconColor'] as Color,
              size: ResponsiveUtils.getResponsiveValue<double>(
                context,
                tablet: 18,
                desktop: 20,
              ),
            ),
          ),
          SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
            context,
            tablet: 16,
            desktop: 20,
          )),

          // Type
          SizedBox(
            width: ResponsiveUtils.getResponsiveValue<double>(
              context,
              tablet: 120,
              desktop: 140,
            ),
            child: ResponsiveText(
              workout['type'] as String,
              tabletFontSize: 14,
              desktopFontSize: 16,
              fontWeight: FontWeight.w500,
              color: isDarkMode ? Colors.white : Colors.black87,
            ),
          ),

          // Date Range
          Expanded(
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    tablet: 14,
                    desktop: 16,
                  ),
                  color: isDarkMode ? Colors.white60 : Colors.black45,
                ),
                SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  tablet: 8,
                  desktop: 10,
                )),
                ResponsiveText(
                  workout['dateRange'] as String,
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ],
            ),
          ),

          // Days
          SizedBox(
            width: ResponsiveUtils.getResponsiveValue<double>(
              context,
              tablet: 100,
              desktop: 120,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.access_time,
                  size: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    tablet: 14,
                    desktop: 16,
                  ),
                  color: isDarkMode ? Colors.white60 : Colors.black45,
                ),
                SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  tablet: 8,
                  desktop: 10,
                )),
                ResponsiveText(
                  '${workout['days']} Days',
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                  color: isDarkMode ? Colors.white70 : Colors.black54,
                ),
              ],
            ),
          ),

          // Status
          Container(
            padding: ResponsiveUtils.getResponsivePadding(
              context,
              tablet: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              desktop: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
            decoration: BoxDecoration(
              color: (workout['statusColor'] as Color).withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: ResponsiveText(
              workout['status'] as String,
              tabletFontSize: 12,
              desktopFontSize: 14,
              fontWeight: FontWeight.w500,
              color: workout['statusColor'] as Color,
            ),
          ),

          // Status Icon
          SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
            context,
            tablet: 16,
            desktop: 20,
          )),
          Icon(
            workout['status'] == 'Approved'
                ? Icons.check_circle
                : (workout['status'] == 'Pending'
                    ? Icons.access_time
                    : Icons.cancel),
            color: workout['statusColor'] as Color,
            size: ResponsiveUtils.getResponsiveValue<double>(
              context,
              tablet: 20,
              desktop: 24,
            ),
          ),
        ],
      ),
    );
  }
}
