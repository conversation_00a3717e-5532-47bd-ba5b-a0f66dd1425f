import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../config/network_config.dart';

class DebugNetworkPage extends StatefulWidget {
  const DebugNetworkPage({super.key});

  @override
  State<DebugNetworkPage> createState() => _DebugNetworkPageState();
}

class _DebugNetworkPageState extends State<DebugNetworkPage> {
  Map<String, dynamic> networkStatus = {};
  bool isTestingConnectivity = false;
  Map<String, bool> endpointStatus = {};

  @override
  void initState() {
    super.initState();
    _loadNetworkStatus();
  }

  void _loadNetworkStatus() {
    setState(() {
      networkStatus = ApiService.getNetworkStatus();
    });
  }

  Future<void> _testAllEndpoints() async {
    if (!kDebugMode) return;

    setState(() {
      isTestingConnectivity = true;
      endpointStatus.clear();
    });

    for (String endpoint in NetworkConfig.developmentEndpoints) {
      final isWorking = await NetworkConfig.testEndpointConnectivity(endpoint);
      setState(() {
        endpointStatus[endpoint] = isWorking;
      });
    }

    setState(() {
      isTestingConnectivity = false;
    });
  }

  void _switchEndpoint() {
    if (!kDebugMode) return;

    ApiService.switchEndpoint();
    _loadNetworkStatus();
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Switched to: ${networkStatus['current_endpoint']}'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  Future<void> _testCurrentEndpoint() async {
    final currentEndpoint = networkStatus['current_endpoint'] as String;
    
    setState(() {
      isTestingConnectivity = true;
    });

    final isWorking = await NetworkConfig.testEndpointConnectivity(currentEndpoint);
    
    setState(() {
      isTestingConnectivity = false;
      endpointStatus[currentEndpoint] = isWorking;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(isWorking ? 'Endpoint is working!' : 'Endpoint failed to respond'),
        backgroundColor: isWorking ? Colors.green : Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!kDebugMode) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Debug Network'),
        ),
        body: const Center(
          child: Text(
            'Debug features are only available in debug mode',
            style: TextStyle(fontSize: 16),
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Debug Network Settings'),
        backgroundColor: Colors.orange,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Current Status Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Current Network Status',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text('Endpoint: ${networkStatus['current_endpoint'] ?? 'Unknown'}'),
                    Text('Debug Mode: ${networkStatus['is_debug_mode'] ?? false}'),
                    Text('Available Endpoints: ${(networkStatus['available_endpoints'] as List?)?.length ?? 0}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _switchEndpoint,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
                    child: const Text('Switch Endpoint'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: isTestingConnectivity ? null : _testCurrentEndpoint,
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                    child: isTestingConnectivity 
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Test Current'),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 8),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isTestingConnectivity ? null : _testAllEndpoints,
                style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                child: isTestingConnectivity 
                    ? const Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                          SizedBox(width: 8),
                          Text('Testing All Endpoints...'),
                        ],
                      )
                    : const Text('Test All Endpoints'),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Endpoints List
            const Text(
              'Available Endpoints',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            
            Expanded(
              child: ListView.builder(
                itemCount: NetworkConfig.developmentEndpoints.length,
                itemBuilder: (context, index) {
                  final endpoint = NetworkConfig.developmentEndpoints[index];
                  final isCurrent = endpoint == networkStatus['current_endpoint'];
                  final status = endpointStatus[endpoint];
                  
                  return Card(
                    color: isCurrent ? Colors.blue.shade50 : null,
                    child: ListTile(
                      title: Text(
                        endpoint,
                        style: TextStyle(
                          fontWeight: isCurrent ? FontWeight.bold : FontWeight.normal,
                          fontSize: 12,
                        ),
                      ),
                      subtitle: isCurrent ? const Text('Current Endpoint') : null,
                      trailing: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (status != null)
                            Icon(
                              status ? Icons.check_circle : Icons.error,
                              color: status ? Colors.green : Colors.red,
                            ),
                          if (isCurrent)
                            const Icon(Icons.star, color: Colors.blue),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ),
            
            // Network Error Help
            const SizedBox(height: 16),
            Card(
              color: Colors.yellow.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Common Issues & Solutions',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    const Text('• Connection Refused: Check if server is running'),
                    const Text('• SSL Errors: App bypasses SSL in debug mode'),
                    const Text('• MIUI Devices: Disable battery optimization'),
                    const Text('• Network Issues: Try different endpoints'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
