import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import '../models/user_profile.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../widgets/progress_card_new.dart';
import '../design_system/kft_design_system.dart';
import '../services/daily_streak_service.dart';
import '../services/progress_service.dart';
import '../services/user_service.dart';
import '../services/video_streak_service.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;

class ProgressPageNew extends StatefulWidget {
  final UserProfile? user;

  const ProgressPageNew({Key? key, this.user}) : super(key: key);

  @override
  State<ProgressPageNew> createState() => _ProgressPageNewState();
}

class _ProgressPageNewState extends State<ProgressPageNew> {
  bool _isInitialLoading = true;
  int _totalVideosWatched = 0;
  int _totalVideoMinutes = 0;
  List<Map<String, dynamic>> _weeklyVideoStats = [];
  double? _bmi;
  String _currentQuote = '';
  final List<String> _motivationalQuotes = [
    "Every workout is a step closer to your goals.",
    "Your body can stand almost anything. It's your mind you have to convince.",
    "The only bad workout is the one that didn't happen.",
    "Success starts with self-discipline.",
    "Your health is an investment, not an expense.",
    "Make yourself proud.",
    "Small progress is still progress.",
    "Your future self will thank you.",
  ];

  @override
  void initState() {
    super.initState();
    _loadData();
    _setupPeriodicRefresh();
    // Set initial quote only once
    _currentQuote = _getMotivationalQuote();
  }

  Future<void> _loadData() async {
    if (!mounted) return;
    setState(() => _isInitialLoading = true);

    try {
      final progressService = Provider.of<ProgressService>(context, listen: false);
      final userService = Provider.of<UserService>(context, listen: false);
      
      // Load other data
      final stats = progressService.overallStats;
      _totalVideosWatched = stats.totalVideosWatched ?? 0;
      _totalVideoMinutes = (stats.totalWatchTimeSeconds ?? 0) ~/ 60;
      
      _weeklyVideoStats = await progressService.getWeeklyVideoStats();
      final profile = await userService.getUserProfile();
      _bmi = profile.currentBMI;
    } catch (e) {
      print('Error loading progress data: $e');
    } finally {
      if (mounted) {
        setState(() => _isInitialLoading = false);
      }
    }
  }

  void _setupPeriodicRefresh() {
    Future.delayed(const Duration(minutes: 5), () {
      if (mounted) {
        _loadData();
        _setupPeriodicRefresh();
      }
    });
  }

  String _getMotivationalQuote() {
    final random = math.Random();
    return _motivationalQuotes[random.nextInt(_motivationalQuotes.length)];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    if (_isInitialLoading) {
      return Scaffold(
        body: Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: colorScheme.background,
      body: RefreshIndicator(
        onRefresh: _loadData,
        child: CustomScrollView(
          slivers: [
            SliverAppBar(
              expandedHeight: 200,
              floating: false,
              pinned: true,
              backgroundColor: colorScheme.primary,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  'Your Progress',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        colorScheme.primary,
                        colorScheme.primary.withOpacity(0.8),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMotivationalQuoteCard(theme, colorScheme),
                    const SizedBox(height: 24),
                    _buildWeeklyActivitySection(theme, colorScheme),
                    const SizedBox(height: 24),
                    _buildDetailedStatsSection(theme, colorScheme),
                    const SizedBox(height: 24),
                    if (_bmi != null) _buildBMISection(theme, colorScheme),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMotivationalQuoteCard(ThemeData theme, ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.format_quote,
                  color: colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Daily Motivation',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _currentQuote,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.8),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeeklyActivitySection(ThemeData theme, ColorScheme colorScheme) {
    final hasWeeklyStats = _weeklyVideoStats.isNotEmpty;
    final totalWeeklyVideos = _weeklyVideoStats.fold<int>(0, (sum, day) => sum + (day['count'] as int? ?? 0));
    final totalWeeklyMinutes = _weeklyVideoStats.fold<int>(0, (sum, day) => sum + (day['minutes'] as int? ?? 0));
    final mostActiveDay = _getMostActiveDay();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Week\'s Activity',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (!hasWeeklyStats)
          _buildNoActivityCard(theme, colorScheme)
        else
          Card(
            elevation: 0,
            color: colorScheme.surface,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(
                color: colorScheme.outlineVariant,
                width: 1,
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  _buildStatRow(
                    theme,
                    'Videos Watched',
                    totalWeeklyVideos.toString(),
                    Icons.play_circle_outline,
                    colorScheme,
                  ),
                  const Divider(height: 24),
                  _buildStatRow(
                    theme,
                    'Watch Time',
                    '${totalWeeklyMinutes} min',
                    Icons.timer_outlined,
                    colorScheme,
                  ),
                  const Divider(height: 24),
                  _buildStatRow(
                    theme,
                    'Most Active Day',
                    mostActiveDay,
                    Icons.calendar_today_outlined,
                    colorScheme,
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  String _getMostActiveDay() {
    if (_weeklyVideoStats.isEmpty) return 'N/A';
    
    var maxVideos = 0;
    var mostActiveDate = DateTime.now();
    
    for (var day in _weeklyVideoStats) {
      final count = day['count'] as int? ?? 0;
      if (count > maxVideos) {
        maxVideos = count;
        mostActiveDate = day['date'] as DateTime;
      }
    }
    
    return DateFormat('EEEE').format(mostActiveDate);
  }

  Widget _buildDetailedStatsSection(ThemeData theme, ColorScheme colorScheme) {
    final avgSessionMinutes = _totalVideosWatched > 0 
        ? (_totalVideoMinutes / _totalVideosWatched).round() 
        : 0;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Detailed Stats',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 0,
          color: colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: colorScheme.outlineVariant,
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildStatRow(
                  theme,
                  'Total Videos',
                  _totalVideosWatched.toString(),
                  Icons.video_library_outlined,
                  colorScheme,
                ),
                const Divider(height: 24),
                _buildStatRow(
                  theme,
                  'Total Watch Time',
                  '${_totalVideoMinutes} min',
                  Icons.timer_outlined,
                  colorScheme,
                ),
                const Divider(height: 24),
                _buildStatRow(
                  theme,
                  'Average Session',
                  '$avgSessionMinutes min',
                  Icons.analytics_outlined,
                  colorScheme,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNoActivityCard(ThemeData theme, ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      color: colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: colorScheme.outlineVariant,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.fitness_center,
              size: 48,
              color: colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No Activity Yet',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Start your fitness journey today!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(ThemeData theme, String label, String value, IconData icon, ColorScheme colorScheme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: colorScheme.primary,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBMISection(ThemeData theme, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your BMI',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Card(
          elevation: 0,
          color: colorScheme.surface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(
              color: colorScheme.outlineVariant,
              width: 1,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.monitor_weight_outlined,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current BMI',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _bmi?.toStringAsFixed(1) ?? 'N/A',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      color: isDarkMode ? KFTDesignSystem.darkSurfaceColor : Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}

class _SectionCard extends StatelessWidget {
  final String icon;
  final String title;
  final String subtitle;
  final Color color;
  final List<_StatInfo> stats;
  final Color chipColor;

  const _SectionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.stats,
    required this.chipColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 18),
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 18),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(22),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(icon, style: const TextStyle(fontSize: 28)),
              const SizedBox(width: 10),
              Text(
                title,
                style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              const SizedBox(width: 8),
              Text(
                subtitle,
                style: TextStyle(fontSize: 14, color: Colors.grey[700]),
              ),
            ],
          ),
          const SizedBox(height: 14),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: stats.map((stat) => _StatChip(stat: stat, chipColor: chipColor)).toList(),
          ),
        ],
      ),
    );
  }
}

class _StatInfo {
  final String value;
  final String label;
  final String emoji;
  const _StatInfo(this.value, this.label, this.emoji);
}

class _StatChip extends StatelessWidget {
  final _StatInfo stat;
  final Color chipColor;
  const _StatChip({required this.stat, required this.chipColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(stat.emoji, style: const TextStyle(fontSize: 18)),
          const SizedBox(width: 6),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                stat.label,
                style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 12),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                stat.value,
                style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12, color: Colors.green),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
