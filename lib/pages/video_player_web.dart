// Only for web
// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/course_video.dart';

// This file is deprecated. Use SimpleVimeoPlayer for all Vimeo playback.

Widget buildVimeoIframePlayerWeb(CourseVideo video, void Function(void Function()) setState, Widget Function() buildVideoPreview) {
  try {
    final String viewId = 'vimeo-player-${video.id}';
    // Register the view factory only on web
    // ignore: undefined_prefixed_name
    ui.platformViewRegistry.registerViewFactory(viewId, (int viewId) {
      final iframeElement = html.IFrameElement()
        ..style.border = 'none'
        ..style.height = '100%'
        ..style.width = '100%'
        ..allowFullscreen = true
        ..allow = 'autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share'
        ..setAttribute('webkitPlaysinline', 'true')
        ..setAttribute('playsinline', 'true')
        ..setAttribute('referrerpolicy', 'origin')
        ..src = video.videoEmbedUrl ?? '';
      return iframeElement;
    });
    return Stack(
      alignment: Alignment.center,
      children: [
        Container(color: Colors.black),
        HtmlElementView(viewType: viewId),
        const CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      ],
    );
  } catch (e) {
    setState(() {
      // ignore: invalid_use_of_visible_for_testing_member
      // ignore: invalid_use_of_protected_member
    });
    return buildVideoPreview();
  }
}