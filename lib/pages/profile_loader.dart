import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/user_service_new.dart';
import '../models/user_profile.dart';
import '../models/user_profile.dart';
import '../pages/profile_page.dart';

class ProfileLoader extends StatefulWidget {
  const ProfileLoader({Key? key}) : super(key: key);

  @override
  State<ProfileLoader> createState() => _ProfileLoaderState();
}

class _ProfileLoaderState extends State<ProfileLoader> {
  late Future<void> _profileFuture;
  final UserService _userService = UserService();

  @override
  void initState() {
    super.initState();
    _profileFuture = _loadProfile();
  }

  Future<void> _loadProfile() async {
    final profile = await _userService.getUserProfile();
    final provider = Provider.of<UserProfileProvider>(context, listen: false);
    provider.setProfile(profile);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<void>(
      future: _profileFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }
        if (snapshot.hasError) {
          return Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red, size: 48),
                  const SizedBox(height: 16),
                  Text('Failed to load profile. Please try again.', style: TextStyle(color: Colors.red)),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _profileFuture = _loadProfile();
                      });
                    },
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }
        // Profile loaded, show the ProfilePage
        return const ProfilePage();
      },
    );
  }
} 