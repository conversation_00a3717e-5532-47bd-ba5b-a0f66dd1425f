import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/course_settings_provider.dart';
import '../design_system/kft_design_system.dart';

class CourseSettingsPage extends StatefulWidget {
  const CourseSettingsPage({Key? key}) : super(key: key);

  @override
  _CourseSettingsPageState createState() => _CourseSettingsPageState();
}

class _CourseSettingsPageState extends State<CourseSettingsPage> {
  final _formKey = GlobalKey<FormState>();
  final _whatsappNumberController = TextEditingController();
  final _messagePrefixController = TextEditingController();
  bool _useCustomNumbers = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  @override
  void dispose() {
    _whatsappNumberController.dispose();
    _messagePrefixController.dispose();
    super.dispose();
  }

  void _loadSettings() {
    final provider = Provider.of<CourseSettingsProvider>(context, listen: false);
    final settings = provider.settings;
    
    setState(() {
      _whatsappNumberController.text = settings.defaultWhatsappNumber;
      _messagePrefixController.text = settings.defaultMessagePrefix;
      _useCustomNumbers = settings.useCustomWhatsappNumbers;
    });
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final provider = Provider.of<CourseSettingsProvider>(context, listen: false);
      await provider.updateSettings(
        defaultWhatsappNumber: _whatsappNumberController.text,
        defaultMessagePrefix: _messagePrefixController.text,
        useCustomWhatsappNumbers: _useCustomNumbers,
      );
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: KFTDesignSystem.getBackgroundColor(context),
      appBar: AppBar(
        title: const Text('Course Settings'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<CourseSettingsProvider>(
        builder: (context, provider, _) {
          if (provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }
          
          return Form(
            key: _formKey,
            child: ListView(
              padding: const EdgeInsets.all(16),
              children: [
                _buildSectionHeader(context, 'WhatsApp Integration'),
                _buildInfoCard(
                  context,
                  'These settings control how the WhatsApp integration works for course enrollment.',
                ),
                const SizedBox(height: 16),
                
                // Default WhatsApp Number
                TextFormField(
                  controller: _whatsappNumberController,
                  decoration: InputDecoration(
                    labelText: 'Default WhatsApp Number',
                    hintText: 'Enter WhatsApp number with country code',
                    prefixIcon: const Icon(Icons.phone),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  keyboardType: TextInputType.phone,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a WhatsApp number';
                    }
                    if (!value.startsWith('+')) {
                      return 'Number must start with country code (e.g., +1)';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Message Prefix
                TextFormField(
                  controller: _messagePrefixController,
                  decoration: InputDecoration(
                    labelText: 'Default Message Prefix',
                    hintText: 'Enter default message prefix',
                    prefixIcon: const Icon(Icons.message),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter a message prefix';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                
                // Use Custom Numbers Switch
                SwitchListTile(
                  title: const Text('Use Custom WhatsApp Numbers'),
                  subtitle: const Text(
                    'Enable to use different WhatsApp numbers for each course',
                  ),
                  value: _useCustomNumbers,
                  onChanged: (value) {
                    setState(() {
                      _useCustomNumbers = value;
                    });
                  },
                  activeColor: theme.colorScheme.primary,
                ),
                const SizedBox(height: 24),
                
                // Save Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _saveSettings,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text('Save Settings'),
                  ),
                ),
                const SizedBox(height: 16),
                
                // Reset Button
                SizedBox(
                  width: double.infinity,
                  height: 50,
                  child: OutlinedButton(
                    onPressed: _isLoading
                        ? null
                        : () {
                            _showResetConfirmation(context);
                          },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: theme.colorScheme.primary,
                      side: BorderSide(color: theme.colorScheme.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Reset to Defaults'),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, top: 16.0, bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildInfoCard(BuildContext context, String text) {
    return Card(
      elevation: 0,
      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                text,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface.withOpacity(0.8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showResetConfirmation(BuildContext context) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Settings'),
        content: const Text(
          'Are you sure you want to reset all course settings to defaults?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Reset'),
          ),
        ],
      ),
    );

    if (result == true) {
      final provider = Provider.of<CourseSettingsProvider>(context, listen: false);
      await provider.resetToDefaults();
      _loadSettings();
    }
  }
}
