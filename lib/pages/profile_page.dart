import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_profile.dart';
import '../services/user_service_new.dart';
import '../widgets/premium_header.dart';
import '../widgets/premium_card.dart';
import '../design_system/kft_design_system.dart';

import 'package:provider/provider.dart';
import '../widgets/profile_avatar_enhanced.dart';
import '../widgets/default_avatar_widget.dart';
import '../widgets/skeleton_widgets.dart';
import '../widgets/theme_toggle_button.dart';
import 'dart:ui';

class ProfilePage extends StatefulWidget {
  const ProfilePage({Key? key}) : super(key: key);

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> with SingleTickerProviderStateMixin {
  final UserService _userService = UserService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _loadProfile();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.1), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _loadProfile() async {
    final profile = await _userService.getUserProfile();
    final provider = Provider.of<UserProfileProvider>(context, listen: false);
    provider.setProfile(profile);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: theme.colorScheme.background,
      body: SafeArea(
        child: Consumer<UserProfileProvider>(
          builder: (context, profileProvider, _) {
            final userProfile = profileProvider.profile;
            final isLoading = userProfile == null;
            
            if (isLoading) {
              return _buildSkeletonLoader();
            }

            return CustomScrollView(
              physics: const BouncingScrollPhysics(),
              slivers: [
                SliverPremiumHeader(
                  title: 'Profile',
                  subtitle: 'Your personal health information',
                  actions: [],
                  expandedHeight: 160,
                ),
                SliverToBoxAdapter(
                  child: FadeTransition(
                    opacity: _fadeAnimation,
                    child: SlideTransition(
                      position: _slideAnimation,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                          _buildProfileCard(context, userProfile),
                      const SizedBox(height: 24),
                      _buildAchievementsSection(context, userProfile),
                      const SizedBox(height: 24),
                      _buildMissionStatement(context),
                      const SizedBox(height: 100),
                    ],
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildProfileCard(BuildContext context, UserProfile userProfile) {
    final theme = Theme.of(context);
    return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: PremiumCard(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Row(
                  children: [
                    _buildProfileAvatar(userProfile),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            userProfile.name,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.height,
                                size: 16,
                                color: theme.colorScheme.onSurface.withOpacity(0.7),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${userProfile.height.toStringAsFixed(1)} cm',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Icon(
                                Icons.monitor_weight_outlined,
                                size: 16,
                                color: theme.colorScheme.onSurface.withOpacity(0.7),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${userProfile.weight.toStringAsFixed(1)} kg',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                IconButton(
                  onPressed: () => _showEditProfileDialog(context, userProfile),
                  icon: Icon(
                    Icons.edit_outlined,
                    color: theme.colorScheme.primary,
                  ),
                ),
                  ],
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: _buildHealthStatItem(
                        icon: Icons.monitor_weight,
                        value: '${userProfile.weight.toStringAsFixed(1)} kg',
                        label: 'Weight',
                        color: Colors.blue,
                      ),
                    ),
                    Expanded(
                      child: _buildHealthStatItem(
                        icon: Icons.health_and_safety,
                        value: userProfile.currentBMI.toStringAsFixed(1),
                        label: 'BMI',
                        color: _getBMIColor(userProfile.currentBMI),
                      ),
                    ),
                    Expanded(
                      child: _buildHealthStatItem(
                        icon: Icons.medical_information,
                        value: userProfile.bmiCategory,
                        label: 'Status',
                        color: _getBMIColor(userProfile.currentBMI),
                      ),
                    ),
                  ],
                ),
              ],
            ),
      ),
    );
  }

  Widget _buildHealthStatItem({
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    final theme = Theme.of(context);
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
          textAlign: TextAlign.center,
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: theme.colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  Color _getBMIColor(double bmi) {
    if (bmi < 18.5) {
      return Colors.blue; // Underweight
    } else if (bmi < 25) {
      return Colors.green; // Normal
    } else if (bmi < 30) {
      return Colors.orange; // Overweight
    } else {
      return Colors.red; // Obese
    }
  }

  Widget _buildProfileAvatar(UserProfile userProfile) {
    final theme = Theme.of(context);
    return Container(
      width: 60,
      height: 60,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: theme.colorScheme.surface,
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: theme.shadowColor.withOpacity(0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          userProfile.name.isNotEmpty ? userProfile.name[0].toUpperCase() : '?',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context, UserProfile userProfile) {
    final theme = Theme.of(context);
    final nameController = TextEditingController(text: userProfile.name);
    final heightController = TextEditingController(
      text: userProfile.height.toString(),
    );
    final weightController = TextEditingController(
      text: userProfile.weight.toString(),
    );

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.edit_outlined,
                      color: theme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Edit Profile',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildInputField(
                controller: nameController,
                label: 'Full Name',
                icon: Icons.person_outline,
              ),
              const SizedBox(height: 16),
              _buildInputField(
                controller: heightController,
                label: 'Height (cm)',
                icon: Icons.height,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              _buildInputField(
                controller: weightController,
                label: 'Weight (kg)',
                icon: Icons.monitor_weight_outlined,
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 32),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: theme.colorScheme.onSurface.withOpacity(0.7),
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    child: const Text('Cancel'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      final name = nameController.text;
                      final height = double.tryParse(heightController.text) ?? userProfile.height;
                      final weight = double.tryParse(weightController.text) ?? userProfile.weight;

                      if (name.isNotEmpty) {
                        await _userService.updateName(name);
                      }

                      if (height > 0) {
                        await _userService.updateHeight(height);
                      }

                      if (weight > 0) {
                        await _userService.updateWeight(weight);
                      }

                      setState(() {
                        _loadProfile();
                      });

                      if (context.mounted) {
                        Navigator.pop(context);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: theme.colorScheme.primary,
                      foregroundColor: theme.brightness == Brightness.dark
                          ? Colors.black
                          : Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Save Changes'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    TextInputType? keyboardType,
  }) {
    final theme = Theme.of(context);
    return TextField(
      controller: controller,
      keyboardType: keyboardType,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: theme.colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.brightness == Brightness.dark
                ? Colors.grey.shade700
                : KFTDesignSystem.borderColor,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.brightness == Brightness.dark
                ? Colors.grey.shade700
                : KFTDesignSystem.borderColor,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      ),
      style: TextStyle(color: theme.colorScheme.onSurface),
    );
  }

  Widget _buildSkeletonLoader() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          const SizedBox(height: 40),
          const SkeletonProfile(),
          const SizedBox(height: 24),
          SkeletonContainer(width: double.infinity, height: 60),
          const SizedBox(height: 24),
          SkeletonContainer(width: double.infinity, height: 120),
        ],
      ),
    );
  }

  Widget _buildMissionStatement(BuildContext context) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: PremiumCard(
        padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            Row(
                  children: [
                Icon(
                  Icons.auto_awesome,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                const SizedBox(width: 8),
                    Text(
                  'Your Mission',
                      style: TextStyle(
                    fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
            const SizedBox(height: 12),
            Text(
              'Transform your body, transform your life. Every workout brings you closer to your goals.',
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurface.withOpacity(0.7),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: theme.colorScheme.onSurface,
        ),
              ),
    );
  }

  Widget _buildAchievementsSection(BuildContext context, UserProfile userProfile) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(context, 'Achievements'),
          const SizedBox(height: 16),
          PremiumCard(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                _buildAchievementItem(
                  context,
                  icon: Icons.emoji_events,
                  title: 'First Workout',
                  description: 'Completed your first workout',
                  isCompleted: true,
                ),
                const SizedBox(height: 16),
                _buildAchievementItem(
                  context,
                  icon: Icons.local_fire_department,
                  title: '7 Day Streak',
                  description: 'Worked out for 7 consecutive days',
                  isCompleted: false,
                ),
                const SizedBox(height: 16),
                _buildAchievementItem(
                  context,
                  icon: Icons.fitness_center,
                  title: 'Strength Master',
                  description: 'Completed 10 strength training workouts',
                  isCompleted: false,
              ),
            ],
          ),
        ),
        ],
      ),
    );
  }

  Widget _buildAchievementItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required bool isCompleted,
  }) {
    final theme = Theme.of(context);
    return Row(
          children: [
            Container(
          padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
            color: isCompleted
                ? theme.colorScheme.primary.withOpacity(0.1)
                : theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
            color: isCompleted
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                    ),
                  ),
              const SizedBox(height: 4),
                  Text(
                description,
                    style: TextStyle(
                  fontSize: 14,
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ),
            Icon(
          isCompleted ? Icons.check_circle : Icons.lock_outline,
          color: isCompleted
              ? theme.colorScheme.primary
              : theme.colorScheme.onSurfaceVariant,
          size: 24,
        ),
      ],
    );
  }
}
