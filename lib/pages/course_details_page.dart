import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:provider/provider.dart';
import 'dart:io';
import '../models/purchasable_course.dart';
import '../models/user_profile.dart';
import '../services/api_service.dart';
import '../providers/course_settings_provider.dart';
import 'package:device_info_plus/device_info_plus.dart';

class CourseDetailsPage extends StatefulWidget {
  final PurchasableCourse course;

  const CourseDetailsPage({Key? key, required this.course}) : super(key: key);

  @override
  _CourseDetailsPageState createState() => _CourseDetailsPageState();
}

class _CourseDetailsPageState extends State<CourseDetailsPage> {
  final ApiService _apiService = ApiService();
  bool _isLoading = false;
  String _errorMessage = '';
  late bool _isPurchased;

  @override
  void initState() {
    super.initState();
    _isPurchased = widget.course.isPurchased;
  }

  // Launch WhatsApp with course details
  Future<void> _launchWhatsAppWithCourse() async {
    // Get course settings
    final settingsProvider = Provider.of<CourseSettingsProvider>(context, listen: false);
    final settings = settingsProvider.settings;

    // Get user profile for personalized message
    UserProfile? userProfile;
    try {
      final profileProvider = Provider.of<UserProfileProvider>(context, listen: false);
      userProfile = profileProvider.profile;
    } catch (e) {
      // If provider not available, continue without user profile
      print('User profile not available: $e');
    }

    // Get device info for better support
    String deviceInfo = '';
    try {
      final deviceInfoPlugin = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        deviceInfo = '${androidInfo.brand} ${androidInfo.model}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        deviceInfo = '${iosInfo.name} ${iosInfo.systemVersion}';
      }
    } catch (e) {
      // If device info not available, continue without it
      print('Device info not available: $e');
    }

    // Determine which WhatsApp number to use
    String phone;
    if (settings.useCustomWhatsappNumbers && widget.course.whatsappNumber.isNotEmpty) {
      // Use course-specific WhatsApp number
      phone = widget.course.whatsappNumber;
      print('Using course-specific WhatsApp number: $phone');
    } else {
      // Use default WhatsApp number from settings
      phone = settings.defaultWhatsappNumber;
      print('Using default WhatsApp number: $phone');
    }

    try {
      // Clean up the phone number - remove all non-digit characters except the plus sign
      phone = phone.replaceAll(RegExp(r'[^\d+]'), '');

      // Ensure the phone number starts with '+'
      if (!phone.startsWith('+')) {
        phone = '+$phone';
      }

      print('Cleaned phone number: $phone');

      // Create message with course details
      String message;
      if (widget.course.isPurchased) {
        message = 'Hi, I\'m already enrolled in the course: *${widget.course.title}* (ID: ${widget.course.id})\n'
            'I need assistance with this course.\n'
            'Course duration: ${widget.course.durationWeeks} weeks\n'
            'Please help me with my query.';
      } else {
        // Use custom message prefix if available
        final messagePrefix = widget.course.whatsappMessagePrefix ?? settings.defaultMessagePrefix;

        message = '$messagePrefix *${widget.course.title}* (ID: ${widget.course.id})\n'
            'Price: ₹${widget.course.price.final_.toStringAsFixed(2)}\n'
            'Duration: ${widget.course.durationWeeks} weeks\n'
            'Please provide more information about enrollment.';
      }

      // Add user details if available
      if (userProfile != null) {
        message += '\n\nMy Details:\n'
            'Name: ${userProfile.name}\n'
            'Phone: ${userProfile.phoneNumber}';
      }

      // Add device info if available
      if (deviceInfo.isNotEmpty) {
        message += '\nDevice: $deviceInfo';
      }

      // Format the phone number for WhatsApp URL (remove '+')
      final formattedPhone = phone.replaceAll('+', '');
      print('Formatted phone number for WhatsApp: $formattedPhone');

      // Direct launch approach - try to launch WhatsApp directly
      bool launched = false;

      // Create platform-specific URLs
      String whatsappUrl;

      if (Platform.isIOS) {
        // iOS-specific URL format
        whatsappUrl = 'https://wa.me/$formattedPhone?text=${Uri.encodeComponent(message)}';
        print('iOS WhatsApp URL: $whatsappUrl');

        try {
          if (await canLaunchUrlString(whatsappUrl)) {
            launched = await launchUrlString(
              whatsappUrl,
              mode: LaunchMode.externalApplication,
            );
            print('Launched WhatsApp on iOS: $launched');
          }
        } catch (e) {
          print('Error launching WhatsApp on iOS: $e');
        }
      } else if (Platform.isAndroid) {
        // Android-specific URL format - try direct intent first
        whatsappUrl = 'whatsapp://send?phone=$formattedPhone&text=${Uri.encodeComponent(message)}';
        print('Android WhatsApp URL: $whatsappUrl');

        try {
          if (await canLaunchUrlString(whatsappUrl)) {
            launched = await launchUrlString(
              whatsappUrl,
              mode: LaunchMode.externalApplication,
            );
            print('Launched WhatsApp on Android: $launched');
          }
        } catch (e) {
          print('Error launching WhatsApp on Android: $e');
        }
      }

      // If direct launch failed, try web URL as fallback
      if (!launched) {
        final webUrl = 'https://api.whatsapp.com/send?phone=$formattedPhone&text=${Uri.encodeComponent(message)}';
        print('Fallback WhatsApp URL: $webUrl');

        try {
          if (await canLaunchUrlString(webUrl)) {
            launched = await launchUrlString(
              webUrl,
              mode: LaunchMode.externalApplication,
            );
            print('Launched WhatsApp via web URL: $launched');
          }
        } catch (e) {
          print('Error launching WhatsApp via web URL: $e');
        }
      }

      // If all launch attempts failed, show dialog with copy options
      if (!launched) {
        if (mounted) {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Contact via WhatsApp'),
                content: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Please contact the following number for course enrollment:'),
                      const SizedBox(height: 16),
                      SelectableText(
                        phone,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 16),
                      const Text('Message to send:'),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: SelectableText(message),
                      ),
                    ],
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: const Text('Cancel'),
                  ),
                  ElevatedButton.icon(
                    icon: const Icon(Icons.message),
                    label: const Text('Try Again'),
                    onPressed: () async {
                      Navigator.of(context).pop();

                      // Try all URL formats as a last resort
                      final urls = [
                        'https://api.whatsapp.com/send?phone=$formattedPhone&text=${Uri.encodeComponent(message)}',
                        'https://wa.me/$formattedPhone?text=${Uri.encodeComponent(message)}',
                        'whatsapp://send?phone=$formattedPhone&text=${Uri.encodeComponent(message)}',
                      ];

                      bool launched = false;
                      for (final url in urls) {
                        if (!launched) {
                          try {
                            if (await canLaunchUrlString(url)) {
                              launched = await launchUrlString(
                                url,
                                mode: LaunchMode.externalApplication,
                              );
                              print('Launched URL: $url');
                              break;
                            }
                          } catch (e) {
                            print('Error launching $url: $e');
                          }
                        }
                      }

                      if (!launched && mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Could not launch WhatsApp. Please install WhatsApp or copy the message manually.'),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    },
                  ),
                ],
              );
            },
          );
        }
      }
    } catch (e) {
      print('Error in WhatsApp launch process: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // This method is kept for reference but we'll use WhatsApp enrollment instead
  Future<void> _purchaseCourse() async {
    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    try {
      await _apiService.purchaseCourse(widget.course.id);

      setState(() {
        _isPurchased = true;
        _isLoading = false;
      });

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Course purchased successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
        _isLoading = false;
      });

      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $_errorMessage'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // App bar with course image
          SliverAppBar(
            expandedHeight: 200,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                widget.course.title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 1),
                      blurRadius: 3.0,
                      color: Color.fromARGB(255, 0, 0, 0),
                    ),
                  ],
                ),
              ),
              background: Stack(
                fit: StackFit.expand,
                children: [
                  // Course image
                  if (widget.course.thumbnailUrl != null && widget.course.thumbnailUrl!.isNotEmpty)
                    Image.network(
                      getFullImageUrl(widget.course.thumbnailUrl),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Theme.of(context).primaryColor.withOpacity(0.3),
                          child: const Icon(
                            Icons.image_not_supported,
                            size: 64,
                            color: Colors.white,
                          ),
                        );
                      },
                    )
                  else
                    Container(
                      color: Theme.of(context).primaryColor.withOpacity(0.3),
                      child: const Icon(
                        Icons.school,
                        size: 64,
                        color: Colors.white,
                      ),
                    ),

                  // Gradient overlay for better text visibility
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),

                  // Featured badge
                  if (widget.course.isFeatured)
                    Positioned(
                      top: 60,
                      left: 16,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.star,
                              size: 16,
                              color: Colors.white,
                            ),
                            SizedBox(width: 4),
                            Text(
                              'Featured',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),

          // Course details
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Price and purchase button
                  _buildPriceAndPurchaseSection(),

                  const SizedBox(height: 24),

                  // Course metadata
                  _buildCourseMetadata(),

                  const SizedBox(height: 24),

                  // Course description
                  const Text(
                    'About this course',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.course.description,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[700],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // What you'll learn
                  const Text(
                    'What you\'ll learn',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildWhatYoullLearn(),

                  const SizedBox(height: 24),

                  // Course content preview
                  const Text(
                    'Course Content',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '${widget.course.totalVideos} videos • ${widget.course.durationWeeks} weeks',
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildCourseContentPreview(),

                  const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: _isPurchased
          ? null
          : Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, -3),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: _isLoading ? null : _launchWhatsAppWithCourse,
                  borderRadius: BorderRadius.circular(8),
                  child: Ink(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF25D366), // WhatsApp green
                          const Color(0xFF128C7E), // Darker WhatsApp green
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _isLoading
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.message_outlined,
                                      color: Colors.white,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 10),
                                    Text(
                                      widget.course.price.original == 0
                                          ? 'Enroll for Free via WhatsApp'
                                          : 'Enroll Now - ₹${widget.course.price.final_.toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.white,
                                        letterSpacing: 0.2,
                                      ),
                                    ),
                                  ],
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildPriceAndPurchaseSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if (_isPurchased)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.green),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: Colors.green,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'Purchased',
                          style: TextStyle(
                            color: Colors.green,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  )
                else if (widget.course.price.original == 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.green.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.green),
                    ),
                    child: const Text(
                      'Free',
                      style: TextStyle(
                        color: Colors.green,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  )
                else
                  Row(
                    children: [
                      Text(
                        '₹${widget.course.price.final_.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      if (widget.course.price.hasDiscount) ...[
                        const SizedBox(width: 8),
                        Text(
                          '₹${widget.course.price.original.toStringAsFixed(2)}',
                          style: const TextStyle(
                            decoration: TextDecoration.lineThrough,
                            color: Colors.grey,
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            '${widget.course.price.discountPercentage}% OFF',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                const Spacer(),
                if (_isPurchased)
                  ElevatedButton(
                    onPressed: () {
                      // Navigate to enrolled course
                      Navigator.pop(context);
                      // In a real app, you would navigate to the enrolled course page
                    },
                    child: const Text('Go to Course'),
                  )
                else
                  Material(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    child: InkWell(
                      onTap: _launchWhatsAppWithCourse,
                      borderRadius: BorderRadius.circular(8),
                      child: Ink(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF25D366), // WhatsApp green
                              const Color(0xFF128C7E), // Darker WhatsApp green
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.message_outlined,
                                color: Colors.white,
                                size: 16,
                              ),
                              const SizedBox(width: 6),
                              const Text(
                                'Enroll via WhatsApp',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 14,
                                  letterSpacing: 0.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            if (_errorMessage.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  _errorMessage,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCourseMetadata() {
    return Row(
      children: [
        _buildMetadataItem(
          icon: Icons.calendar_today,
          label: 'Duration',
          value: '${widget.course.durationWeeks} weeks',
        ),
        _buildMetadataItem(
          icon: Icons.video_library,
          label: 'Videos',
          value: '${widget.course.totalVideos} lessons',
        ),
        _buildMetadataItem(
          icon: Icons.category,
          label: 'Category',
          value: widget.course.category,
        ),
      ],
    );
  }

  Widget _buildMetadataItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWhatYoullLearn() {
    // This would normally come from the API, but we'll hardcode some examples
    final List<String> benefits = [
      'Master the fundamentals of ${widget.course.category.toLowerCase()}',
      'Build a strong foundation for advanced techniques',
      'Learn at your own pace with step-by-step videos',
      'Get personalized feedback on your progress',
      'Join a community of like-minded fitness enthusiasts',
    ];

    return Column(
      children: benefits.map((benefit) => _buildBenefitItem(benefit)).toList(),
    );
  }

  Widget _buildBenefitItem(String benefit) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              benefit,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCourseContentPreview() {
    // This would normally come from the API, but we'll create some example weeks
    return Column(
      children: List.generate(
        widget.course.durationWeeks > 3 ? 3 : widget.course.durationWeeks,
        (index) => _buildWeekItem(index + 1),
      ),
    );
  }

  Widget _buildWeekItem(int weekNumber) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        title: Text(
          'Week $weekNumber',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          weekNumber == 1
              ? 'Introduction and fundamentals'
              : weekNumber == 2
                  ? 'Building core skills'
                  : 'Advanced techniques',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 12,
          ),
        ),
        children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3, // Example: 3 videos per week
            itemBuilder: (context, index) {
              return ListTile(
                leading: const Icon(Icons.play_circle_outline),
                title: Text('Lesson ${index + 1}'),
                subtitle: Text(
                  '${5 + index * 3} minutes',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
                trailing: _isPurchased && weekNumber == 1
                    ? const Icon(
                        Icons.lock_open,
                        color: Colors.green,
                      )
                    : const Icon(
                        Icons.lock,
                        color: Colors.grey,
                      ),
              );
            },
          ),
        ],
      ),
    );
  }

  String getFullImageUrl(String? url) {
    if (url == null || url.isEmpty) return '';
    if (url.startsWith('http://') || url.startsWith('https://')) return url;
    final cleanPath = url.startsWith('/') ? url.substring(1) : url;
    return '${ApiService.baseUrl.replaceAll('/api', '')}/$cleanPath';
  }
}
