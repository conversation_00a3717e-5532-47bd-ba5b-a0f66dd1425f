import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/quote_provider.dart';
import '../models/motivational_quote.dart';
import '../design_system/kft_design_system.dart';

class MotivationalQuotePage extends StatefulWidget {
  const MotivationalQuotePage({Key? key}) : super(key: key);

  @override
  State<MotivationalQuotePage> createState() => _MotivationalQuotePageState();
}

class _MotivationalQuotePageState extends State<MotivationalQuotePage> {
  final List<String> _categories = [
    'All',
    'Weight Loss',
    'Workout',
    'Mental',
    'Nutrition',
    'Fitness',
  ];
  
  String _selectedCategory = 'All';
  
  @override
  void initState() {
    super.initState();
    // Refresh quotes when page opens
    Future.microtask(() {
      Provider.of<QuoteProvider>(context, listen: false).refreshQuotes();
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Motivational Quotes'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings_outlined),
            onPressed: () => _showSettingsDialog(context),
            tooltip: 'Settings',
          ),
        ],
      ),
      body: Consumer<QuoteProvider>(
        builder: (context, provider, _) {
          if (provider.isLoading && provider.currentQuote == null) {
            return const Center(child: CircularProgressIndicator());
          }
          
          if (provider.error != null && provider.currentQuote == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 48, color: Colors.red.shade400),
                  const SizedBox(height: 16),
                  Text(
                    'Error: ${provider.error}',
                    style: TextStyle(color: Colors.red.shade700),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => provider.refreshQuotes(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }
          
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildCurrentQuoteCard(context, provider),
                const SizedBox(height: 24),
                _buildCategoryFilter(context),
                const SizedBox(height: 16),
                _buildQuotesList(context, provider),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Provider.of<QuoteProvider>(context, listen: false).getNewQuote();
        },
        backgroundColor: theme.colorScheme.primary,
        child: const Icon(Icons.refresh),
      ),
    );
  }
  
  Widget _buildCurrentQuoteCard(BuildContext context, QuoteProvider provider) {
    final theme = Theme.of(context);
    final quote = provider.currentQuote;
    
    if (quote == null) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Center(
            child: Text(
              'No quote available. Tap the refresh button to get a new quote.',
              style: theme.textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }
    
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Today\'s Quote',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (quote.category != null)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          quote.categoryIcon,
                          style: const TextStyle(fontSize: 14),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          quote.displayCategory,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: theme.colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 24),
            Text(
              '"${quote.quote}"',
              style: theme.textTheme.titleMedium?.copyWith(
                fontStyle: FontStyle.italic,
                height: 1.5,
              ),
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                '- ${quote.displayAuthor}',
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextButton.icon(
                  onPressed: () => provider.getNewQuote(),
                  icon: const Icon(Icons.refresh, size: 18),
                  label: const Text('New Quote'),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    visualDensity: VisualDensity.compact,
                  ),
                ),
                IconButton(
                  onPressed: () => provider.toggleFavorite(quote),
                  icon: Icon(
                    quote.isFavorite ? Icons.favorite : Icons.favorite_border,
                    color: quote.isFavorite ? Colors.red : null,
                  ),
                  tooltip: quote.isFavorite ? 'Remove from favorites' : 'Add to favorites',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildCategoryFilter(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Browse by Category',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: _categories.map((category) {
              final isSelected = _selectedCategory == category;
              
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Text(category),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = category;
                    });
                  },
                  backgroundColor: theme.colorScheme.surface,
                  selectedColor: theme.colorScheme.primary.withOpacity(0.2),
                  checkmarkColor: theme.colorScheme.primary,
                  labelStyle: TextStyle(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
  
  Widget _buildQuotesList(BuildContext context, QuoteProvider provider) {
    final theme = Theme.of(context);
    final allQuotes = provider.allQuotes;
    
    // Filter quotes by category
    final filteredQuotes = _selectedCategory == 'All'
        ? allQuotes
        : allQuotes.where((quote) {
            return quote.category != null &&
                quote.displayCategory.toLowerCase() ==
                    _selectedCategory.toLowerCase();
          }).toList();
    
    if (filteredQuotes.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.format_quote,
                size: 48,
                color: theme.colorScheme.primary.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                'No quotes found for this category',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: filteredQuotes.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final quote = filteredQuotes[index];
        return _buildQuoteListItem(context, quote, provider);
      },
    );
  }
  
  Widget _buildQuoteListItem(
    BuildContext context,
    MotivationalQuote quote,
    QuoteProvider provider,
  ) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (quote.category != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      quote.categoryIcon,
                      style: const TextStyle(fontSize: 12),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      quote.displayCategory,
                      style: TextStyle(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            const SizedBox(height: 12),
            Text(
              '"${quote.quote}"',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '- ${quote.displayAuthor}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                IconButton(
                  onPressed: () => provider.toggleFavorite(quote),
                  icon: Icon(
                    quote.isFavorite ? Icons.favorite : Icons.favorite_border,
                    size: 18,
                    color: quote.isFavorite ? Colors.red : null,
                  ),
                  visualDensity: VisualDensity.compact,
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                  tooltip: quote.isFavorite ? 'Remove from favorites' : 'Add to favorites',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  void _showSettingsDialog(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<QuoteProvider>(context, listen: false);
    final preferences = provider.preferences;
    
    if (preferences == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Failed to load preferences'),
        ),
      );
      return;
    }
    
    bool enabled = preferences.enabled;
    bool showOnHomeScreen = preferences.showOnHomeScreen;
    bool showOnLockScreen = preferences.showOnLockScreen;
    int frequencyPerDay = preferences.frequencyPerDay;
    
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Quote Settings',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 24),
                SwitchListTile(
                  title: const Text('Enable Quotes'),
                  subtitle: const Text('Show motivational quotes in the app'),
                  value: enabled,
                  onChanged: (value) {
                    setState(() {
                      enabled = value;
                    });
                  },
                  contentPadding: EdgeInsets.zero,
                ),
                const Divider(),
                SwitchListTile(
                  title: const Text('Show on Home Screen'),
                  subtitle: const Text('Display quotes on the home screen'),
                  value: showOnHomeScreen,
                  onChanged: enabled
                      ? (value) {
                          setState(() {
                            showOnHomeScreen = value;
                          });
                        }
                      : null,
                  contentPadding: EdgeInsets.zero,
                ),
                SwitchListTile(
                  title: const Text('Show on Lock Screen'),
                  subtitle: const Text('Display quotes on the lock screen'),
                  value: showOnLockScreen,
                  onChanged: enabled
                      ? (value) {
                          setState(() {
                            showOnLockScreen = value;
                          });
                        }
                      : null,
                  contentPadding: EdgeInsets.zero,
                ),
                const Divider(),
                ListTile(
                  title: const Text('Frequency'),
                  subtitle: const Text('How often to show new quotes'),
                  contentPadding: EdgeInsets.zero,
                  trailing: DropdownButton<int>(
                    value: frequencyPerDay,
                    onChanged: enabled
                        ? (value) {
                            if (value != null) {
                              setState(() {
                                frequencyPerDay = value;
                              });
                            }
                          }
                        : null,
                    items: [
                      DropdownMenuItem(
                        value: 1,
                        child: Text('Once a day', style: theme.textTheme.bodyMedium),
                      ),
                      DropdownMenuItem(
                        value: 2,
                        child: Text('Twice a day', style: theme.textTheme.bodyMedium),
                      ),
                      DropdownMenuItem(
                        value: 3,
                        child: Text('Three times a day', style: theme.textTheme.bodyMedium),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {
                        provider.updatePreferences(
                          enabled: enabled,
                          showOnHomeScreen: showOnHomeScreen,
                          showOnLockScreen: showOnLockScreen,
                          frequencyPerDay: frequencyPerDay,
                        );
                        Navigator.pop(context);
                      },
                      child: const Text('Save'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
