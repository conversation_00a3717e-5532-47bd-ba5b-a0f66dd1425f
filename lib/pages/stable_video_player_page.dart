import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/course_video.dart';
import '../models/course.dart';
import '../widgets/stable_vimeo_player.dart';
import '../services/api_service.dart';
import '../services/course_tracking_service.dart';
import '../services/awesome_notification_service.dart';
import '../design_system/kft_design_system.dart';

/// Example page demonstrating how to use the StableVimeoPlayer
/// This replaces the existing video player pages with improved stability and domain verification
class StableVideoPlayerPage extends StatefulWidget {
  final CourseVideo video;
  final Course? course;
  final bool autoPlay;
  final bool showControls;

  const StableVideoPlayerPage({
    Key? key,
    required this.video,
    this.course,
    this.autoPlay = true,
    this.showControls = true,
  }) : super(key: key);

  @override
  State<StableVideoPlayerPage> createState() => _StableVideoPlayerPageState();
}

class _StableVideoPlayerPageState extends State<StableVideoPlayerPage> {
  final ApiService _apiService = ApiService();
  final CourseTrackingService _trackingService = CourseTrackingService();
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();

  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  bool _isCompleted = false;
  int _currentPosition = 0;
  int _totalDuration = 0;

  @override
  void initState() {
    super.initState();
    _initializePage();
  }

  Future<void> _initializePage() async {
    try {
      setState(() {
        _isLoading = true;
        _hasError = false;
      });

      // Track video access
      await _trackingService.saveLastAccessedVideo(widget.video);

      if (widget.course != null) {
        await _trackingService.saveLastOpenedCourse(widget.course!);
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = 'Failed to initialize video player: $e';
      });
    }
  }

  void _onVideoProgress(int position) {
    setState(() {
      _currentPosition = position;
    });
  }

  void _onVideoCompleted() {
    setState(() {
      _isCompleted = true;
    });

    // Show completion notification
    _showCompletionNotification();

    // Trigger streak completion if applicable
    _checkStreakCompletion();
  }

  void _onVideoError(String error) {
    setState(() {
      _hasError = true;
      _errorMessage = error;
    });

    // Show error snackbar
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Video Error: $error'),
        backgroundColor: Colors.red,
        action: SnackBarAction(
          label: 'Retry',
          textColor: Colors.white,
          onPressed: () {
            setState(() {
              _hasError = false;
              _errorMessage = '';
            });
          },
        ),
      ),
    );
  }

  void _onVideoPlay() {
    debugPrint('Video started playing: ${widget.video.title}');
  }

  void _onVideoPause() {
    debugPrint('Video paused: ${widget.video.title}');
  }

  void _onVideoReady() {
    debugPrint('Video player ready: ${widget.video.title}');
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _showCompletionNotification() async {
    try {
      await _notificationService.showVideoCompletionNotification(
        widget.video.title,
        'Great job! You completed "${widget.video.title}"',
      );
    } catch (e) {
      debugPrint('Failed to show completion notification: $e');
    }
  }

  Future<void> _checkStreakCompletion() async {
    try {
      // Check if this video completion should trigger a streak
      final completionPercentage = _totalDuration > 0
          ? (_currentPosition / _totalDuration) * 100
          : 0;

      if (completionPercentage >= 80) {
        // Trigger streak completion logic
        await _apiService.makeApiRequest(
          'update_streak.php',
          method: 'POST',
          data: {
            'video_id': widget.video.id,
            'completion_percentage': completionPercentage,
            'completed_at': DateTime.now().toIso8601String(),
          },
        );
      }
    } catch (e) {
      debugPrint('Failed to update streak: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    if (_hasError) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        // Video player section
        Expanded(
          child: Container(
            width: double.infinity,
            color: Colors.black,
            child: _buildVideoPlayer(),
          ),
        ),

        // Video information section
        if (!_isLoading) _buildVideoInfo(),
      ],
    );
  }

  Widget _buildVideoPlayer() {
    return StableVimeoPlayer(
      video: widget.video,
      autoPlay: widget.autoPlay,
      showControls: widget.showControls,
      enableFullscreen: true,
      enableQualitySelection: true,
      showProgressIndicator: true,
      playbackSpeeds: const [0.5, 0.75, 1.0, 1.25, 1.5, 2.0],
      initialPlaybackSpeed: 1.0,
      onProgress: _onVideoProgress,
      onCompleted: _onVideoCompleted,
      onError: _onVideoError,
      onPlay: _onVideoPlay,
      onPause: _onVideoPause,
      onReady: _onVideoReady,
      onStreakUpdated: () {
        // Streak updated - could trigger UI refresh if needed
        debugPrint('Streak updated from video player');
      },
    );
  }

  Widget _buildVideoInfo() {
    return Container(
      color: KFTDesignSystem.backgroundColor(context),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Video title
          Text(
            widget.video.title,
            style: KFTDesignSystem.headlineStyle(context),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // Video description
          if (widget.video.description.isNotEmpty) ...[
            Text(
              widget.video.description,
              style: KFTDesignSystem.bodyStyle(context),
              maxLines: 3,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 12),
          ],

          // Progress information
          Row(
            children: [
              Icon(
                Icons.play_circle_outline,
                color: KFTDesignSystem.primaryColor(context),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _formatProgress(),
                style: KFTDesignSystem.captionStyle(context),
              ),

              const Spacer(),

              if (_isCompleted)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Completed',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              // Back button
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back),
                  label: const Text('Back'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: KFTDesignSystem.secondaryColor(context),
                    foregroundColor: Colors.white,
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Next video button (if available)
              if (widget.course != null)
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _navigateToNextVideo,
                    icon: const Icon(Icons.skip_next),
                    label: const Text('Next'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.primaryColor(context),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              color: Colors.red,
              size: 64,
            ),
            const SizedBox(height: 16),
            Text(
              'Video Unavailable',
              style: KFTDesignSystem.headlineStyle(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage.isNotEmpty
                  ? _errorMessage
                  : 'This video cannot be played. Please check your connection and try again.',
              style: KFTDesignSystem.bodyStyle(context),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.arrow_back),
                    label: const Text('Back'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.secondaryColor(context),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _initializePage,
                    icon: const Icon(Icons.refresh),
                    label: const Text('Retry'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: KFTDesignSystem.primaryColor(context),
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _formatProgress() {
    if (_totalDuration <= 0) return 'Loading...';

    final currentMinutes = (_currentPosition / 60).floor();
    final currentSeconds = _currentPosition % 60;
    final totalMinutes = (_totalDuration / 60).floor();
    final totalSeconds = _totalDuration % 60;

    return '${currentMinutes.toString().padLeft(2, '0')}:${currentSeconds.toString().padLeft(2, '0')} / ${totalMinutes.toString().padLeft(2, '0')}:${totalSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _navigateToNextVideo() async {
    // Implementation for navigating to next video in course
    // This would typically fetch the next video from the course
    try {
      if (widget.course != null) {
        // Get next video logic here
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Next video feature coming soon!'),
          ),
        );
      }
    } catch (e) {
      debugPrint('Failed to navigate to next video: $e');
    }
  }
}
