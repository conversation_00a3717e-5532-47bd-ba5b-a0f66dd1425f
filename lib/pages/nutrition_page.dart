import 'package:flutter/material.dart';
import '../widgets/kft_app_bar.dart';
import 'package:flutter/services.dart';

class NutritionPage extends StatefulWidget {
  const NutritionPage({Key? key}) : super(key: key);

  @override
  State<NutritionPage> createState() => _NutritionPageState();
}

class _NutritionPageState extends State<NutritionPage> {
  @override
  void initState() {
    super.initState();
    // Always restore overlays when entering this page
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = colorScheme.brightness == Brightness.dark;
    return Scaffold(
      backgroundColor: colorScheme.background,
      appBar: KFTAppBar(
        title: 'Nutrition',
        centerTitle: false,
        elevation: 0,
        foregroundColor: colorScheme.onSurface,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.symmetric(vertical: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 32,
                    backgroundColor: colorScheme.surface,
                    child: const Text('🥗', style: TextStyle(fontSize: 32)),
                  ),
                  const SizedBox(width: 18),
                  Expanded(
                    child: Text(
                      'Eat smart, stay strong! Discover top protein and fiber sources.',
                      style: TextStyle(
                        color: colorScheme.onSurface.withOpacity(0.85),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 18),
            _SectionCard(
              icon: '🍗',
              title: 'Animal Protein',
              subtitle: 'Per 100g',
              color: isDark ? colorScheme.surfaceVariant : Colors.orange.shade50,
              foods: const [
                _FoodInfo('Chicken Breast', '31g', '🍗'),
                _FoodInfo('Fish', '20-25g', '🐟'),
                _FoodInfo('Paneer', '18g', '🧀'),
                _FoodInfo('Egg Whites (3)', '11g', '🥚'),
                _FoodInfo('Greek Yogurt', '10g', '🥛'),
                _FoodInfo('Skimmed Milk (100ml)', '3.4g', '🥛'),
              ],
              chipColor: isDark ? colorScheme.primary.withOpacity(0.12) : Colors.orange.shade100,
              borderColor: colorScheme.outlineVariant,
              textColor: colorScheme.onSurface,
            ),
            const SizedBox(height: 18),
            _SectionCard(
              icon: '🌱',
              title: 'Plant-based Protein',
              subtitle: 'Per 100g',
              color: isDark ? colorScheme.surfaceVariant : Colors.green.shade50,
              foods: const [
                _FoodInfo('Peanuts', '26g', '🥜'),
                _FoodInfo('Almonds', '21g', '🌰'),
                _FoodInfo('Chickpeas', '19g', '🧆'),
                _FoodInfo('Chia Seeds', '17g', '🌾'),
                _FoodInfo('Lentils & Kidney Beans', '9g', '🥣'),
                _FoodInfo('Tofu', '8g', '🍥'),
              ],
              chipColor: isDark ? colorScheme.primary.withOpacity(0.12) : Colors.green.shade100,
              borderColor: colorScheme.outlineVariant,
              textColor: colorScheme.onSurface,
            ),
            const SizedBox(height: 18),
            _SectionCard(
              icon: '🌾',
              title: 'High-Fiber Foods',
              subtitle: 'Per 100g',
              color: isDark ? colorScheme.surfaceVariant : Colors.blue.shade50,
              foods: const [
                _FoodInfo('Chia Seeds', '34g', '🌾'),
                _FoodInfo('Flax Seeds', '27g', '🌻'),
                _FoodInfo('Popcorn (air-popped)', '14.5g', '🍿'),
                _FoodInfo('Dried Fig', '9.8g', '🍑'),
                _FoodInfo('Oats', '10.6g', '🥣'),
                _FoodInfo('Chickpeas', '7.6g', '🧆'),
                _FoodInfo('Lentils', '7.9g', '🥣'),
              ],
              chipColor: isDark ? colorScheme.primary.withOpacity(0.12) : Colors.blue.shade100,
              borderColor: colorScheme.outlineVariant,
              textColor: colorScheme.onSurface,
            ),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }
}

class _SectionCard extends StatelessWidget {
  final String icon;
  final String title;
  final String subtitle;
  final Color color;
  final List<_FoodInfo> foods;
  final Color chipColor;
  final Color borderColor;
  final Color textColor;

  const _SectionCard({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.color,
    required this.foods,
    required this.chipColor,
    required this.borderColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 18),
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 18),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(22),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(icon, style: const TextStyle(fontSize: 28)),
              const SizedBox(width: 10),
              Text(
                title,
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: textColor),
              ),
              const SizedBox(width: 8),
              Text(
                subtitle,
                style: TextStyle(fontSize: 14, color: textColor.withOpacity(0.7)),
              ),
            ],
          ),
          const SizedBox(height: 14),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: foods.map((food) => _FoodChip(food: food, chipColor: chipColor, borderColor: borderColor, textColor: textColor)).toList(),
          ),
        ],
      ),
    );
  }
}

class _FoodInfo {
  final String name;
  final String value;
  final String emoji;
  const _FoodInfo(this.name, this.value, this.emoji);
}

class _FoodChip extends StatelessWidget {
  final _FoodInfo food;
  final Color chipColor;
  final Color borderColor;
  final Color textColor;
  const _FoodChip({required this.food, required this.chipColor, required this.borderColor, required this.textColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: chipColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: borderColor),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(food.emoji, style: const TextStyle(fontSize: 18)),
          const SizedBox(width: 6),
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                food.name,
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 12, color: textColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              Text(
                food.value,
                style: TextStyle(fontSize: 11, color: textColor.withOpacity(0.7)),
              ),
            ],
          ),
        ],
      ),
    );
  }
} 