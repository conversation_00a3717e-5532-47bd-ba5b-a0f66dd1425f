import 'package:flutter/material.dart';
import '../widgets/responsive_dashboard_widget.dart';
import '../utils/responsive_utils.dart';
import '../design_system/kft_design_system.dart';

/// Demo page showcasing the responsive dashboard capabilities
class ResponsiveDashboardDemo extends StatelessWidget {
  const ResponsiveDashboardDemo({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveDashboardWidget(
      title: 'Responsive Dashboard Demo',
      cards: _buildDemoCards(),
      additionalWidgets: [
        _buildDemoSection(context),
        _buildDemoCharts(context),
        _buildDemoList(context),
      ],
      appBarActions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Dashboard refreshed!')),
            );
          },
        ),
      ],
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showDeviceInfoDialog(context);
        },
        child: const Icon(Icons.info),
      ),
    );
  }

  List<DashboardCard> _buildDemoCards() {
    return [
      const DashboardCard(
        title: 'Total Users',
        value: '1,234',
        subtitle: '+12% from last month',
        icon: Icons.people,
        iconColor: Colors.blue,
        iconBackgroundColor: Color(0xFFE3F2FD),
      ),
      const DashboardCard(
        title: 'Active Sessions',
        value: '567',
        subtitle: 'Currently online',
        icon: Icons.trending_up,
        iconColor: Colors.green,
        iconBackgroundColor: Color(0xFFE8F5E9),
      ),
      const DashboardCard(
        title: 'Revenue',
        value: '\$12,345',
        subtitle: '+8% from last week',
        icon: Icons.attach_money,
        iconColor: Colors.orange,
        iconBackgroundColor: Color(0xFFFFF8E1),
      ),
      const DashboardCard(
        title: 'Conversion Rate',
        value: '3.2%',
        subtitle: 'Above average',
        icon: Icons.analytics,
        iconColor: Colors.purple,
        iconBackgroundColor: Color(0xFFF3E5F5),
      ),
    ];
  }

  Widget _buildDemoSection(BuildContext context) {
    return ResponsiveDashboardSection(
      title: 'Recent Activity',
      children: [
        ResponsiveBuilder(
          builder: (context, deviceType) {
            return ResponsiveGrid(
              mobileColumns: 1,
              tabletColumns: 2,
              desktopColumns: 3,
              spacing: ResponsiveUtils.getResponsiveSpacing(
                context,
                mobile: 12,
                tablet: 16,
                desktop: 20,
              ),
              children: [
                _buildActivityCard(
                  context,
                  'New User Registration',
                  'John Doe joined the platform',
                  '2 minutes ago',
                  Icons.person_add,
                  Colors.blue,
                ),
                _buildActivityCard(
                  context,
                  'Course Completed',
                  'Sarah completed "Flutter Basics"',
                  '15 minutes ago',
                  Icons.school,
                  Colors.green,
                ),
                _buildActivityCard(
                  context,
                  'Payment Received',
                  'Premium subscription renewed',
                  '1 hour ago',
                  Icons.payment,
                  Colors.orange,
                ),
              ],
            );
          },
        ),
      ],
    );
  }

  Widget _buildActivityCard(
    BuildContext context,
    String title,
    String description,
    String time,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(18),
        desktop: const EdgeInsets.all(20),
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ResponsiveText(
                  title,
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  fontWeight: FontWeight.w600,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ResponsiveText(
            description,
            mobileFontSize: 12,
            tabletFontSize: 14,
            desktopFontSize: 16,
            color: isDarkMode ? Colors.white70 : Colors.black54,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          ResponsiveText(
            time,
            mobileFontSize: 10,
            tabletFontSize: 12,
            desktopFontSize: 14,
            color: isDarkMode ? Colors.white60 : Colors.black45,
          ),
        ],
      ),
    );
  }

  Widget _buildDemoCharts(BuildContext context) {
    return ResponsiveDashboardSection(
      title: 'Analytics Overview',
      children: [
        ResponsiveBuilder(
          builder: (context, deviceType) {
            if (ResponsiveUtils.isMobile(context)) {
              // Stack charts vertically on mobile
              return Column(
                children: [
                  _buildChartPlaceholder(context, 'User Growth', Colors.blue),
                  const SizedBox(height: 16),
                  _buildChartPlaceholder(context, 'Revenue Trends', Colors.green),
                ],
              );
            } else {
              // Side by side on larger screens
              return Row(
                children: [
                  Expanded(
                    child: _buildChartPlaceholder(context, 'User Growth', Colors.blue),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: _buildChartPlaceholder(context, 'Revenue Trends', Colors.green),
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildChartPlaceholder(BuildContext context, String title, Color color) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      height: ResponsiveUtils.getResponsiveValue<double>(
        context,
        mobile: 200,
        tablet: 250,
        desktop: 300,
      ),
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
                const SizedBox(width: 8),
                ResponsiveText(
                  title,
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ],
            ),
          ),
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: ResponsiveText(
                  'Chart Placeholder',
                  mobileFontSize: 12,
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                  color: color,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDemoList(BuildContext context) {
    return ResponsiveDashboardSection(
      title: 'Recent Transactions',
      showDivider: false,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 5,
            separatorBuilder: (context, index) => const Divider(height: 1),
            itemBuilder: (context, index) {
              return ListTile(
                leading: CircleAvatar(
                  backgroundColor: KFTDesignSystem.primaryColor.withOpacity(0.1),
                  child: Icon(
                    Icons.receipt,
                    color: KFTDesignSystem.primaryColor,
                    size: 20,
                  ),
                ),
                title: ResponsiveText(
                  'Transaction #${1000 + index}',
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  fontWeight: FontWeight.w500,
                ),
                subtitle: ResponsiveText(
                  'Payment processed successfully',
                  mobileFontSize: 12,
                  tabletFontSize: 14,
                  desktopFontSize: 16,
                ),
                trailing: ResponsiveText(
                  '\$${(index + 1) * 25}.00',
                  mobileFontSize: 14,
                  tabletFontSize: 16,
                  desktopFontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  void _showDeviceInfoDialog(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final screenSize = MediaQuery.of(context).size;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Device Information'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Device Type: ${deviceType.name}'),
            Text('Screen Width: ${screenSize.width.toInt()}px'),
            Text('Screen Height: ${screenSize.height.toInt()}px'),
            Text('Is Mobile: ${ResponsiveUtils.isMobile(context)}'),
            Text('Is Tablet: ${ResponsiveUtils.isTablet(context)}'),
            Text('Is Desktop: ${ResponsiveUtils.isDesktop(context)}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
