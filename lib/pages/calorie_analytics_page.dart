import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/calorie_log.dart';
import '../models/calorie_goal.dart';
import '../services/calorie_service.dart';
import '../design_system/kft_design_system.dart';

class CalorieAnalyticsPage extends StatefulWidget {
  const CalorieAnalyticsPage({Key? key}) : super(key: key);

  @override
  _CalorieAnalyticsPageState createState() => _CalorieAnalyticsPageState();
}

class _CalorieAnalyticsPageState extends State<CalorieAnalyticsPage> {
  final CalorieService _calorieService = CalorieService();

  bool _isLoading = true;
  Map<DateTime, List<CalorieLog>> _logsMap = {};
  Map<DateTime, DailyCalorieSummary> _summariesMap = {};
  CalorieGoal? _goal;

  // Date range
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 7));
  DateTime _endDate = DateTime.now();

  // Analytics metrics
  int _totalCalories = 0;
  int _avgCalories = 0;
  int _maxCalories = 0;
  int _minCalories = 0;
  int _daysOnTarget = 0;
  int _daysOverTarget = 0;
  int _daysUnderTarget = 0;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load logs for date range
      final logsMap = await _calorieService.getCalorieLogsRange(_startDate, _endDate);

      // Load active goal
      final goal = await _calorieService.getActiveCalorieGoal();

      // Create summaries
      final summariesMap = <DateTime, DailyCalorieSummary>{};
      for (final date in logsMap.keys) {
        final logs = logsMap[date]!;
        summariesMap[date] = DailyCalorieSummary.fromLogs(logs, date, goal: goal);
      }

      // Calculate metrics
      _calculateMetrics(summariesMap, goal);

      setState(() {
        _logsMap = logsMap;
        _summariesMap = summariesMap;
        _goal = goal;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading analytics data: $e');
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error loading analytics data: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _calculateMetrics(Map<DateTime, DailyCalorieSummary> summaries, CalorieGoal? goal) {
    if (summaries.isEmpty) {
      _totalCalories = 0;
      _avgCalories = 0;
      _maxCalories = 0;
      _minCalories = 0;
      _daysOnTarget = 0;
      _daysOverTarget = 0;
      _daysUnderTarget = 0;
      return;
    }

    final values = summaries.values.toList();

    // Total calories
    _totalCalories = values.fold(0, (sum, summary) => sum + summary.totalCalories);

    // Average calories
    _avgCalories = (_totalCalories / values.length).round();

    // Max calories
    _maxCalories = values.fold(0, (max, summary) =>
      summary.totalCalories > max ? summary.totalCalories : max
    );

    // Min calories (excluding zeros)
    final nonZeroValues = values.where((summary) => summary.totalCalories > 0).toList();
    _minCalories = nonZeroValues.isEmpty ? 0 : nonZeroValues.fold(
      nonZeroValues.first.totalCalories,
      (min, summary) => summary.totalCalories < min ? summary.totalCalories : min
    );

    // Days on/over/under target
    if (goal != null) {
      final targetCalories = goal.dailyCalories;
      final targetRange = (targetCalories * 0.05).round(); // 5% margin

      _daysOnTarget = values.where((summary) =>
        summary.totalCalories >= (targetCalories - targetRange) &&
        summary.totalCalories <= (targetCalories + targetRange)
      ).length;

      _daysOverTarget = values.where((summary) =>
        summary.totalCalories > (targetCalories + targetRange)
      ).length;

      _daysUnderTarget = values.where((summary) =>
        summary.totalCalories < (targetCalories - targetRange) &&
        summary.totalCalories > 0
      ).length;
    } else {
      _daysOnTarget = 0;
      _daysOverTarget = 0;
      _daysUnderTarget = 0;
    }
  }

  void _selectDateRange(BuildContext context) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: DateTimeRange(
        start: _startDate,
        end: _endDate,
      ),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });

      _loadData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calorie Analytics'),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _selectDateRange(context),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildAnalyticsView(),
    );
  }

  Widget _buildAnalyticsView() {
    if (_summariesMap.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: 16),
            Text(
              'No data available for the selected period',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _selectDateRange(context),
              child: const Text('Select Different Dates'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date range display
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Date Range',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${DateFormat('MMM d, yyyy').format(_startDate)} - ${DateFormat('MMM d, yyyy').format(_endDate)}',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                ElevatedButton(
                  onPressed: () => _selectDateRange(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.primary,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Change'),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),

          // Summary metrics
          Text(
            'Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildMetricsGrid(),
          const SizedBox(height: 24),

          // Calorie chart
          Text(
            'Daily Calories',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildCalorieChart(),
          const SizedBox(height: 24),

          // Macronutrient distribution
          Text(
            'Macronutrient Distribution',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildMacronutrientChart(),
          const SizedBox(height: 24),

          // Goal adherence
          if (_goal != null) ...[
            Text(
              'Goal Adherence',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildGoalAdherenceChart(),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }

  Widget _buildMetricsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildMetricCard(
          'Total Calories',
          '$_totalCalories',
          'For the selected period',
          Icons.local_fire_department,
          Colors.orange,
        ),
        _buildMetricCard(
          'Average Daily',
          '$_avgCalories',
          'Calories per day',
          Icons.calendar_today,
          Colors.blue,
        ),
        _buildMetricCard(
          'Highest Day',
          '$_maxCalories',
          'Maximum calories',
          Icons.arrow_upward,
          Colors.red,
        ),
        _buildMetricCard(
          'Lowest Day',
          '$_minCalories',
          'Minimum calories',
          Icons.arrow_downward,
          Colors.green,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCalorieChart() {
    // Sort dates
    final sortedDates = _summariesMap.keys.toList()
      ..sort((a, b) => a.compareTo(b));

    // Create bar data
    final barGroups = <BarChartGroupData>[];
    for (int i = 0; i < sortedDates.length; i++) {
      final date = sortedDates[i];
      final summary = _summariesMap[date]!;

      // Determine bar color based on goal
      Color barColor;
      if (_goal != null) {
        if (summary.totalCalories > _goal!.dailyCalories) {
          barColor = Colors.red;
        } else if (summary.totalCalories < _goal!.dailyCalories * 0.8) {
          barColor = Colors.orange;
        } else {
          barColor = Colors.green;
        }
      } else {
        barColor = Theme.of(context).colorScheme.primary;
      }

      barGroups.add(
        BarChartGroupData(
          x: i,
          barRods: [
            BarChartRodData(
              toY: summary.totalCalories.toDouble(),
              color: barColor,
              width: 16,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
            ),
          ],
        ),
      );
    }

    return SizedBox(
      height: 300,
      child: Card(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: (_maxCalories * 1.2).toDouble(),
              barGroups: barGroups,
              titlesData: FlTitlesData(
                show: true,
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= sortedDates.length) {
                        return const SizedBox.shrink();
                      }
                      final date = sortedDates[value.toInt()];
                      return Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          DateFormat('MM/dd').format(date),
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    reservedSize: 40,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        value.toInt().toString(),
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                  ),
                ),
                topTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                rightTitles: AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
              ),
              gridData: FlGridData(
                show: true,
                horizontalInterval: 500,
                getDrawingHorizontalLine: (value) {
                  return FlLine(
                    color: Colors.grey.shade300,
                    strokeWidth: 1,
                  );
                },
              ),
              borderData: FlBorderData(show: false),
              // Add goal line if available
              extraLinesData: _goal != null
                  ? ExtraLinesData(
                      horizontalLines: [
                        HorizontalLine(
                          y: _goal!.dailyCalories.toDouble(),
                          color: Colors.green,
                          strokeWidth: 2,
                          dashArray: [5, 5],
                          label: HorizontalLineLabel(
                            show: true,
                            alignment: Alignment.topRight,
                            padding: const EdgeInsets.only(right: 8, bottom: 4),
                            style: const TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                              fontSize: 10,
                            ),
                            labelResolver: (line) => 'Goal: ${_goal!.dailyCalories}',
                          ),
                        ),
                      ],
                    )
                  : null,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMacronutrientChart() {
    // Calculate average macronutrients
    double totalProtein = 0;
    double totalCarbs = 0;
    double totalFat = 0;
    int count = 0;

    for (final summary in _summariesMap.values) {
      if (summary.totalProtein != null &&
          summary.totalCarbs != null &&
          summary.totalFat != null) {
        totalProtein += summary.totalProtein!;
        totalCarbs += summary.totalCarbs!;
        totalFat += summary.totalFat!;
        count++;
      }
    }

    if (count == 0) {
      return const Center(
        child: Text('No macronutrient data available'),
      );
    }

    final avgProtein = totalProtein / count;
    final avgCarbs = totalCarbs / count;
    final avgFat = totalFat / count;

    // Calculate percentages
    final totalCals = (avgProtein * 4) + (avgCarbs * 4) + (avgFat * 9);
    final proteinPct = totalCals > 0 ? (avgProtein * 4 / totalCals) * 100 : 0;
    final carbsPct = totalCals > 0 ? (avgCarbs * 4 / totalCals) * 100 : 0;
    final fatPct = totalCals > 0 ? (avgFat * 9 / totalCals) * 100 : 0;

    return SizedBox(
      height: 300,
      child: Card(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: PieChart(
                  PieChartData(
                    sections: [
                      PieChartSectionData(
                        value: proteinPct.toDouble(),
                        title: '${proteinPct.toStringAsFixed(0)}%',
                        color: Colors.blue,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      PieChartSectionData(
                        value: carbsPct.toDouble(),
                        title: '${carbsPct.toStringAsFixed(0)}%',
                        color: Colors.orange,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      PieChartSectionData(
                        value: fatPct.toDouble(),
                        title: '${fatPct.toStringAsFixed(0)}%',
                        color: Colors.red,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildMacroLegendItem(
                      'Protein',
                      '${avgProtein.toStringAsFixed(1)}g',
                      '${proteinPct.toStringAsFixed(0)}%',
                      Colors.blue,
                    ),
                    const SizedBox(height: 16),
                    _buildMacroLegendItem(
                      'Carbs',
                      '${avgCarbs.toStringAsFixed(1)}g',
                      '${carbsPct.toStringAsFixed(0)}%',
                      Colors.orange,
                    ),
                    const SizedBox(height: 16),
                    _buildMacroLegendItem(
                      'Fat',
                      '${avgFat.toStringAsFixed(1)}g',
                      '${fatPct.toStringAsFixed(0)}%',
                      Colors.red,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMacroLegendItem(
    String label,
    String value,
    String percentage,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '$value ($percentage)',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildGoalAdherenceChart() {
    if (_goal == null) {
      return const Center(
        child: Text('No calorie goal set'),
      );
    }

    return SizedBox(
      height: 200,
      child: Card(
        elevation: 2,
        shadowColor: Colors.black.withOpacity(0.1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: PieChart(
                  PieChartData(
                    sections: [
                      PieChartSectionData(
                        value: _daysOnTarget.toDouble(),
                        title: _daysOnTarget > 0 ? '$_daysOnTarget' : '',
                        color: Colors.green,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      PieChartSectionData(
                        value: _daysUnderTarget.toDouble(),
                        title: _daysUnderTarget > 0 ? '$_daysUnderTarget' : '',
                        color: Colors.orange,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      PieChartSectionData(
                        value: _daysOverTarget.toDouble(),
                        title: _daysOverTarget > 0 ? '$_daysOverTarget' : '',
                        color: Colors.red,
                        radius: 100,
                        titleStyle: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                    sectionsSpace: 2,
                    centerSpaceRadius: 40,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildGoalLegendItem(
                      'On Target',
                      '$_daysOnTarget days',
                      Colors.green,
                    ),
                    const SizedBox(height: 16),
                    _buildGoalLegendItem(
                      'Under Target',
                      '$_daysUnderTarget days',
                      Colors.orange,
                    ),
                    const SizedBox(height: 16),
                    _buildGoalLegendItem(
                      'Over Target',
                      '$_daysOverTarget days',
                      Colors.red,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoalLegendItem(
    String label,
    String value,
    Color color,
  ) {
    return Row(
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
