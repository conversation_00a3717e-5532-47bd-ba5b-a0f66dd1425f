import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// In-app guide for setting up notifications on different Android devices
class NotificationSetupGuidePage extends StatefulWidget {
  const NotificationSetupGuidePage({super.key});

  @override
  State<NotificationSetupGuidePage> createState() => _NotificationSetupGuidePageState();
}

class _NotificationSetupGuidePageState extends State<NotificationSetupGuidePage> {
  bool _isLoading = false;
  String? _deviceBrand;
  bool _hasAggressivePowerManagement = false;

  @override
  void initState() {
    super.initState();
    _initializeDeviceInfo();
  }

  Future<void> _initializeDeviceInfo() async {
    setState(() {
      _deviceBrand = "Your device";
      _hasAggressivePowerManagement = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('📱 Notification Setup Guide'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(),
            const SizedBox(height: 24),
            
            // Device-specific warning
            if (_hasAggressivePowerManagement) _buildAggressiveWarning(),
            
            // Permission request section
            _buildPermissionSection(),
            const SizedBox(height: 24),
            
            // Device-specific instructions
            _buildDeviceSpecificInstructions(),
            const SizedBox(height: 24),
            
            // General Android instructions
            _buildGeneralInstructions(),
            const SizedBox(height: 24),
            
            // Test section
            _buildTestSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade400, Colors.blue.shade600],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔔 Enable Reliable Notifications',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Follow these steps to ensure your workout and water reminders work perfectly on ${_deviceBrand ?? "your device"}.',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAggressiveWarning() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.orange.shade50,
        border: Border.all(color: Colors.orange.shade300),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(Icons.warning, color: Colors.orange.shade600, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '⚠️ ${_deviceBrand?.toUpperCase()} Device Detected',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Your device has aggressive power management. Extra setup steps are required for reliable notifications.',
                  style: TextStyle(color: Colors.orange.shade700),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🔐 Step 1: Grant Critical Permissions',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'Tap the button below to grant all necessary permissions for notifications:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: _isLoading 
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                    )
                  : const Icon(Icons.security, size: 24),
                label: Text(_isLoading ? 'Requesting Permissions...' : '🔐 Grant All Permissions'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 4,
                ),
                onPressed: _isLoading ? null : _requestAllPermissions,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceSpecificInstructions() {
    if (_deviceBrand == null) return const SizedBox.shrink();

    final instructions = _getDeviceSpecificInstructions(_deviceBrand!);
    if (instructions.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📱 Step 2: ${_deviceBrand!.toUpperCase()} Specific Settings',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...instructions.map((instruction) => _buildInstructionItem(instruction)),
          ],
        ),
      ),
    );
  }

  Widget _buildGeneralInstructions() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '⚙️ Step 3: General Android Settings',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildInstructionItem('🔋 Set battery usage to "Unrestricted"'),
            _buildInstructionItem('🚀 Enable "Auto Start" or "Background Activity"'),
            _buildInstructionItem('🔕 Turn "Do Not Disturb" OFF'),
            _buildInstructionItem('📱 Allow notifications from system settings'),
            _buildInstructionItem('🔒 Enable "Display on Lock Screen"'),
          ],
        ),
      ),
    );
  }

  Widget _buildTestSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '🧪 Step 4: Test Your Setup',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              'After completing the setup, go back to Notification Settings and test your notifications to ensure they work properly.',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                icon: const Icon(Icons.arrow_back, size: 24),
                label: const Text('🔙 Back to Notification Settings'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.green,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 4,
                ),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructionItem(String instruction) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 8,
            height: 8,
            margin: const EdgeInsets.only(top: 6, right: 12),
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          Expanded(
            child: Text(
              instruction,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  List<String> _getDeviceSpecificInstructions(String brand) {
    switch (brand.toLowerCase()) {
      case 'xiaomi':
      case 'redmi':
        return [
          '🔓 Go to Security → Permissions → Autostart → Enable KFT',
          '🔋 Go to Settings → Apps → Manage apps → KFT → Battery saver → "No restrictions"',
          '🔒 Go to Settings → Apps → Manage apps → KFT → Display on lock screen → Enable',
          '⚡ Go to Security → Permissions → Background activity → Enable KFT',
        ];
      case 'oneplus':
      case 'oppo':
      case 'realme':
        return [
          '🔋 Go to Settings → Battery → Battery Optimization → KFT → "Don\'t optimize"',
          '🚀 Go to Settings → Apps → KFT → Battery → "Allow background activity"',
          '🔔 Go to Settings → Apps → KFT → Notifications → Enable all channels',
          '⚡ Go to Settings → Apps → KFT → App Auto-Launch → Enable',
        ];
      case 'vivo':
        return [
          '🔋 Go to Settings → Battery → Background App Refresh → KFT → Enable',
          '🚀 Go to i Manager → App Manager → Autostart Manager → KFT → Enable',
          '🔔 Go to Settings → Notifications → KFT → Enable all',
          '⚡ Go to Settings → More Settings → Permission Management → Auto-start → KFT → Enable',
        ];
      case 'huawei':
      case 'honor':
        return [
          '🔋 Go to Settings → Apps → KFT → Battery → "Manage manually" → Enable all',
          '🚀 Go to Phone Manager → Protected Apps → KFT → Enable',
          '🔔 Go to Settings → Notifications → KFT → Enable all',
          '⚡ Go to Settings → Apps → KFT → Permissions → Enable all',
        ];
      case 'samsung':
        return [
          '🔋 Go to Settings → Apps → KFT → Battery → "Unrestricted"',
          '🚀 Go to Settings → Device care → Battery → App power management → KFT → "Unrestricted"',
          '🔔 Go to Settings → Apps → KFT → Notifications → Enable all channels',
          '⚡ Go to Settings → Apps → KFT → Permissions → Enable all',
        ];
      default:
        return [];
    }
  }

  Future<void> _requestAllPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      // Placeholder for the removed permission service
      final success = true; // Replace with actual implementation
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              success 
                ? '✅ All permissions granted successfully!'
                : '⚠️ Some permissions need manual setup. Please follow the device-specific instructions below.',
            ),
            backgroundColor: success ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error requesting permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }
}
