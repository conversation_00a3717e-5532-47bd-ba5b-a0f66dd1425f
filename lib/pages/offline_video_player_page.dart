import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';
import 'package:path_provider/path_provider.dart';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:io';

class OfflineVideoPlayerPage extends StatefulWidget {
  final String videoId;
  const OfflineVideoPlayerPage({Key? key, required this.videoId}) : super(key: key);

  @override
  State<OfflineVideoPlayerPage> createState() => _OfflineVideoPlayerPageState();
}

class _OfflineVideoPlayerPageState extends State<OfflineVideoPlayerPage> {
  VideoPlayerController? _controller;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAndDecryptVideo();
  }

  Future<void> _loadAndDecryptVideo() async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/video_${widget.videoId}.aes');
      if (!await file.exists()) {
        setState(() {
          _error = 'Offline video not found.';
          _isLoading = false;
        });
        return;
      }
      final encryptedBytes = await file.readAsBytes();
      final storage = const FlutterSecureStorage();
      final keyString = await storage.read(key: 'video_key_${widget.videoId}');
      if (keyString == null) {
        setState(() {
          _error = 'Decryption key not found.';
          _isLoading = false;
        });
        return;
      }
      final key = encrypt.Key.fromBase64(keyString);
      final iv = encrypt.IV.fromLength(16);
      final encrypter = encrypt.Encrypter(encrypt.AES(key));
      final decrypted = encrypter.decryptBytes(
        encrypt.Encrypted(encryptedBytes),
        iv: iv,
      );
      // Write decrypted bytes to a temp file
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/video_${widget.videoId}_decrypted.mp4');
      await tempFile.writeAsBytes(decrypted, flush: true);
      _controller = VideoPlayerController.file(tempFile);
      await _controller!.initialize();
      setState(() {
        _isLoading = false;
      });
      _controller!.play();
    } catch (e) {
      setState(() {
        _error = 'Failed to load offline video: $e';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(title: const Text('Offline Video')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }
    if (_error != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Offline Video')),
        body: Center(child: Text(_error!, style: const TextStyle(color: Colors.red))),
      );
    }
    return Scaffold(
      appBar: AppBar(title: const Text('Offline Video')),
      body: Center(
        child: AspectRatio(
          aspectRatio: _controller!.value.aspectRatio,
          child: VideoPlayer(_controller!),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          setState(() {
            if (_controller!.value.isPlaying) {
              _controller!.pause();
            } else {
              _controller!.play();
            }
          });
        },
        child: Icon(
          _controller!.value.isPlaying ? Icons.pause : Icons.play_arrow,
        ),
      ),
    );
  }
} 