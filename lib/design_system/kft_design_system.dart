import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter/services.dart';
import 'dart:ui';

/// KFT Design System
/// A comprehensive design system for the KFT Fitness app
/// This file contains all the design tokens, components, and styles used throughout the app

class KFTDesignSystem {
  // Private constructor to prevent instantiation
  KFTDesignSystem._();

  /// Colors - Light Theme
  static const Color primaryColor = Color(0xFF3D5AFE);  // Indigo accent
  static const Color primaryDarkColor = Color(0xFF0031CA); // Darker indigo
  static const Color primaryLightColor = Color(0xFF8187FF); // Lighter indigo

  static const Color secondaryColor = Color(0xFF00BFA5);  // Teal accent
  static const Color secondaryDarkColor = Color(0xFF008E76); // Darker teal
  static const Color secondaryLightColor = Color(0xFF5DF2D6); // Lighter teal

  static const Color accentColor = Color(0xFFFF4081);  // Pink accent

  static const Color successColor = Color(0xFF00C853);  // Green
  static const Color warningColor = Color(0xFFFFAB00);  // Amber
  static const Color errorColor = Color(0xFFFF5252);    // Red
  static const Color infoColor = Color(0xFF2196F3);     // Blue

  static const Color surfaceColor = Colors.white;
  static const Color backgroundColor = Color(0xFFF5F5F7); // Light gray with slight blue tint
  static const Color cardColor = Colors.white;

  static const Color textPrimaryColor = Color(0xFF212121); // Almost black
  static const Color textSecondaryColor = Color(0xFF757575); // Medium gray
  static const Color textTertiaryColor = Color(0xFFBDBDBD); // Light gray

  static const Color dividerColor = Color(0xFFEEEEEE); // Very light gray
  static const Color borderColor = Color(0xFFE0E0E0); // Light gray

  /// Colors - Dark Theme
  static const Color darkSurfaceColor = Color(0xFF121212); // Material dark surface
  static const Color darkBackgroundColor = Color(0xFF121212); // Material dark background
  static const Color darkCardColor = Color(0xFF1E1E1E); // Slightly lighter than background

  static const Color darkTextPrimaryColor = Color(0xFFFFFFFF); // White
  static const Color darkTextSecondaryColor = Color(0xFFB3B3B3); // Light gray
  static const Color darkTextTertiaryColor = Color(0xFF757575); // Medium gray

  static const Color darkDividerColor = Color(0xFF2C2C2C); // Dark gray
  static const Color darkBorderColor = Color(0xFF2C2C2C); // Dark gray

  /// Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, primaryDarkColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryColor, secondaryDarkColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [accentColor, Color(0xFFD81B60)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  /// Spacing - Refined for pixel-perfect alignment
  static const double spacingXxs = 2.0;
  static const double spacingXs = 4.0;
  static const double spacingSm = 8.0;
  static const double spacingMd = 16.0;
  static const double spacingLg = 24.0;
  static const double spacingXl = 32.0;
  static const double spacingXxl = 48.0;
  static const double spacingHuge = 64.0;

  /// Border Radius - Refined for consistent UI
  static const double borderRadiusXs = 4.0;
  static const double borderRadiusSm = 8.0;
  static const double borderRadiusMd = 12.0;
  static const double borderRadiusLg = 16.0;
  static const double borderRadiusXl = 20.0; // Adjusted for better visual harmony
  static const double borderRadiusXxl = 28.0; // Adjusted for better visual harmony
  static const double borderRadiusCircular = 100.0;

  /// Border Width
  static const double borderWidthThin = 0.5;
  static const double borderWidthRegular = 1.0;
  static const double borderWidthThick = 1.5;
  static const double borderWidthFocus = 2.0;

  /// Theme-aware color getters
  static Color getSurfaceColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkSurfaceColor
        : surfaceColor;
  }

  static Color getBackgroundColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBackgroundColor
        : backgroundColor;
  }

  static Color getCardColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkCardColor
        : cardColor;
  }

  static Color getTextPrimaryColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextPrimaryColor
        : textPrimaryColor;
  }

  static Color getTextSecondaryColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextSecondaryColor
        : textSecondaryColor;
  }

  static Color getTextTertiaryColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkTextTertiaryColor
        : textTertiaryColor;
  }

  static Color getDividerColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkDividerColor
        : dividerColor;
  }

  static Color getBorderColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? darkBorderColor
        : borderColor;
  }

  /// Shadows - Refined for subtle depth and pixel-perfect appearance
  static List<BoxShadow> get shadowSm => [
    BoxShadow(
      color: Colors.black.withOpacity(0.03),
      blurRadius: 8,
      spreadRadius: 0,
      offset: const Offset(0, 1),
    ),
  ];

  static List<BoxShadow> get shadowMd => [
    BoxShadow(
      color: Colors.black.withOpacity(0.04),
      blurRadius: 12,
      spreadRadius: 0,
      offset: const Offset(0, 2),
    ),
  ];

  static List<BoxShadow> get shadowLg => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 16,
      spreadRadius: 0,
      offset: const Offset(0, 4),
    ),
  ];

  /// Card-specific shadows
  static List<BoxShadow> get cardShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.03),
      blurRadius: 12,
      spreadRadius: 0,
      offset: const Offset(0, 2),
    ),
  ];

  /// Typography - Refined for pixel-perfect text rendering
  static const String fontFamily = 'Inter';

  // Font weights
  static const FontWeight fontWeightLight = FontWeight.w300;
  static const FontWeight fontWeightRegular = FontWeight.w400;
  static const FontWeight fontWeightMedium = FontWeight.w500;
  static const FontWeight fontWeightSemiBold = FontWeight.w600;
  static const FontWeight fontWeightBold = FontWeight.w700;

  // Line heights
  static const double lineHeightTight = 1.2;
  static const double lineHeightNormal = 1.5;
  static const double lineHeightRelaxed = 1.8;

  static const TextStyle displayLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: fontWeightBold,
    letterSpacing: -0.5,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28,
    fontWeight: fontWeightBold,
    letterSpacing: -0.5,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24,
    fontWeight: fontWeightBold,
    letterSpacing: -0.25,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 22,
    fontWeight: fontWeightSemiBold,
    letterSpacing: -0.25,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 20,
    fontWeight: fontWeightSemiBold,
    letterSpacing: -0.25,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 18,
    fontWeight: fontWeightSemiBold,
    letterSpacing: -0.25,
    height: lineHeightTight,
    color: textPrimaryColor,
  );

  static const TextStyle titleLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: fontWeightSemiBold,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: fontWeightSemiBold,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: fontWeightSemiBold,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: fontWeightRegular,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: fontWeightRegular,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: fontWeightRegular,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textSecondaryColor,
  );

  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: fontWeightMedium,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12,
    fontWeight: fontWeightMedium,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textPrimaryColor,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 10,
    fontWeight: fontWeightMedium,
    letterSpacing: 0,
    height: lineHeightNormal,
    color: textSecondaryColor,
  );

  /// SnackBar Helpers
  static const double paddingValue = 16.0;
  static const double paddingSm = 8.0;
  
  static SnackBar infoSnackBar(String message) {
    return SnackBar(
      content: Text(message),
      backgroundColor: Colors.blue,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 3),
    );
  }

  static SnackBar successSnackBar(String message) {
    return SnackBar(
      content: Text(message),
      backgroundColor: Colors.green,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 3),
    );
  }

  static SnackBar errorSnackBar(String message) {
    return SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
      behavior: SnackBarBehavior.floating,
      duration: const Duration(seconds: 3),
    );
  }
}
