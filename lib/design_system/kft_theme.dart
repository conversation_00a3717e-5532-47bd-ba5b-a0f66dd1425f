import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'kft_design_system.dart';

/// KFT Theme
/// This file contains the theme configuration for the KFT Fitness app
/// It uses the KFTDesignSystem to create a consistent theme across the app

class KFTTheme {
  // Private constructor to prevent instantiation
  KFTTheme._();

  /// Light Theme
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme(
        brightness: Brightness.light,
        primary: KFTDesignSystem.primaryColor,
        onPrimary: Colors.white,
        primaryContainer: KFTDesignSystem.primaryLightColor,
        onPrimaryContainer: Colors.white,
        secondary: KFTDesignSystem.secondaryColor,
        onSecondary: Colors.white,
        secondaryContainer: KFTDesignSystem.secondaryLightColor,
        onSecondaryContainer: Colors.white,
        tertiary: KFTDesignSystem.accentColor,
        onTertiary: Colors.white,
        tertiaryContainer: KFTDesignSystem.accentColor.withOpacity(0.1),
        onTertiaryContainer: KFTDesignSystem.accentColor,
        error: KFTDesignSystem.errorColor,
        onError: Colors.white,
        errorContainer: KFTDesignSystem.errorColor.withOpacity(0.1),
        onErrorContainer: KFTDesignSystem.errorColor,
        background: KFTDesignSystem.backgroundColor,
        onBackground: KFTDesignSystem.textPrimaryColor,
        surface: KFTDesignSystem.surfaceColor,
        onSurface: KFTDesignSystem.textPrimaryColor,
        surfaceVariant: KFTDesignSystem.surfaceColor.withOpacity(0.7),
        onSurfaceVariant: KFTDesignSystem.textSecondaryColor,
        outline: KFTDesignSystem.borderColor,
        outlineVariant: KFTDesignSystem.dividerColor,
        shadow: Colors.black.withOpacity(0.1),
        scrim: Colors.black.withOpacity(0.3),
        inverseSurface: KFTDesignSystem.textPrimaryColor,
        onInverseSurface: KFTDesignSystem.surfaceColor,
        inversePrimary: KFTDesignSystem.primaryLightColor,
        surfaceTint: KFTDesignSystem.primaryColor.withOpacity(0.05),
      ),

      // Typography
      fontFamily: KFTDesignSystem.fontFamily,
      textTheme: TextTheme(
        displayLarge: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        displayMedium: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        displaySmall: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.25),
        headlineLarge: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        headlineMedium: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        headlineSmall: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        titleLarge: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(color: KFTDesignSystem.textPrimaryColor),
        bodyMedium: TextStyle(color: KFTDesignSystem.textPrimaryColor),
        bodySmall: TextStyle(color: KFTDesignSystem.textSecondaryColor),
        labelLarge: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(color: KFTDesignSystem.textPrimaryColor, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(color: KFTDesignSystem.textSecondaryColor, fontWeight: FontWeight.w500),
      ),

      // Component Themes
      appBarTheme: AppBarTheme(
        backgroundColor: KFTDesignSystem.surfaceColor,
        foregroundColor: KFTDesignSystem.textPrimaryColor,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: KFTDesignSystem.textPrimaryColor,
          fontWeight: FontWeight.w600,
          fontSize: 18,
          letterSpacing: -0.5,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.dark.copyWith(
          statusBarColor: Colors.transparent,
        ),
        iconTheme: IconThemeData(color: KFTDesignSystem.textPrimaryColor, size: 24),
        actionsIconTheme: IconThemeData(color: KFTDesignSystem.textPrimaryColor, size: 24),
      ),

      cardTheme: CardTheme(
        color: KFTDesignSystem.surfaceColor,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
          side: BorderSide(color: KFTDesignSystem.borderColor.withOpacity(0.1), width: 0.5),
        ),
        margin: const EdgeInsets.all(12),
        shadowColor: Colors.black.withOpacity(0.08),
        clipBehavior: Clip.antiAlias,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: KFTDesignSystem.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 18),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          textStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.2,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: KFTDesignSystem.primaryColor,
          side: BorderSide(color: KFTDesignSystem.primaryColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: TextStyle(
            color: KFTDesignSystem.primaryColor,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.2,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: KFTDesignSystem.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          textStyle: TextStyle(
            color: KFTDesignSystem.primaryColor,
            fontWeight: FontWeight.w500,
            fontSize: 15,
          ),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: KFTDesignSystem.surfaceColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(color: KFTDesignSystem.borderColor.withOpacity(0.2)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(color: KFTDesignSystem.borderColor.withOpacity(0.2)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(color: KFTDesignSystem.primaryColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(20),
          borderSide: BorderSide(color: KFTDesignSystem.errorColor),
        ),
        labelStyle: TextStyle(
          color: KFTDesignSystem.textPrimaryColor,
          fontWeight: FontWeight.w500,
          fontSize: 15,
        ),
        hintStyle: TextStyle(
          color: KFTDesignSystem.textSecondaryColor,
          fontSize: 15,
        ),
        prefixIconColor: KFTDesignSystem.primaryColor,
        suffixIconColor: KFTDesignSystem.textSecondaryColor,
      ),

      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: KFTDesignSystem.surfaceColor,
        selectedItemColor: KFTDesignSystem.primaryColor,
        unselectedItemColor: KFTDesignSystem.textSecondaryColor,
        selectedLabelStyle: TextStyle(
          color: KFTDesignSystem.primaryColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: TextStyle(
          color: KFTDesignSystem.textSecondaryColor,
          fontSize: 12,
        ),
        showSelectedLabels: true,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Other
      scaffoldBackgroundColor: KFTDesignSystem.backgroundColor,
      dividerTheme: DividerThemeData(
        color: KFTDesignSystem.dividerColor,
        thickness: 0.5,
        space: 16,
      ),

      // Chip theme
      chipTheme: ChipThemeData(
        backgroundColor: KFTDesignSystem.surfaceColor,
        disabledColor: KFTDesignSystem.surfaceColor.withOpacity(0.5),
        selectedColor: KFTDesignSystem.primaryColor.withOpacity(0.1),
        secondarySelectedColor: KFTDesignSystem.primaryColor.withOpacity(0.2),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        labelStyle: TextStyle(color: KFTDesignSystem.textPrimaryColor),
        secondaryLabelStyle: TextStyle(color: KFTDesignSystem.primaryColor),
        brightness: Brightness.light,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: KFTDesignSystem.borderColor),
        ),
      ),

      // Slider theme
      sliderTheme: SliderThemeData(
        activeTrackColor: KFTDesignSystem.primaryColor,
        inactiveTrackColor: KFTDesignSystem.primaryColor.withOpacity(0.2),
        thumbColor: KFTDesignSystem.primaryColor,
        overlayColor: KFTDesignSystem.primaryColor.withOpacity(0.1),
        valueIndicatorColor: KFTDesignSystem.primaryColor,
        valueIndicatorTextStyle: const TextStyle(color: Colors.white),
      ),

      // Animations
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }

  /// Dark Theme
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme(
        brightness: Brightness.dark,
        primary: KFTDesignSystem.primaryLightColor,
        onPrimary: Colors.black,
        primaryContainer: KFTDesignSystem.primaryDarkColor,
        onPrimaryContainer: Colors.white,
        secondary: KFTDesignSystem.secondaryLightColor,
        onSecondary: Colors.black,
        secondaryContainer: KFTDesignSystem.secondaryDarkColor,
        onSecondaryContainer: Colors.white,
        tertiary: KFTDesignSystem.accentColor,
        onTertiary: Colors.black,
        tertiaryContainer: KFTDesignSystem.accentColor.withOpacity(0.2),
        onTertiaryContainer: KFTDesignSystem.accentColor,
        error: KFTDesignSystem.errorColor,
        onError: Colors.black,
        errorContainer: KFTDesignSystem.errorColor.withOpacity(0.2),
        onErrorContainer: KFTDesignSystem.errorColor,
        background: KFTDesignSystem.darkBackgroundColor,
        onBackground: KFTDesignSystem.darkTextPrimaryColor,
        surface: KFTDesignSystem.darkSurfaceColor,
        onSurface: KFTDesignSystem.darkTextPrimaryColor,
        surfaceVariant: KFTDesignSystem.darkSurfaceColor.withOpacity(0.7),
        onSurfaceVariant: KFTDesignSystem.darkTextSecondaryColor,
        outline: KFTDesignSystem.darkBorderColor,
        outlineVariant: KFTDesignSystem.darkDividerColor,
        shadow: Colors.black.withOpacity(0.3),
        scrim: Colors.black.withOpacity(0.5),
        inverseSurface: KFTDesignSystem.darkTextPrimaryColor,
        onInverseSurface: KFTDesignSystem.darkSurfaceColor,
        inversePrimary: KFTDesignSystem.primaryDarkColor,
        surfaceTint: KFTDesignSystem.primaryColor.withOpacity(0.1),
      ),

      // Typography
      fontFamily: KFTDesignSystem.fontFamily,
      textTheme: TextTheme(
        displayLarge: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        displayMedium: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        displaySmall: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.bold, letterSpacing: -0.25),
        headlineLarge: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        headlineMedium: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        headlineSmall: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600, letterSpacing: -0.25),
        titleLarge: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600),
        titleMedium: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600),
        titleSmall: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w600),
        bodyLarge: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor),
        bodyMedium: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor),
        bodySmall: TextStyle(color: KFTDesignSystem.darkTextSecondaryColor),
        labelLarge: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w500),
        labelMedium: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w500),
        labelSmall: TextStyle(color: KFTDesignSystem.darkTextSecondaryColor, fontWeight: FontWeight.w500),
      ),

      // Component Themes
      appBarTheme: AppBarTheme(
        backgroundColor: KFTDesignSystem.darkSurfaceColor,
        foregroundColor: KFTDesignSystem.darkTextPrimaryColor,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: TextStyle(
          color: KFTDesignSystem.darkTextPrimaryColor,
          fontWeight: FontWeight.w600,
          fontSize: 18,
          letterSpacing: -0.5,
        ),
        systemOverlayStyle: SystemUiOverlayStyle.light.copyWith(
          statusBarColor: Colors.transparent,
        ),
        iconTheme: IconThemeData(color: KFTDesignSystem.darkTextPrimaryColor, size: 24),
        actionsIconTheme: IconThemeData(color: KFTDesignSystem.darkTextPrimaryColor, size: 24),
      ),

      cardTheme: CardTheme(
        color: KFTDesignSystem.darkCardColor,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: KFTDesignSystem.darkBorderColor, width: 0.5),
        ),
        margin: const EdgeInsets.all(8),
        shadowColor: Colors.black.withOpacity(0.3),
        clipBehavior: Clip.antiAlias,
      ),

      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: KFTDesignSystem.primaryLightColor,
          foregroundColor: Colors.black,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.2,
          ),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: KFTDesignSystem.primaryLightColor,
          side: BorderSide(color: KFTDesignSystem.primaryLightColor, width: 1.5),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          textStyle: TextStyle(
            color: KFTDesignSystem.primaryLightColor,
            fontWeight: FontWeight.w600,
            fontSize: 16,
            letterSpacing: 0.2,
          ),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: KFTDesignSystem.primaryLightColor,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          textStyle: TextStyle(
            color: KFTDesignSystem.primaryLightColor,
            fontWeight: FontWeight.w500,
            fontSize: 15,
          ),
        ),
      ),

      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: KFTDesignSystem.darkCardColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: KFTDesignSystem.darkBorderColor),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: KFTDesignSystem.darkBorderColor),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: KFTDesignSystem.primaryLightColor, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: KFTDesignSystem.errorColor),
        ),
        labelStyle: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor, fontWeight: FontWeight.w500),
        hintStyle: TextStyle(color: KFTDesignSystem.darkTextSecondaryColor),
      ),

      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: KFTDesignSystem.darkSurfaceColor,
        selectedItemColor: KFTDesignSystem.primaryLightColor,
        unselectedItemColor: KFTDesignSystem.darkTextSecondaryColor,
        selectedLabelStyle: TextStyle(
          color: KFTDesignSystem.primaryLightColor,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: TextStyle(
          color: KFTDesignSystem.darkTextSecondaryColor,
          fontSize: 12,
        ),
        showSelectedLabels: true,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),

      // Other
      scaffoldBackgroundColor: KFTDesignSystem.darkBackgroundColor,
      dividerTheme: DividerThemeData(
        color: KFTDesignSystem.darkDividerColor,
        thickness: 0.5,
        space: 16,
      ),

      // Chip theme
      chipTheme: ChipThemeData(
        backgroundColor: KFTDesignSystem.darkCardColor,
        disabledColor: KFTDesignSystem.darkCardColor.withOpacity(0.5),
        selectedColor: KFTDesignSystem.primaryLightColor.withOpacity(0.2),
        secondarySelectedColor: KFTDesignSystem.primaryLightColor.withOpacity(0.3),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        labelStyle: TextStyle(color: KFTDesignSystem.darkTextPrimaryColor),
        secondaryLabelStyle: TextStyle(color: KFTDesignSystem.primaryLightColor),
        brightness: Brightness.dark,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(color: KFTDesignSystem.darkBorderColor),
        ),
      ),

      // Slider theme
      sliderTheme: SliderThemeData(
        activeTrackColor: KFTDesignSystem.primaryLightColor,
        inactiveTrackColor: KFTDesignSystem.primaryLightColor.withOpacity(0.2),
        thumbColor: KFTDesignSystem.primaryLightColor,
        overlayColor: KFTDesignSystem.primaryLightColor.withOpacity(0.1),
        valueIndicatorColor: KFTDesignSystem.primaryLightColor,
        valueIndicatorTextStyle: const TextStyle(color: Colors.black),
      ),

      // Animations
      pageTransitionsTheme: const PageTransitionsTheme(
        builders: {
          TargetPlatform.android: CupertinoPageTransitionsBuilder(),
          TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        },
      ),
    );
  }
}
