import 'package:flutter/material.dart';

class SpecialCategoryWorkoutsPage extends StatefulWidget {
  const SpecialCategoryWorkoutsPage({Key? key}) : super(key: key);

  @override
  State<SpecialCategoryWorkoutsPage> createState() => _SpecialCategoryWorkoutsPageState();
}

class _SpecialCategoryWorkoutsPageState extends State<SpecialCategoryWorkoutsPage> {
  // Selected filter category
  String _selectedCategory = 'All';
  
  // Selected user goal
  String _selectedGoal = 'Build Muscle';
  
  // List of workout categories
  final List<String> _categories = [
    'All',
    'Abs',
    'Chest',
    'Arms',
    'Back',
    'Legs',
    'Shoulders',
    'Cardio',
    'Full Body',
  ];
  
  // List of user goals
  final List<String> _goals = [
    'Build Muscle',
    'Lose Weight',
    'Improve Endurance',
    'Increase Flexibility',
    'General Fitness',
  ];
  
  // Workout data - in a real app, this would come from a database
  final List<Map<String, dynamic>> _workouts = [
    {
      'title': 'Six-Pack Abs',
      'category': 'Abs',
      'duration': '20 min',
      'level': 'Intermediate',
      'color': Colors.blue,
      'recommended': ['Build Muscle', 'Lose Weight'],
    },
    {
      'title': 'Chest Sculptor',
      'category': 'Chest',
      'duration': '25 min',
      'level': 'Advanced',
      'color': Colors.green,
      'recommended': ['Build Muscle'],
    },
    {
      'title': 'Arm Blaster',
      'category': 'Arms',
      'duration': '15 min',
      'level': 'Beginner',
      'color': Colors.orange,
      'recommended': ['Build Muscle'],
    },
    {
      'title': 'Back Definition',
      'category': 'Back',
      'duration': '30 min',
      'level': 'Intermediate',
      'color': Colors.purple,
      'recommended': ['Build Muscle', 'General Fitness'],
    },
    {
      'title': 'Leg Day',
      'category': 'Legs',
      'duration': '35 min',
      'level': 'Advanced',
      'color': Colors.red,
      'recommended': ['Build Muscle', 'Improve Endurance'],
    },
    {
      'title': 'Shoulder Press',
      'category': 'Shoulders',
      'duration': '20 min',
      'level': 'Intermediate',
      'color': Colors.teal,
      'recommended': ['Build Muscle'],
    },
    {
      'title': 'HIIT Cardio',
      'category': 'Cardio',
      'duration': '25 min',
      'level': 'Advanced',
      'color': Colors.pink,
      'recommended': ['Lose Weight', 'Improve Endurance'],
    },
    {
      'title': 'Total Body Burn',
      'category': 'Full Body',
      'duration': '45 min',
      'level': 'Advanced',
      'color': Colors.amber,
      'recommended': ['Lose Weight', 'General Fitness'],
    },
    {
      'title': 'Core Crusher',
      'category': 'Abs',
      'duration': '15 min',
      'level': 'Beginner',
      'color': Colors.indigo,
      'recommended': ['Build Muscle', 'General Fitness'],
    },
    {
      'title': 'Bicep Builder',
      'category': 'Arms',
      'duration': '20 min',
      'level': 'Intermediate',
      'color': Colors.cyan,
      'recommended': ['Build Muscle'],
    },
    {
      'title': 'Leg Toner',
      'category': 'Legs',
      'duration': '25 min',
      'level': 'Beginner',
      'color': Colors.deepOrange,
      'recommended': ['Lose Weight', 'General Fitness'],
    },
    {
      'title': 'Flexibility Flow',
      'category': 'Full Body',
      'duration': '30 min',
      'level': 'Beginner',
      'color': Colors.lightBlue,
      'recommended': ['Increase Flexibility', 'General Fitness'],
    },
  ];

  // Filtered workouts based on selected category and goal
  List<Map<String, dynamic>> get filteredWorkouts {
    return _workouts.where((workout) {
      // Filter by category
      final categoryMatch = _selectedCategory == 'All' || workout['category'] == _selectedCategory;
      
      // Filter by recommended goal
      final List<String> recommended = workout['recommended'] as List<String>;
      final goalMatch = recommended.contains(_selectedGoal);
      
      return categoryMatch && goalMatch;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text(
          'Special Workouts',
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.search, color: Colors.grey.shade700),
            onPressed: () {
              // Search functionality would go here
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Category filter
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Filter by Target Area',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 40,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _categories.length,
                    itemBuilder: (context, index) {
                      final category = _categories[index];
                      final isSelected = category == _selectedCategory;
                      
                      return Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category),
                          selected: isSelected,
                          onSelected: (selected) {
                            setState(() {
                              _selectedCategory = category;
                            });
                          },
                          backgroundColor: Colors.grey.shade100,
                          selectedColor: primaryColor.withOpacity(0.15),
                          checkmarkColor: primaryColor,
                          labelStyle: TextStyle(
                            color: isSelected ? primaryColor : Colors.grey.shade800,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20),
                            side: BorderSide(
                              color: isSelected ? primaryColor : Colors.transparent,
                              width: 1,
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          
          // Goal selector
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Goal',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: _selectedGoal,
                      isExpanded: true,
                      icon: const Icon(Icons.keyboard_arrow_down),
                      elevation: 0,
                      style: TextStyle(
                        color: Colors.grey.shade800,
                        fontSize: 16,
                      ),
                      onChanged: (String? newValue) {
                        if (newValue != null) {
                          setState(() {
                            _selectedGoal = newValue;
                          });
                        }
                      },
                      items: _goals.map<DropdownMenuItem<String>>((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // Results count
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${filteredWorkouts.length} workouts found',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: Colors.grey.shade600,
                  ),
                ),
                TextButton.icon(
                  icon: Icon(Icons.sort, size: 18, color: primaryColor),
                  label: Text(
                    'Sort',
                    style: TextStyle(color: primaryColor),
                  ),
                  onPressed: () {
                    // Sort functionality would go here
                  },
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.zero,
                    minimumSize: const Size(0, 0),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ),
              ],
            ),
          ),
          
          // Workout grid
          Expanded(
            child: filteredWorkouts.isEmpty
                ? _buildEmptyState(context)
                : GridView.builder(
                    padding: const EdgeInsets.all(16),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      childAspectRatio: 0.75,
                      crossAxisSpacing: 16,
                      mainAxisSpacing: 16,
                    ),
                    itemCount: filteredWorkouts.length,
                    itemBuilder: (context, index) {
                      final workout = filteredWorkouts[index];
                      return _buildWorkoutCard(context, workout);
                    },
                  ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.fitness_center,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'No workouts found',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try changing your filters',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildWorkoutCard(BuildContext context, Map<String, dynamic> workout) {
    final theme = Theme.of(context);
    final Color workoutColor = workout['color'] as Color;
    
    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: () {
          // Open workout details
          showDialog(
            context: context,
            builder: (_) => AlertDialog(
              title: Text(workout['title']),
              content: Text('This is a ${workout['category']} workout.'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Close'),
                ),
              ],
            ),
          );
        },
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Workout image/color area
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                color: workoutColor.withOpacity(0.8),
                child: Stack(
                  children: [
                    // This would be an Image widget in a real app
                    // Image.asset('assets/workouts/${workout['category'].toLowerCase()}.jpg', fit: BoxFit.cover),
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.6),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          workout['category'],
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    Center(
                      child: Icon(
                        _getCategoryIcon(workout['category']),
                        color: Colors.white,
                        size: 40,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            // Workout details
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      workout['title'],
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${workout['duration']} • ${workout['level']}',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const Spacer(),
                    Row(
                      children: [
                        Icon(
                          Icons.recommend,
                          size: 14,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Recommended',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  IconData _getCategoryIcon(String category) {
    switch (category) {
      case 'Abs':
        return Icons.sports_gymnastics;
      case 'Chest':
        return Icons.fitness_center;
      case 'Arms':
        return Icons.front_hand;
      case 'Back':
        return Icons.accessibility_new;
      case 'Legs':
        return Icons.directions_run;
      case 'Shoulders':
        return Icons.fitness_center;
      case 'Cardio':
        return Icons.monitor_heart;
      case 'Full Body':
        return Icons.accessibility;
      default:
        return Icons.fitness_center;
    }
  }
}
