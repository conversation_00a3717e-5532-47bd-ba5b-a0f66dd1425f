class UpcomingEvent {
  final int id;
  final String title;
  final String description;
  final DateTime dateTime;
  final String type; // 'workout', 'class', 'challenge', etc.
  final String? imageUrl;
  final bool isLive;
  final int participantsCount;

  UpcomingEvent({
    required this.id,
    required this.title,
    required this.description,
    required this.dateTime,
    required this.type,
    this.imageUrl,
    this.isLive = false,
    this.participantsCount = 0,
  });

  // Format the date for display
  String get formattedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    final eventDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (eventDate.isAtSameMomentAs(today)) {
      return 'Today at ${_formatTime(dateTime)}';
    } else if (eventDate.isAtSameMomentAs(tomorrow)) {
      return 'Tomorrow at ${_formatTime(dateTime)}';
    } else {
      return '${_formatDate(dateTime)} at ${_formatTime(dateTime)}';
    }
  }

  // Format time as HH:MM AM/PM
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }

  // Format date as Month Day
  String _formatDate(DateTime dateTime) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[dateTime.month - 1]} ${dateTime.day}';
  }

  // Get icon data based on event type
  String get typeIcon {
    switch (type) {
      case 'workout':
        return 'fitness_center';
      case 'class':
        return 'groups';
      case 'challenge':
        return 'emoji_events';
      case 'webinar':
        return 'video_camera_front';
      default:
        return 'event';
    }
  }

  // Get color based on event type
  String get typeColor {
    switch (type) {
      case 'workout':
        return 'blue';
      case 'class':
        return 'purple';
      case 'challenge':
        return 'orange';
      case 'webinar':
        return 'green';
      default:
        return 'grey';
    }
  }

  // Factory method to create mock events
  static List<UpcomingEvent> getMockEvents() {
    final now = DateTime.now();
    
    return [
      UpcomingEvent(
        id: 1,
        title: 'HIIT Workout Session',
        description: 'High-intensity interval training with Coach Mike',
        dateTime: now.add(const Duration(hours: 3)),
        type: 'workout',
        participantsCount: 12,
      ),
      UpcomingEvent(
        id: 2,
        title: 'Yoga for Beginners',
        description: 'Learn the basics of yoga with Coach Sarah',
        dateTime: now.add(const Duration(days: 1, hours: 2)),
        type: 'class',
        isLive: true,
        participantsCount: 8,
      ),
      UpcomingEvent(
        id: 3,
        title: '30-Day Fitness Challenge',
        description: 'Join our monthly fitness challenge',
        dateTime: now.add(const Duration(days: 2)),
        type: 'challenge',
        participantsCount: 45,
      ),
      UpcomingEvent(
        id: 4,
        title: 'Nutrition Webinar',
        description: 'Learn about proper nutrition for fitness',
        dateTime: now.add(const Duration(days: 3, hours: 4)),
        type: 'webinar',
        isLive: true,
        participantsCount: 32,
      ),
    ];
  }
}
