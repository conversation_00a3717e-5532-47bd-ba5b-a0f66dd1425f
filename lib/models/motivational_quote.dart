class MotivationalQuote {
  final int? id;
  final String quote;
  final String? author;
  final String? category;
  final bool isAiGenerated;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? viewedAt; // When the quote was last viewed by the user
  final int viewCount; // How many times the quote has been viewed
  final bool isFavorite; // Whether the user has favorited this quote

  MotivationalQuote({
    this.id,
    required this.quote,
    this.author,
    this.category,
    this.isAiGenerated = false,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
    this.viewedAt,
    this.viewCount = 0,
    this.isFavorite = false,
  }) :
    this.createdAt = createdAt ?? DateTime.now(),
    this.updatedAt = updatedAt ?? DateTime.now();

  factory MotivationalQuote.fromJson(Map<String, dynamic> json) {
    return MotivationalQuote(
      id: json['id'] != null ? int.parse(json['id'].toString()) : null,
      quote: json['quote'] ?? '',
      author: json['author'],
      category: json['category'],
      isAiGenerated: json['is_ai_generated'] == 1 || json['is_ai_generated'] == true,
      isActive: json['is_active'] == 1 || json['is_active'] == true,
      createdAt: json['created_at'] != null
        ? DateTime.parse(json['created_at'])
        : DateTime.now(),
      updatedAt: json['updated_at'] != null
        ? DateTime.parse(json['updated_at'])
        : DateTime.now(),
      viewedAt: json['viewed_at'] != null
        ? DateTime.parse(json['viewed_at'])
        : null,
      viewCount: json['view_count'] ?? 0,
      isFavorite: json['is_favorite'] == 1 || json['is_favorite'] == true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'quote': quote,
      'author': author,
      'category': category,
      'is_ai_generated': isAiGenerated ? 1 : 0,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'viewed_at': viewedAt?.toIso8601String(),
      'view_count': viewCount,
      'is_favorite': isFavorite ? 1 : 0,
    };
  }

  MotivationalQuote copyWith({
    int? id,
    String? quote,
    String? author,
    String? category,
    bool? isAiGenerated,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? viewedAt,
    int? viewCount,
    bool? isFavorite,
  }) {
    return MotivationalQuote(
      id: id ?? this.id,
      quote: quote ?? this.quote,
      author: author ?? this.author,
      category: category ?? this.category,
      isAiGenerated: isAiGenerated ?? this.isAiGenerated,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      viewedAt: viewedAt ?? this.viewedAt,
      viewCount: viewCount ?? this.viewCount,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }

  // Mark the quote as viewed
  MotivationalQuote markAsViewed() {
    return copyWith(
      viewedAt: DateTime.now(),
      viewCount: viewCount + 1,
    );
  }

  // Toggle favorite status
  MotivationalQuote toggleFavorite() {
    return copyWith(
      isFavorite: !isFavorite,
    );
  }

  String get displayAuthor => author?.isNotEmpty == true ? author! : 'Unknown';

  String get displayCategory => category?.isNotEmpty == true
    ? category!.substring(0, 1).toUpperCase() + category!.substring(1)
    : 'General';

  // Get category icon
  String get categoryIcon {
    final cat = category?.toLowerCase() ?? '';

    if (cat.contains('weight') || cat.contains('loss')) {
      return '⚖️';
    } else if (cat.contains('workout') || cat.contains('exercise')) {
      return '💪';
    } else if (cat.contains('mental') || cat.contains('mind')) {
      return '🧠';
    } else if (cat.contains('nutrition') || cat.contains('food')) {
      return '🥗';
    } else if (cat.contains('fitness') || cat.contains('health')) {
      return '🏃';
    } else {
      return '✨';
    }
  }

  @override
  String toString() {
    return '"$quote" - $displayAuthor';
  }
}

class QuotePreferences {
  final int userId;
  final String? preferredCategories;
  final bool personalizationEnabled;
  final bool deepSeekEnabled;
  final int? lastQuoteId;
  final DateTime? lastQuoteDate;
  final bool enabled; // Whether quotes feature is enabled
  final bool showOnHomeScreen; // Whether to show quotes on home screen
  final bool showOnLockScreen; // Whether to show quotes on lock screen
  final int frequencyPerDay; // How many quotes to show per day
  final List<int> viewedQuoteIds; // IDs of quotes that have been viewed

  QuotePreferences({
    required this.userId,
    this.preferredCategories,
    this.personalizationEnabled = true,
    this.deepSeekEnabled = false,
    this.lastQuoteId,
    this.lastQuoteDate,
    this.enabled = true,
    this.showOnHomeScreen = true,
    this.showOnLockScreen = false,
    this.frequencyPerDay = 1,
    this.viewedQuoteIds = const [],
  });

  factory QuotePreferences.fromJson(Map<String, dynamic> json) {
    List<int> viewedIds = [];
    if (json['viewed_quote_ids'] != null) {
      if (json['viewed_quote_ids'] is String) {
        viewedIds = (json['viewed_quote_ids'] as String)
            .split(',')
            .where((id) => id.isNotEmpty)
            .map((id) => int.parse(id))
            .toList();
      } else if (json['viewed_quote_ids'] is List) {
        viewedIds = (json['viewed_quote_ids'] as List)
            .map((id) => int.parse(id.toString()))
            .toList();
      }
    }

    return QuotePreferences(
      userId: int.parse(json['user_id'].toString()),
      preferredCategories: json['preferred_categories'],
      personalizationEnabled: json['personalization_enabled'] == 1 || json['personalization_enabled'] == true,
      deepSeekEnabled: json['deepseek_enabled'] == 1 || json['deepseek_enabled'] == true,
      lastQuoteId: json['last_quote_id'] != null ? int.parse(json['last_quote_id'].toString()) : null,
      lastQuoteDate: json['last_quote_date'] != null ? DateTime.parse(json['last_quote_date']) : null,
      enabled: json['enabled'] == 1 || json['enabled'] == true,
      showOnHomeScreen: json['show_on_home_screen'] == 1 || json['show_on_home_screen'] == true,
      showOnLockScreen: json['show_on_lock_screen'] == 1 || json['show_on_lock_screen'] == true,
      frequencyPerDay: json['frequency_per_day'] ?? 1,
      viewedQuoteIds: viewedIds,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'preferred_categories': preferredCategories,
      'personalization_enabled': personalizationEnabled ? 1 : 0,
      'deepseek_enabled': deepSeekEnabled ? 1 : 0,
      'last_quote_id': lastQuoteId,
      'last_quote_date': lastQuoteDate?.toIso8601String(),
      'enabled': enabled ? 1 : 0,
      'show_on_home_screen': showOnHomeScreen ? 1 : 0,
      'show_on_lock_screen': showOnLockScreen ? 1 : 0,
      'frequency_per_day': frequencyPerDay,
      'viewed_quote_ids': viewedQuoteIds.join(','),
    };
  }

  List<String> get categoriesList => preferredCategories?.split(',') ?? [];

  // Check if a quote has been viewed
  bool hasViewedQuote(int quoteId) {
    return viewedQuoteIds.contains(quoteId);
  }

  // Add a quote to the viewed list
  QuotePreferences addViewedQuote(int quoteId) {
    if (hasViewedQuote(quoteId)) {
      return this;
    }

    final updatedViewedIds = List<int>.from(viewedQuoteIds)..add(quoteId);
    return copyWith(
      viewedQuoteIds: updatedViewedIds,
      lastQuoteId: quoteId,
      lastQuoteDate: DateTime.now(),
    );
  }

  // Reset viewed quotes
  QuotePreferences resetViewedQuotes() {
    return copyWith(
      viewedQuoteIds: [],
    );
  }

  // Toggle enabled status
  QuotePreferences toggleEnabled() {
    return copyWith(
      enabled: !enabled,
    );
  }

  QuotePreferences copyWith({
    int? userId,
    String? preferredCategories,
    bool? personalizationEnabled,
    bool? deepSeekEnabled,
    int? lastQuoteId,
    DateTime? lastQuoteDate,
    bool? enabled,
    bool? showOnHomeScreen,
    bool? showOnLockScreen,
    int? frequencyPerDay,
    List<int>? viewedQuoteIds,
  }) {
    return QuotePreferences(
      userId: userId ?? this.userId,
      preferredCategories: preferredCategories ?? this.preferredCategories,
      personalizationEnabled: personalizationEnabled ?? this.personalizationEnabled,
      deepSeekEnabled: deepSeekEnabled ?? this.deepSeekEnabled,
      lastQuoteId: lastQuoteId ?? this.lastQuoteId,
      lastQuoteDate: lastQuoteDate ?? this.lastQuoteDate,
      enabled: enabled ?? this.enabled,
      showOnHomeScreen: showOnHomeScreen ?? this.showOnHomeScreen,
      showOnLockScreen: showOnLockScreen ?? this.showOnLockScreen,
      frequencyPerDay: frequencyPerDay ?? this.frequencyPerDay,
      viewedQuoteIds: viewedQuoteIds ?? this.viewedQuoteIds,
    );
  }
}
