class CourseCategory {
  final String name;
  final String displayName;
  final String? description;
  final String? iconName;
  
  CourseCategory({
    required this.name,
    required this.displayName,
    this.description,
    this.iconName,
  });
  
  // Factory method to create a category from a string
  factory CourseCategory.fromString(String categoryName) {
    // Convert category name to display name (e.g., "beginner" -> "Beginner")
    final displayName = categoryName.isNotEmpty 
        ? categoryName[0].toUpperCase() + categoryName.substring(1) 
        : 'General';
    
    // Determine icon based on category name
    String? iconName;
    switch (categoryName.toLowerCase()) {
      case 'beginner':
        iconName = 'fitness_center';
        break;
      case 'intermediate':
        iconName = 'directions_run';
        break;
      case 'advanced':
        iconName = 'sports_martial_arts';
        break;
      case 'premium':
        iconName = 'star';
        break;
      case 'nutrition':
        iconName = 'restaurant';
        break;
      case 'yoga':
        iconName = 'self_improvement';
        break;
      default:
        iconName = 'school';
    }
    
    return CourseCategory(
      name: categoryName,
      displayName: displayName,
      iconName: iconName,
    );
  }
  
  // Predefined categories
  static List<CourseCategory> getPredefinedCategories() {
    return [
      CourseCategory(
        name: 'beginner',
        displayName: 'Beginner',
        description: 'Perfect for those just starting their fitness journey',
        iconName: 'fitness_center',
      ),
      CourseCategory(
        name: 'intermediate',
        displayName: 'Intermediate',
        description: 'For those with some fitness experience',
        iconName: 'directions_run',
      ),
      CourseCategory(
        name: 'advanced',
        displayName: 'Advanced',
        description: 'Challenging workouts for experienced fitness enthusiasts',
        iconName: 'sports_martial_arts',
      ),
      CourseCategory(
        name: 'premium',
        displayName: 'Premium',
        description: 'Exclusive courses with advanced techniques',
        iconName: 'star',
      ),
      CourseCategory(
        name: 'nutrition',
        displayName: 'Nutrition',
        description: 'Learn about healthy eating and meal planning',
        iconName: 'restaurant',
      ),
      CourseCategory(
        name: 'yoga',
        displayName: 'Yoga & Mindfulness',
        description: 'Improve flexibility, balance, and mental wellbeing',
        iconName: 'self_improvement',
      ),
    ];
  }
}
