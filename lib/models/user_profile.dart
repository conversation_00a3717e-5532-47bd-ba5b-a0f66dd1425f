import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/painting.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../services/api_service.dart';

class UserProfile {
  String name;
  String username;
  String email;
  String phoneNumber;
  double height; // in cm
  double weight; // in kg
  bool isPremium;
  String profileImageUrl;
  List<BMIRecord> bmiHistory;
  List<WorkoutRecord> workoutHistory;
  List<DateTime> streakDays;
  int? assignedStaffId;
  double bmi;
  double? bodyFat; // in percentage
  double? muscleMass; // in percentage
  double? waterPercentage; // in percentage

  UserProfile({
    this.name = 'User',
    this.username = '',
    this.email = '',
    this.phoneNumber = '',
    this.height = 170.0,
    this.weight = 70.0,
    this.isPremium = false,
    this.profileImageUrl = '',
    List<BMIRecord>? bmiHistory,
    List<WorkoutRecord>? workoutHistory,
    List<DateTime>? streakDays,
    this.assignedStaffId,
    this.bmi = 0.0,
    this.bodyFat,
    this.muscleMass,
    this.waterPercentage,
  })  : bmiHistory = bmiHistory ?? [],
        workoutHistory = workoutHistory ?? [],
        streakDays = streakDays ?? [];

  // Calculate current BMI
  double get currentBMI {
    if (height <= 0) return 0;
    return weight / ((height / 100) * (height / 100));
  }

  // Get BMI category
  String get bmiCategory {
    final bmi = currentBMI;
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }

  // Get current streak
  int get currentStreak {
    if (streakDays.isEmpty) return 0;

    // Sort streak days
    final sortedDays = List<DateTime>.from(streakDays)
      ..sort((a, b) => b.compareTo(a));

    // Check if today is in the streak
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);

    if (sortedDays.isEmpty ||
        !_isSameDay(sortedDays.first, todayDate) &&
        !_isSameDay(sortedDays.first, todayDate.subtract(const Duration(days: 1)))) {
      return 0;
    }

    int streak = 1;
    for (int i = 0; i < sortedDays.length - 1; i++) {
      final current = sortedDays[i];
      final next = sortedDays[i + 1];

      if (_isSameDay(current.subtract(const Duration(days: 1)), next)) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  // Get total workout time in minutes
  int get totalWorkoutTime {
    return workoutHistory.fold(0, (sum, workout) => sum + workout.durationMinutes);
  }

  // Get average workout time in minutes
  double get averageWorkoutTime {
    if (workoutHistory.isEmpty) return 0;
    return totalWorkoutTime / workoutHistory.length;
  }

  // Get total number of workouts
  int get totalWorkouts {
    return workoutHistory.length;
  }

  // Add a BMI record
  void addBMIRecord(double weight) {
    this.weight = weight;
    final bmi = currentBMI;
    bmiHistory.add(BMIRecord(
      date: DateTime.now(),
      bmi: bmi,
      weight: weight,
    ));
  }

  // Add a workout record
  void addWorkoutRecord(String title, int durationMinutes) {
    final workout = WorkoutRecord(
      date: DateTime.now(),
      title: title,
      durationMinutes: durationMinutes,
    );
    workoutHistory.add(workout);

    // Add to streak if not already recorded for today
    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);

    if (!streakDays.any((date) => _isSameDay(date, todayDate))) {
      streakDays.add(todayDate);
    }
  }

  // Check if two dates are the same day
  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'username': username,
      'email': email,
      'phoneNumber': phoneNumber,
      'height': height,
      'weight': weight,
      'isPremium': isPremium,
      'profileImageUrl': profileImageUrl,
      'bmiHistory': bmiHistory.map((record) => record.toJson()).toList(),
      'workoutHistory': workoutHistory.map((record) => record.toJson()).toList(),
      'streakDays': streakDays.map((date) => date.toIso8601String()).toList(),
      'assignedStaffId': assignedStaffId,
      'bmi': bmi,
      'bmiCategory': bmiCategory,
      'bodyFat': bodyFat,
      'muscleMass': muscleMass,
      'waterPercentage': waterPercentage,
    };
  }

  // Create from JSON
  factory UserProfile.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse double values
    double parseDoubleSafely(dynamic value, double defaultValue) {
      if (value == null) return defaultValue;
      if (value is num) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (_) {
          return defaultValue;
        }
      }
      return defaultValue;
    }

    // Helper function to safely parse nullable double values
    double? parseNullableDouble(dynamic value) {
      if (value == null) return null;
      if (value is num) return value.toDouble();
      if (value is String) {
        try {
          return double.parse(value);
        } catch (_) {
          return null;
        }
      }
      return null;
    }

    // Helper function to safely parse lists
    List<T> parseListSafely<T>(dynamic value, T Function(Map<String, dynamic>) fromJson) {
      if (value == null || value is! List) return [];
      return value.where((item) => item is Map).map((item) {
        try {
          final map = item is Map<String, dynamic> ? item : Map<String, dynamic>.from(item);
          return fromJson(map);
        } catch (e) {
          print('Error parsing list item: $e');
          return null;
        }
      }).where((item) => item != null).cast<T>().toList();
    }

    return UserProfile(
      name: json['name']?.toString() ?? 'User',
      username: json['username']?.toString() ?? '',
      email: json['email']?.toString() ?? '',
      phoneNumber: json['phoneNumber']?.toString() ?? json['phone_number']?.toString() ?? '',
      height: parseDoubleSafely(json['height'], 170.0),
      weight: parseDoubleSafely(json['weight'], 70.0),
      isPremium: json['isPremium'] == true || json['isPremium'] == 1 || json['is_premium'] == true || json['is_premium'] == 1,
      profileImageUrl: json['profileImageUrl']?.toString() ?? json['profile_image_url']?.toString() ?? '',
      bmiHistory: parseListSafely(json['bmiHistory'], BMIRecord.fromJson),
      workoutHistory: parseListSafely(json['workoutHistory'], WorkoutRecord.fromJson),
      streakDays: (json['streakDays'] as List<dynamic>?)?.map((date) {
        try {
          return DateTime.parse(date.toString());
        } catch (e) {
          print('Error parsing streak day: $e');
          return null;
        }
      }).where((date) => date != null).cast<DateTime>().toList() ?? [],
      assignedStaffId: json['assignedStaffId'] ?? json['assigned_staff_id'],
      bmi: parseDoubleSafely(json['bmi'], 0.0),
      bodyFat: parseNullableDouble(json['bodyFat'] ?? json['body_fat']),
      muscleMass: parseNullableDouble(json['muscleMass'] ?? json['muscle_mass']),
      waterPercentage: parseNullableDouble(json['waterPercentage'] ?? json['water_percentage']),
    );
  }
}

class BMIRecord {
  final DateTime date;
  final double bmi;
  final double weight;

  BMIRecord({
    required this.date,
    required this.bmi,
    required this.weight,
  });

  // Format date as string
  String get formattedDate {
    return DateFormat('MMM d, yyyy').format(date);
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'bmi': bmi,
      'weight': weight,
    };
  }

  // Create from JSON
  factory BMIRecord.fromJson(Map<String, dynamic> json) {
    return BMIRecord(
      date: DateTime.parse(json['date'] as String),
      bmi: json['bmi'] as double,
      weight: json['weight'] as double,
    );
  }
}

class WorkoutRecord {
  final DateTime date;
  final String title;
  final int durationMinutes;

  WorkoutRecord({
    required this.date,
    required this.title,
    required this.durationMinutes,
  });

  // Format date as string
  String get formattedDate {
    return DateFormat('MMM d, yyyy').format(date);
  }

  // Format duration as string
  String get formattedDuration {
    final hours = durationMinutes ~/ 60;
    final minutes = durationMinutes % 60;

    if (hours > 0) {
      return '$hours hr ${minutes > 0 ? '$minutes min' : ''}';
    } else {
      return '$minutes min';
    }
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'date': date.toIso8601String(),
      'title': title,
      'durationMinutes': durationMinutes,
    };
  }

  // Create from JSON
  factory WorkoutRecord.fromJson(Map<String, dynamic> json) {
    return WorkoutRecord(
      date: DateTime.parse(json['date'] as String),
      title: json['title'] as String,
      durationMinutes: json['durationMinutes'] as int,
    );
  }
}

class UserProfileProvider extends ChangeNotifier {
  UserProfile? _profile;
  // Keep track of when the profile image was last updated
  int _profileImageTimestamp = 0;

  UserProfile? get profile => _profile;

  // Getter for profile image timestamp for cache busting
  int get profileImageTimestamp => _profileImageTimestamp;

  void setProfile(UserProfile profile) {
    _profile = profile;

    // Save the profile to local storage
    _saveProfileToLocal(profile);

    notifyListeners();
  }

  // Save profile to local storage
  Future<void> _saveProfileToLocal(UserProfile profile) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final profileJson = jsonEncode(profile.toJson());
      await prefs.setString('user_profile', profileJson);
      print('Profile saved to local storage with image URL: ${profile.profileImageUrl}');
    } catch (e) {
      print('Error saving profile to local storage: $e');
    }
  }

  void updateProfile(UserProfile profile) {
    _profile = profile;

    // Save the updated profile to local storage
    _saveProfileToLocal(profile);

    notifyListeners();
  }

  // Get profile image URL with cache busting parameter
  String getProfileImageUrl() {
    if (_profile == null || _profile!.profileImageUrl.isEmpty) {
      return '';
    }

    String baseUrl = _profile!.profileImageUrl;
    print('Original profile image URL: $baseUrl');

    // Check if the URL is a relative path and convert it to an absolute URL
    if (!baseUrl.startsWith('http://') && !baseUrl.startsWith('https://')) {
      // Use the API base URL from ApiService
      final apiBaseUrl = ApiService.baseUrl.replaceAll('/api', '');

      // Remove leading slash if present
      if (baseUrl.startsWith('/')) {
        baseUrl = baseUrl.substring(1);
      }

      baseUrl = '$apiBaseUrl/$baseUrl';
      print('Converted to absolute URL: $baseUrl');
    }

    // Add cache busting parameter
    final separator = baseUrl.contains('?') ? '&' : '?';
    final finalUrl = '$baseUrl${separator}t=$_profileImageTimestamp';
    print('Final URL with cache busting: $finalUrl');

    return finalUrl;
  }

  // Helper method to check if a URL exists
  Future<void> _checkUrlExists(String url) async {
    try {
      final response = await http.head(Uri.parse(url));
      print('URL check status code: ${response.statusCode} for URL: $url');
    } catch (e) {
      print('Error checking URL: $e for URL: $url');
    }
  }
}
