import 'package:intl/intl.dart';

class UserStreak {
  final int currentStreak;
  final int longestStreak;
  final DateTime lastActivityDate;
  final List<DateTime> activityDates;

  UserStreak({
    required this.currentStreak,
    required this.longestStreak,
    required this.lastActivityDate,
    required this.activityDates,
  });

  factory UserStreak.initial() {
    return UserStreak(
      currentStreak: 0,
      longestStreak: 0,
      lastActivityDate: DateTime.now().subtract(const Duration(days: 1)),
      activityDates: [],
    );
  }

  factory UserStreak.fromJson(Map<String, dynamic> json) {
    return UserStreak(
      currentStreak: json['current_streak'] ?? 0,
      longestStreak: json['longest_streak'] ?? 0,
      lastActivityDate: json['last_activity_date'] != null
          ? DateTime.parse(json['last_activity_date'])
          : DateTime.now().subtract(const Duration(days: 1)),
      activityDates: json['activity_dates'] != null
          ? (json['activity_dates'] as List)
              .map((date) => DateTime.parse(date))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_streak': currentStreak,
      'longest_streak': longestStreak,
      'last_activity_date': DateFormat('yyyy-MM-dd').format(lastActivityDate),
      'activity_dates': activityDates
          .map((date) => DateFormat('yyyy-MM-dd').format(date))
          .toList(),
    };
  }

  // Check if the user has been active today
  bool get isActiveToday {
    final today = DateTime.now();
    return activityDates.any((date) =>
        date.year == today.year &&
        date.month == today.month &&
        date.day == today.day);
  }

  // Get the percentage of days active in the current week
  double getWeeklyProgress() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    
    int daysInWeek = 7;
    int daysActiveThisWeek = 0;
    
    for (var date in activityDates) {
      if (date.isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
          date.isBefore(now.add(const Duration(days: 1)))) {
        daysActiveThisWeek++;
      }
    }
    
    return daysActiveThisWeek / daysInWeek;
  }
}
