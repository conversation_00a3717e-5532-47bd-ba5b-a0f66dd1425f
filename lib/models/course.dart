import 'course_video.dart';

class Course {
  final int id;
  final String title;
  final String description;
  final String? thumbnailUrl;
  final int durationWeeks;
  final String startDate;
  final String? endDate;
  final String status;
  final int totalVideos;
  final int unlockedVideos;
  final int completedVideos;
  final int progressPercentage;
  final int? progress;
  final String imageUrl;
  final String? instructor;
  final String? level;
  List<CourseVideo>? videos;

  Course({
    required this.id,
    required this.title,
    required this.description,
    this.thumbnailUrl,
    required this.durationWeeks,
    required this.startDate,
    this.endDate,
    required this.status,
    required this.totalVideos,
    required this.unlockedVideos,
    required this.completedVideos,
    required this.progressPercentage,
    this.progress,
    String? imageUrl,
    this.instructor,
    this.level,
    this.videos,
  }) : imageUrl = imageUrl ?? thumbnailUrl ?? '';

  factory Course.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse integers
    int parseIntSafely(dynamic value, int defaultValue) {
      if (value == null) return defaultValue;
      if (value is int) return value;
      if (value is String) {
        try {
          return int.parse(value);
        } catch (_) {
          return defaultValue;
        }
      }
      if (value is double) return value.toInt();
      if (value is num) return value.toInt();
      return defaultValue;
    }

    // Helper function to safely convert values to string
    String? toStringNullable(dynamic value) {
      if (value == null) return null;
      if (value is String) return value.isEmpty ? null : value;
      return value.toString();
    }

    // Calculate progress if not provided
    int progressPercentage = parseIntSafely(json['progress_percentage'], 0);
    int? progress = json['progress'] != null ? parseIntSafely(json['progress'], 0) : progressPercentage;

    return Course(
      id: parseIntSafely(json['id'], 0),
      title: toStringNullable(json['title']) ?? 'Untitled Course',
      description: toStringNullable(json['description']) ?? '',
      thumbnailUrl: toStringNullable(json['thumbnail_url']),
      durationWeeks: parseIntSafely(json['duration_weeks'], 1),
      startDate: toStringNullable(json['start_date']) ?? DateTime.now().toString(),
      endDate: toStringNullable(json['end_date']),
      status: toStringNullable(json['status']) ?? 'active',
      totalVideos: parseIntSafely(json['total_videos'], 0),
      unlockedVideos: parseIntSafely(json['unlocked_videos'], 0),
      completedVideos: parseIntSafely(json['completed_videos'], 0),
      progressPercentage: progressPercentage,
      progress: progress,
      imageUrl: toStringNullable(json['image_url']) ?? toStringNullable(json['thumbnail_url']) ?? '',
      instructor: toStringNullable(json['instructor']),
      level: toStringNullable(json['level']) ?? toStringNullable(json['difficulty']),
      videos: json['videos'] != null ? List<CourseVideo>.from(json['videos'].map((x) => CourseVideo.fromJson(x))) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'thumbnail_url': thumbnailUrl,
      'duration_weeks': durationWeeks,
      'start_date': startDate,
      'end_date': endDate,
      'status': status,
      'total_videos': totalVideos,
      'unlocked_videos': unlockedVideos,
      'completed_videos': completedVideos,
      'progress_percentage': progressPercentage,
      'progress': progress,
      'image_url': imageUrl,
      'instructor': instructor,
      'level': level,
      'videos': videos?.map((v) => v.toJson()).toList(),
    };
  }
}
