import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

/// Represents a single nutrition story item (like a WhatsApp status)
class NutritionStory {
  final String id;
  final String title;
  final String description;
  final Color color;
  final IconData icon;
  final List<NutritionStoryItem> items;
  final DateTime createdAt;
  bool isViewed;

  NutritionStory({
    required this.id,
    required this.title,
    required this.description,
    required this.color,
    required this.icon,
    required this.items,
    DateTime? createdAt,
    this.isViewed = false,
  }) : this.createdAt = createdAt ?? DateTime.now();
}

/// Represents a single page within a nutrition story
class NutritionStoryItem {
  final String id;
  final String title;
  final Widget content;
  final Duration duration;
  bool isViewed;

  NutritionStoryItem({
    required this.id,
    required this.title,
    required this.content,
    this.duration = const Duration(seconds: 30), // 30-second duration by default
    this.isViewed = false,
  });
}

/// Factory to create nutrition stories with predefined content
class NutritionStoryFactory {
  static List<NutritionStory> createNutritionStories(BuildContext context) {
    return [
      _createProteinStory(context),
      _createCarbsStory(context),
      _createFatsStory(context),
      _createFiberStory(context),
    ];
  }

  static NutritionStory _createProteinStory(BuildContext context) {
    final theme = Theme.of(context);
    final color = Colors.red.shade400;

    return NutritionStory(
      id: 'protein',
      title: 'Protein',
      description: 'Build & repair tissues',
      color: color,
      icon: FontAwesomeIcons.drumstickBite,
      items: [
        NutritionStoryItem(
          id: 'protein_intro',
          title: 'What is Protein?',
          content: _buildInfoCard(
            context,
            title: 'Protein',
            description: 'Protein is the building block of life, essential for muscle growth, tissue repair, and enzyme production. It plays a crucial role in your immune system and helps maintain a healthy metabolism.',
            recommendations: 'Active adults should aim for 1.2-2.0g of protein per kg of body weight daily for optimal muscle recovery and growth.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1606914501449-5a96b6ce24ca?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'protein_benefits',
          title: 'Benefits of Protein',
          content: _buildInfoCard(
            context,
            title: 'Why Protein Matters',
            description: 'Adequate protein intake helps build lean muscle, speeds recovery after workouts, reduces hunger, boosts metabolism, and helps maintain weight loss. It is particularly important for active individuals and those looking to transform their physique.',
            recommendations: 'Spread your protein intake throughout the day for optimal absorption. Aim for 20-30g per meal.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1583454110551-21f2fa2afe61?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'animal_protein',
          title: 'Animal Protein Sources',
          content: _buildFoodList(
            context,
            title: 'Premium Animal Protein',
            items: [
              {'name': 'Chicken Breast', 'value': '31g protein', 'calories': '165 cal'},
              {'name': 'Wild Salmon', 'value': '25g protein', 'calories': '208 cal'},
              {'name': 'Tuna Steak', 'value': '30g protein', 'calories': '144 cal'},
              {'name': 'Grass-fed Beef', 'value': '26g protein', 'calories': '250 cal'},
              {'name': 'Greek Yogurt', 'value': '10g protein', 'calories': '59 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1615937722923-67f6deaf2cc9?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'plant_protein',
          title: 'Plant Protein Sources',
          content: _buildFoodList(
            context,
            title: 'Plant-Based Protein',
            items: [
              {'name': 'Tempeh', 'value': '19g protein', 'calories': '195 cal'},
              {'name': 'Edamame', 'value': '17g protein', 'calories': '189 cal'},
              {'name': 'Lentils', 'value': '18g protein', 'calories': '230 cal'},
              {'name': 'Quinoa', 'value': '8g protein', 'calories': '222 cal'},
              {'name': 'Chia Seeds', 'value': '5g protein', 'calories': '138 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1515543904379-3d757afe72e4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
      ],
    );
  }

  static NutritionStory _createCarbsStory(BuildContext context) {
    final theme = Theme.of(context);
    final color = Colors.blue.shade400;

    return NutritionStory(
      id: 'carbs',
      title: 'Carbs',
      description: 'Energy source',
      color: color,
      icon: FontAwesomeIcons.breadSlice,
      items: [
        NutritionStoryItem(
          id: 'carbs_intro',
          title: 'What are Carbohydrates?',
          content: _buildInfoCard(
            context,
            title: 'Carbohydrates',
            description: 'Carbohydrates are your body\'s primary fuel source, powering everything from high-intensity workouts to basic brain function. They are essential for optimal athletic performance and recovery.',
            recommendations: 'Active individuals should consume 3-5g of carbs per kg of body weight daily, with higher amounts needed for intense training days.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1490818387583-1baba5e638af?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'carbs_timing',
          title: 'Carb Timing',
          content: _buildInfoCard(
            context,
            title: 'Strategic Carb Timing',
            description: 'When you eat carbs matters almost as much as what you eat. Pre-workout carbs fuel performance, while post-workout carbs replenish glycogen stores and enhance recovery. Carb cycling—alternating between high and low carb days—can optimize body composition.',
            recommendations: 'Consume 25-40g of fast-digesting carbs before workouts and 50-100g of carbs within 30 minutes after training for optimal recovery.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1498837167922-ddd27525d352?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'complex_carbs',
          title: 'Complex Carbohydrates',
          content: _buildFoodList(
            context,
            title: 'Complex Carbs',
            items: [
              {'name': 'Sweet Potato', 'value': '20g carbs', 'calories': '86 cal'},
              {'name': 'Quinoa', 'value': '21g carbs', 'calories': '120 cal'},
              {'name': 'Oats', 'value': '27g carbs', 'calories': '158 cal'},
              {'name': 'Brown Rice', 'value': '23g carbs', 'calories': '112 cal'},
              {'name': 'Beans', 'value': '20g carbs', 'calories': '127 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1536304993881-ff6e9eefa2a6?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'simple_carbs',
          title: 'Simple Carbohydrates',
          content: _buildFoodList(
            context,
            title: 'Fast-Acting Carbs',
            items: [
              {'name': 'Banana', 'value': '23g carbs', 'calories': '89 cal'},
              {'name': 'Rice Cakes', 'value': '7g carbs', 'calories': '35 cal'},
              {'name': 'Honey', 'value': '17g carbs', 'calories': '64 cal'},
              {'name': 'Dried Fruit', 'value': '22g carbs', 'calories': '96 cal'},
              {'name': 'Sports Drink', 'value': '15g carbs', 'calories': '60 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1550258987-190a2d41a8ba?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
      ],
    );
  }

  static NutritionStory _createFatsStory(BuildContext context) {
    final theme = Theme.of(context);
    final color = Colors.amber.shade400;

    return NutritionStory(
      id: 'fats',
      title: 'Fats',
      description: 'Hormone production',
      color: color,
      icon: FontAwesomeIcons.oilWell,
      items: [
        NutritionStoryItem(
          id: 'fats_intro',
          title: 'What are Healthy Fats?',
          content: _buildInfoCard(
            context,
            title: 'Essential Fats',
            description: 'Dietary fats are crucial for hormone optimization, brain function, and nutrient absorption. They are not just calories but signaling molecules that influence everything from muscle growth to fat loss.',
            recommendations: 'Aim for 0.5-1g of fat per kg of body weight daily, with at least 30% coming from omega-3 rich sources.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1519623286359-e9f3cbef015b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'fat_benefits',
          title: 'Benefits of Healthy Fats',
          content: _buildInfoCard(
            context,
            title: 'Fat Benefits',
            description: 'Healthy fats optimize testosterone and growth hormone levels, improve recovery, reduce inflammation, enhance brain function, and help maintain healthy skin. They are also crucial for vitamin D, E, A, and K absorption.',
            recommendations: 'Include a source of healthy fat with each meal to optimize hormone production and nutrient absorption.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1615485290382-441e4d049cb5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'healthy_fats',
          title: 'Healthy Fat Sources',
          content: _buildFoodList(
            context,
            title: 'Premium Healthy Fats',
            items: [
              {'name': 'Wild Salmon', 'value': '13g fat', 'calories': '208 cal'},
              {'name': 'Avocado', 'value': '15g fat', 'calories': '160 cal'},
              {'name': 'Extra Virgin Olive Oil', 'value': '14g fat', 'calories': '119 cal'},
              {'name': 'Walnuts', 'value': '18g fat', 'calories': '185 cal'},
              {'name': 'Flaxseeds', 'value': '6g fat', 'calories': '55 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-**********-133340f12ecd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'cooking_fats',
          title: 'Cooking Fats',
          content: _buildFoodList(
            context,
            title: 'Cooking Fats',
            items: [
              {'name': 'Coconut Oil', 'value': '14g fat', 'calories': '121 cal'},
              {'name': 'Grass-fed Butter', 'value': '11g fat', 'calories': '102 cal'},
              {'name': 'Avocado Oil', 'value': '14g fat', 'calories': '124 cal'},
              {'name': 'Ghee', 'value': '13g fat', 'calories': '112 cal'},
              {'name': 'MCT Oil', 'value': '14g fat', 'calories': '115 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1620405116976-f735da963c02?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
      ],
    );
  }

  static NutritionStory _createFiberStory(BuildContext context) {
    final theme = Theme.of(context);
    final color = Colors.green.shade400;

    return NutritionStory(
      id: 'fiber',
      title: 'Fiber',
      description: 'Digestive health',
      color: color,
      icon: FontAwesomeIcons.carrot,
      items: [
        NutritionStoryItem(
          id: 'fiber_intro',
          title: 'What is Fiber?',
          content: _buildInfoCard(
            context,
            title: 'Dietary Fiber',
            description: 'Fiber is the unsung hero of nutrition—it controls hunger, stabilizes blood sugar, improves gut health, and even helps with fat loss. Both soluble and insoluble fiber are essential for optimal health and performance.',
            recommendations: 'Active adults should aim for 30-35g of fiber daily, with a mix of soluble and insoluble sources. Increase intake gradually to avoid digestive discomfort.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1610832958506-aa56368176cf?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'fiber_benefits',
          title: 'Benefits of Fiber',
          content: _buildInfoCard(
            context,
            title: 'Fiber Benefits',
            description: 'Beyond digestive health, fiber helps control appetite, improves insulin sensitivity, lowers cholesterol, feeds beneficial gut bacteria, and may reduce the risk of certain cancers. It is also crucial for maintaining a healthy weight.',
            recommendations: 'Spread fiber intake throughout the day and drink plenty of water to maximize benefits.',
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1563379926898-05f4575a45d8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'soluble_fiber',
          title: 'Soluble Fiber Sources',
          content: _buildFoodList(
            context,
            title: 'Soluble Fiber',
            items: [
              {'name': 'Oats', 'value': '5g fiber', 'calories': '150 cal'},
              {'name': 'Apples', 'value': '4g fiber', 'calories': '95 cal'},
              {'name': 'Flaxseeds', 'value': '3g fiber', 'calories': '55 cal'},
              {'name': 'Beans', 'value': '6g fiber', 'calories': '127 cal'},
              {'name': 'Psyllium Husk', 'value': '7g fiber', 'calories': '30 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1495078065017-564723e7e3e7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
        NutritionStoryItem(
          id: 'insoluble_fiber',
          title: 'Insoluble Fiber Sources',
          content: _buildFoodList(
            context,
            title: 'Insoluble Fiber',
            items: [
              {'name': 'Broccoli', 'value': '2.6g fiber', 'calories': '55 cal'},
              {'name': 'Nuts', 'value': '3g fiber', 'calories': '170 cal'},
              {'name': 'Whole Grains', 'value': '3.5g fiber', 'calories': '120 cal'},
              {'name': 'Berries', 'value': '4g fiber', 'calories': '85 cal'},
              {'name': 'Leafy Greens', 'value': '2g fiber', 'calories': '30 cal'},
            ],
            color: color,
            imageUrl: 'https://images.unsplash.com/photo-1610348725531-843dff563e2c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&q=80',
          ),
        ),
      ],
    );
  }

  static Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required String description,
    required String recommendations,
    required Color color,
    String? imageUrl,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        color: Colors.black,
      ),
      child: Stack(
        children: [
          // Background image or gradient
          Positioned.fill(
            child: imageUrl != null
                ? Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              color,
                              color.withOpacity(0.7),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          color,
                          color.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
          ),

          // Enhanced overlay for better text readability
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withOpacity(0.85),  // Darker at bottom for text contrast
                    Colors.black.withOpacity(0.6),   // Medium darkness in middle
                    Colors.black.withOpacity(0.3),   // Lighter at top to show image
                  ],
                  stops: const [0.0, 0.5, 0.85],
                ),
              ),
            ),
          ),

          // Additional text clarity layer
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerRight,
                  end: Alignment.centerLeft,
                  colors: [
                    Colors.black.withOpacity(0.2),
                    Colors.black.withOpacity(0.4),
                    Colors.black.withOpacity(0.2),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          ),

          // Content - centered vertically
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                // Title with improved readability
                Text(
                  title,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: -0.3,
                    fontSize: 28,
                    height: 1.2,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Description with improved readability
                Text(
                  description,
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: Colors.white,
                    height: 1.6,
                    letterSpacing: 0.3,
                    fontSize: 16,
                    fontWeight: FontWeight.w400,
                    wordSpacing: 1.0,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.4),
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 20),

                // Recommendations with improved readability
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.2),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.recommend, color: Colors.white, size: 22),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          recommendations,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                            height: 1.5,
                            fontSize: 14.5,
                            letterSpacing: 0.2,
                            shadows: [
                              Shadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildFoodList(
    BuildContext context, {
    required String title,
    required List<Map<String, String>> items,
    required Color color,
    String? imageUrl,
  }) {
    final theme = Theme.of(context);

    return Container(
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        color: Colors.black,
      ),
      child: Stack(
        children: [
          // Background image or gradient
          Positioned.fill(
            child: imageUrl != null
                ? Image.network(
                    imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              color,
                              color.withOpacity(0.7),
                            ],
                          ),
                        ),
                      );
                    },
                  )
                : Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          color,
                          color.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
          ),

          // Enhanced overlay for better text readability
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [
                    Colors.black.withOpacity(0.85),  // Darker at bottom for text contrast
                    Colors.black.withOpacity(0.6),   // Medium darkness in middle
                    Colors.black.withOpacity(0.3),   // Lighter at top to show image
                  ],
                  stops: const [0.0, 0.5, 0.85],
                ),
              ),
            ),
          ),

          // Additional text clarity layer
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerRight,
                  end: Alignment.centerLeft,
                  colors: [
                    Colors.black.withOpacity(0.2),
                    Colors.black.withOpacity(0.4),
                    Colors.black.withOpacity(0.2),
                  ],
                  stops: const [0.0, 0.5, 1.0],
                ),
              ),
            ),
          ),

          // Content - centered vertically
          Positioned.fill(
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                // Title with improved readability
                Text(
                  title,
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    letterSpacing: -0.3,
                    fontSize: 28,
                    height: 1.2,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Subtitle for context
                Text(
                  'Recommended daily sources',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: Colors.white.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                    fontSize: 14,
                    height: 1.4,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.4),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),

                // Food items with improved layout
                ...items.map((item) => _buildFoodItem(context, item, color)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static Widget _buildFoodItem(
    BuildContext context,
    Map<String, String> item,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.only(bottom: 14),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.12),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.25),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Food icon based on name
          Container(
            width: 40,
            height: 40,
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                _getFoodIcon(item['name'] ?? ''),
                color: Colors.white,
                size: 20,
              ),
            ),
          ),

          // Food name and details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Food name with improved readability
                Text(
                  item['name']!,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                    fontSize: 16,
                    letterSpacing: 0.2,
                    height: 1.3,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 6),

                // Food details with improved readability
                Row(
                  children: [
                    // Protein/carb/fat value
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                      decoration: BoxDecoration(
                        color: color.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        item['value']!,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Calories
                    Text(
                      item['calories']!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 12,
                        letterSpacing: 0.1,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Visual indicator
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: color.withOpacity(0.3),
              shape: BoxShape.circle,
            ),
            child: const Center(
              child: Icon(
                Icons.add,
                color: Colors.white,
                size: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get appropriate icon for food items
  static IconData _getFoodIcon(String foodName) {
    final name = foodName.toLowerCase();

    if (name.contains('chicken') || name.contains('beef') || name.contains('meat') || name.contains('fish') || name.contains('salmon') || name.contains('tuna')) {
      return FontAwesomeIcons.drumstickBite;
    } else if (name.contains('rice') || name.contains('bread') || name.contains('oat') || name.contains('quinoa')) {
      return FontAwesomeIcons.breadSlice;
    } else if (name.contains('oil') || name.contains('butter') || name.contains('fat')) {
      return FontAwesomeIcons.oilWell;
    } else if (name.contains('nut') || name.contains('almond') || name.contains('walnut') || name.contains('peanut')) {
      return FontAwesomeIcons.seedling;
    } else if (name.contains('yogurt') || name.contains('cheese') || name.contains('milk')) {
      return FontAwesomeIcons.cheese;
    } else if (name.contains('apple') || name.contains('banana') || name.contains('orange') || name.contains('fruit')) {
      return FontAwesomeIcons.apple;
    } else if (name.contains('bean') || name.contains('lentil') || name.contains('chickpea')) {
      return FontAwesomeIcons.seedling;
    } else if (name.contains('seed')) {
      return FontAwesomeIcons.seedling;
    } else if (name.contains('potato') || name.contains('carrot') || name.contains('vegetable')) {
      return FontAwesomeIcons.carrot;
    } else if (name.contains('avocado')) {
      return FontAwesomeIcons.leaf;
    }

    // Default icon
    return FontAwesomeIcons.utensils;
  }
}
