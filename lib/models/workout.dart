class Workout {
  final int id;
  final String title;
  final String type;
  final String startDate;
  final String endDate;
  final int durationDays;
  final String status;
  
  Workout({
    required this.id,
    required this.title,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.durationDays,
    required this.status,
  });
  
  factory Workout.fromJson(Map<String, dynamic> json) {
    return Workout(
      id: json['id'],
      title: json['title'],
      type: json['type'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      durationDays: json['duration_days'],
      status: json['status'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type,
      'start_date': startDate,
      'end_date': endDate,
      'duration_days': durationDays,
      'status': status,
    };
  }
}
