import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class CourseSettings {
  final String defaultWhatsappNumber;
  final String defaultMessagePrefix;
  final bool useCustomWhatsappNumbers;

  CourseSettings({
    required this.defaultWhatsappNumber,
    required this.defaultMessagePrefix,
    required this.useCustomWhatsappNumbers,
  });

  factory CourseSettings.fromJson(Map<String, dynamic> json) {
    return CourseSettings(
      defaultWhatsappNumber: json['default_whatsapp_number'] ?? '+1234567890',
      defaultMessagePrefix: json['default_message_prefix'] ?? 'Hi, I\'m interested in enrolling in the course:',
      useCustomWhatsappNumbers: json['use_custom_whatsapp_numbers'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'default_whatsapp_number': defaultWhatsappNumber,
      'default_message_prefix': defaultMessagePrefix,
      'use_custom_whatsapp_numbers': useCustomWhatsappNumbers,
    };
  }

  // Create a copy with updated fields
  CourseSettings copyWith({
    String? defaultWhatsappNumber,
    String? defaultMessagePrefix,
    bool? useCustomWhatsappNumbers,
  }) {
    return CourseSettings(
      defaultWhatsappNumber: defaultWhatsappNumber ?? this.defaultWhatsappNumber,
      defaultMessagePrefix: defaultMessagePrefix ?? this.defaultMessagePrefix,
      useCustomWhatsappNumbers: useCustomWhatsappNumbers ?? this.useCustomWhatsappNumbers,
    );
  }

  // Default settings
  static CourseSettings get defaults => CourseSettings(
    defaultWhatsappNumber: '+1234567890',
    defaultMessagePrefix: 'Hi, I\'m interested in enrolling in the course:',
    useCustomWhatsappNumbers: true,
  );

  // Save settings to local storage
  Future<void> saveToPrefs() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('course_settings', jsonEncode(toJson()));
  }

  // Load settings from local storage
  static Future<CourseSettings> loadFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString('course_settings');

      if (settingsJson != null) {
        return CourseSettings.fromJson(jsonDecode(settingsJson));
      }
    } catch (e) {
      print('Error loading course settings: $e');
    }

    return CourseSettings.defaults;
  }
}
