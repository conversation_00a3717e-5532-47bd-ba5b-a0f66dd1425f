import 'package:flutter/material.dart';
import '../models/food_item.dart';
import 'food_image_helper.dart';

/// A utility class to cache and manage food images
class FoodImageCache {
  // In-memory cache of image URLs
  static final Map<String, String> _imageCache = {};

  /// Get the image URL for a food item, with caching
  static String getImageUrl(FoodItem foodItem) {
    // Check if we have a cached URL for this food item
    final cacheKey = '${foodItem.id ?? ''}:${foodItem.name}';
    if (_imageCache.containsKey(cacheKey)) {
      return _imageCache[cacheKey]!;
    }

    // Get the best available image URL
    final imageUrl = _getBestImageUrl(foodItem);
    
    // Cache the URL for future use
    _imageCache[cacheKey] = imageUrl;
    
    return imageUrl;
  }

  /// Get the best available image URL for a food item
  static String _getBestImageUrl(FoodItem foodItem) {
    // Priority 1: Use the food item's own image URL if available
    if (foodItem.imageUrl != null && foodItem.imageUrl!.isNotEmpty) {
      return foodItem.imageUrl!;
    }
    
    // Priority 2: Use the FoodImageHelper to get a suitable image
    return FoodImageHelper.getImageUrl(foodItem);
  }

  /// Build an image widget for a food item with proper error handling
  static Widget buildFoodImage(
    BuildContext context,
    FoodItem foodItem, {
    double width = 70,
    double height = 70,
    BoxFit fit = BoxFit.cover,
    BorderRadius? borderRadius,
  }) {
    final imageUrl = getImageUrl(foodItem);
    
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(12),
      child: SizedBox(
        width: width,
        height: height,
        child: Image.network(
          imageUrl,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to category image if direct image fails
            return Image.network(
              FoodImageHelper.getCategoryImageUrl(foodItem.category),
              fit: fit,
              errorBuilder: (context, error, stackTrace) {
                // Final fallback to first letter
                return Container(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  child: Center(
                    child: Text(
                      foodItem.name.isNotEmpty ? foodItem.name[0].toUpperCase() : '?',
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 24),
                    ),
                  ),
                );
              },
            );
          },
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              color: Theme.of(context).colorScheme.surface,
              child: Center(
                child: CircularProgressIndicator(
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                  strokeWidth: 2,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
