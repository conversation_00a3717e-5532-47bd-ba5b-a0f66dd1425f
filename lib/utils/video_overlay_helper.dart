import 'package:flutter/material.dart';
import '../models/course_video.dart';
import '../models/course.dart';
import '../widgets/video_player_overlay.dart';

class VideoOverlayHelper {
  /// Shows a video in an overlay without navigating away from the current page
  /// Returns the updated video through the onVideoCompleted callback
  static Future<void> showVideoOverlay({
    required BuildContext context,
    required CourseVideo video,
    Course? course,
    Function(CourseVideo?)? onVideoCompleted,
  }) async {
    // Create an overlay entry
    OverlayState? overlayState = Overlay.of(context);
    late OverlayEntry overlayEntry;

    // Function to close the overlay
    void closeOverlay() {
      overlayEntry.remove();
    }

    // Create the overlay entry with the video player
    overlayEntry = OverlayEntry(
      builder: (context) => VideoPlayerOverlay(
        video: video,
        course: course,
        onClose: closeOverlay,
        onVideoCompleted: onVideoCompleted,
      ),
    );

    // Add the overlay to the screen
    overlayState.insert(overlayEntry);
  }
}
