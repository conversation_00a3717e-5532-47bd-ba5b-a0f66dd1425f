import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import '../services/api_service.dart';

class VideoSecurityHelper {
  static const String _appDomain = 'com.kft.fitness'; // Your app's package name
  static const String _secretKey = 'kft_fitness_video_secret_key_2025'; // Should be stored securely

  // Local IP server configuration for domain verification
  static const String _localServerDomain = 'https://d8fc-152-58-47-140.ngrok-free.app/';

  /// Extract Vimeo video ID from various URL formats
  static String? extractVimeoId(String url) {
    if (url.isEmpty) return null;

    final patterns = [
      RegExp(r'vimeo\.com/([0-9]+)'),
      RegExp(r'player\.vimeo\.com/video/([0-9]+)'),
      RegExp(r'vimeo\.com/([0-9]+)/([a-zA-Z0-9]+)'),
      RegExp(r'player\.vimeo\.com/video/([0-9]+)\?h=([a-zA-Z0-9]+)'),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null && match.group(1) != null) {
        return match.group(1)!;
      }
    }

    return null;
  }

  /// Extract private hash from Vimeo private URLs
  static String? extractVimeoPrivateHash(String url) {
    if (url.isEmpty) return null;

    // Pattern for private Vimeo URLs: https://vimeo.com/123456789/abcdef1234
    final privatePattern = RegExp(r'vimeo\.com/[0-9]+/([a-zA-Z0-9]+)');
    final privateMatch = privatePattern.firstMatch(url);
    if (privateMatch != null && privateMatch.group(1) != null) {
      return privateMatch.group(1)!;
    }

    // Pattern for embed URLs with hash: https://player.vimeo.com/video/123456789?h=abcdef1234
    final embedPattern = RegExp(r'player\.vimeo\.com/video/[0-9]+\?h=([a-zA-Z0-9]+)');
    final embedMatch = embedPattern.firstMatch(url);
    if (embedMatch != null && embedMatch.group(1) != null) {
      return embedMatch.group(1)!;
    }

    return null;
  }

  /// Generate a secure embed URL with domain restrictions and authentication
  static Future<String?> getSecureEmbedUrl({
    required String vimeoId,
    required int videoId,
    required int userId,
    String? originalUrl,
  }) async {
    try {
      // Validate user access to this video
      final hasAccess = await _validateUserAccess(videoId, userId);
      if (!hasAccess) {
        throw Exception('User does not have access to this video');
      }

      // Extract private hash from original URL if available
      String? privateHash;
      if (originalUrl != null) {
        privateHash = extractVimeoPrivateHash(originalUrl);
      }

      // Generate security token
      final securityToken = _generateSecurityToken(vimeoId, userId);

      // Build secure embed URL with domain restrictions
      final embedUrl = _buildSecureEmbedUrl(vimeoId, securityToken, privateHash: privateHash);

      return embedUrl;
    } catch (e) {
      debugPrint('Failed to generate secure embed URL: $e');
      return null;
    }
  }

  /// Validate if user has access to the video
  static Future<bool> _validateUserAccess(int videoId, int userId) async {
    try {
      final apiService = ApiService();

      // Check if user is enrolled in the course containing this video
      // This should call your backend API to verify access
      final response = await apiService.makeApiRequest(
        'validate_video_access.php',
        method: 'POST',
        data: {
          'video_id': videoId,
          'user_id': userId,
        },
      );

      return response['success'] == true && response['has_access'] == true;
    } catch (e) {
      // Do NOT fail video access validation due to API errors
      // This prevents video playback from being blocked by temporary network issues
      debugPrint('Failed to validate user access: $e');
      return true; // Allow access if validation fails to prevent blocking legitimate users
    }
  }

  /// Generate a security token for video access
  static String _generateSecurityToken(String vimeoId, int userId) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final data = '$vimeoId:$userId:$timestamp:$_appDomain';
    final bytes = utf8.encode(data + _secretKey);
    final digest = sha256.convert(bytes);

    return base64Url.encode([
      ...utf8.encode('$timestamp:$userId'),
      ...digest.bytes.take(16), // First 16 bytes of hash
    ]);
  }

  /// Build secure embed URL with domain restrictions and security parameters
  static String _buildSecureEmbedUrl(String vimeoId, String securityToken, {String? privateHash}) {
    final baseUrl = 'https://player.vimeo.com/video/$vimeoId';

    final params = <String, String>{
      // Player configuration for security
      'autoplay': '0',
      'title': '0',
      'byline': '0',
      'portrait': '0',
      'responsive': '1',
      'dnt': '1', // Do Not Track

      // Domain restrictions (if supported by your Vimeo account)
      'referrer': _appDomain,

      // Disable sharing and embedding outside app
      'sharing': '0',
      'download': '0',
      'fullscreen': '1',
    };

    // Use private hash if available, otherwise use security token
    if (privateHash != null && privateHash.isNotEmpty) {
      params['h'] = privateHash;
    } else {
      params['h'] = securityToken.substring(0, 16); // Use part of token as hash
    }

    // Add additional security parameters
    params['app_id'] = _appDomain;
    params['security_token'] = securityToken;

    final queryString = params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return '$baseUrl?$queryString';
  }

  /// Validate security token (for backend verification)
  static bool validateSecurityToken(String token, String vimeoId, int userId) {
    try {
      final decoded = base64Url.decode(token);
      final timestampUserBytes = decoded.take(decoded.length - 16).toList();
      final hashBytes = decoded.skip(decoded.length - 16).toList();

      final timestampUserStr = utf8.decode(timestampUserBytes);
      final parts = timestampUserStr.split(':');

      if (parts.length != 2) return false;

      final timestamp = int.tryParse(parts[0]);
      final tokenUserId = int.tryParse(parts[1]);

      if (timestamp == null || tokenUserId == null) return false;
      if (tokenUserId != userId) return false;

      // Check if token is not expired (24 hours)
      final now = DateTime.now().millisecondsSinceEpoch;
      if (now - timestamp > 24 * 60 * 60 * 1000) return false;

      // Verify hash
      final data = '$vimeoId:$userId:$timestamp:$_appDomain';
      final bytes = utf8.encode(data + _secretKey);
      final expectedHash = sha256.convert(bytes).bytes.take(16).toList();

      return _listEquals(hashBytes, expectedHash);
    } catch (e) {
      debugPrint('Token validation failed: $e');
      return false;
    }
  }

  /// Helper method to compare lists
  static bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Get Vimeo video metadata with security validation
  static Future<Map<String, dynamic>?> getVideoMetadata({
    required String vimeoId,
    required int userId,
  }) async {
    try {
      final apiService = ApiService();

      // Get video metadata through your backend (which should validate access)
      final response = await apiService.makeApiRequest(
        'vimeo_metadata.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'user_id': userId,
        },
      );

      if (response['success'] == true) {
        return response['metadata'];
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get video metadata: $e');
      return null;
    }
  }

  /// Check if the current environment allows video playback
  static bool isPlaybackAllowed() {
    // Add additional security checks here
    // For example, check if app is running in debug mode, rooted device, etc.

    if (kDebugMode) {
      // Allow in debug mode for development
      return true;
    }

    // Add production security checks
    return true;
  }

  /// Generate a one-time playback token
  static Future<String?> generatePlaybackToken({
    required String vimeoId,
    required int videoId,
    required int userId,
  }) async {
    try {
      final apiService = ApiService();

      // Request a one-time playback token from your backend
      final response = await apiService.makeApiRequest(
        'generate_playback_token.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': videoId,
          'user_id': userId,
          'app_domain': _appDomain,
        },
      );

      if (response['success'] == true) {
        return response['playback_token'];
      }

      return null;
    } catch (e) {
      debugPrint('Failed to generate playback token: $e');
      return null;
    }
  }

  /// Validate playback environment
  static Future<bool> validatePlaybackEnvironment() async {
    try {
      // Check if app is running in a secure environment
      if (!isPlaybackAllowed()) {
        return false;
      }

      // Additional security checks can be added here
      // - Check for screen recording
      // - Check for rooted/jailbroken device
      // - Validate app signature

      return true;
    } catch (e) {
      debugPrint('Playback environment validation failed: $e');
      return false;
    }
  }

  /// Verify domain restrictions for Vimeo video
  static Future<bool> verifyDomainRestrictions({
    required String vimeoId,
    required int videoId,
  }) async {
    try {
      final apiService = ApiService();

      final response = await apiService.makeApiRequest(
        'verify_vimeo_domain.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': videoId,
          'domain': _appDomain,
        },
      );

      return response['success'] == true && response['domain_verified'] == true;
    } catch (e) {
      debugPrint('Failed to verify domain restrictions: $e');
      return false;
    }
  }

  /// Get direct video URL for better performance
  static Future<String?> getDirectVideoUrl({
    required String vimeoId,
    required int videoId,
    String quality = 'auto',
  }) async {
    try {
      final apiService = ApiService();

      final response = await apiService.makeApiRequest(
        'get_vimeo_direct_url.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': videoId,
          'quality': quality,
        },
      );

      if (response['success'] == true) {
        return response['direct_url'];
      }

      return null;
    } catch (e) {
      debugPrint('Failed to get direct video URL: $e');
      return null;
    }
  }

  /// Log video access for analytics and security monitoring
  static Future<void> logVideoAccess({
    required String vimeoId,
    required int videoId,
    required int userId,
    required String action, // 'play', 'pause', 'complete', etc.
  }) async {
    try {
      final apiService = ApiService();

      await apiService.makeApiRequest(
        'log_video_access.php',
        method: 'POST',
        data: {
          'vimeo_id': vimeoId,
          'video_id': videoId,
          'user_id': userId,
          'action': action,
          'timestamp': DateTime.now().toIso8601String(),
          'app_domain': _appDomain,
        },
      );
    } catch (e) {
      // Do NOT interfere with authentication state for video logging failures
      // Video access logging is for analytics only and should not affect user experience
      debugPrint('Failed to log video access: $e');
    }
  }
}
