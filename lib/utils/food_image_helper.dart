import '../models/food_item.dart';

/// Helper class to provide food images based on categories
class FoodImageHelper {
  /// Default food image URLs by category
  static const Map<String, String> _defaultImageUrls = {
    // Indian food categories with more accurate images
    'indian_breakfast': 'https://cdn.pixabay.com/photo/2019/09/15/08/06/food-4477693_1280.jpg',
    'indian_main_course_veg': 'https://cdn.pixabay.com/photo/2017/09/09/12/09/india-2731817_1280.jpg',
    'indian_main_course_non_veg': 'https://cdn.pixabay.com/photo/2015/05/31/13/59/salad-791891_1280.jpg',
    'indian_rice_breads': 'https://cdn.pixabay.com/photo/2014/11/05/16/00/indian-food-518041_1280.jpg',
    'indian_desserts': 'https://cdn.pixabay.com/photo/2016/10/27/22/12/cakes-1776661_1280.jpg',
    'indian_snacks': 'https://cdn.pixabay.com/photo/2019/08/13/18/44/samosa-4404419_1280.jpg',

    // Specific Indian foods
    'idli': 'https://cdn.pixabay.com/photo/2017/06/16/11/38/breakfast-2409006_1280.jpg',
    'dosa': 'https://cdn.pixabay.com/photo/2020/03/25/21/03/dosa-4968282_1280.jpg',
    'samosa': 'https://cdn.pixabay.com/photo/2019/08/13/18/44/samosa-4404419_1280.jpg',
    'butter_chicken': 'https://cdn.pixabay.com/photo/2020/06/10/10/43/chicken-curry-5281269_1280.jpg',
    'paneer_butter_masala': 'https://cdn.pixabay.com/photo/2017/09/09/12/09/india-2731817_1280.jpg',
    'gulab_jamun': 'https://cdn.pixabay.com/photo/2015/02/24/02/05/dessert-647247_1280.jpg',

    // Standard food categories
    'fruits': 'https://cdn.pixabay.com/photo/2017/05/11/19/44/fresh-fruits-2305192_1280.jpg',
    'vegetables': 'https://cdn.pixabay.com/photo/2016/08/11/08/04/vegetables-1584999_1280.jpg',
    'protein': 'https://cdn.pixabay.com/photo/2016/03/05/19/02/salmon-1238248_1280.jpg',
    'grains': 'https://cdn.pixabay.com/photo/2014/12/11/02/55/cereals-563796_1280.jpg',
    'dairy': 'https://cdn.pixabay.com/photo/2017/07/05/15/41/milk-2474993_1280.jpg',
    'nuts': 'https://cdn.pixabay.com/photo/2017/08/21/06/28/nuts-2664799_1280.jpg',
    'default': 'https://cdn.pixabay.com/photo/2017/03/23/19/57/asparagus-2169305_1280.jpg',
  };

  /// Get an image URL for a food item
  /// If the food item has an image URL, use that
  /// Otherwise, return a default image URL based on the food name or category
  static String getImageUrl(FoodItem foodItem) {
    // If the food item has an image URL, use that
    if (foodItem.imageUrl != null && foodItem.imageUrl!.isNotEmpty) {
      return foodItem.imageUrl!;
    }

    // Check for specific food items first
    final specificFoodUrl = getSpecificFoodImageUrl(foodItem.name);
    if (specificFoodUrl != null) {
      return specificFoodUrl;
    }

    // Otherwise, return a default image URL based on the category
    return getCategoryImageUrl(foodItem.category);
  }

  /// Get an image URL for a specific food item by name
  static String? getSpecificFoodImageUrl(String foodName) {
    final lowerName = foodName.toLowerCase();

    // Check for specific Indian foods
    if (lowerName.contains('idli')) {
      return _defaultImageUrls['idli'];
    }
    if (lowerName.contains('dosa')) {
      return _defaultImageUrls['dosa'];
    }
    if (lowerName.contains('samosa')) {
      return _defaultImageUrls['samosa'];
    }
    if (lowerName.contains('butter chicken')) {
      return _defaultImageUrls['butter_chicken'];
    }
    if (lowerName.contains('paneer butter masala') || lowerName.contains('paneer makhani')) {
      return _defaultImageUrls['paneer_butter_masala'];
    }
    if (lowerName.contains('gulab jamun')) {
      return _defaultImageUrls['gulab_jamun'];
    }

    // No specific match found
    return null;
  }

  /// Get a default image URL based on the category
  static String getCategoryImageUrl(String? category) {
    if (category == null) {
      return _defaultImageUrls['default']!;
    }

    final lowerCategory = category.toLowerCase();

    // Check for Indian food categories
    if (lowerCategory.contains('indian breakfast')) {
      return _defaultImageUrls['indian_breakfast']!;
    }
    if (lowerCategory.contains('indian main course')) {
      if (lowerCategory.contains('veg')) {
        return _defaultImageUrls['indian_main_course_veg']!;
      }
      if (lowerCategory.contains('non-veg')) {
        return _defaultImageUrls['indian_main_course_non_veg']!;
      }
    }
    if (lowerCategory.contains('indian rice') || lowerCategory.contains('breads')) {
      return _defaultImageUrls['indian_rice_breads']!;
    }
    if (lowerCategory.contains('indian dessert')) {
      return _defaultImageUrls['indian_desserts']!;
    }
    if (lowerCategory.contains('indian snack')) {
      return _defaultImageUrls['indian_snacks']!;
    }

    // Check for standard categories
    if (lowerCategory.contains('fruit')) {
      return _defaultImageUrls['fruits']!;
    }
    if (lowerCategory.contains('vegetable')) {
      return _defaultImageUrls['vegetables']!;
    }
    if (lowerCategory.contains('protein')) {
      return _defaultImageUrls['protein']!;
    }
    if (lowerCategory.contains('grain')) {
      return _defaultImageUrls['grains']!;
    }
    if (lowerCategory.contains('dairy')) {
      return _defaultImageUrls['dairy']!;
    }
    if (lowerCategory.contains('nut')) {
      return _defaultImageUrls['nuts']!;
    }

    // Default image
    return _defaultImageUrls['default']!;
  }
}
