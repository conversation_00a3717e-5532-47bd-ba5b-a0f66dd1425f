import '../services/api_service.dart';

String getFullImageUrl(String? url) {
  if (url == null || url.isEmpty) return '';
  if (url.startsWith('http://') || url.startsWith('https://')) return url;

  String baseUrl = ApiService.baseUrl.replaceAll('/api', '');
  if (baseUrl.endsWith('/')) {
    baseUrl = baseUrl.substring(0, baseUrl.length - 1);
  }

  String cleanPath = url;
  if (cleanPath.startsWith('/')) {
    cleanPath = cleanPath.substring(1);
  }

  // Add a cache busting parameter to the URL
  final String finalUrl = '$baseUrl/$cleanPath';
  final Uri uri = Uri.parse(finalUrl);
  final Map<String, String> queryParameters = Map.from(uri.queryParameters);
  queryParameters['t'] = DateTime.now().millisecondsSinceEpoch.toString(); // Add a timestamp for cache busting

  return uri.replace(queryParameters: queryParameters).toString();
} 