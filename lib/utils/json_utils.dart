/// Utility class for safe JSON parsing and type conversion
class JsonUtils {
  /// Safely converts a dynamic value to Map<String, dynamic>
  static Map<String, dynamic> safeMapConversion(dynamic value) {
    if (value == null) {
      return <String, dynamic>{};
    }
    
    if (value is Map<String, dynamic>) {
      return value;
    }
    
    if (value is Map) {
      try {
        return Map<String, dynamic>.from(value);
      } catch (e) {
        print('Error converting Map to Map<String, dynamic>: $e');
        return <String, dynamic>{};
      }
    }
    
    print('Warning: Expected Map but got ${value.runtimeType}');
    return <String, dynamic>{};
  }

  /// Safely parses a string value from dynamic input
  static String safeString(dynamic value, [String defaultValue = '']) {
    if (value == null) return defaultValue;
    return value.toString();
  }

  /// Safely parses an integer value from dynamic input
  static int safeInt(dynamic value, [int defaultValue = 0]) {
    if (value == null) return defaultValue;
    
    if (value is int) return value;
    
    if (value is num) return value.toInt();
    
    if (value is String) {
      try {
        return int.parse(value);
      } catch (e) {
        print('Error parsing int from string "$value": $e');
        return defaultValue;
      }
    }
    
    print('Warning: Cannot convert ${value.runtimeType} to int');
    return defaultValue;
  }

  /// Safely parses a double value from dynamic input
  static double safeDouble(dynamic value, [double defaultValue = 0.0]) {
    if (value == null) return defaultValue;
    
    if (value is double) return value;
    
    if (value is num) return value.toDouble();
    
    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print('Error parsing double from string "$value": $e');
        return defaultValue;
      }
    }
    
    print('Warning: Cannot convert ${value.runtimeType} to double');
    return defaultValue;
  }

  /// Safely parses a boolean value from dynamic input
  static bool safeBool(dynamic value, [bool defaultValue = false]) {
    if (value == null) return defaultValue;
    
    if (value is bool) return value;
    
    if (value is int) return value == 1;
    
    if (value is String) {
      final lowerValue = value.toLowerCase();
      if (lowerValue == 'true' || lowerValue == '1') return true;
      if (lowerValue == 'false' || lowerValue == '0') return false;
    }
    
    print('Warning: Cannot convert ${value.runtimeType} to bool');
    return defaultValue;
  }

  /// Safely parses a List from dynamic input
  static List<T> safeList<T>(dynamic value, T Function(dynamic) converter, [List<T>? defaultValue]) {
    defaultValue ??= <T>[];
    
    if (value == null) return defaultValue;
    
    if (value is! List) {
      print('Warning: Expected List but got ${value.runtimeType}');
      return defaultValue;
    }
    
    final result = <T>[];
    for (var item in value) {
      try {
        final converted = converter(item);
        result.add(converted);
      } catch (e) {
        print('Error converting list item: $e');
        // Skip invalid items instead of failing completely
      }
    }
    
    return result;
  }

  /// Safely parses a DateTime from dynamic input
  static DateTime? safeDateTime(dynamic value) {
    if (value == null) return null;
    
    if (value is DateTime) return value;
    
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        print('Error parsing DateTime from string "$value": $e');
        return null;
      }
    }
    
    print('Warning: Cannot convert ${value.runtimeType} to DateTime');
    return null;
  }

  /// Safely handles API response parsing
  static Map<String, dynamic> safeApiResponse(dynamic response) {
    if (response == null) {
      return {'success': false, 'error': 'Null response'};
    }
    
    if (response is Map<String, dynamic>) {
      return response;
    }
    
    if (response is Map) {
      try {
        return Map<String, dynamic>.from(response);
      } catch (e) {
        print('Error converting API response: $e');
        return {'success': false, 'error': 'Invalid response format'};
      }
    }
    
    return {'success': false, 'error': 'Unexpected response type: ${response.runtimeType}'};
  }

  /// Validates that a Map has the expected structure
  static bool validateMapStructure(Map<String, dynamic> map, List<String> requiredKeys) {
    for (final key in requiredKeys) {
      if (!map.containsKey(key)) {
        print('Missing required key: $key');
        return false;
      }
    }
    return true;
  }
}
