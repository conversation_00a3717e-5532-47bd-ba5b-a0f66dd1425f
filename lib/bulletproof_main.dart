import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'services/bulletproof_app_manager.dart';
import 'services/crash_prevention_service.dart';
import 'main.dart' as original_main;

/// Bulletproof main entry point that wraps the original app with enhanced stability
void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Set up global error handling before anything else
  await _setupGlobalErrorHandling();

  // Initialize bulletproof systems
  await _initializeBulletproofSystems();

  // Run the app with bulletproof wrapper
  runApp(const BulletproofAppWrapper());
}

/// Set up comprehensive global error handling
Future<void> _setupGlobalErrorHandling() async {
  try {
    print('🛡️ Setting up global error handling...');

    // Initialize crash prevention service first
    final crashService = CrashPreventionService();
    await crashService.initialize();

    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) async {
      await crashService.reportFlutterError(details);
      
      // In debug mode, also show the error
      if (kDebugMode) {
        FlutterError.presentError(details);
      }
    };

    // Handle platform errors
    PlatformDispatcher.instance.onError = (error, stack) {
      crashService.reportError(error, 'PlatformDispatcher', stackTrace: stack);
      return true; // Handled
    };

    print('✅ Global error handling configured');
  } catch (e) {
    print('❌ Failed to setup global error handling: $e');
    // Continue anyway - better to have a working app than no app
  }
}

/// Initialize all bulletproof systems
Future<void> _initializeBulletproofSystems() async {
  try {
    print('🚀 Initializing bulletproof systems...');

    final stopwatch = Stopwatch()..start();

    // Initialize the bulletproof app manager
    final appManager = BulletproofAppManager();
    await appManager.initialize();

    stopwatch.stop();
    print('✅ Bulletproof systems initialized in ${stopwatch.elapsedMilliseconds}ms');
  } catch (e) {
    print('❌ Failed to initialize bulletproof systems: $e');
    // Continue with degraded functionality
  }
}

/// Bulletproof app wrapper that provides enhanced stability
class BulletproofAppWrapper extends StatefulWidget {
  const BulletproofAppWrapper({super.key});

  @override
  State<BulletproofAppWrapper> createState() => _BulletproofAppWrapperState();
}

class _BulletproofAppWrapperState extends State<BulletproofAppWrapper> 
    with WidgetsBindingObserver {
  
  final BulletproofAppManager _appManager = BulletproofAppManager();
  bool _isInitialized = false;
  String? _initializationError;

  @override
  void initState() {
    super.initState();
    
    // Add lifecycle observer
    WidgetsBinding.instance.addObserver(this);
    
    // Initialize app
    _initializeApp();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _appManager.dispose();
    super.dispose();
  }

  /// Initialize the app with error handling
  Future<void> _initializeApp() async {
    try {
      // Ensure bulletproof systems are ready
      if (!_appManager.healthStatus.isHealthy) {
        await _appManager.initialize();
      }

      setState(() {
        _isInitialized = true;
      });

      print('✅ Bulletproof app wrapper initialized');
    } catch (e) {
      print('❌ App initialization failed: $e');
      setState(() {
        _initializationError = e.toString();
        _isInitialized = true; // Show error screen
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Handle app lifecycle changes through bulletproof manager
    _appManager.handleLifecycleChange(state);
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return MaterialApp(
        title: 'KFT',
        home: Scaffold(
          backgroundColor: Colors.white,
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                ),
                const SizedBox(height: 24),
                Text(
                  'Initializing KFT...',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w500,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Setting up bulletproof systems',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    if (_initializationError != null) {
      return MaterialApp(
        title: 'KFT',
        home: BulletproofErrorScreen(
          error: _initializationError!,
          onRetry: () {
            setState(() {
              _isInitialized = false;
              _initializationError = null;
            });
            _initializeApp();
          },
        ),
      );
    }

    // Wrap the original app with bulletproof error boundary
    return BulletproofErrorBoundary(
      child: original_main.MyApp(),
    );
  }
}

/// Bulletproof error boundary that catches and handles widget errors
class BulletproofErrorBoundary extends StatefulWidget {
  final Widget child;

  const BulletproofErrorBoundary({
    super.key,
    required this.child,
  });

  @override
  State<BulletproofErrorBoundary> createState() => _BulletproofErrorBoundaryState();
}

class _BulletproofErrorBoundaryState extends State<BulletproofErrorBoundary> {
  String? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return MaterialApp(
        title: 'KFT',
        home: BulletproofErrorScreen(
          error: _error!,
          stackTrace: _stackTrace,
          onRetry: () {
            setState(() {
              _error = null;
              _stackTrace = null;
            });
          },
        ),
      );
    }

    return ErrorWidget.withErrorWidgetBuilder(
      (FlutterErrorDetails details) {
        // Report error to crash prevention service
        final crashService = CrashPreventionService();
        crashService.reportFlutterError(details);

        // Update state to show error screen
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _error = details.exception.toString();
              _stackTrace = details.stack;
            });
          }
        });

        // Return a simple error widget for now
        return Container(
          color: Colors.red[50],
          child: const Center(
            child: Text(
              'An error occurred. Recovering...',
              style: TextStyle(color: Colors.red),
            ),
          ),
        );
      },
      widget.child,
    );
  }
}

/// Bulletproof error screen with recovery options
class BulletproofErrorScreen extends StatelessWidget {
  final String error;
  final StackTrace? stackTrace;
  final VoidCallback onRetry;

  const BulletproofErrorScreen({
    super.key,
    required this.error,
    this.stackTrace,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: 64,
                color: Colors.orange[600],
              ),
              const SizedBox(height: 24),
              Text(
                'Oops! Something went wrong',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              Text(
                'Don\'t worry! Our bulletproof system is working to fix this.',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Error Details:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[700],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      error,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: onRetry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Try Again',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        // Force restart the app
                        SystemNavigator.pop();
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.grey[700],
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Restart App',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Text(
                'This error has been automatically reported and will help us improve the app.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
