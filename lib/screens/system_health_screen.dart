import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/bulletproof_app_manager.dart';
import '../services/device_compatibility_service.dart';
import '../services/network_resilience_service.dart';
import '../services/offline_data_service.dart';
import '../services/crash_prevention_service.dart';
import '../services/performance_monitor_service.dart';
import '../services/device_compatibility_service.dart';

/// System health dashboard showing bulletproof system status
class SystemHealthScreen extends StatefulWidget {
  const SystemHealthScreen({super.key});

  @override
  State<SystemHealthScreen> createState() => _SystemHealthScreenState();
}

class _SystemHealthScreenState extends State<SystemHealthScreen> {
  final BulletproofAppManager _appManager = BulletproofAppManager();
  final DeviceCompatibilityService _deviceService = DeviceCompatibilityService();
  final NetworkResilienceService _networkService = NetworkResilienceService();
  final OfflineDataService _offlineService = OfflineDataService();
  final CrashPreventionService _crashService = CrashPreventionService();
  final PerformanceMonitorService _performanceService = PerformanceMonitorService();

  bool _isLoading = true;
  AppHealthStatus? _healthStatus;
  Map<String, bool> _serviceStatus = {};

  @override
  void initState() {
    super.initState();
    _loadSystemHealth();
  }

  Future<void> _loadSystemHealth() async {
    setState(() => _isLoading = true);

    try {
      _healthStatus = _appManager.healthStatus;
      _serviceStatus = _appManager.getServiceStatus();
    } catch (e) {
      print('❌ Failed to load system health: $e');
    }

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'System Health',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadSystemHealth,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadSystemHealth,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOverallHealthCard(),
                    const SizedBox(height: 16),
                    _buildServiceStatusSection(),
                    const SizedBox(height: 16),
                    _buildDeviceInfoSection(),
                    const SizedBox(height: 16),
                    _buildPerformanceSection(),
                    const SizedBox(height: 16),
                    _buildNetworkSection(),
                    const SizedBox(height: 16),
                    _buildDataSection(),
                    const SizedBox(height: 16),
                    _buildActionsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildOverallHealthCard() {
    final isHealthy = _healthStatus?.isHealthy ?? false;
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: isHealthy
                ? [Colors.green[400]!, Colors.green[600]!]
                : [Colors.orange[400]!, Colors.orange[600]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                isHealthy ? Icons.health_and_safety : Icons.warning,
                color: Colors.white,
                size: 32,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    isHealthy ? 'System Healthy' : 'System Degraded',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isHealthy
                        ? 'All bulletproof systems operational'
                        : 'Some systems need attention',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceStatusSection() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Service Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._serviceStatus.entries.map((entry) => _buildServiceItem(
              _getServiceDisplayName(entry.key),
              entry.value,
              _getServiceIcon(entry.key),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceItem(String name, bool isHealthy, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: isHealthy ? Colors.green : Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: const TextStyle(fontSize: 16),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isHealthy ? Colors.green[100] : Colors.orange[100],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isHealthy ? 'Healthy' : 'Degraded',
              style: TextStyle(
                color: isHealthy ? Colors.green[700] : Colors.orange[700],
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceInfoSection() {
    final deviceInfo = _deviceService.deviceInfo;
    final performanceProfile = _deviceService.performanceProfile;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Device Information',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Brand', deviceInfo.brand.toUpperCase()),
            _buildInfoRow('Model', deviceInfo.model),
            _buildInfoRow('Android Version', '${deviceInfo.androidVersion} (API ${deviceInfo.sdkInt})'),
            _buildInfoRow('Performance Tier', performanceProfile.tier.toString().split('.').last.toUpperCase()),
            _buildInfoRow('Memory', '${(deviceInfo.totalMemory / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB'),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceSection() {
    final performanceStatus = _performanceService.currentStatus;
    final statistics = _performanceService.getStatistics();

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Text(
                  'Performance',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: performanceStatus.isHealthy ? Colors.green[100] : Colors.orange[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Score: ${statistics.performanceScore}',
                    style: TextStyle(
                      color: performanceStatus.isHealthy ? Colors.green[700] : Colors.orange[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildPerformanceMetric('FPS', '${performanceStatus.fps.toStringAsFixed(1)}', Icons.speed),
            _buildPerformanceMetric('Memory', '${performanceStatus.memoryUsageMb} MB', Icons.memory),
            _buildPerformanceMetric('CPU', '${performanceStatus.cpuUsagePercent}%', Icons.developer_board),
            _buildPerformanceMetric('Response Time', '${performanceStatus.responseTimeMs} ms', Icons.timer),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetric(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(label, style: const TextStyle(fontSize: 14)),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkSection() {
    final networkStatus = _networkService.networkStatus;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Network Status',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Status', networkStatus.isOnline ? 'Online' : 'Offline'),
            _buildInfoRow('Connection', networkStatus.connectivity.toString().split('.').last),
            _buildInfoRow('Quality', '${networkStatus.quality}%'),
            _buildInfoRow('Queued Requests', '${networkStatus.queuedRequests}'),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSection() {
    return FutureBuilder<CacheStatistics>(
      future: _offlineService.getCacheStatistics(),
      builder: (context, snapshot) {
        final cacheStats = snapshot.data ?? CacheStatistics(
          totalEntries: 0,
          cacheSize: 0,
          pendingSyncCount: 0,
          queuedOperations: 0,
        );
        final errorStats = _crashService.getErrorStatistics();

        return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Data & Stability',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow('Cache Entries', '${cacheStats.totalEntries}'),
            _buildInfoRow('Cache Size', '${(cacheStats.cacheSize / 1024).toStringAsFixed(1)} KB'),
            _buildInfoRow('Pending Sync', '${cacheStats.pendingSyncCount}'),
            _buildInfoRow('Total Errors', '${errorStats.totalErrors}'),
            _buildInfoRow('Errors (24h)', '${errorStats.errorsLast24h}'),
          ],
        ),
      ),
    );
      },
    );
  }

  Widget _buildActionsSection() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'System Actions',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildActionButton(
              'Force Health Check',
              Icons.health_and_safety,
              () async {
                await _appManager.forceHealthCheck();
                _loadSystemHealth();
              },
            ),
            _buildActionButton(
              'Optimize Performance',
              Icons.tune,
              () async {
                await _performanceService.forceOptimization();
                _loadSystemHealth();
              },
            ),
            _buildActionButton(
              'Sync Data Now',
              Icons.sync,
              () async {
                await _offlineService.forceSyncNow();
                _loadSystemHealth();
              },
            ),
            _buildActionButton(
              'Clear Error History',
              Icons.clear_all,
              () async {
                await _crashService.clearErrorHistory();
                _loadSystemHealth();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: SizedBox(
        width: double.infinity,
        child: OutlinedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 18),
          label: Text(label),
          style: OutlinedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getServiceDisplayName(String key) {
    switch (key) {
      case 'auth':
        return 'Authentication';
      case 'network':
        return 'Network Resilience';
      case 'offline':
        return 'Offline Data';
      case 'device':
        return 'Device Compatibility';
      case 'crash_prevention':
        return 'Crash Prevention';
      case 'performance':
        return 'Performance Monitor';
      default:
        return key;
    }
  }

  IconData _getServiceIcon(String key) {
    switch (key) {
      case 'auth':
        return Icons.security;
      case 'network':
        return Icons.wifi;
      case 'offline':
        return Icons.storage;
      case 'device':
        return Icons.phone_android;
      case 'crash_prevention':
        return Icons.shield;
      case 'performance':
        return Icons.speed;
      default:
        return Icons.settings;
    }
  }
}
