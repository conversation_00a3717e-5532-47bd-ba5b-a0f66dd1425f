import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'performance_monitor.dart';
import 'connectivity_service.dart';
import 'optimized_image_service.dart';
import 'optimized_video_service.dart';
import 'persistent_auth_service.dart';

/// App lifecycle manager for optimal performance and resource management
class AppLifecycleManager extends WidgetsBindingObserver {
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();
  factory AppLifecycleManager() => _instance;
  AppLifecycleManager._internal();

  // Services
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  final ConnectivityService _connectivityService = ConnectivityService();
  final OptimizedImageService _imageService = OptimizedImageService();
  final OptimizedVideoService _videoService = OptimizedVideoService();
  final PersistentAuthService _authService = PersistentAuthService();

  // Lifecycle state
  AppLifecycleState _currentState = AppLifecycleState.resumed;
  bool _isInitialized = false;
  Timer? _backgroundTimer;
  Timer? _memoryCheckTimer;

  // Performance tracking
  DateTime? _lastActiveTime;
  int _backgroundDuration = 0;
  bool _isLowEndDevice = false;

  // Configuration
  static const Duration _backgroundCheckInterval = Duration(seconds: 30);
  static const Duration _memoryCheckInterval = Duration(minutes: 2);
  static const int _maxBackgroundDuration = 300; // 5 minutes

  /// Initialize the app lifecycle manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _detectDeviceCapabilities();
      _startLifecycleMonitoring();
      _startPeriodicChecks();
      _isInitialized = true;
      debugPrint('🔄 AppLifecycleManager initialized');
    } catch (e) {
      debugPrint('❌ AppLifecycleManager initialization failed: $e');
      _isInitialized = true; // Continue with defaults
    }
  }

  /// Detect device capabilities
  Future<void> _detectDeviceCapabilities() async {
    try {
      // For web platform, assume it's not low-end
      if (kIsWeb) {
        _isLowEndDevice = false;
        return;
      }

      if (Platform.isAndroid) {
        const platform = MethodChannel('com.kft.fitness/device_info');
        final result = await platform.invokeMethod('getMemoryInfo');
        final totalMemoryMB = result['totalMemory'] ?? 2048;
        _isLowEndDevice = totalMemoryMB < 3072; // Less than 3GB
      } else if (Platform.isIOS) {
        const platform = MethodChannel('com.kft.fitness/device_info');
        final result = await platform.invokeMethod('getMemoryInfo');
        final totalMemoryMB = result['totalMemory'] ?? 4096;
        _isLowEndDevice = totalMemoryMB < 3072; // Less than 3GB
      } else {
        _isLowEndDevice = false; // Default for other platforms
      }
    } catch (e) {
      debugPrint('⚠️ Error detecting device capabilities: $e');
      _isLowEndDevice = true; // Default to low-end for safety
    }
  }

  /// Start lifecycle monitoring
  void _startLifecycleMonitoring() {
    WidgetsBinding.instance.addObserver(this);
  }

  /// Start periodic checks
  void _startPeriodicChecks() {
    _backgroundTimer = Timer.periodic(_backgroundCheckInterval, (_) {
      _checkBackgroundDuration();
    });

    _memoryCheckTimer = Timer.periodic(_memoryCheckInterval, (_) {
      _performMemoryCheck();
    });
  }

  /// Check background duration
  void _checkBackgroundDuration() {
    if (_currentState == AppLifecycleState.paused || 
        _currentState == AppLifecycleState.inactive) {
      _backgroundDuration += _backgroundCheckInterval.inSeconds;
      
      if (_backgroundDuration >= _maxBackgroundDuration) {
        _handleExtendedBackground();
      }
    }
  }

  /// Perform memory check
  void _performMemoryCheck() {
    try {
      // For web platform, skip platform-specific memory checks
      if (kIsWeb) {
        return;
      }

      const platform = MethodChannel('com.kft.fitness/performance');
      platform.invokeMethod('checkMemoryPressure');
    } catch (e) {
      debugPrint('⚠️ Error performing memory check: $e');
    }
  }

  /// Handle extended background state
  void _handleExtendedBackground() {
    debugPrint('⚠️ App in background for extended period, optimizing resources');
    
    // Trigger garbage collection
    _triggerGarbageCollection();
    
    // Clear caches
    _clearCaches();
    
    // Save app state
    _saveAppState();
  }

  /// Trigger garbage collection
  void _triggerGarbageCollection() {
    try {
      // For web platform, skip platform-specific GC
      if (kIsWeb) {
        return;
      }

      const platform = MethodChannel('com.kft.fitness/performance');
      platform.invokeMethod('triggerGC');
    } catch (e) {
      debugPrint('⚠️ Error triggering GC: $e');
    }
  }

  // --- STUBS for missing methods ---
  void _clearCaches() {
    // Stub: implement cache clearing if needed
  }

  void _saveAppState() {
    // Stub: implement app state saving if needed
  }

  Future<void> _initializeServices() async {
    // Stub: implement service initialization if needed
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _currentState = state;
    // Add any additional logic here if needed
    super.didChangeAppLifecycleState(state);
  }

  /// Handle app resumed state
  Future<void> _handleAppResumed(AppLifecycleState previousState) async {
    _performanceMonitor.logEvent('app_resumed', {
      'previous_state': previousState.name,
      'background_duration_seconds': _backgroundDuration,
    });

    // Cancel background timer
    _backgroundTimer?.cancel();
    _backgroundTimer = null;

    // Check if app was in background for a long time
    if (_backgroundDuration > _maxBackgroundDuration) {
      await _handleLongBackgroundReturn();
    } else {
      await _handleShortBackgroundReturn();
    }

    _backgroundDuration = 0;

    // Refresh authentication if needed
    await _refreshAuthenticationIfNeeded();

    // Resume connectivity monitoring
    if (!_connectivityService.isConnected) {
      await _connectivityService.initialize();
    }
  }

  /// Handle app paused state
  Future<void> _handleAppPaused() async {
    _backgroundDuration = 0;

    _performanceMonitor.logEvent('app_paused', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Start background timer for cleanup
    _startBackgroundTimer();

    // Pause video playback
    await _pauseAllVideos();

    // Optimize memory after a delay
    Timer(_memoryCheckInterval, () {
      _performMemoryCheck();
    });
  }

  /// Handle app inactive state
  void _handleAppInactive() {
    _performanceMonitor.logEvent('app_inactive', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Prepare for potential backgrounding
    _prepareForBackground();
  }

  /// Handle app hidden state
  void _handleAppHidden() {
    _performanceMonitor.logEvent('app_hidden', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Handle app detached state
  Future<void> _handleAppDetached() async {
    _performanceMonitor.logEvent('app_detached', {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });

    // Cleanup all resources
    await _cleanupResources();
  }

  /// Handle return from long background
  Future<void> _handleLongBackgroundReturn() async {
    debugPrint('🔄 App returned from long background, performing full refresh');

    // Clear caches to free memory
    await _imageService.clearCache();

    // Reinitialize services
    await _initializeServices();

    // Trigger memory optimization
    _performanceMonitor.logEvent('long_background_return', {
      'background_duration_seconds': _backgroundDuration,
    });
  }

  /// Handle return from short background
  Future<void> _handleShortBackgroundReturn() async {
    debugPrint('🔄 App returned from short background, performing light refresh');

    // Light refresh - just check connectivity
    await _connectivityService.initialize();

    _performanceMonitor.logEvent('short_background_return', {
      'background_duration_seconds': _backgroundDuration,
    });
  }

  /// Start background timer for cleanup
  void _startBackgroundTimer() {
    _backgroundTimer = Timer(_backgroundCheckInterval, () {
      debugPrint('🔄 Background timeout reached, performing cleanup');
      _performBackgroundCleanup();
    });
  }

  /// Perform background cleanup
  Future<void> _performBackgroundCleanup() async {
    try {
      // Clear image caches
      await _imageService.clearCache();

      // Dispose unused video controllers
      await _videoService.dispose();

      // Clear performance metrics
      _performanceMonitor.logEvent('background_cleanup', {
        'cleanup_time': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('🧹 Background cleanup completed');
    } catch (e) {
      debugPrint('❌ Error during background cleanup: $e');
    }
  }

  /// Prepare for background
  void _prepareForBackground() {
    // Pause any ongoing operations
    _pauseNonEssentialOperations();
  }

  /// Pause all video playback
  Future<void> _pauseAllVideos() async {
    try {
      // This would pause all active video controllers
      // Implementation depends on video service design
      debugPrint('⏸️ Pausing all video playback');
    } catch (e) {
      debugPrint('⚠️ Error pausing videos: $e');
    }
  }

  /// Pause non-essential operations
  void _pauseNonEssentialOperations() {
    // Pause background tasks, animations, etc.
    debugPrint('⏸️ Pausing non-essential operations');
  }

  /// Optimize memory for background
  void _optimizeMemoryForBackground() {
    try {
      // Clear image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Trigger garbage collection
      _triggerGarbageCollection();

      _performanceMonitor.logEvent('background_memory_optimization', {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      debugPrint('🧹 Memory optimized for background');
    } catch (e) {
      debugPrint('⚠️ Error optimizing memory: $e');
    }
  }

  /// Refresh authentication if needed
  Future<void> _refreshAuthenticationIfNeeded() async {
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        // Check if token needs refresh
        // Note: refreshTokenIfNeeded method would need to be implemented in PersistentAuthService
        // For now, we'll just check if logged in
        debugPrint('🔐 Checking authentication status');
      }
    } catch (e) {
      debugPrint('⚠️ Error refreshing authentication: $e');
    }
  }

  /// Cleanup all resources
  Future<void> _cleanupResources() async {
    try {
      await Future.wait([
        _imageService.clearCache(),
        _videoService.dispose(),
      ]);

      _performanceMonitor.dispose();
      _connectivityService.dispose();

      debugPrint('🧹 All resources cleaned up');
    } catch (e) {
      debugPrint('❌ Error cleaning up resources: $e');
    }
  }

  /// Get current app state
  AppLifecycleState get currentState => _currentState;

  /// Check if app is in foreground
  bool get isInForeground => _currentState == AppLifecycleState.resumed;

  /// Check if app is in background
  bool get isInBackground => _currentState == AppLifecycleState.paused;

  /// Get lifecycle statistics
  Map<String, dynamic> getLifecycleStats() {
    return {
      'currentState': _currentState.name,
      'isInForeground': isInForeground,
      'isInBackground': isInBackground,
      'backgroundDuration': _backgroundDuration,
      'isInitialized': _isInitialized,
    };
  }

  /// Dispose lifecycle manager
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _backgroundTimer?.cancel();
    _cleanupResources();
    _isInitialized = false;
    debugPrint('🔄 AppLifecycleManager disposed');
  }
}
