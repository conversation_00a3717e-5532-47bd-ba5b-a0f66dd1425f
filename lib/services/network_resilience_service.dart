import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;

/// Network resilience service with offline support and intelligent retry logic
class NetworkResilienceService {
  static final NetworkResilienceService _instance = NetworkResilienceService._internal();
  factory NetworkResilienceService() => _instance;
  NetworkResilienceService._internal();

  // Network state
  bool _isInitialized = false;
  bool _isOnline = true;
  ConnectivityResult _currentConnectivity = ConnectivityResult.none;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  // Request queue for offline scenarios
  final List<QueuedRequest> _requestQueue = [];
  Timer? _syncTimer;
  
  // Network quality monitoring
  int _networkQuality = 100; // 0-100 scale
  DateTime? _lastQualityCheck;
  
  // Configuration
  static const Duration _syncInterval = Duration(seconds: 30);
  static const Duration _networkTimeout = Duration(seconds: 30);
  static const int _maxQueueSize = 100;
  static const String _queueStorageKey = 'network_request_queue';

  /// Initialize network resilience service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🌐 Initializing NetworkResilienceService...');

      // Check initial connectivity
      await _checkConnectivity();

      // Start connectivity monitoring
      _startConnectivityMonitoring();

      // Load queued requests from storage
      await _loadQueuedRequests();

      // Start periodic sync
      _startPeriodicSync();

      // Perform initial network quality check
      await _checkNetworkQuality();

      _isInitialized = true;
      print('✅ NetworkResilienceService initialized');
    } catch (e) {
      print('❌ NetworkResilienceService initialization failed: $e');
      _isInitialized = true; // Continue with degraded functionality
    }
  }

  /// Check current connectivity status
  Future<void> _checkConnectivity() async {
    try {
      final connectivityResults = await Connectivity().checkConnectivity();
      _currentConnectivity = connectivityResults.isNotEmpty 
          ? connectivityResults.first 
          : ConnectivityResult.none;
      
      _isOnline = _currentConnectivity != ConnectivityResult.none;
      
      print('🌐 Connectivity status: $_currentConnectivity (Online: $_isOnline)');
    } catch (e) {
      print('❌ Connectivity check failed: $e');
      _isOnline = false;
    }
  }

  /// Start monitoring connectivity changes
  void _startConnectivityMonitoring() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) async {
        final newConnectivity = results.isNotEmpty ? results.first : ConnectivityResult.none;
        final wasOnline = _isOnline;
        
        _currentConnectivity = newConnectivity;
        _isOnline = newConnectivity != ConnectivityResult.none;
        
        print('🌐 Connectivity changed: $newConnectivity (Online: $_isOnline)');
        
        // If we came back online, sync queued requests
        if (!wasOnline && _isOnline) {
          print('🔄 Network restored, syncing queued requests...');
          await _syncQueuedRequests();
          await _checkNetworkQuality();
        }
      },
      onError: (error) {
        print('❌ Connectivity monitoring error: $error');
      },
    );
  }

  /// Start periodic sync for queued requests
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_syncInterval, (_) async {
      if (_isOnline && _requestQueue.isNotEmpty) {
        await _syncQueuedRequests();
      }
    });
  }

  /// Execute request with retry logic and offline queueing
  Future<T> executeWithRetry<T>(
    Future<T> Function() request, {
    int maxAttempts = 3,
    double backoffMultiplier = 2.0,
    Duration initialDelay = const Duration(seconds: 1),
    bool queueIfOffline = true,
  }) async {
    if (!_isOnline && queueIfOffline) {
      // Queue request for later execution
      final queuedRequest = QueuedRequest(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        request: request,
        maxAttempts: maxAttempts,
        backoffMultiplier: backoffMultiplier,
        initialDelay: initialDelay,
        timestamp: DateTime.now(),
      );
      
      await _queueRequest(queuedRequest);
      throw NetworkException('Request queued for offline execution');
    }

    Exception? lastException;
    Duration delay = initialDelay;

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        print('🔄 Network request attempt $attempt/$maxAttempts');
        
        // Adjust timeout based on network quality
        final timeout = _calculateTimeout();
        
        return await request().timeout(timeout);
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        
        print('❌ Request attempt $attempt failed: $e');
        
        if (attempt < maxAttempts) {
          // Check if we should continue retrying
          if (_shouldRetry(e)) {
            print('⏳ Retrying in ${delay.inSeconds}s...');
            await Future.delayed(delay);
            delay = Duration(milliseconds: (delay.inMilliseconds * backoffMultiplier).round());
          } else {
            print('🚫 Non-retryable error, stopping attempts');
            break;
          }
        }
      }
    }

    throw lastException ?? Exception('Request failed after $maxAttempts attempts');
  }

  /// Calculate timeout based on network quality
  Duration _calculateTimeout() {
    if (_networkQuality >= 80) {
      return const Duration(seconds: 10);
    } else if (_networkQuality >= 50) {
      return const Duration(seconds: 20);
    } else {
      return _networkTimeout;
    }
  }

  /// Determine if an error should trigger a retry
  bool _shouldRetry(dynamic error) {
    if (error is SocketException) {
      return true; // Network connectivity issues
    }
    if (error is TimeoutException) {
      return true; // Timeout issues
    }
    if (error is HttpException) {
      return true; // HTTP-level issues
    }
    if (error.toString().contains('Connection refused')) {
      return true;
    }
    if (error.toString().contains('Network is unreachable')) {
      return true;
    }
    
    return false; // Don't retry for other types of errors
  }

  /// Queue request for offline execution
  Future<void> _queueRequest(QueuedRequest request) async {
    try {
      if (_requestQueue.length >= _maxQueueSize) {
        // Remove oldest request to make room
        _requestQueue.removeAt(0);
        print('⚠️ Request queue full, removed oldest request');
      }

      _requestQueue.add(request);
      await _saveQueuedRequests();
      
      print('📥 Request queued for offline execution (Queue size: ${_requestQueue.length})');
    } catch (e) {
      print('❌ Failed to queue request: $e');
    }
  }

  /// Sync all queued requests
  Future<void> _syncQueuedRequests() async {
    if (!_isOnline || _requestQueue.isEmpty) return;

    print('🔄 Syncing ${_requestQueue.length} queued requests...');
    
    final requestsToProcess = List<QueuedRequest>.from(_requestQueue);
    _requestQueue.clear();

    int successCount = 0;
    int failureCount = 0;

    for (final queuedRequest in requestsToProcess) {
      try {
        await executeWithRetry(
          queuedRequest.request,
          maxAttempts: queuedRequest.maxAttempts,
          backoffMultiplier: queuedRequest.backoffMultiplier,
          initialDelay: queuedRequest.initialDelay,
          queueIfOffline: false, // Don't re-queue during sync
        );
        successCount++;
      } catch (e) {
        print('❌ Failed to sync queued request ${queuedRequest.id}: $e');
        
        // Re-queue if it's a retryable error and not too old
        if (_shouldRetry(e) && _isRequestStillValid(queuedRequest)) {
          _requestQueue.add(queuedRequest);
        }
        failureCount++;
      }
    }

    await _saveQueuedRequests();
    print('✅ Sync complete: $successCount successful, $failureCount failed');
  }

  /// Check if a queued request is still valid (not too old)
  bool _isRequestStillValid(QueuedRequest request) {
    const maxAge = Duration(hours: 24);
    return DateTime.now().difference(request.timestamp) < maxAge;
  }

  /// Save queued requests to persistent storage
  Future<void> _saveQueuedRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueData = _requestQueue.map((r) => r.toJson()).toList();
      await prefs.setString(_queueStorageKey, json.encode(queueData));
    } catch (e) {
      print('❌ Failed to save request queue: $e');
    }
  }

  /// Load queued requests from persistent storage
  Future<void> _loadQueuedRequests() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final queueDataString = prefs.getString(_queueStorageKey);
      
      if (queueDataString != null) {
        final List<dynamic> queueData = json.decode(queueDataString);
        
        for (final requestData in queueData) {
          // Note: We can't restore the actual function, so we'll need to
          // implement a different approach for persistent request queueing
          // This is a simplified version for demonstration
        }
        
        print('📥 Loaded ${_requestQueue.length} queued requests from storage');
      }
    } catch (e) {
      print('❌ Failed to load request queue: $e');
    }
  }

  /// Check network quality
  Future<void> _checkNetworkQuality() async {
    if (!_isOnline) {
      _networkQuality = 0;
      return;
    }

    try {
      final stopwatch = Stopwatch()..start();
      
      // Perform a simple network test
      final response = await http.get(
        Uri.parse('https://www.google.com'),
        headers: {'Cache-Control': 'no-cache'},
      ).timeout(const Duration(seconds: 5));
      
      stopwatch.stop();
      
      if (response.statusCode == 200) {
        final latency = stopwatch.elapsedMilliseconds;
        
        // Calculate quality based on latency
        if (latency < 100) {
          _networkQuality = 100;
        } else if (latency < 300) {
          _networkQuality = 80;
        } else if (latency < 1000) {
          _networkQuality = 60;
        } else if (latency < 3000) {
          _networkQuality = 40;
        } else {
          _networkQuality = 20;
        }
        
        print('🌐 Network quality: $_networkQuality% (${latency}ms latency)');
      } else {
        _networkQuality = 30;
      }
      
      _lastQualityCheck = DateTime.now();
    } catch (e) {
      print('❌ Network quality check failed: $e');
      _networkQuality = 10;
    }
  }

  /// Get current network status
  NetworkStatus get networkStatus => NetworkStatus(
    isOnline: _isOnline,
    connectivity: _currentConnectivity,
    quality: _networkQuality,
    queuedRequests: _requestQueue.length,
  );

  /// Check if device is online
  bool get isOnline => _isOnline;

  /// Get network quality (0-100)
  int get networkQuality => _networkQuality;

  /// Get number of queued requests
  int get queuedRequestCount => _requestQueue.length;

  /// Dispose the service
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _isInitialized = false;
  }
}

/// Queued request model
class QueuedRequest {
  final String id;
  final Future Function() request;
  final int maxAttempts;
  final double backoffMultiplier;
  final Duration initialDelay;
  final DateTime timestamp;

  QueuedRequest({
    required this.id,
    required this.request,
    required this.maxAttempts,
    required this.backoffMultiplier,
    required this.initialDelay,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'maxAttempts': maxAttempts,
    'backoffMultiplier': backoffMultiplier,
    'initialDelay': initialDelay.inMilliseconds,
    'timestamp': timestamp.toIso8601String(),
  };
}

/// Network status model
class NetworkStatus {
  final bool isOnline;
  final ConnectivityResult connectivity;
  final int quality;
  final int queuedRequests;

  NetworkStatus({
    required this.isOnline,
    required this.connectivity,
    required this.quality,
    required this.queuedRequests,
  });
}

/// Network exception
class NetworkException implements Exception {
  final String message;
  NetworkException(this.message);
  
  @override
  String toString() => 'NetworkException: $message';
}
