import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/painting.dart';
import 'package:device_info_plus/device_info_plus.dart';

/// Production-grade performance monitoring service
/// Tracks app performance, memory usage, and provides optimization insights
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  // Performance metrics
  final Map<String, DateTime> _operationStartTimes = {};
  final Map<String, List<Duration>> _operationDurations = {};
  final List<PerformanceMetric> _metrics = [];

  // Device information
  DeviceInfo? _deviceInfo;
  bool _isLowEndDevice = false;
  Timer? _memoryMonitorTimer;

  // Memory tracking
  int _peakMemoryUsage = 0;
  int _currentMemoryUsage = 0;
  final List<MemorySnapshot> _memorySnapshots = [];

  /// Initialize performance monitoring
  Future<void> initialize() async {
    await _loadDeviceInfo();
    _startMemoryMonitoring();
    debugPrint('📊 PerformanceMonitor initialized for ${_isLowEndDevice ? "low-end" : "high-end"} device');
  }

  /// Load device information
  Future<void> _loadDeviceInfo() async {
    try {
      // For web platform, use simplified device info
      if (kIsWeb) {
        _deviceInfo = DeviceInfo(
          platform: 'Web',
          model: 'Web Browser',
          version: 'Unknown',
          totalMemory: 4.0, // Assume 4GB for web
        );
        _isLowEndDevice = false; // Assume web is not low-end
        return;
      }

      final deviceInfoPlugin = DeviceInfoPlugin();

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfoPlugin.androidInfo;
        _deviceInfo = DeviceInfo(
          platform: 'Android',
          model: androidInfo.model,
          version: androidInfo.version.release,
          sdkInt: androidInfo.version.sdkInt,
          manufacturer: androidInfo.manufacturer,
          totalMemory: await _getAndroidMemoryInfo(),
        );
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfoPlugin.iosInfo;
        _deviceInfo = DeviceInfo(
          platform: 'iOS',
          model: iosInfo.model,
          version: iosInfo.systemVersion,
          name: iosInfo.name,
          totalMemory: await _getIOSMemoryInfo(),
        );
      }

      // Determine if device is low-end
      _isLowEndDevice = _deviceInfo!.totalMemory < 3.0 ||
                       (_deviceInfo!.sdkInt != null && _deviceInfo!.sdkInt! < 26);

    } catch (e) {
      debugPrint('⚠️ Error loading device info: $e');
      _isLowEndDevice = true; // Default to low-end for safety
    }
  }

  /// Get Android memory information
  Future<double> _getAndroidMemoryInfo() async {
    try {
      const platform = MethodChannel('com.kft.fitness/device_info');
      final result = await platform.invokeMethod('getMemoryInfo');
      return (result['totalMemory'] ?? 2048) / 1024.0; // Convert MB to GB
    } catch (e) {
      return 2.0; // Default 2GB
    }
  }

  /// Get iOS memory information
  Future<double> _getIOSMemoryInfo() async {
    try {
      const platform = MethodChannel('com.kft.fitness/device_info');
      final result = await platform.invokeMethod('getMemoryInfo');
      return (result['totalMemory'] ?? 4096) / 1024.0; // Convert MB to GB
    } catch (e) {
      return 4.0; // Default 4GB for iOS
    }
  }

  /// Start memory monitoring
  void _startMemoryMonitoring() {
    _memoryMonitorTimer = Timer.periodic(
      Duration(seconds: _isLowEndDevice ? 30 : 60), // More frequent on low-end devices
      (_) => _captureMemorySnapshot(),
    );
  }

  /// Capture memory snapshot
  Future<void> _captureMemorySnapshot() async {
    try {
      // For web platform, skip memory monitoring
      if (kIsWeb) {
        return;
      }

      const platform = MethodChannel('com.kft.fitness/performance');
      final result = await platform.invokeMethod('getMemoryUsage');

      _currentMemoryUsage = result['currentMemory'] ?? 0;
      if (_currentMemoryUsage > _peakMemoryUsage) {
        _peakMemoryUsage = _currentMemoryUsage;
      }

      _memorySnapshots.add(MemorySnapshot(
        timestamp: DateTime.now(),
        memoryUsageMB: _currentMemoryUsage / (1024 * 1024),
        availableMemoryMB: (result['availableMemory'] ?? 0) / (1024 * 1024),
      ));

      // Keep only last 100 snapshots
      if (_memorySnapshots.length > 100) {
        _memorySnapshots.removeAt(0);
      }

      // Check for memory pressure
      if (_isMemoryPressureHigh()) {
        _handleMemoryPressure();
      }
    } catch (e) {
      debugPrint('⚠️ Error capturing memory snapshot: $e');
    }
  }

  /// Check if memory pressure is high
  bool _isMemoryPressureHigh() {
    if (_memorySnapshots.isEmpty) return false;

    final latest = _memorySnapshots.last;
    final threshold = _isLowEndDevice ? 80.0 : 85.0; // Percentage

    return (latest.memoryUsageMB / (_deviceInfo?.totalMemory ?? 2.0) * 100) > threshold;
  }

  /// Handle memory pressure
  void _handleMemoryPressure() {
    debugPrint('⚠️ High memory pressure detected, triggering cleanup');

    // Trigger garbage collection
    _triggerGarbageCollection();

    // Clear image caches
    _clearImageCaches();

    // Log memory pressure event
    logEvent('memory_pressure', {
      'current_memory_mb': _currentMemoryUsage / (1024 * 1024),
      'peak_memory_mb': _peakMemoryUsage / (1024 * 1024),
      'device_total_gb': _deviceInfo?.totalMemory ?? 0,
      'is_low_end_device': _isLowEndDevice,
    });
  }

  /// Trigger garbage collection
  void _triggerGarbageCollection() {
    try {
      // For web platform, skip platform-specific GC
      if (kIsWeb) {
        return;
      }

      const platform = MethodChannel('com.kft.fitness/performance');
      platform.invokeMethod('triggerGC');
    } catch (e) {
      debugPrint('⚠️ Error triggering GC: $e');
    }
  }

  /// Clear image caches to free memory
  void _clearImageCaches() {
    try {
      // Clear Flutter's image cache
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Clear our optimized image cache
      // This would be called on OptimizedImageService if available
    } catch (e) {
      debugPrint('⚠️ Error clearing image caches: $e');
    }
  }

  /// Start timing an operation
  void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  /// End timing an operation
  void endOperation(String operationName) {
    final startTime = _operationStartTimes.remove(operationName);
    if (startTime != null) {
      final duration = DateTime.now().difference(startTime);

      _operationDurations.putIfAbsent(operationName, () => []).add(duration);

      // Keep only last 50 measurements per operation
      final durations = _operationDurations[operationName]!;
      if (durations.length > 50) {
        durations.removeAt(0);
      }

      // Log slow operations
      if (duration.inMilliseconds > 1000) {
        debugPrint('🐌 Slow operation detected: $operationName took ${duration.inMilliseconds}ms');
        logEvent('slow_operation', {
          'operation': operationName,
          'duration_ms': duration.inMilliseconds,
          'is_low_end_device': _isLowEndDevice,
        });
      }
    }
  }

  /// Log a performance event
  void logEvent(String eventName, Map<String, dynamic> parameters) {
    final metric = PerformanceMetric(
      timestamp: DateTime.now(),
      eventName: eventName,
      parameters: parameters,
      deviceInfo: _deviceInfo,
    );

    _metrics.add(metric);

    // Keep only last 1000 metrics
    if (_metrics.length > 1000) {
      _metrics.removeAt(0);
    }

    // In production, send to analytics service
    if (kReleaseMode) {
      _sendToAnalytics(metric);
    } else {
      debugPrint('📊 Performance Event: $eventName - $parameters');
    }
  }

  /// Send metrics to analytics service
  void _sendToAnalytics(PerformanceMetric metric) {
    // Implementation would depend on your analytics provider
    // e.g., Firebase Analytics, Crashlytics, etc.
  }

  /// Get operation statistics
  Map<String, OperationStats> getOperationStats() {
    final stats = <String, OperationStats>{};

    for (final entry in _operationDurations.entries) {
      final durations = entry.value;
      if (durations.isNotEmpty) {
        final totalMs = durations.fold<int>(0, (sum, d) => sum + d.inMilliseconds);
        final avgMs = totalMs / durations.length;
        final maxMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a > b ? a : b);
        final minMs = durations.map((d) => d.inMilliseconds).reduce((a, b) => a < b ? a : b);

        stats[entry.key] = OperationStats(
          operationName: entry.key,
          averageDurationMs: avgMs,
          maxDurationMs: maxMs,
          minDurationMs: minMs,
          totalCalls: durations.length,
        );
      }
    }

    return stats;
  }

  /// Get memory statistics
  MemoryStats getMemoryStats() {
    return MemoryStats(
      currentMemoryMB: _currentMemoryUsage / (1024 * 1024),
      peakMemoryMB: _peakMemoryUsage / (1024 * 1024),
      deviceTotalMemoryGB: _deviceInfo?.totalMemory ?? 0,
      isLowEndDevice: _isLowEndDevice,
      snapshots: List.from(_memorySnapshots),
    );
  }

  /// Get device information
  DeviceInfo? getDeviceInfo() => _deviceInfo;

  /// Check if device is low-end
  bool get isLowEndDevice => _isLowEndDevice;

  /// Get performance recommendations
  List<String> getPerformanceRecommendations() {
    final recommendations = <String>[];

    if (_isLowEndDevice) {
      recommendations.add('Device detected as low-end. Consider reducing animation complexity.');
      recommendations.add('Enable aggressive image caching and compression.');
      recommendations.add('Reduce concurrent network requests.');
    }

    final memoryStats = getMemoryStats();
    if (memoryStats.currentMemoryMB > 200) {
      recommendations.add('High memory usage detected. Consider clearing caches.');
    }

    final operationStats = getOperationStats();
    for (final stat in operationStats.values) {
      if (stat.averageDurationMs > 500) {
        recommendations.add('Operation "${stat.operationName}" is slow (${stat.averageDurationMs.toStringAsFixed(0)}ms avg).');
      }
    }

    return recommendations;
  }

  /// Dispose resources
  void dispose() {
    _memoryMonitorTimer?.cancel();
    _operationStartTimes.clear();
    _operationDurations.clear();
    _metrics.clear();
    _memorySnapshots.clear();
  }
}

/// Device information model
class DeviceInfo {
  final String platform;
  final String model;
  final String version;
  final int? sdkInt;
  final String? manufacturer;
  final String? name;
  final double totalMemory; // in GB

  DeviceInfo({
    required this.platform,
    required this.model,
    required this.version,
    this.sdkInt,
    this.manufacturer,
    this.name,
    required this.totalMemory,
  });
}

/// Performance metric model
class PerformanceMetric {
  final DateTime timestamp;
  final String eventName;
  final Map<String, dynamic> parameters;
  final DeviceInfo? deviceInfo;

  PerformanceMetric({
    required this.timestamp,
    required this.eventName,
    required this.parameters,
    this.deviceInfo,
  });
}

/// Memory snapshot model
class MemorySnapshot {
  final DateTime timestamp;
  final double memoryUsageMB;
  final double availableMemoryMB;

  MemorySnapshot({
    required this.timestamp,
    required this.memoryUsageMB,
    required this.availableMemoryMB,
  });
}

/// Operation statistics model
class OperationStats {
  final String operationName;
  final double averageDurationMs;
  final int maxDurationMs;
  final int minDurationMs;
  final int totalCalls;

  OperationStats({
    required this.operationName,
    required this.averageDurationMs,
    required this.maxDurationMs,
    required this.minDurationMs,
    required this.totalCalls,
  });
}

/// Memory statistics model
class MemoryStats {
  final double currentMemoryMB;
  final double peakMemoryMB;
  final double deviceTotalMemoryGB;
  final bool isLowEndDevice;
  final List<MemorySnapshot> snapshots;

  MemoryStats({
    required this.currentMemoryMB,
    required this.peakMemoryMB,
    required this.deviceTotalMemoryGB,
    required this.isLowEndDevice,
    required this.snapshots,
  });
}
