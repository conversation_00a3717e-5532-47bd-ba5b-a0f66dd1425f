import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import 'api_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  static const FlutterSecureStorage _storage = FlutterSecureStorage();
  static const String _userIdKey = 'user_id';
  static const String _userNameKey = 'user_name';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenExpiryKey = 'token_expiry';

  // Use existing ApiService for token management
  final ApiService _apiService = ApiService();

  // Stream controller for authentication state changes
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  Stream<bool> get authStateStream => _authStateController.stream;

  // Current authentication state
  bool _isAuthenticated = false;
  bool get isAuthenticated => _isAuthenticated;

  // Token refresh lock to prevent multiple simultaneous refresh attempts
  final _refreshLock = Lock();

  // Initialize the auth service
  Future<void> initialize() async {
    await _checkAuthState();
  }

  // Check current authentication state
  Future<void> _checkAuthState() async {
    try {
      final token = await getToken();
      _isAuthenticated = token != null && token.isNotEmpty;
      _authStateController.add(_isAuthenticated);
    } catch (e) {
      debugPrint('Error checking auth state: $e');
      _isAuthenticated = false;
      _authStateController.add(false);
    }
  }

  // Get stored token (delegate to ApiService)
  Future<String?> getToken() async {
    return await _apiService.getToken();
  }

  // Get refresh token
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  // Get token expiry time
  Future<DateTime?> getTokenExpiry() async {
    final expiryStr = await _storage.read(key: _tokenExpiryKey);
    if (expiryStr == null) return null;
    return DateTime.parse(expiryStr);
  }

  // Check if token is expired or about to expire (within 5 minutes)
  Future<bool> isTokenExpired() async {
    final expiry = await getTokenExpiry();
    if (expiry == null) return true;
    
    // Consider token expired if it's within 5 minutes of expiry
    return DateTime.now().isAfter(expiry.subtract(const Duration(minutes: 5)));
  }

  // Refresh the access token using refresh token
  Future<bool> refreshToken() async {
    // Use lock to prevent multiple simultaneous refresh attempts
    return await _refreshLock.synchronized(() async {
      try {
        final refreshToken = await getRefreshToken();
        if (refreshToken == null) {
          debugPrint('No refresh token available');
          return false;
        }

        final response = await http.post(
          Uri.parse('${AppConfig.defaultApiBaseUrl}/refresh_token.php'),
          headers: {'Content-Type': 'application/json'},
          body: jsonEncode({'refresh_token': refreshToken}),
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);
          await saveAuthData(
            token: data['access_token'],
            refreshToken: data['refresh_token'],
            expiresIn: data['expires_in'],
          );
          return true;
        } else {
          debugPrint('Token refresh failed: ${response.statusCode}');
          return false;
        }
      } catch (e) {
        debugPrint('Error refreshing token: $e');
        return false;
      }
    });
  }

  // Validate token with server
  Future<bool> _validateToken(String token) async {
    try {
      final response = await http.get(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/profile.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
        },
      ).timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Token validation failed: $e');
      return false;
    }
  }

  // Save authentication data
  Future<void> saveAuthData({
    required String token,
    String? refreshToken,
    int? expiresIn,
    int? userId,
    String? userName,
  }) async {
    try {
      // Save token using ApiService
      await _apiService.saveToken(token);

      // Save refresh token if provided
      if (refreshToken != null) {
        await _storage.write(key: _refreshTokenKey, value: refreshToken);
      }

      // Save token expiry if provided
      if (expiresIn != null) {
        final expiry = DateTime.now().add(Duration(seconds: expiresIn));
        await _storage.write(key: _tokenExpiryKey, value: expiry.toIso8601String());
      }

      // Save additional user data
      if (userId != null) {
        await _storage.write(key: _userIdKey, value: userId.toString());
      }

      if (userName != null) {
        await _storage.write(key: _userNameKey, value: userName);
      }

      _isAuthenticated = true;
      _authStateController.add(true);

      debugPrint('Auth data saved successfully');
    } catch (e) {
      debugPrint('Error saving auth data: $e');
      throw Exception('Failed to save authentication data');
    }
  }

  // Get user ID
  Future<int?> getUserId() async {
    try {
      final userIdStr = await _storage.read(key: _userIdKey);
      return userIdStr != null ? int.tryParse(userIdStr) : null;
    } catch (e) {
      debugPrint('Error getting user ID: $e');
      return null;
    }
  }

  // Get user name
  Future<String?> getUserName() async {
    try {
      return await _storage.read(key: _userNameKey);
    } catch (e) {
      debugPrint('Error getting user name: $e');
      return null;
    }
  }

  // Check if user needs to re-authenticate
  Future<bool> needsReauth() async {
    final token = await getToken();
    final refreshToken = await getRefreshToken();
    return token == null || token.isEmpty || refreshToken == null || refreshToken.isEmpty;
  }

  // Refresh authentication state
  Future<void> refreshAuthState() async {
    await _checkAuthState();
  }

  // Dispose resources
  void dispose() {
    _authStateController.close();
  }
}

// Simple lock implementation for synchronizing token refresh
class Lock {
  Future<void> _lock = Future.value();
  Future<T> synchronized<T>(Future<T> Function() computation) async {
    final previous = _lock;
    final completer = Completer<void>();
    _lock = completer.future;
    try {
      await previous;
      return await computation();
    } finally {
      completer.complete();
    }
  }
}
