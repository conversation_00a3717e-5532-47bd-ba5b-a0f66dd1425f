import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import '../models/course.dart';
import '../models/course_video.dart';
import '../models/purchasable_course.dart';
import '../models/food_item.dart';
import '../models/motivational_quote.dart';
import '../utils/json_utils.dart';
import 'package:http/http.dart' show Response;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';

import '../config/app_config.dart';
import '../config/network_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import '../providers/auth_provider.dart';
import 'package:provider/provider.dart' as provider;
import 'persistent_auth_service.dart';

// Cache entry class
class _CacheEntry {
  final dynamic data;
  final DateTime timestamp;
  
  _CacheEntry(this.data) : timestamp = DateTime.now();
  
  bool get isExpired => DateTime.now().difference(timestamp) > const Duration(minutes: 5);
}

// Cancel token class
class CancelToken {
  bool _isCancelled = false;
  void cancel() => _isCancelled = true;
  bool get isCancelled => _isCancelled;
}

// Custom exception for authentication errors
class AuthenticationException implements Exception {
  final String message;
  AuthenticationException(this.message);

  @override
  String toString() => 'AuthenticationException: $message';
}

final storage = FlutterSecureStorage();

class ApiService {
  // Base URL with setter for dynamic updates
  static String _baseUrl = AppConfig.defaultApiBaseUrl;

  // Getter for base URL
  static String get baseUrl => _baseUrl;

  // HTTP client - now uses global HttpOverrides for SSL handling
  static http.Client? _httpClient;

  static http.Client get httpClient {
    if (_httpClient == null) {
      _httpClient = http.Client();
      print('Created HTTP client (SSL handling via global HttpOverrides)');
    }
    return _httpClient!;
  }

  // Cache configuration
  static final Map<String, _CacheEntry> _cache = {};
  
  // Request timeout configuration
  static const Duration _requestTimeout = Duration(seconds: 10);
  
  // Retry configuration
  static const int _maxRetries = 3;
  static const Duration _initialRetryDelay = Duration(seconds: 1);

  // Request cancellation
  final Map<String, CancelToken> _cancelTokens = {};

  // For debugging
  void _logRequest(String method, String url, Map<String, dynamic>? body) {
    print('API Request: $method $url');
    if (body != null) {
      print('Request Body: ${jsonEncode(body)}');
    }
    print('Request Headers: Content-Type: application/json');
  }

  void _logResponse(http.Response response) {
    print('API Response: ${response.statusCode}');
    print('Response Headers: ${response.headers}');

    // Try to format JSON response for better readability
    try {
      if (response.body.isNotEmpty && response.headers['content-type']?.contains('application/json') == true) {
        final jsonData = jsonDecode(response.body);
        print('Response Body (JSON): ${jsonEncode(jsonData)}');
      } else {
        print('Response Body (Raw): ${response.body}');
      }
    } catch (e) {
      print('Response Body (Raw): ${response.body}');
      print('Error formatting response: $e');
    }
  }

  // Token storage key
  static const String _tokenKey = 'auth_token';

  // Singleton instance
  static final ApiService _instance = ApiService._internal();

  factory ApiService() {
    return _instance;
  }

  // Private constructor for singleton
  ApiService._internal();

  /// Initialize API service with automatic endpoint detection
  static Future<void> initialize() async {
    if (kDebugMode) {
      print('Initializing API service in debug mode...');
      // Use cached endpoint if available, otherwise find working endpoint
      final cachedEndpoint = await NetworkConfig.getCachedEndpoint();
      if (cachedEndpoint != null) {
        _baseUrl = cachedEndpoint;
        print('API service initialized with cached endpoint: $_baseUrl');
        // Verify endpoint in background
        _verifyEndpointInBackground();
      } else {
        final workingEndpoint = await NetworkConfig.findWorkingEndpoint();
        _baseUrl = workingEndpoint;
        print('API service initialized with endpoint: $_baseUrl');
      }
    } else {
      _baseUrl = AppConfig.defaultApiBaseUrl;
      print('API service initialized with production endpoint: $_baseUrl');
    }
  }

  /// Verify endpoint in background and update if needed
  static void _verifyEndpointInBackground() async {
    try {
      final workingEndpoint = await NetworkConfig.findWorkingEndpoint();
      if (workingEndpoint != _baseUrl) {
        _baseUrl = workingEndpoint;
        print('API endpoint updated in background: $_baseUrl');
      }
    } catch (e) {
      print('Background endpoint verification failed: $e');
    }
  }

  /// Switch to next available endpoint (debug mode only)
  static String switchEndpoint() {
    if (kDebugMode) {
      _baseUrl = NetworkConfig.switchToNextEndpoint();
      print('Switched API endpoint to: $_baseUrl');
      return _baseUrl;
    }
    return _baseUrl;
  }

  /// Get network status and suggestions
  static Map<String, dynamic> getNetworkStatus() {
    return NetworkConfig.getNetworkStatus();
  }

  // Default constructor for subclasses
  ApiService.create();

  // Get the stored auth token using persistent auth service
  Future<String?> getToken() async {
    try {
      // Use the persistent auth service for token management
      final persistentAuthService = PersistentAuthService();
      final token = await persistentAuthService.getToken();

      if (token != null) {
        print('Token loaded from persistent auth service: ${token.substring(0, 20)}...');
        return token;
      }

      // Fall back to legacy storage for backward compatibility
      final secureToken = await storage.read(key: 'auth_token');
      if (secureToken != null) {
        print('Token loaded from legacy secure storage: ${secureToken.substring(0, 20)}...');
        return secureToken;
      }

      // Fall back to SharedPreferences for backward compatibility
      final prefs = await SharedPreferences.getInstance();
      final legacyToken = prefs.getString(_tokenKey);

      // If token exists in SharedPreferences, migrate it to persistent auth service
      if (legacyToken != null) {
        print('Migrating token from SharedPreferences to persistent auth service');
        await storage.write(key: 'auth_token', value: legacyToken);
        await prefs.remove(_tokenKey); // Remove from SharedPreferences after migration
      }

      if (legacyToken != null) {
        print('Token loaded from SharedPreferences: ${legacyToken.substring(0, 20)}...');
      } else {
        print('No token found in any storage');
      }
      return legacyToken;
    } catch (e) {
      print('Error getting token: $e');
      return null;
    }
  }

  // Save the auth token
  Future<void> saveToken(String token) async {
    try {
      // Save to FlutterSecureStorage
      await storage.write(key: 'auth_token', value: token);
      print('Token saved to secure storage: ${token.substring(0, 20)}...');
    } catch (e) {
      print('Error saving token to secure storage: $e');
      // Fall back to SharedPreferences if secure storage fails
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
      print('Token saved to SharedPreferences as fallback: ${token.substring(0, 20)}...');
    }
  }

  // Clear the auth token (logout)
  Future<void> clearToken({bool force = false, String? backendError}) async {
    // Only clear tokens if force is true or backendError is user_deleted/inactive
    if (!force && backendError != 'user_deleted' && backendError != 'inactive') {
      print('Not clearing token: not a user deletion/inactive event');
      return;
    }
    try {
      // Clear from both storage mechanisms
      await storage.delete(key: 'auth_token');
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      print('Token cleared from all storage');
    } catch (e) {
      print('Error clearing token: $e');
    }
  }

  // Check if user is logged in using persistent auth service
  Future<bool> isLoggedIn() async {
    try {
      final persistentAuthService = PersistentAuthService();
      final isLoggedIn = await persistentAuthService.isLoggedIn();
      print('ApiService.isLoggedIn() result: $isLoggedIn');
      return isLoggedIn;
    } catch (e) {
      print('Error checking login status with persistent auth service: $e');
      // Fall back to token check
      final token = await getToken();
      return token != null && token.isNotEmpty;
    }
  }

  // Attempt to refresh the token
  Future<bool> refreshToken() async {
    final token = await getToken();

    if (token == null) {
      print('No token found to refresh');
      return false;
    }

    try {
      // Try to validate the token with a lightweight API call
      final response = await httpClient.get(
        Uri.parse('$baseUrl/profile.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      // If the token is valid, return true
      if (response.statusCode == 200) {
        print('Token is still valid');

        // Re-save the token to ensure it's stored correctly in secure storage
        await saveToken(token);

        return true;
      }

      // If we get a 401, the token is expired or invalid
      if (response.statusCode == 401) {
        print('Token is expired or invalid, clearing it');
        await clearToken();
        return false;
      }

      // If the token is invalid for other reasons, clear it and return false
      print('Token is invalid (status ${response.statusCode}), clearing it');
      await clearToken();
      return false;
    } catch (e) {
      print('Error refreshing token: $e');
      return false;
    }
  }

  // Login with phone number and PIN
  Future<Map<String, dynamic>> login(String phoneNumber, {required String pin, required String deviceId, required String baseUrl}) async {
    try {
      final url = 'login.php';
      final data = {
        'phone_number': phoneNumber,
        'pin': pin,
        'device_id': deviceId,
      };

      final response = await makeApiRequest(
        url,
        method: 'POST',
        data: data,
        requiresAuth: false,
      );

      if (response['success'] == true) {
        // Defensive null checks
        if (response['token'] == null) {
          throw Exception('Login failed: Missing token from server. Please try again or contact support.');
        }

        // Save the token
        await saveToken(response['token']);
        return response;
      }

      // For specific error types, pass the entire error response
      if (response['error'] == 'already_logged_in' ||
          response['error'] == 'already_logged_in ' || // Handle extra space
          response['error'] == 'device_mismatch') {
        throw Exception(jsonEncode(response));
      } else {
        throw Exception(response['message'] ?? response['error'] ?? 'Login failed');
      }
    } catch (e) {
      print('Login error: $e');
      if (e is Exception) {
        rethrow;
      }
      throw Exception('Network error: $e');
    }
  }

  // Get user profile from API
  Future<UserProfile> getUserProfile() async {
    try {
      final response = await makeApiRequest('profile.php');

      if (response['success'] == true && response['profile'] != null) {
        // Ensure profile is a Map<String, dynamic>
        final profileData = response['profile'];
        final Map<String, dynamic> profile;

        if (profileData is Map<String, dynamic>) {
          profile = profileData;
        } else if (profileData is Map) {
          profile = Map<String, dynamic>.from(profileData);
        } else {
          throw Exception('Profile data is not in expected format');
        }

        // Create UserProfile from API data
        final userProfile = UserProfile(
          name: profile['name']?.toString() ?? 'Unknown',
          username: profile['username']?.toString() ?? '',
          email: profile['email']?.toString() ?? '',
          phoneNumber: profile['phone_number']?.toString() ?? profile['phone']?.toString() ?? '',
          height: _parseDouble(profile['height'], 170.0),
          weight: _parseDouble(profile['weight'], 70.0),
          isPremium: profile['is_premium'] == 1 || profile['is_premium'] == true,
          profileImageUrl: profile['profile_image_url']?.toString() ?? '',
          assignedStaffId: profile['assigned_staff_id'],
        );

        print('Loaded profile with image URL: ${userProfile.profileImageUrl}');

        // Add BMI records if available
        if (response['bmi_records'] != null && response['bmi_records'] is List) {
          for (var record in response['bmi_records']) {
            if (record is Map) {
              final recordMap = record is Map<String, dynamic> ? record : Map<String, dynamic>.from(record);
              try {
                userProfile.bmiHistory.add(BMIRecord(
                  date: DateTime.parse(recordMap['recorded_at']?.toString() ?? DateTime.now().toIso8601String()),
                  bmi: _parseDouble(recordMap['bmi'], 0.0),
                  weight: _parseDouble(recordMap['weight'], 0.0),
                ));
              } catch (e) {
                print('Error parsing BMI record: $e');
              }
            }
          }
        }

        // Add streak days if available
        if (response['streak_days'] != null && response['streak_days'] is List) {
          for (var day in response['streak_days']) {
            try {
              userProfile.streakDays.add(DateTime.parse(day.toString()));
            } catch (e) {
              print('Error parsing streak day: $e');
            }
          }
        }

        return userProfile;
      } else {
        throw Exception('Invalid profile data structure: ${response.toString()}');
      }
    } catch (e) {
      print('Error loading profile: $e');
      throw Exception('Error loading profile: $e');
    }
  }

  // Helper method to parse double values from API
  double _parseDouble(dynamic value, double defaultValue) {
    if (value == null) {
      return defaultValue;
    }

    if (value is num) {
      return value.toDouble();
    }

    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print('Error parsing double from string: $value');
        return defaultValue;
      }
    }

    print('Unknown type for double value: ${value.runtimeType}');
    return defaultValue;
  }

  // Generic API request method
  Future<Map<String, dynamic>> makeApiRequest(String endpoint, {String method = 'GET', Map<String, dynamic>? data, bool requiresAuth = true, bool isRetry = false}) async {
    try {
      String cleanBaseUrl = _baseUrl;
      if (!cleanBaseUrl.endsWith('/')) cleanBaseUrl += '/';
      final url = Uri.parse('${cleanBaseUrl}${endpoint.startsWith('/') ? endpoint.substring(1) : endpoint}');
      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      // Add authorization header if required
      if (requiresAuth) {
        final token = await getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
          print('Using token for $endpoint: ${token.substring(0, 20)}...');
        } else {
          print('Token is null for $endpoint');
          if (!isRetry) {
            // Try to refresh the token
            print('Attempting to refresh token...');
            final refreshed = await refreshToken();
            if (refreshed) {
              // Retry the request with the new token
              return makeApiRequest(endpoint, method: method, data: data, requiresAuth: requiresAuth, isRetry: true);
            } else {
              print('Token refresh failed');
              throw Exception('Not authenticated');
            }
          } else {
            throw Exception('Not authenticated');
          }
        }
      }

      http.Response response;

      print('API Request: $method $url');
      if (data != null) {
        print('Request Body: ${jsonEncode(data)}');
      }
      print('Request Headers: ${headers.entries.map((e) => '${e.key}: ${e.value}').join(', ')}');

      if (method == 'GET') {
        response = await httpClient.get(url, headers: headers);
      } else if (method == 'POST') {
        response = await httpClient.post(url, headers: headers, body: data != null ? jsonEncode(data) : null);
      } else if (method == 'PUT') {
        response = await httpClient.put(url, headers: headers, body: data != null ? jsonEncode(data) : null);
      } else if (method == 'DELETE') {
        response = await httpClient.delete(url, headers: headers);
      } else {
        throw Exception('Unsupported HTTP method: $method');
      }

      print('API Response: ${response.statusCode}');
      print('Response Headers: ${response.headers}');

      // For debugging
      if (response.body.isNotEmpty) {
        try {
          print('Response Body (JSON): ${jsonDecode(response.body)}');
        } catch (e) {
          print('Response Body (raw): ${response.body}');
        }
      }

      // Handle authentication errors
      if (response.statusCode == 401) {
        print('🚨 Authentication failed (401) for $endpoint. Retry attempt: ${isRetry ? '2' : '1'}');
        print('🔍 Response body: ${response.body}');
        print('🔍 Response headers: ${response.headers}');

        // Get current token for debugging
        final currentToken = await getToken();
        if (currentToken != null) {
          print('🔑 Current token (first 20 chars): ${currentToken.substring(0, 20)}...');
        } else {
          print('🔑 No token found in storage');
        }

        // For video-related endpoints, be more lenient and don't clear tokens immediately
        final isVideoRelated = endpoint.contains('video_progress') ||
                              endpoint.contains('log_video_access') ||
                              endpoint.contains('validate_video_access');

        if (!isRetry) {
          // Try to refresh the token
          print('Attempting to refresh token...');
          final refreshed = await refreshToken();
          if (refreshed) {
            // Retry the request with the new token
            return makeApiRequest(endpoint, method: method, data: data, requiresAuth: requiresAuth, isRetry: true);
          } else {
            print('Token refresh failed');
            if (!isVideoRelated) {
              // Only clear token for non-video related failures
              print('Clearing token due to authentication failure...');
              await clearToken();
              throw Exception('Authentication token lost during request');
            } else {
              // For video-related failures, just throw without clearing token
              print('Video-related API call failed, preserving authentication state');
              throw Exception('Video API call failed - authentication preserved');
            }
          }
        } else {
          // Already retried once
          if (!isVideoRelated) {
            // Clear token and force re-login for non-video endpoints
            await clearToken();
            throw AuthenticationException('Session expired. Please log in again.');
          } else {
            // For video endpoints, preserve authentication state
            print('Video-related API retry failed, preserving authentication state');
            throw Exception('Video API retry failed - authentication preserved');
          }
        }
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) {
          return {'success': true};
        }

        try {
          final responseData = jsonDecode(response.body);

          // Ensure responseData is a Map<String, dynamic>
          if (responseData is Map<String, dynamic>) {
            // Check if the response contains a new token
            if (responseData.containsKey('token')) {
              final newToken = responseData['token'];
              if (newToken != null && newToken is String) {
                print('Received new token from server, saving it');
                await saveToken(newToken);
              }
            }

            return responseData;
          } else if (responseData is Map) {
            // Convert Map to Map<String, dynamic>
            return Map<String, dynamic>.from(responseData);
          } else {
            // If response is not a Map, wrap it
            return {'success': true, 'data': responseData};
          }
        } catch (e) {
          print('Error parsing JSON response: $e');
          print('Response body: ${response.body}');
          return {'success': false, 'error': 'Invalid response format: ${e.toString()}'};
        }
      } else {
        print('API error: ${response.statusCode} - ${response.body}');

        try {
          final responseData = jsonDecode(response.body);
          if (responseData is Map<String, dynamic>) {
            return responseData;
          } else if (responseData is Map) {
            return Map<String, dynamic>.from(responseData);
          } else {
            return {
              'success': false,
              'error': 'Request failed with status: ${response.statusCode}',
              'status': response.statusCode,
              'data': responseData,
            };
          }
        } catch (e) {
          return {
            'success': false,
            'error': 'Request failed with status: ${response.statusCode}',
            'status': response.statusCode,
            'raw_response': response.body,
          };
        }
      }
    } catch (e) {
      print('Error making API request: $e');

      // Handle specific network errors
      if (e is SocketException) {
        print('Network connection error: ${e.message}');
        if (e.message.contains('Connection refused')) {
          return {'success': false, 'error': 'Server connection refused. Please check if the server is running and accessible.'};
        } else if (e.message.contains('Network is unreachable')) {
          return {'success': false, 'error': 'Network is unreachable. Please check your internet connection.'};
        } else {
          return {'success': false, 'error': 'Network error: ${e.message}'};
        }
      }

      if (e is HandshakeException) {
        print('SSL Handshake error: ${e.message}');
        return {'success': false, 'error': 'SSL certificate error. This may be due to invalid certificates on the server.'};
      }

      if (e is HttpException) {
        print('HTTP error: ${e.message}');
        return {'success': false, 'error': 'HTTP error: ${e.message}'};
      }

      return {'success': false, 'error': e.toString()};
    }
  }

  // Register a new user
  Future<Map<String, dynamic>> register(String name, String phone, {String? deviceId}) async {
    try {
      final response = await makeApiRequest(
        'register.php',
        method: 'POST',
        data: {
          'name': name,
          'phone_number': phone,
          'device_id': deviceId ?? '',
        },
        requiresAuth: false,
      );
      return response;
    } catch (e) {
      print('Registration error: $e');
      rethrow;
    }
  }

  // Update user profile with real-time sync
  Future<Map<String, dynamic>> updateProfile({
    String? name,
    double? height,
    double? weight,
    int? age,
    String? email,
    String? gender,
    String? fitnessGoal
  }) async {
    try {
      final Map<String, dynamic> data = {};
      if (name != null) data['name'] = name;
      if (height != null) {
        if (height < 1 || height > 300) {
          throw Exception('Height must be between 1 and 300 cm');
        }
        data['height'] = height;
      }
      if (weight != null) data['weight'] = weight;
      if (age != null) data['age'] = age;
      if (email != null) data['email'] = email;
      if (gender != null) data['gender'] = gender;
      if (fitnessGoal != null) data['fitness_goal'] = fitnessGoal;

      print('Updating profile with data: ${jsonEncode(data)}');

      final response = await makeApiRequest(
        'profile.php',
        method: 'PUT',
        data: data,
      );

      print('Profile update response: ${jsonEncode(response)}');

      // Return the full response including updated profile data
      return response;
    } catch (e) {
      print('Update profile error: $e');
      rethrow;
    }
  }

  // Enhanced profile update with BMI calculation
  Future<Map<String, dynamic>> updateHealthMetrics({
    double? height,
    double? weight,
    int? age
  }) async {
    try {
      final Map<String, dynamic> data = {};
      if (height != null) {
        if (height < 1 || height > 300) {
          throw Exception('Height must be between 1 and 300 cm');
        }
        data['height'] = height;
      }
      if (weight != null) data['weight'] = weight;
      if (age != null) data['age'] = age;

      print('Updating health metrics: ${jsonEncode(data)}');

      final response = await makeApiRequest(
        'profile.php',
        method: 'PUT',
        data: data,
      );

      // The API will automatically calculate BMI and log BMI records
      print('Health metrics update response: ${jsonEncode(response)}');

      return response;
    } catch (e) {
      print('Update health metrics error: $e');
      rethrow;
    }
  }

  // Get staff information
  Future<Map<String, String>?> getStaffInfo(int staffId) async {
    try {
      print('🔍 Fetching staff info for ID: $staffId');
      final response = await makeApiRequest('staff_info.php?staff_id=$staffId');
      print('📋 Staff API response: $response');

      if (response['success'] == true) {
        // The staff_info.php returns data directly, not nested under 'staff'
        final staffData = <String, String>{};
        staffData['id'] = response['staff_id']?.toString() ?? '';
        staffData['name'] = response['name']?.toString() ?? '';
        staffData['phone'] = response['phone']?.toString() ?? '';

        print('✅ Staff data parsed: $staffData');
        return staffData;
      } else {
        print('❌ Staff API failed: ${response['error'] ?? 'Unknown error'}');
        return null;
      }
    } catch (e) {
      print('❌ Get staff info error: $e');
      return null;
    }
  }

  // Get user courses
  Future<Map<String, dynamic>> getUserCourses() async {
    try {
      final response = await makeApiRequest('courses.php?enrolled=1');

      if (response['success'] == true) {
        // Return the raw response to let the caller handle the parsing
        return response;
      } else {
        return {'success': false, 'courses': []};
      }
    } catch (e) {
      print('Get user courses error: $e');
      return {'success': false, 'courses': [], 'error': e.toString()};
    }
  }

  // Get user courses as parsed Course objects
  Future<List<Course>> getUserCoursesAsList() async {
    try {
      final response = await getUserCourses();

      if (response['success'] == true && response['courses'] != null) {
        final coursesData = response['courses'];

        if (coursesData is List) {
          final courses = <Course>[];
          for (var courseData in coursesData) {
            try {
              if (courseData is Map<String, dynamic>) {
                courses.add(Course.fromJson(courseData));
              } else if (courseData is Map) {
                courses.add(Course.fromJson(Map<String, dynamic>.from(courseData)));
              }
            } catch (e) {
              print('Error parsing course: $e');
              // Skip invalid course data instead of failing completely
            }
          }
          return courses;
        } else {
          print('Courses data is not a list: ${coursesData.runtimeType}');
          return [];
        }
      } else {
        print('Failed to get courses: ${response['error'] ?? 'Unknown error'}');
        return [];
      }
    } catch (e) {
      print('Get user courses as list error: $e');
      return [];
    }
  }

  // Get available courses
  Future<Map<String, dynamic>> getAvailableCourses({String? category}) async {
    try {
      String endpoint = 'courses.php?available=1';
      if (category != null && category.isNotEmpty) {
        endpoint += '&category=${Uri.encodeComponent(category)}';
      }

      final response = await makeApiRequest(endpoint);

      if (response['success'] == true) {
        // Return the raw response to let the caller handle the parsing
        return response;
      } else {
        return {'success': false, 'courses': [], 'categories': []};
      }
    } catch (e) {
      print('Get available courses error: $e');
      return {'success': false, 'courses': [], 'categories': [], 'error': e.toString()};
    }
  }

  // Get available courses as parsed Course objects
  Future<List<Course>> getAvailableCoursesAsList({String? category}) async {
    try {
      final response = await getAvailableCourses(category: category);

      if (response['success'] == true && response['courses'] != null) {
        final coursesData = response['courses'];

        if (coursesData is List) {
          final courses = <Course>[];
          for (var courseData in coursesData) {
            try {
              if (courseData is Map<String, dynamic>) {
                courses.add(Course.fromJson(courseData));
              } else if (courseData is Map) {
                courses.add(Course.fromJson(Map<String, dynamic>.from(courseData)));
              }
            } catch (e) {
              print('Error parsing available course: $e');
              // Skip invalid course data instead of failing completely
            }
          }
          return courses;
        } else {
          print('Available courses data is not a list: ${coursesData.runtimeType}');
          return [];
        }
      } else {
        print('Failed to get available courses: ${response['error'] ?? 'Unknown error'}');
        return [];
      }
    } catch (e) {
      print('Get available courses as list error: $e');
      return [];
    }
  }

  // Get course videos
  Future<Map<String, dynamic>> getCourseVideos(int courseId) async {
    try {
      final response = await makeApiRequest('course_videos.php?course_id=$courseId');

      if (response['success'] == true) {
        // Return the raw response to let the caller handle the parsing
        return response;
      } else {
        return {'success': false, 'videos': []};
      }
    } catch (e) {
      print('Get course videos error: $e');
      return {'success': false, 'videos': [], 'error': e.toString()};
    }
  }

  // Get course videos as parsed CourseVideo objects
  Future<List<CourseVideo>> getCourseVideosAsList(int courseId) async {
    try {
      final response = await getCourseVideos(courseId);

      if (response['success'] == true && response['videos'] != null) {
        final videosData = response['videos'];

        if (videosData is List) {
          final videos = <CourseVideo>[];
          for (var videoData in videosData) {
            try {
              if (videoData is Map<String, dynamic>) {
                videos.add(CourseVideo.fromJson(videoData));
              } else if (videoData is Map) {
                videos.add(CourseVideo.fromJson(Map<String, dynamic>.from(videoData)));
              }
            } catch (e) {
              print('Error parsing course video: $e');
              // Skip invalid video data instead of failing completely
            }
          }
          return videos;
        } else {
          print('Course videos data is not a list: ${videosData.runtimeType}');
          return [];
        }
      } else {
        print('Failed to get course videos: ${response['error'] ?? 'Unknown error'}');
        return [];
      }
    } catch (e) {
      print('Get course videos as list error: $e');
      return [];
    }
  }

  // Update video progress - Enhanced version with better error handling and logging
  Future<void> updateVideoProgress({
    required int videoId,
    int? courseId,
    int? position,
    int? duration,
    bool? completed,
    int? watchDurationSeconds,
    bool? isCompleted,
    int? lastPositionSeconds,
  }) async {
    try {
      print('🎬 API: Updating video progress for video $videoId');

      final Map<String, dynamic> data = {
        'video_id': videoId,
      };

      if (courseId != null) {
        data['course_id'] = courseId;
      }

      if (position != null) {
        data['position'] = position;
      }

      if (duration != null) {
        data['duration'] = duration;
      }

      if (completed != null) {
        data['completed'] = completed ? 1 : 0;
      }

      // Support for legacy parameters
      if (watchDurationSeconds != null) {
        data['watch_duration_seconds'] = watchDurationSeconds;
      }

      if (isCompleted != null) {
        data['completed'] = isCompleted ? 1 : 0;
      }

      if (lastPositionSeconds != null) {
        data['last_position_seconds'] = lastPositionSeconds;
      }

      print('🎬 API: Sending data: ${data.toString()}');

      final response = await makeApiRequest(
        'video_progress.php',
        method: 'POST',
        data: data,
      );

      print('✅ API: Video progress updated successfully: ${response.toString()}');
    } catch (e) {
      print('❌ API: Update video progress error: $e');
      // Don't rethrow to avoid disrupting video playback
    }
  }

  // Enhanced video progress update with retry logic
  Future<bool> updateVideoProgressWithRetry({
    required int videoId,
    int? courseId,
    int? watchDurationSeconds,
    int? lastPositionSeconds,
    bool? isCompleted,
    int maxRetries = 3,
  }) async {
    int retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        print('🔄 API: Attempt ${retryCount + 1} to update video $videoId progress');

        await updateVideoProgress(
          videoId: videoId,
          courseId: courseId,
          watchDurationSeconds: watchDurationSeconds,
          lastPositionSeconds: lastPositionSeconds,
          isCompleted: isCompleted,
        );

        print('✅ API: Video progress updated on attempt ${retryCount + 1}');
        return true;
      } catch (e) {
        retryCount++;
        print('❌ API: Attempt $retryCount failed: $e');

        if (retryCount < maxRetries) {
          // Wait before retrying (exponential backoff)
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }
    }

    print('❌ API: Failed to update video progress after $maxRetries attempts');
    return false;
  }

  // Purchase a course
  Future<void> purchaseCourse(int courseId) async {
    try {
      await makeApiRequest(
        'purchase_course.php',
        method: 'POST',
        data: {
          'course_id': courseId,
        },
      );
    } catch (e) {
      print('Purchase course error: $e');
      rethrow;
    }
  }

  // Get quote
  Future<Map<String, dynamic>> getQuote() async {
    try {
      return await makeApiRequest('quotes.php?random=1');
    } catch (e) {
      print('Get quote error: $e');
      rethrow;
    }
  }

  // Get all quotes
  Future<Map<String, dynamic>> getAllQuotes() async {
    try {
      return await makeApiRequest('quotes.php');
    } catch (e) {
      print('Get all quotes error: $e');
      rethrow;
    }
  }

  // Generate AI quote
  Future<Map<String, dynamic>> generateAiQuote({String? theme, String? category}) async {
    try {
      final Map<String, dynamic> data = {};
      if (theme != null) data['theme'] = theme;
      if (category != null) data['category'] = category;

      return await makeApiRequest(
        'quotes.php?generate=1',
        method: 'POST',
        data: data,
      );
    } catch (e) {
      print('Generate AI quote error: $e');
      rethrow;
    }
  }

  // Get quote preferences
  Future<Map<String, dynamic>> getQuotePreferences() async {
    try {
      return await makeApiRequest('quote_preferences.php');
    } catch (e) {
      print('Get quote preferences error: $e');
      rethrow;
    }
  }

  // Update quote preferences
  Future<Map<String, dynamic>> updateQuotePreferences({
    List<String>? categories,
    List<String>? preferredCategories,
    bool? dailyNotifications,
    String? notificationTime,
    bool? personalizationEnabled,
    bool? deepSeekEnabled,
  }) async {
    try {
      final Map<String, dynamic> data = {};

      if (categories != null) {
        data['categories'] = categories;
      }

      if (preferredCategories != null) {
        data['preferred_categories'] = preferredCategories;
      }

      if (dailyNotifications != null) {
        data['daily_notifications'] = dailyNotifications;
      }

      if (notificationTime != null) {
        data['notification_time'] = notificationTime;
      }

      if (personalizationEnabled != null) {
        data['personalization_enabled'] = personalizationEnabled;
      }

      if (deepSeekEnabled != null) {
        data['deep_seek_enabled'] = deepSeekEnabled;
      }

      return await makeApiRequest(
        'quote_preferences.php',
        method: 'PUT',
        data: data,
      );
    } catch (e) {
      print('Update quote preferences error: $e');
      rethrow;
    }
  }

  // Toggle favorite quote
  Future<void> toggleFavoriteQuote({
    required int? quoteId,
    required bool isFavorite,
  }) async {
    try {
      if (quoteId == null) {
        throw Exception('Quote ID cannot be null');
      }

      await makeApiRequest(
        'quotes.php',
        method: 'PUT',
        data: {
          'quote_id': quoteId,
          'is_favorite': isFavorite,
        },
      );
    } catch (e) {
      print('Toggle favorite quote error: $e');
      rethrow;
    }
  }

  // Get app settings
  Future<Map<String, dynamic>> getAppSettings() async {
    try {
      return await makeApiRequest('settings.php');
    } catch (e) {
      print('Get app settings error: $e');
      rethrow;
    }
  }

  // Helper method to format date for API
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Static method to get profile (used in main.dart)
  static Future<Map<String, dynamic>> getProfile(BuildContext context, riverpod.WidgetRef ref) async {
    try {
      final apiService = ApiService();
      final userProfile = await apiService.getUserProfile();
      final result = {
        'success': true,
        'profile': userProfile,
        'statusCode': 200,
      };

      // Add a getter for statusCode to support legacy code
      result['statusCode'] = 200;

      return result;
    } catch (e) {
      print('Error in static getProfile: $e');
      final result = {
        'success': false,
        'error': e.toString(),
        'statusCode': 500,
      };

      // Add a getter for statusCode to support legacy code
      result['statusCode'] = 500;

      return result;
    }
  }

  // Get cached data or fetch from API
  Future<T?> _getCachedOrFetch<T>(String key, Future<T> Function() fetchData) async {
    final cachedEntry = _cache[key];
    if (cachedEntry != null && !cachedEntry.isExpired) {
      print('Cache hit for key: $key');
      return cachedEntry.data as T;
    }
    
    final data = await fetchData();
    _cache[key] = _CacheEntry(data);
    return data;
  }

  // Execute request with timeout and retry logic
  Future<Response> _executeRequest(
    Future<Response> Function() request, {
    String? requestId,
    bool useCache = false,
    String? cacheKey,
  }) async {
    int retryCount = 0;
    Duration retryDelay = _initialRetryDelay;
    
    while (retryCount < _maxRetries) {
      try {
        // Check if request was cancelled
        if (requestId != null && _cancelTokens[requestId]?.isCancelled == true) {
          throw Exception('Request cancelled');
        }

        // Check connectivity
        final connectivityResult = await Connectivity().checkConnectivity();
        if (connectivityResult == ConnectivityResult.none) {
          throw Exception('No internet connection');
        }

        // Execute request with timeout
        final response = await request().timeout(_requestTimeout);
        
        // Handle response
        if (response.statusCode >= 200 && response.statusCode < 300) {
          return response;
        } else if (response.statusCode == 401) {
          // Handle authentication error
          throw AuthenticationException('Authentication failed');
        } else {
          throw Exception('Request failed with status: ${response.statusCode}');
        }
      } catch (e) {
        retryCount++;
        if (retryCount >= _maxRetries) {
          rethrow;
        }
        
        // Exponential backoff
        await Future.delayed(retryDelay);
        retryDelay *= 2;
      }
    }
    
    throw Exception('Max retries exceeded');
  }

  // Batch multiple requests
  Future<List<T>> batchRequests<T>(List<Future<T>> requests) async {
    return Future.wait(requests);
  }

  // Cancel ongoing request
  void cancelRequest(String requestId) {
    _cancelTokens[requestId]?.cancel();
    _cancelTokens.remove(requestId);
  }

  // Modified fetchUserProfile with caching and optimization
  Future<UserProfile> fetchUserProfile() async {
    const cacheKey = 'user_profile';
    const requestId = 'fetch_user_profile';
    
    final userProfile = await _getCachedOrFetch<UserProfile?>(cacheKey, () async {
      final token = await getToken();
      if (token == null) throw AuthenticationException('No token available');

      final response = await _executeRequest(
        () => httpClient.get(
          Uri.parse('$baseUrl/profile.php'),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
        requestId: requestId,
        useCache: true,
        cacheKey: cacheKey,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        return UserProfile.fromJson(data);
      }
      return null;
    });
    if (userProfile == null) throw Exception('Failed to fetch user profile');
    return userProfile;
  }

  // Modified fetchCourses with parallel requests
  Future<List<Course>> fetchCourses() async {
    const cacheKey = 'courses';
    const requestId = 'fetch_courses';
    
    final courses = await _getCachedOrFetch<List<Course>?>(cacheKey, () async {
      final token = await getToken();
      if (token == null) throw AuthenticationException('No token available');

      // Fetch courses and videos in parallel
      final coursesResponse = await _executeRequest(
        () => httpClient.get(
          Uri.parse('$baseUrl/courses.php'),
          headers: {
            'Authorization': 'Bearer $token',
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
        ),
        requestId: requestId,
        useCache: true,
        cacheKey: cacheKey,
      );

      if (coursesResponse.statusCode == 200) {
        final coursesData = jsonDecode(coursesResponse.body) as List;
        return coursesData.map((data) => Course.fromJson(data)).toList();
      }
      return null;
    });

    if (courses == null) {
      throw Exception('Failed to fetch courses');
    }

    // Fetch videos for all courses in parallel
    final videoRequests = courses.map((course) => fetchCourseVideos(course.id)).toList();
    final videosList = await batchRequests(videoRequests);
    
    // Associate videos with courses
    for (var i = 0; i < courses.length; i++) {
      if (i < videosList.length) {
        courses[i].videos = videosList[i];
      }
    }
    
    return courses;
  }

  // Fetch videos for a course
  Future<List<CourseVideo>> fetchCourseVideos(int courseId) async {
    const requestId = 'fetch_course_videos';
    
    final videos = await _getCachedOrFetch<List<CourseVideo>?>(
      'course_videos_$courseId',
      () async {
        final token = await getToken();
        if (token == null) throw AuthenticationException('No token available');

        final response = await _executeRequest(
          () => httpClient.get(
            Uri.parse('$baseUrl/course_videos.php?course_id=$courseId'),
            headers: {
              'Authorization': 'Bearer $token',
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
          ),
          requestId: requestId,
        );

        if (response.statusCode == 200) {
          final data = jsonDecode(response.body) as List;
          return data.map((videoData) => CourseVideo.fromJson(videoData)).toList();
        }
        return null;
      },
    );

    if (videos == null) {
      throw Exception('Failed to fetch course videos');
    }

    return videos;
  }
}
