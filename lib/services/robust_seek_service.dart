import 'dart:async';
import 'dart:math';
import 'package:flutter/foundation.dart';

/// Robust video seeking service that handles aggressive user interactions
/// without crashes or instability
class RobustSeekService {
  // Seek throttling configuration
  static const Duration _seekThrottleDelay = Duration(milliseconds: 300);
  static const Duration _seekDebounceDelay = Duration(milliseconds: 150);
  static const Duration _seekTimeoutDuration = Duration(seconds: 8);
  static const int _maxConcurrentSeeks = 3;
  static const int _maxRetryAttempts = 3;

  // State management
  Timer? _throttleTimer;
  Timer? _debounceTimer;
  Timer? _timeoutTimer;
  Timer? _retryTimer;
  
  bool _isSeekInProgress = false;
  bool _isThrottling = false;
  bool _isDebouncing = false;
  int _pendingSeekPosition = -1;
  int _lastSeekPosition = -1;
  int _currentRetryCount = 0;
  int _concurrentSeekCount = 0;
  
  DateTime? _lastSeekTime;
  DateTime? _lastSuccessfulSeekTime;
  
  // Performance tracking
  int _totalSeekAttempts = 0;
  int _successfulSeeks = 0;
  int _failedSeeks = 0;
  int _throttledSeeks = 0;
  int _debouncedSeeks = 0;
  
  // Callbacks
  Function(int position)? _onSeekStart;
  Function(int position)? _onSeekSuccess;
  Function(int position, String error)? _onSeekFailure;
  Function(int position)? _onSeekThrottled;
  Function(int position)? _onSeekDebounced;
  Function(String message, bool isSuccess)? _onSeekFeedback;
  
  // Device performance detection
  bool _isLowEndDevice = false;
  bool _isNetworkSlow = false;

  RobustSeekService({
    Function(int position)? onSeekStart,
    Function(int position)? onSeekSuccess,
    Function(int position, String error)? onSeekFailure,
    Function(int position)? onSeekThrottled,
    Function(int position)? onSeekDebounced,
    Function(String message, bool isSuccess)? onSeekFeedback,
  }) {
    _onSeekStart = onSeekStart;
    _onSeekSuccess = onSeekSuccess;
    _onSeekFailure = onSeekFailure;
    _onSeekThrottled = onSeekThrottled;
    _onSeekDebounced = onSeekDebounced;
    _onSeekFeedback = onSeekFeedback;
    
    _detectDevicePerformance();
  }

  /// Main seek method with comprehensive throttling and error handling
  Future<bool> seekToPosition(
    int targetPosition, {
    required Future<bool> Function(int position) seekImplementation,
    bool isUserInitiated = true,
    bool bypassThrottling = false,
    int? videoDuration,
  }) async {
    debugPrint('🎯 RobustSeek: Request to seek to ${targetPosition}s');
    
    // Validate position
    if (!_validateSeekPosition(targetPosition, videoDuration)) {
      debugPrint('❌ RobustSeek: Invalid seek position: ${targetPosition}s');
      _onSeekFailure?.call(targetPosition, 'Invalid position');
      return false;
    }

    // Check if we should throttle this seek
    if (!bypassThrottling && _shouldThrottleSeek(targetPosition)) {
      return _handleThrottledSeek(targetPosition, seekImplementation, videoDuration);
    }

    // Check if we should debounce this seek
    if (!bypassThrottling && _shouldDebounceSeek(targetPosition)) {
      return _handleDebouncedSeek(targetPosition, seekImplementation, videoDuration);
    }

    // Check concurrent seek limit
    if (_concurrentSeekCount >= _maxConcurrentSeeks) {
      debugPrint('⚠️ RobustSeek: Too many concurrent seeks, queuing...');
      return _queueSeek(targetPosition, seekImplementation, videoDuration);
    }

    // Perform the actual seek
    return _performSeek(targetPosition, seekImplementation);
  }

  /// Validates seek position
  bool _validateSeekPosition(int position, int? duration) {
    if (position < 0) return false;
    if (duration != null && position > duration + 30) return false; // Allow some buffer
    return true;
  }

  /// Determines if seek should be throttled
  bool _shouldThrottleSeek(int targetPosition) {
    if (_isSeekInProgress) return true;
    if (_isThrottling) return true;
    
    final now = DateTime.now();
    if (_lastSeekTime != null) {
      final timeSinceLastSeek = now.difference(_lastSeekTime!);
      if (timeSinceLastSeek < _seekThrottleDelay) {
        return true;
      }
    }
    
    return false;
  }

  /// Determines if seek should be debounced
  bool _shouldDebounceSeek(int targetPosition) {
    // Debounce rapid seeks to similar positions
    if (_pendingSeekPosition != -1) {
      final positionDiff = (targetPosition - _pendingSeekPosition).abs();
      if (positionDiff < 5) { // Within 5 seconds
        return true;
      }
    }
    
    return false;
  }

  /// Handles throttled seek requests
  Future<bool> _handleThrottledSeek(
    int targetPosition,
    Future<bool> Function(int position) seekImplementation,
    int? videoDuration,
  ) async {
    debugPrint('🎛️ RobustSeek: Throttling seek to ${targetPosition}s');
    _throttledSeeks++;
    _onSeekThrottled?.call(targetPosition);
    _onSeekFeedback?.call('Seeking throttled...', false);

    // Cancel existing throttle timer
    _throttleTimer?.cancel();
    
    _isThrottling = true;
    _pendingSeekPosition = targetPosition;

    final completer = Completer<bool>();
    
    _throttleTimer = Timer(_seekThrottleDelay, () async {
      _isThrottling = false;
      final result = await _performSeek(targetPosition, seekImplementation);
      completer.complete(result);
    });

    return completer.future;
  }

  /// Handles debounced seek requests
  Future<bool> _handleDebouncedSeek(
    int targetPosition,
    Future<bool> Function(int position) seekImplementation,
    int? videoDuration,
  ) async {
    debugPrint('⏱️ RobustSeek: Debouncing seek to ${targetPosition}s');
    _debouncedSeeks++;
    _onSeekDebounced?.call(targetPosition);
    _onSeekFeedback?.call('Seeking...', false);

    // Cancel existing debounce timer
    _debounceTimer?.cancel();
    
    _isDebouncing = true;
    _pendingSeekPosition = targetPosition;

    final completer = Completer<bool>();
    
    _debounceTimer = Timer(_seekDebounceDelay, () async {
      _isDebouncing = false;
      final result = await _performSeek(targetPosition, seekImplementation);
      completer.complete(result);
    });

    return completer.future;
  }

  /// Queues seek when concurrent limit is reached
  Future<bool> _queueSeek(
    int targetPosition,
    Future<bool> Function(int position) seekImplementation,
    int? videoDuration,
  ) async {
    debugPrint('📋 RobustSeek: Queueing seek to ${targetPosition}s');
    _onSeekFeedback?.call('Seek queued...', false);
    
    // Wait for current seeks to complete
    while (_concurrentSeekCount >= _maxConcurrentSeeks) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    
    return _performSeek(targetPosition, seekImplementation);
  }

  /// Performs the actual seek operation with error handling
  Future<bool> _performSeek(
    int targetPosition,
    Future<bool> Function(int position) seekImplementation,
  ) async {
    debugPrint('🎯 RobustSeek: Performing seek to ${targetPosition}s');
    
    _totalSeekAttempts++;
    _concurrentSeekCount++;
    _isSeekInProgress = true;
    _lastSeekTime = DateTime.now();
    _pendingSeekPosition = -1;
    
    _onSeekStart?.call(targetPosition);
    _onSeekFeedback?.call('Seeking to ${_formatTime(targetPosition)}...', false);

    // Set timeout for seek operation
    _timeoutTimer?.cancel();
    bool timedOut = false;
    
    _timeoutTimer = Timer(_seekTimeoutDuration, () {
      timedOut = true;
      debugPrint('⏰ RobustSeek: Seek timeout for position ${targetPosition}s');
    });

    try {
      final result = await seekImplementation(targetPosition);
      
      _timeoutTimer?.cancel();
      _concurrentSeekCount = max(0, _concurrentSeekCount - 1);
      _isSeekInProgress = false;
      
      if (timedOut) {
        debugPrint('❌ RobustSeek: Seek timed out for position ${targetPosition}s');
        _failedSeeks++;
        _onSeekFailure?.call(targetPosition, 'Seek timeout');
        return _attemptSeekRecovery(targetPosition, seekImplementation);
      }
      
      if (result) {
        debugPrint('✅ RobustSeek: Successfully seeked to ${targetPosition}s');
        _successfulSeeks++;
        _lastSuccessfulSeekTime = DateTime.now();
        _lastSeekPosition = targetPosition;
        _currentRetryCount = 0;
        _onSeekSuccess?.call(targetPosition);
        _onSeekFeedback?.call('Seeked to ${_formatTime(targetPosition)}', true);
        return true;
      } else {
        debugPrint('❌ RobustSeek: Seek failed for position ${targetPosition}s');
        _failedSeeks++;
        _onSeekFailure?.call(targetPosition, 'Seek operation failed');
        return _attemptSeekRecovery(targetPosition, seekImplementation);
      }
    } catch (e) {
      debugPrint('❌ RobustSeek: Exception during seek: $e');
      _timeoutTimer?.cancel();
      _concurrentSeekCount = max(0, _concurrentSeekCount - 1);
      _isSeekInProgress = false;
      _failedSeeks++;
      _onSeekFailure?.call(targetPosition, 'Exception: $e');
      return _attemptSeekRecovery(targetPosition, seekImplementation);
    }
  }

  /// Attempts to recover from failed seeks
  Future<bool> _attemptSeekRecovery(
    int targetPosition,
    Future<bool> Function(int position) seekImplementation,
  ) async {
    if (_currentRetryCount >= _maxRetryAttempts) {
      debugPrint('❌ RobustSeek: Max retry attempts reached for position ${targetPosition}s');
      _onSeekFeedback?.call('Seek failed after retries', false);
      return false;
    }

    _currentRetryCount++;
    final retryDelay = Duration(milliseconds: 500 * _currentRetryCount);
    
    debugPrint('🔄 RobustSeek: Attempting recovery ${_currentRetryCount}/${_maxRetryAttempts} for position ${targetPosition}s');
    _onSeekFeedback?.call('Retrying seek...', false);

    await Future.delayed(retryDelay);
    
    // Try alternative positions for recovery
    final alternativePositions = _generateAlternativePositions(targetPosition);
    
    for (final altPosition in alternativePositions) {
      try {
        final result = await seekImplementation(altPosition);
        if (result) {
          debugPrint('✅ RobustSeek: Recovery successful with alternative position ${altPosition}s');
          _onSeekSuccess?.call(altPosition);
          _onSeekFeedback?.call('Seeked to nearby position ${_formatTime(altPosition)}', true);
          return true;
        }
      } catch (e) {
        debugPrint('❌ RobustSeek: Alternative position ${altPosition}s failed: $e');
        continue;
      }
    }

    return false;
  }

  /// Generates alternative seek positions for recovery
  List<int> _generateAlternativePositions(int targetPosition) {
    return [
      targetPosition,
      targetPosition - 1,
      targetPosition + 1,
      targetPosition - 5,
      targetPosition + 5,
      (targetPosition / 10).round() * 10, // Round to nearest 10 seconds
    ].where((pos) => pos >= 0).toSet().toList();
  }

  /// Detects device performance characteristics
  void _detectDevicePerformance() {
    // This would be implemented based on device specs
    // For now, assume good performance
    _isLowEndDevice = false;
    _isNetworkSlow = false;
  }

  /// Formats time for display
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Gets performance statistics
  Map<String, dynamic> getPerformanceStats() {
    final successRate = _totalSeekAttempts > 0 
        ? (_successfulSeeks / _totalSeekAttempts * 100).toStringAsFixed(1)
        : '0.0';
    
    return {
      'totalAttempts': _totalSeekAttempts,
      'successful': _successfulSeeks,
      'failed': _failedSeeks,
      'throttled': _throttledSeeks,
      'debounced': _debouncedSeeks,
      'successRate': '$successRate%',
      'isSeekInProgress': _isSeekInProgress,
      'concurrentSeeks': _concurrentSeekCount,
    };
  }

  /// Resets all state and timers
  void reset() {
    _throttleTimer?.cancel();
    _debounceTimer?.cancel();
    _timeoutTimer?.cancel();
    _retryTimer?.cancel();
    
    _isSeekInProgress = false;
    _isThrottling = false;
    _isDebouncing = false;
    _pendingSeekPosition = -1;
    _currentRetryCount = 0;
    _concurrentSeekCount = 0;
  }

  /// Disposes of the service
  void dispose() {
    reset();
  }
}
