import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'bulletproof_auth_service.dart';
import 'network_resilience_service.dart';
import 'device_compatibility_service.dart';
import 'offline_data_service.dart';
import 'crash_prevention_service.dart';
import 'performance_monitor_service.dart';

/// Bulletproof app manager that coordinates all stability and compatibility services
class BulletproofAppManager {
  static final BulletproofAppManager _instance = BulletproofAppManager._internal();
  factory BulletproofAppManager() => _instance;
  BulletproofAppManager._internal();

  // Service instances
  final BulletproofAuthService _authService = BulletproofAuthService();
  final NetworkResilienceService _networkService = NetworkResilienceService();
  final DeviceCompatibilityService _deviceService = DeviceCompatibilityService();
  final OfflineDataService _offlineService = OfflineDataService();
  final CrashPreventionService _crashService = CrashPreventionService();
  final PerformanceMonitorService _performanceService = PerformanceMonitorService();

  // App state
  bool _isInitialized = false;
  bool _isHealthy = true;
  Timer? _healthCheckTimer;
  AppLifecycleState _currentLifecycleState = AppLifecycleState.resumed;

  // Configuration
  static const Duration _healthCheckInterval = Duration(minutes: 1);
  static const String _appVersionKey = 'app_version';
  static const String _lastHealthCheckKey = 'last_health_check';

  /// Initialize the bulletproof app manager and all services
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🚀 Initializing BulletproofAppManager...');
      
      // Set up global error handling
      await _setupGlobalErrorHandling();

      // Initialize core services in order of dependency
      await _initializeServices();

      // Perform initial health check
      await _performHealthCheck();

      // Start continuous monitoring
      _startHealthMonitoring();

      // Handle app lifecycle changes
      _setupLifecycleHandling();

      // Check for app updates or migrations
      await _checkAppVersion();

      _isInitialized = true;
      print('✅ BulletproofAppManager initialized successfully');
      
      // Log initialization summary
      await _logInitializationSummary();
    } catch (e) {
      print('❌ BulletproofAppManager initialization failed: $e');
      await _crashService.reportError(e, 'BulletproofAppManager.initialize');
      _isInitialized = true; // Continue with degraded functionality
    }
  }

  /// Set up global error handling
  Future<void> _setupGlobalErrorHandling() async {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) async {
      await _crashService.reportFlutterError(details);
    };

    // Handle platform errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _crashService.reportError(error, 'PlatformDispatcher', stackTrace: stack);
      return true;
    };

    print('🛡️ Global error handling configured');
  }

  /// Initialize all services in dependency order
  Future<void> _initializeServices() async {
    final stopwatch = Stopwatch()..start();

    try {
      // 1. Crash prevention (must be first)
      await _crashService.initialize();
      print('✅ CrashPreventionService initialized');

      // 2. Performance monitoring
      await _performanceService.initialize();
      print('✅ PerformanceMonitorService initialized');

      // 3. Device compatibility (needed by other services)
      await _deviceService.initialize();
      print('✅ DeviceCompatibilityService initialized');

      // 4. Network resilience
      await _networkService.initialize();
      print('✅ NetworkResilienceService initialized');

      // 5. Offline data management
      await _offlineService.initialize();
      print('✅ OfflineDataService initialized');

      // 6. Authentication (depends on network and offline services)
      await _authService.initialize();
      print('✅ BulletproofAuthService initialized');

      stopwatch.stop();
      print('⚡ All services initialized in ${stopwatch.elapsedMilliseconds}ms');
    } catch (e) {
      stopwatch.stop();
      print('❌ Service initialization failed after ${stopwatch.elapsedMilliseconds}ms: $e');
      throw e;
    }
  }

  /// Perform comprehensive health check
  Future<void> _performHealthCheck() async {
    try {
      print('🔍 Performing health check...');
      
      // Ensure device service is initialized before health check
      if (!_deviceService.isInitialized) {
        await _deviceService.initialize();
      }
      final healthResults = <String, bool>{};

      // Check authentication service
      healthResults['auth'] = _authService.isAuthenticated;

      // Check network service
      healthResults['network'] = _networkService.isOnline;

      // Check device compatibility
      healthResults['device'] = _deviceService.requiresSpecialHandling ? 
          await _checkDeviceSpecificHealth() : true;

      // Check offline data service
      final cacheStats = await _offlineService.getCacheStatistics();
      healthResults['offline'] = cacheStats.totalEntries >= 0;

      // Check crash prevention service
      healthResults['crash_prevention'] = await _crashService.isHealthy();

      // Check performance monitoring
      healthResults['performance'] = _performanceService.isMonitoring;

      // Calculate overall health
      final healthyServices = healthResults.values.where((h) => h).length;
      final totalServices = healthResults.length;
      _isHealthy = healthyServices >= (totalServices * 0.8); // 80% threshold

      // Store health check timestamp
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastHealthCheckKey, DateTime.now().millisecondsSinceEpoch);

      print('🔍 Health check complete: ${healthyServices}/${totalServices} services healthy');
      
      if (!_isHealthy) {
        print('⚠️ App health degraded, initiating recovery procedures...');
        await _initiateRecovery(healthResults);
      }
    } catch (e) {
      print('❌ Health check failed: $e');
      _isHealthy = false;
    }
  }

  /// Check device-specific health
  Future<bool> _checkDeviceSpecificHealth() async {
    try {
      // Ensure device service is initialized before checking device info
      if (!_deviceService.isInitialized) {
        await _deviceService.initialize();
      }
      final deviceInfo = _deviceService.deviceInfo;
      
      // Check if device-specific optimizations are working
      if (_deviceService.hasAggressivePowerManagement) {
        // Verify power management settings
        return await _verifyPowerManagementSettings();
      }

      return true;
    } catch (e) {
      print('❌ Device-specific health check failed: $e');
      return false;
    }
  }

  /// Verify power management settings
  Future<bool> _verifyPowerManagementSettings() async {
    try {
      // This would check actual device settings
      // For now, return true as a placeholder
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Initiate recovery procedures for unhealthy services
  Future<void> _initiateRecovery(Map<String, bool> healthResults) async {
    print('🔧 Initiating recovery procedures...');

    for (final entry in healthResults.entries) {
      if (!entry.value) {
        await _recoverService(entry.key);
      }
    }
  }

  /// Recover a specific service
  Future<void> _recoverService(String serviceName) async {
    try {
      print('🔧 Recovering service: $serviceName');

      switch (serviceName) {
        case 'auth':
          await _authService.initialize();
          break;
        case 'network':
          await _networkService.initialize();
          break;
        case 'device':
          await _deviceService.initialize();
          break;
        case 'offline':
          await _offlineService.initialize();
          break;
        case 'crash_prevention':
          await _crashService.initialize();
          break;
        case 'performance':
          await _performanceService.initialize();
          break;
      }

      print('✅ Service recovered: $serviceName');
    } catch (e) {
      print('❌ Failed to recover service $serviceName: $e');
    }
  }

  /// Start continuous health monitoring
  void _startHealthMonitoring() {
    _healthCheckTimer?.cancel();
    _healthCheckTimer = Timer.periodic(_healthCheckInterval, (_) async {
      await _performHealthCheck();
    });
  }

  /// Setup app lifecycle handling
  void _setupLifecycleHandling() {
    // This would integrate with your app's lifecycle management
    // For now, we'll simulate lifecycle handling
    print('📱 App lifecycle handling configured');
  }

  /// Handle app lifecycle state changes
  Future<void> handleLifecycleChange(AppLifecycleState state) async {
    final previousState = _currentLifecycleState;
    _currentLifecycleState = state;

    print('📱 App lifecycle changed: $previousState → $state');

    switch (state) {
      case AppLifecycleState.resumed:
        await _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        await _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        await _handleAppDetached();
        break;
      case AppLifecycleState.inactive:
        // Handle inactive state if needed
        break;
      case AppLifecycleState.hidden:
        // Handle hidden state if needed
        break;
    }
  }

  /// Handle app resumed
  Future<void> _handleAppResumed() async {
    try {
      print('📱 App resumed, performing validation...');

      // Validate authentication
      if (_authService.isAuthenticated) {
        // Refresh session if needed
        print('🔐 Validating authentication session...');
      }

      // Check network connectivity
      await _networkService.initialize();

      // Sync offline data if online
      if (_networkService.isOnline) {
        await _offlineService.forceSyncNow();
      }

      // Perform quick health check
      await _performHealthCheck();
    } catch (e) {
      print('❌ App resume handling failed: $e');
    }
  }

  /// Handle app paused
  Future<void> _handleAppPaused() async {
    try {
      print('📱 App paused, saving state...');

      // Save critical data
      await _saveAppState();

      // Reduce background activity
      await _reduceBackgroundActivity();
    } catch (e) {
      print('❌ App pause handling failed: $e');
    }
  }

  /// Handle app detached
  Future<void> _handleAppDetached() async {
    try {
      print('📱 App detached, performing cleanup...');

      // Save all pending data
      await _saveAppState();

      // Clean up resources
      await _cleanupResources();
    } catch (e) {
      print('❌ App detach handling failed: $e');
    }
  }

  /// Save app state
  Future<void> _saveAppState() async {
    try {
      // Save authentication state
      // Save user preferences
      // Save any pending data
      print('💾 App state saved');
    } catch (e) {
      print('❌ Failed to save app state: $e');
    }
  }

  /// Reduce background activity
  Future<void> _reduceBackgroundActivity() async {
    try {
      // Reduce timer frequencies
      // Pause non-critical operations
      print('⏸️ Background activity reduced');
    } catch (e) {
      print('❌ Failed to reduce background activity: $e');
    }
  }

  /// Clean up resources
  Future<void> _cleanupResources() async {
    try {
      // Close database connections
      // Cancel timers
      // Release memory
      print('🧹 Resources cleaned up');
    } catch (e) {
      print('❌ Failed to clean up resources: $e');
    }
  }

  /// Check app version and handle migrations
  Future<void> _checkAppVersion() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final storedVersion = prefs.getString(_appVersionKey);
      const currentVersion = '1.0.0'; // This would come from your app config

      if (storedVersion != currentVersion) {
        print('📱 App version changed: $storedVersion → $currentVersion');
        await _handleVersionMigration(storedVersion, currentVersion);
        await prefs.setString(_appVersionKey, currentVersion);
      }
    } catch (e) {
      print('❌ Version check failed: $e');
    }
  }

  /// Handle version migration
  Future<void> _handleVersionMigration(String? oldVersion, String newVersion) async {
    try {
      print('🔄 Performing version migration...');

      // Perform any necessary data migrations
      // Update database schema if needed
      // Clear incompatible cache data

      print('✅ Version migration completed');
    } catch (e) {
      print('❌ Version migration failed: $e');
    }
  }

  /// Log initialization summary
  Future<void> _logInitializationSummary() async {
    try {
      final deviceInfo = _deviceService.deviceInfo;
      final networkStatus = _networkService.networkStatus;
      final cacheStats = await _offlineService.getCacheStatistics();

      print('📊 INITIALIZATION SUMMARY:');
      print('   Device: ${deviceInfo.brand} ${deviceInfo.model}');
      print('   Android: ${deviceInfo.androidVersion} (API ${deviceInfo.sdkInt})');
      print('   Performance: ${_deviceService.performanceProfile.tier}');
      print('   Network: ${networkStatus.isOnline ? 'Online' : 'Offline'} (${networkStatus.quality}%)');
      print('   Cache: ${cacheStats.totalEntries} entries');
      print('   Auth: ${_authService.isAuthenticated ? 'Authenticated' : 'Not authenticated'}');
      print('   Health: ${_isHealthy ? 'Healthy' : 'Degraded'}');
    } catch (e) {
      print('❌ Failed to log initialization summary: $e');
    }
  }

  /// Get app health status
  AppHealthStatus get healthStatus => AppHealthStatus(
    isHealthy: _isHealthy,
    isAuthenticated: _authService.isAuthenticated,
    isOnline: _networkService.isOnline,
    networkQuality: _networkService.networkQuality,
    pendingSyncCount: _offlineService.pendingSyncCount,
    deviceCompatibility: _deviceService.requiresSpecialHandling,
    performanceTier: _deviceService.performanceProfile.tier,
  );

  /// Force health check
  Future<void> forceHealthCheck() async {
    await _performHealthCheck();
  }

  /// Get service status
  Map<String, bool> getServiceStatus() => {
    'auth': _authService.isAuthenticated,
    'network': _networkService.isOnline,
    'offline': _offlineService.pendingSyncCount >= 0,
    'device': true, // Device service is always available
    'crash_prevention': true, // Assume healthy if no recent crashes
    'performance': _performanceService.isMonitoring,
  };

  /// Dispose all services
  void dispose() {
    _healthCheckTimer?.cancel();
    _authService.dispose();
    _networkService.dispose();
    _offlineService.dispose();
    _crashService.dispose();
    _performanceService.dispose();
    _isInitialized = false;
  }
}

/// App health status model
class AppHealthStatus {
  final bool isHealthy;
  final bool isAuthenticated;
  final bool isOnline;
  final int networkQuality;
  final int pendingSyncCount;
  final bool deviceCompatibility;
  final PerformanceTier performanceTier;

  AppHealthStatus({
    required this.isHealthy,
    required this.isAuthenticated,
    required this.isOnline,
    required this.networkQuality,
    required this.pendingSyncCount,
    required this.deviceCompatibility,
    required this.performanceTier,
  });
}
