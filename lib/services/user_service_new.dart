import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import 'api_service.dart';
import 'package:http/http.dart' as http;
import 'package:http_parser/http_parser.dart';

class UserService {
  static const String _userProfileKey = 'user_profile';

  // Singleton instance
  static final UserService _instance = UserService._internal();

  factory UserService() {
    return _instance;
  }

  UserService._internal();

  // In-memory cache of the user profile
  UserProfile? _cachedProfile;
  final ApiService _apiService = ApiService();

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _apiService.isLoggedIn();
  }

  // Logout user
  Future<void> logout() async {
    await _apiService.clearToken();
    _cachedProfile = null;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_userProfileKey);
  }

  // Get the user profile
  Future<UserProfile> getUserProfile() async {
    // Return cached profile if available
    if (_cachedProfile != null) {
      return _cachedProfile!;
    }

    // Check if user is logged in with API
    final isLoggedIn = await _apiService.isLoggedIn();

    if (isLoggedIn) {
      try {
        // Try to get profile from API
        final profile = await _apiService.getUserProfile();
        _cachedProfile = profile;
        await _saveProfileToLocal(profile);
        return profile;
      } catch (e) {
        // If API fails, try to load from local storage
        return await _getProfileFromLocal();
      }
    } else {
      // If not logged in, load from local storage
      return await _getProfileFromLocal();
    }
  }

  // Get profile from local storage
  Future<UserProfile> _getProfileFromLocal() async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = prefs.getString(_userProfileKey);

    if (profileJson != null) {
      try {
        final profileMap = jsonDecode(profileJson) as Map<String, dynamic>;
        _cachedProfile = UserProfile.fromJson(profileMap);
        return _cachedProfile!;
      } catch (e) {
        // If there's an error parsing the profile, return a new one
        _cachedProfile = _createDefaultProfile();
        return _cachedProfile!;
      }
    } else {
      // If no profile exists, create a default one
      _cachedProfile = _createDefaultProfile();
      return _cachedProfile!;
    }
  }

  // Save profile to local storage
  Future<void> _saveProfileToLocal(UserProfile profile) async {
    final prefs = await SharedPreferences.getInstance();
    final profileJson = jsonEncode(profile.toJson());

    await prefs.setString(_userProfileKey, profileJson);
  }

  // Save the user profile
  Future<void> saveUserProfile(UserProfile profile) async {
    _cachedProfile = profile;

    // Save to local storage
    await _saveProfileToLocal(profile);

    // If logged in, try to update on server
    if (await _apiService.isLoggedIn()) {
      try {
        await _apiService.updateProfile(
          name: profile.name,
          height: profile.height,
          weight: profile.weight,
        );
      } catch (e) {
        // Ignore API errors, we've already saved locally
        print('Failed to update profile on server: $e');
      }
    }
  }

  // Update user's weight and add BMI record
  Future<void> updateWeight(double weight) async {
    final profile = await getUserProfile();
    profile.addBMIRecord(weight);
    await saveUserProfile(profile);
  }

  // Add a workout record
  Future<void> addWorkout(String title, int durationMinutes) async {
    final profile = await getUserProfile();
    profile.addWorkoutRecord(title, durationMinutes);
    await saveUserProfile(profile);
  }

  // Update user's name
  Future<void> updateName(String name) async {
    final profile = await getUserProfile();
    profile.name = name;
    await saveUserProfile(profile);
  }

  // Update user's height
  Future<void> updateHeight(double height) async {
    final profile = await getUserProfile();
    profile.height = height;
    await saveUserProfile(profile);
  }

  // Create a default profile with sample data
  UserProfile _createDefaultProfile() {
    final profile = UserProfile(
      name: 'User',
      username: '',
      email: '',
      height: 175.0,
      weight: 70.0,
      isPremium: false,
    );

    return profile;
  }

  // Upload profile image and return the image URL
  Future<String> uploadProfileImage(File imageFile) async {
    try {
      // Get authentication token
      final token = await _apiService.getToken();
      if (token == null) {
        throw Exception('Not authenticated');
      }

      // Prepare the request
      final uri = Uri.parse('${ApiService.baseUrl}/upload_profile_image.php');
      print('Uploading to: $uri');

      // Get file extension
      final fileExt = imageFile.path.split('.').last.toLowerCase();

      // Determine content type based on file extension
      String contentType;
      switch (fileExt) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'gif':
          contentType = 'image/gif';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        case 'heic':
        case 'heif':
          contentType = 'image/heic';
          break;
        default:
          contentType = 'image/jpeg'; // Default to JPEG
      }

      print('File path: ${imageFile.path}');
      print('File extension: $fileExt');
      print('Content type: $contentType');

      final request = http.MultipartRequest('POST', uri)
        ..headers['Authorization'] = 'Bearer $token'
        ..fields['token'] = token // Add token as a field as well
        ..files.add(
          await http.MultipartFile.fromPath(
            'profile_image',
            imageFile.path,
            contentType: MediaType.parse(contentType)
          )
        );

      // Send the request
      print('Sending profile image upload request...');
      final response = await request.send().timeout(
        const Duration(seconds: 30),
        onTimeout: () => throw Exception('Upload timed out'),
      );

      print('Upload response status: ${response.statusCode}');
      final respStr = await response.stream.bytesToString();
      print('Upload response body: $respStr');

      // Parse response
      Map<String, dynamic> data;
      try {
        data = jsonDecode(respStr);
      } catch (e) {
        print('Failed to parse response: $e');
        throw Exception('Invalid response from server');
      }

      // Handle response
      if (response.statusCode == 200) {
        if (data['success'] == true && data['image_url'] != null) {
          print('Profile image uploaded successfully: ${data['image_url']}');

          // Update local profile
          final profile = await getUserProfile();
          profile.profileImageUrl = data['image_url'];

          // Clear the in-memory cache to ensure we get the latest data
          _cachedProfile = profile;

          // Save to local storage
          await _saveProfileToLocal(profile);

          return data['image_url'];
        } else {
          throw Exception(data['error'] ?? 'Failed to upload image');
        }
      } else {
        throw Exception('Server error: ${response.statusCode}, ${data['error'] ?? 'Unknown error'}');
      }
    } catch (e) {
      print('Profile image upload error: $e');
      rethrow;
    }
  }
}
