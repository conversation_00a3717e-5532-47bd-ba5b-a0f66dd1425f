import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/course.dart';
import '../models/course_video.dart';

class CourseTrackingService {
  // Keys for SharedPreferences
  static const String _lastOpenedCourseIdKey = 'last_opened_course_id';
  static const String _lastAccessedVideoIdKey = 'last_accessed_video_id';
  static const String _lastAccessedVideoTitleKey = 'last_accessed_video_title';
  static const String _lastAccessedVideoThumbnailKey = 'last_accessed_video_thumbnail';

  // Singleton instance
  static final CourseTrackingService _instance = CourseTrackingService._internal();

  factory CourseTrackingService() {
    return _instance;
  }

  CourseTrackingService._internal();

  // Save the last opened course
  Future<void> saveLastOpenedCourse(Course course) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastOpenedCourseIdKey, course.id);
      print('Saved last opened course ID: ${course.id}');
    } catch (e) {
      print('Error saving last opened course: $e');
    }
  }

  // Get the last opened course ID
  Future<int?> getLastOpenedCourseId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_lastOpenedCourseIdKey);
    } catch (e) {
      print('Error getting last opened course ID: $e');
      return null;
    }
  }

  // Find the last opened course from a list of courses
  Future<Course?> findLastOpenedCourse(List<Course> courses) async {
    final lastCourseId = await getLastOpenedCourseId();
    if (lastCourseId == null || courses.isEmpty) {
      return null;
    }

    try {
      return courses.firstWhere(
        (course) => course.id == lastCourseId,
        orElse: () => courses.first,
      );
    } catch (e) {
      print('Error finding last opened course: $e');
      return courses.isNotEmpty ? courses.first : null;
    }
  }

  // Save the last accessed video
  Future<void> saveLastAccessedVideo(CourseVideo video) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastAccessedVideoIdKey, video.id);
      await prefs.setString(_lastAccessedVideoTitleKey, video.title);
      if (video.thumbnailUrl != null) {
        await prefs.setString(_lastAccessedVideoThumbnailKey, video.thumbnailUrl!);
      }
      print('Saved last accessed video ID: ${video.id}');
    } catch (e) {
      print('Error saving last accessed video: $e');
    }
  }

  // Get the last accessed video ID
  Future<int?> getLastAccessedVideoId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_lastAccessedVideoIdKey);
    } catch (e) {
      print('Error getting last accessed video ID: $e');
      return null;
    }
  }

  // Get the last accessed video title
  Future<String?> getLastAccessedVideoTitle() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastAccessedVideoTitleKey);
    } catch (e) {
      print('Error getting last accessed video title: $e');
      return null;
    }
  }

  // Get the last accessed video thumbnail
  Future<String?> getLastAccessedVideoThumbnail() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_lastAccessedVideoThumbnailKey);
    } catch (e) {
      print('Error getting last accessed video thumbnail: $e');
      return null;
    }
  }

  // Find the last accessed video from a list of videos
  Future<CourseVideo?> findLastAccessedVideo(List<CourseVideo> videos) async {
    final lastVideoId = await getLastAccessedVideoId();
    if (lastVideoId == null || videos.isEmpty) {
      return null;
    }

    try {
      return videos.firstWhere(
        (video) => video.id == lastVideoId,
        orElse: () => videos.first,
      );
    } catch (e) {
      print('Error finding last accessed video: $e');
      return videos.isNotEmpty ? videos.first : null;
    }
  }

  // Save both course and video in one call
  Future<void> trackCourseAndVideo(Course course, CourseVideo video) async {
    await saveLastOpenedCourse(course);
    await saveLastAccessedVideo(video);
  }

  // Clear the last opened course
  Future<void> clearLastOpenedCourse() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastOpenedCourseIdKey);
      print('Cleared last opened course');
    } catch (e) {
      print('Error clearing last opened course: $e');
    }
  }
}
