import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:stack_trace/stack_trace.dart';

/// Comprehensive crash prevention and error handling service
class CrashPreventionService {
  static final CrashPreventionService _instance = CrashPreventionService._internal();
  factory CrashPreventionService() => _instance;
  CrashPreventionService._internal();

  // Error tracking
  bool _isInitialized = false;
  final List<ErrorReport> _errorHistory = [];
  Timer? _errorReportTimer;
  int _crashCount = 0;
  DateTime? _lastCrashTime;

  // Configuration
  static const int _maxErrorHistory = 100;
  static const Duration _errorReportInterval = Duration(minutes: 5);
  static const String _crashCountKey = 'crash_count';
  static const String _lastCrashKey = 'last_crash_time';
  static const String _errorHistoryKey = 'error_history';

  /// Initialize crash prevention service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('🛡️ Initializing CrashPreventionService...');

      // Load crash history
      await _loadCrashHistory();

      // Set up error handlers
      _setupErrorHandlers();

      // Start error reporting
      _startErrorReporting();

      // Perform crash recovery if needed
      await _performCrashRecovery();

      _isInitialized = true;
      print('✅ CrashPreventionService initialized');
    } catch (e) {
      print('❌ CrashPreventionService initialization failed: $e');
      _isInitialized = true; // Continue with basic functionality
    }
  }

  /// Load crash history from storage
  Future<void> _loadCrashHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      _crashCount = prefs.getInt(_crashCountKey) ?? 0;
      
      final lastCrashMs = prefs.getInt(_lastCrashKey);
      if (lastCrashMs != null) {
        _lastCrashTime = DateTime.fromMillisecondsSinceEpoch(lastCrashMs);
      }

      final errorHistoryJson = prefs.getString(_errorHistoryKey);
      if (errorHistoryJson != null) {
        final List<dynamic> errorList = json.decode(errorHistoryJson);
        _errorHistory.clear();
        _errorHistory.addAll(
          errorList.map((e) => ErrorReport.fromJson(e)).take(_maxErrorHistory)
        );
      }

      print('🛡️ Loaded crash history: $_crashCount crashes, ${_errorHistory.length} errors');
    } catch (e) {
      print('❌ Failed to load crash history: $e');
    }
  }

  /// Set up comprehensive error handlers
  void _setupErrorHandlers() {
    // Handle Flutter framework errors
    FlutterError.onError = (FlutterErrorDetails details) {
      _handleFlutterError(details);
    };

    // Handle platform errors
    PlatformDispatcher.instance.onError = (error, stack) {
      _handlePlatformError(error, stack);
      return true;
    };

    // Handle zone errors
    runZonedGuarded(() {
      // This would wrap your main app
    }, (error, stack) {
      _handleZoneError(error, stack);
    });

    print('🛡️ Error handlers configured');
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    try {
      final errorReport = ErrorReport(
        type: ErrorType.flutter,
        error: details.exception.toString(),
        stackTrace: details.stack?.toString(),
        context: details.context?.toString(),
        timestamp: DateTime.now(),
        isFatal: details.silent == false,
      );

      _recordError(errorReport);

      // Log to console in debug mode
      if (kDebugMode) {
        FlutterError.presentError(details);
      }

      print('🛡️ Flutter error handled: ${details.exception}');
    } catch (e) {
      print('❌ Failed to handle Flutter error: $e');
    }
  }

  /// Handle platform errors
  void _handlePlatformError(Object error, StackTrace stack) {
    try {
      final errorReport = ErrorReport(
        type: ErrorType.platform,
        error: error.toString(),
        stackTrace: stack.toString(),
        timestamp: DateTime.now(),
        isFatal: true,
      );

      _recordError(errorReport);
      print('🛡️ Platform error handled: $error');
    } catch (e) {
      print('❌ Failed to handle platform error: $e');
    }
  }

  /// Handle zone errors
  void _handleZoneError(Object error, StackTrace stack) {
    try {
      final errorReport = ErrorReport(
        type: ErrorType.zone,
        error: error.toString(),
        stackTrace: stack.toString(),
        timestamp: DateTime.now(),
        isFatal: false,
      );

      _recordError(errorReport);
      print('🛡️ Zone error handled: $error');
    } catch (e) {
      print('❌ Failed to handle zone error: $e');
    }
  }

  /// Record error in history
  void _recordError(ErrorReport errorReport) {
    try {
      _errorHistory.add(errorReport);

      // Keep only recent errors
      if (_errorHistory.length > _maxErrorHistory) {
        _errorHistory.removeAt(0);
      }

      // Update crash count if fatal
      if (errorReport.isFatal) {
        _crashCount++;
        _lastCrashTime = errorReport.timestamp;
        _saveCrashData();
      }

      // Save error history periodically
      _saveErrorHistory();

      // Check for error patterns
      _analyzeErrorPatterns();
    } catch (e) {
      print('❌ Failed to record error: $e');
    }
  }

  /// Save crash data
  Future<void> _saveCrashData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_crashCountKey, _crashCount);
      if (_lastCrashTime != null) {
        await prefs.setInt(_lastCrashKey, _lastCrashTime!.millisecondsSinceEpoch);
      }
    } catch (e) {
      print('❌ Failed to save crash data: $e');
    }
  }

  /// Save error history
  Future<void> _saveErrorHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final errorHistoryJson = json.encode(
        _errorHistory.map((e) => e.toJson()).toList()
      );
      await prefs.setString(_errorHistoryKey, errorHistoryJson);
    } catch (e) {
      print('❌ Failed to save error history: $e');
    }
  }

  /// Analyze error patterns for proactive prevention
  void _analyzeErrorPatterns() {
    try {
      final recentErrors = _errorHistory
          .where((e) => DateTime.now().difference(e.timestamp).inHours < 24)
          .toList();

      if (recentErrors.length >= 10) {
        print('⚠️ High error rate detected: ${recentErrors.length} errors in 24h');
        _triggerErrorMitigation();
      }

      // Check for repeated errors
      final errorGroups = <String, int>{};
      for (final error in recentErrors) {
        final key = error.error.split('\n').first; // First line of error
        errorGroups[key] = (errorGroups[key] ?? 0) + 1;
      }

      for (final entry in errorGroups.entries) {
        if (entry.value >= 5) {
          print('⚠️ Repeated error detected: ${entry.key} (${entry.value} times)');
          _handleRepeatedError(entry.key);
        }
      }
    } catch (e) {
      print('❌ Error pattern analysis failed: $e');
    }
  }

  /// Trigger error mitigation strategies
  void _triggerErrorMitigation() {
    try {
      print('🔧 Triggering error mitigation...');

      // Reduce app functionality temporarily
      // Clear problematic cache data
      // Reset to safe defaults
      // Notify user if necessary

      print('✅ Error mitigation applied');
    } catch (e) {
      print('❌ Error mitigation failed: $e');
    }
  }

  /// Handle repeated errors
  void _handleRepeatedError(String errorSignature) {
    try {
      print('🔧 Handling repeated error: $errorSignature');

      // Apply specific fixes based on error type
      if (errorSignature.contains('Network')) {
        _handleNetworkErrors();
      } else if (errorSignature.contains('Database')) {
        _handleDatabaseErrors();
      } else if (errorSignature.contains('Permission')) {
        _handlePermissionErrors();
      }
    } catch (e) {
      print('❌ Repeated error handling failed: $e');
    }
  }

  /// Handle network-related errors
  void _handleNetworkErrors() {
    // Reset network configuration
    // Clear network cache
    // Switch to offline mode temporarily
    print('🌐 Network error mitigation applied');
  }

  /// Handle database-related errors
  void _handleDatabaseErrors() {
    // Repair database
    // Clear corrupted data
    // Reinitialize database connection
    print('💾 Database error mitigation applied');
  }

  /// Handle permission-related errors
  void _handlePermissionErrors() {
    // Reset permission requests
    // Guide user to settings
    // Disable features requiring permissions
    print('🔐 Permission error mitigation applied');
  }

  /// Start error reporting
  void _startErrorReporting() {
    _errorReportTimer?.cancel();
    _errorReportTimer = Timer.periodic(_errorReportInterval, (_) {
      _generateErrorReport();
    });
  }

  /// Generate periodic error report
  void _generateErrorReport() {
    try {
      final recentErrors = _errorHistory
          .where((e) => DateTime.now().difference(e.timestamp).inMinutes < 5)
          .toList();

      if (recentErrors.isNotEmpty) {
        print('📊 Error report: ${recentErrors.length} errors in last 5 minutes');
        
        // This would send to analytics service in production
        _sendErrorReport(recentErrors);
      }
    } catch (e) {
      print('❌ Error report generation failed: $e');
    }
  }

  /// Send error report to analytics
  void _sendErrorReport(List<ErrorReport> errors) {
    try {
      // This would integrate with your analytics service
      // For now, just log the summary
      final errorTypes = <ErrorType, int>{};
      for (final error in errors) {
        errorTypes[error.type] = (errorTypes[error.type] ?? 0) + 1;
      }

      print('📊 Error summary: $errorTypes');
    } catch (e) {
      print('❌ Error report sending failed: $e');
    }
  }

  /// Perform crash recovery
  Future<void> _performCrashRecovery() async {
    try {
      if (_lastCrashTime != null) {
        final timeSinceLastCrash = DateTime.now().difference(_lastCrashTime!);
        
        if (timeSinceLastCrash.inMinutes < 5) {
          print('🔧 Recent crash detected, performing recovery...');
          await _recoverFromCrash();
        }
      }

      if (_crashCount >= 5) {
        print('⚠️ Multiple crashes detected, entering safe mode...');
        await _enterSafeMode();
      }
    } catch (e) {
      print('❌ Crash recovery failed: $e');
    }
  }

  /// Recover from recent crash
  Future<void> _recoverFromCrash() async {
    try {
      // Clear potentially corrupted data
      // Reset to safe defaults
      // Restore from backup if available
      
      print('✅ Crash recovery completed');
    } catch (e) {
      print('❌ Crash recovery failed: $e');
    }
  }

  /// Enter safe mode
  Future<void> _enterSafeMode() async {
    try {
      // Disable advanced features
      // Use minimal functionality
      // Clear all cache
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('safe_mode', true);
      
      print('🛡️ Safe mode activated');
    } catch (e) {
      print('❌ Safe mode activation failed: $e');
    }
  }

  /// Report error manually
  Future<void> reportError(
    Object error, 
    String context, {
    StackTrace? stackTrace,
    bool isFatal = false,
  }) async {
    try {
      final errorReport = ErrorReport(
        type: ErrorType.manual,
        error: error.toString(),
        stackTrace: stackTrace?.toString(),
        context: context,
        timestamp: DateTime.now(),
        isFatal: isFatal,
      );

      _recordError(errorReport);
    } catch (e) {
      print('❌ Manual error reporting failed: $e');
    }
  }

  /// Report Flutter error manually
  Future<void> reportFlutterError(FlutterErrorDetails details) async {
    _handleFlutterError(details);
  }

  /// Check if app is healthy
  Future<bool> isHealthy() async {
    try {
      final recentErrors = _errorHistory
          .where((e) => DateTime.now().difference(e.timestamp).inHours < 1)
          .toList();

      // Consider unhealthy if more than 5 errors in the last hour
      return recentErrors.length < 5;
    } catch (e) {
      return false;
    }
  }

  /// Get error statistics
  ErrorStatistics getErrorStatistics() {
    try {
      final now = DateTime.now();
      final last24h = _errorHistory
          .where((e) => now.difference(e.timestamp).inHours < 24)
          .toList();
      final lastHour = _errorHistory
          .where((e) => now.difference(e.timestamp).inHours < 1)
          .toList();

      return ErrorStatistics(
        totalErrors: _errorHistory.length,
        errorsLast24h: last24h.length,
        errorsLastHour: lastHour.length,
        totalCrashes: _crashCount,
        lastCrashTime: _lastCrashTime,
      );
    } catch (e) {
      return ErrorStatistics(
        totalErrors: 0,
        errorsLast24h: 0,
        errorsLastHour: 0,
        totalCrashes: 0,
        lastCrashTime: null,
      );
    }
  }

  /// Clear error history
  Future<void> clearErrorHistory() async {
    try {
      _errorHistory.clear();
      _crashCount = 0;
      _lastCrashTime = null;

      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_errorHistoryKey);
      await prefs.remove(_crashCountKey);
      await prefs.remove(_lastCrashKey);

      print('🧹 Error history cleared');
    } catch (e) {
      print('❌ Failed to clear error history: $e');
    }
  }

  /// Dispose the service
  void dispose() {
    _errorReportTimer?.cancel();
    _isInitialized = false;
  }
}

/// Error report model
class ErrorReport {
  final ErrorType type;
  final String error;
  final String? stackTrace;
  final String? context;
  final DateTime timestamp;
  final bool isFatal;

  ErrorReport({
    required this.type,
    required this.error,
    this.stackTrace,
    this.context,
    required this.timestamp,
    required this.isFatal,
  });

  Map<String, dynamic> toJson() => {
    'type': type.toString(),
    'error': error,
    'stackTrace': stackTrace,
    'context': context,
    'timestamp': timestamp.toIso8601String(),
    'isFatal': isFatal,
  };

  factory ErrorReport.fromJson(Map<String, dynamic> json) => ErrorReport(
    type: ErrorType.values.firstWhere(
      (e) => e.toString() == json['type'],
      orElse: () => ErrorType.unknown,
    ),
    error: json['error'],
    stackTrace: json['stackTrace'],
    context: json['context'],
    timestamp: DateTime.parse(json['timestamp']),
    isFatal: json['isFatal'] ?? false,
  );
}

/// Error type enumeration
enum ErrorType { flutter, platform, zone, manual, unknown }

/// Error statistics model
class ErrorStatistics {
  final int totalErrors;
  final int errorsLast24h;
  final int errorsLastHour;
  final int totalCrashes;
  final DateTime? lastCrashTime;

  ErrorStatistics({
    required this.totalErrors,
    required this.errorsLast24h,
    required this.errorsLastHour,
    required this.totalCrashes,
    this.lastCrashTime,
  });
}
