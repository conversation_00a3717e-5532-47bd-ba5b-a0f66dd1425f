import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:video_player/video_player.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'performance_monitor.dart';

/// Production-optimized video service for cross-device compatibility
/// Handles video playback with device-specific optimizations and memory management
class OptimizedVideoService {
  static final OptimizedVideoService _instance = OptimizedVideoService._internal();
  factory OptimizedVideoService() => _instance;
  OptimizedVideoService._internal();

  // Video player management
  final Map<String, VideoPlayerController> _controllers = {};
  final Map<String, DateTime> _lastUsed = {};
  final Set<String> _preloadedVideos = {};
  
  // Performance monitoring
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  
  // Configuration
  static const int _maxConcurrentControllers = 3;
  static const int _maxConcurrentControllersLowEnd = 1;
  static const Duration _controllerTimeout = Duration(minutes: 5);
  
  bool _isLowEndDevice = false;
  bool _isInitialized = false;

  /// Initialize the video service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Detect device capabilities
      _isLowEndDevice = _performanceMonitor.isLowEndDevice;
      
      // Start cleanup timer
      _startCleanupTimer();
      
      _isInitialized = true;
      debugPrint('🎥 OptimizedVideoService initialized for ${_isLowEndDevice ? "low-end" : "high-end"} device');
    } catch (e) {
      debugPrint('❌ Error initializing OptimizedVideoService: $e');
    }
  }

  /// Get device-specific max controllers
  int get _maxControllersForDevice => _isLowEndDevice ? _maxConcurrentControllersLowEnd : _maxConcurrentControllers;

  /// Create or get video controller with optimizations
  Future<VideoPlayerController> getVideoController(
    String videoUrl, {
    bool autoPlay = false,
    bool enablePreload = true,
    VideoQuality? preferredQuality,
  }) async {
    _performanceMonitor.startOperation('video_controller_creation');
    
    try {
      // Check if controller already exists
      if (_controllers.containsKey(videoUrl)) {
        _lastUsed[videoUrl] = DateTime.now();
        _performanceMonitor.endOperation('video_controller_creation');
        return _controllers[videoUrl]!;
      }

      // Clean up old controllers if needed
      await _cleanupOldControllers();

      // Create new controller with device-specific optimizations
      final controller = await _createOptimizedController(
        videoUrl,
        preferredQuality: preferredQuality,
      );

      // Store controller
      _controllers[videoUrl] = controller;
      _lastUsed[videoUrl] = DateTime.now();

      // Initialize controller
      await controller.initialize();

      // Configure for device capabilities
      await _configureController(controller);

      // Auto-play if requested
      if (autoPlay) {
        await controller.play();
        await _enableWakelock();
      }

      _performanceMonitor.endOperation('video_controller_creation');
      return controller;
    } catch (e) {
      _performanceMonitor.endOperation('video_controller_creation');
      debugPrint('❌ Error creating video controller: $e');
      rethrow;
    }
  }

  /// Create optimized video controller
  Future<VideoPlayerController> _createOptimizedController(
    String videoUrl, {
    VideoQuality? preferredQuality,
  }) async {
    VideoPlayerController controller;

    if (videoUrl.startsWith('http')) {
      // Network video with optimizations
      controller = VideoPlayerController.networkUrl(
        Uri.parse(videoUrl),
        httpHeaders: _getOptimizedHeaders(),
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: false,
          allowBackgroundPlayback: false,
        ),
      );
    } else {
      // Asset video
      controller = VideoPlayerController.asset(videoUrl);
    }

    return controller;
  }

  /// Get optimized HTTP headers for video requests
  Map<String, String> _getOptimizedHeaders() {
    final headers = <String, String>{
      'User-Agent': 'KFT-Fitness-App/1.0',
      'Accept': 'video/*',
    };

    // Add quality preferences for low-end devices
    if (_isLowEndDevice) {
      headers['Accept-Encoding'] = 'gzip, deflate';
      headers['Connection'] = 'keep-alive';
    }

    return headers;
  }

  /// Configure controller for device capabilities
  Future<void> _configureController(VideoPlayerController controller) async {
    try {
      // Set volume based on device capabilities
      await controller.setVolume(1.0);

      // Configure playback speed options
      if (!_isLowEndDevice) {
        // High-end devices can handle variable playback speeds
        // This would be implemented based on the video player capabilities
      }

      // Set looping to false by default to save resources
      await controller.setLooping(false);
    } catch (e) {
      debugPrint('⚠️ Error configuring video controller: $e');
    }
  }

  /// Preload video for better performance
  Future<void> preloadVideo(String videoUrl) async {
    if (_preloadedVideos.contains(videoUrl) || _controllers.containsKey(videoUrl)) {
      return;
    }

    try {
      _performanceMonitor.startOperation('video_preload');
      
      final controller = await _createOptimizedController(videoUrl);
      await controller.initialize();
      
      // Pause immediately to save bandwidth
      await controller.pause();
      
      _controllers[videoUrl] = controller;
      _lastUsed[videoUrl] = DateTime.now();
      _preloadedVideos.add(videoUrl);
      
      _performanceMonitor.endOperation('video_preload');
      debugPrint('✅ Video preloaded: $videoUrl');
    } catch (e) {
      _performanceMonitor.endOperation('video_preload');
      debugPrint('❌ Error preloading video: $e');
    }
  }

  /// Play video with optimizations
  Future<void> playVideo(String videoUrl) async {
    try {
      final controller = _controllers[videoUrl];
      if (controller != null) {
        await controller.play();
        await _enableWakelock();
        _lastUsed[videoUrl] = DateTime.now();
      }
    } catch (e) {
      debugPrint('❌ Error playing video: $e');
    }
  }

  /// Pause video
  Future<void> pauseVideo(String videoUrl) async {
    try {
      final controller = _controllers[videoUrl];
      if (controller != null) {
        await controller.pause();
        await _disableWakelock();
      }
    } catch (e) {
      debugPrint('❌ Error pausing video: $e');
    }
  }

  /// Stop video and clean up
  Future<void> stopVideo(String videoUrl) async {
    try {
      final controller = _controllers[videoUrl];
      if (controller != null) {
        await controller.pause();
        await controller.seekTo(Duration.zero);
        await _disableWakelock();
      }
    } catch (e) {
      debugPrint('❌ Error stopping video: $e');
    }
  }

  /// Seek to position with optimization
  Future<void> seekTo(String videoUrl, Duration position) async {
    try {
      final controller = _controllers[videoUrl];
      if (controller != null && controller.value.isInitialized) {
        // Optimize seeking for low-end devices
        if (_isLowEndDevice) {
          // Pause before seeking to reduce load
          final wasPlaying = controller.value.isPlaying;
          if (wasPlaying) {
            await controller.pause();
          }
          
          await controller.seekTo(position);
          
          if (wasPlaying) {
            await controller.play();
          }
        } else {
          await controller.seekTo(position);
        }
        
        _lastUsed[videoUrl] = DateTime.now();
      }
    } catch (e) {
      debugPrint('❌ Error seeking video: $e');
    }
  }

  /// Set playback speed
  Future<void> setPlaybackSpeed(String videoUrl, double speed) async {
    try {
      final controller = _controllers[videoUrl];
      if (controller != null && !_isLowEndDevice) {
        // Only allow speed changes on high-end devices
        await controller.setPlaybackSpeed(speed);
        _lastUsed[videoUrl] = DateTime.now();
      }
    } catch (e) {
      debugPrint('❌ Error setting playback speed: $e');
    }
  }

  /// Enable wakelock to prevent screen timeout during video playback
  Future<void> _enableWakelock() async {
    try {
      await WakelockPlus.enable();
    } catch (e) {
      debugPrint('⚠️ Error enabling wakelock: $e');
    }
  }

  /// Disable wakelock when video is not playing
  Future<void> _disableWakelock() async {
    try {
      // Check if any video is still playing
      final hasPlayingVideo = _controllers.values.any((controller) => 
          controller.value.isInitialized && controller.value.isPlaying);
      
      if (!hasPlayingVideo) {
        await WakelockPlus.disable();
      }
    } catch (e) {
      debugPrint('⚠️ Error disabling wakelock: $e');
    }
  }

  /// Start cleanup timer for old controllers
  void _startCleanupTimer() {
    Timer.periodic(const Duration(minutes: 1), (_) {
      _cleanupOldControllers();
    });
  }

  /// Clean up old or unused controllers
  Future<void> _cleanupOldControllers() async {
    final now = DateTime.now();
    final controllersToRemove = <String>[];

    // Find controllers that haven't been used recently
    for (final entry in _lastUsed.entries) {
      if (now.difference(entry.value) > _controllerTimeout) {
        controllersToRemove.add(entry.key);
      }
    }

    // Also remove excess controllers if we have too many
    if (_controllers.length > _maxControllersForDevice) {
      final sortedEntries = _lastUsed.entries.toList()
        ..sort((a, b) => a.value.compareTo(b.value));
      
      final excessCount = _controllers.length - _maxControllersForDevice;
      for (int i = 0; i < excessCount; i++) {
        controllersToRemove.add(sortedEntries[i].key);
      }
    }

    // Remove old controllers
    for (final videoUrl in controllersToRemove) {
      await _removeController(videoUrl);
    }

    if (controllersToRemove.isNotEmpty) {
      debugPrint('🧹 Cleaned up ${controllersToRemove.length} video controllers');
    }
  }

  /// Remove a specific controller
  Future<void> _removeController(String videoUrl) async {
    try {
      final controller = _controllers.remove(videoUrl);
      if (controller != null) {
        await controller.pause();
        await controller.dispose();
      }
      _lastUsed.remove(videoUrl);
      _preloadedVideos.remove(videoUrl);
    } catch (e) {
      debugPrint('⚠️ Error removing controller: $e');
    }
  }

  /// Get video controller if it exists
  VideoPlayerController? getExistingController(String videoUrl) {
    return _controllers[videoUrl];
  }

  /// Check if video is preloaded
  bool isVideoPreloaded(String videoUrl) {
    return _preloadedVideos.contains(videoUrl);
  }

  /// Get video service statistics
  Map<String, dynamic> getStats() {
    return {
      'activeControllers': _controllers.length,
      'maxControllers': _maxControllersForDevice,
      'preloadedVideos': _preloadedVideos.length,
      'isLowEndDevice': _isLowEndDevice,
      'memoryOptimized': _isLowEndDevice,
    };
  }

  /// Dispose all resources
  Future<void> dispose() async {
    // Dispose all controllers
    for (final controller in _controllers.values) {
      try {
        await controller.dispose();
      } catch (e) {
        debugPrint('⚠️ Error disposing controller: $e');
      }
    }
    
    _controllers.clear();
    _lastUsed.clear();
    _preloadedVideos.clear();
    
    // Disable wakelock
    await _disableWakelock();
    
    debugPrint('🎥 OptimizedVideoService disposed');
  }
}

/// Video quality enumeration
enum VideoQuality {
  auto,
  low,     // 360p
  medium,  // 480p
  high,    // 720p
  ultra,   // 1080p+
}
