import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_profile.dart';
import '../models/course.dart';
import '../models/course_video.dart';
import '../models/purchasable_course.dart';
import '../models/motivational_quote.dart';
import '../models/workout.dart';

import '../config/app_config.dart';
import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import '../providers/auth_provider.dart';
import 'package:provider/provider.dart' as provider;
import '../utils/platform_storage_io.dart';

final storage = FlutterSecureStorage();

class ApiService {
  // Base URL with setter for dynamic updates
  static String _baseUrl = AppConfig.defaultApiBaseUrl;

  // Getter for base URL
  static String get baseUrl => _baseUrl;

  // For debugging
  void _logRequest(String method, String url, Map<String, dynamic>? body) {
    print('API Request: $method $url');
    if (body != null) {
      print('Request Body: ${jsonEncode(body)}');
    }
    print('Request Headers: Content-Type: application/json');
  }

  void _logResponse(http.Response response) {
    print('API Response: ${response.statusCode}');
    print('Response Headers: ${response.headers}');

    // Try to format JSON response for better readability
    try {
      if (response.body.isNotEmpty && response.headers['content-type']?.contains('application/json') == true) {
        final jsonData = jsonDecode(response.body);
        print('Response Body (JSON): ${jsonEncode(jsonData)}');
      } else {
        print('Response Body (Raw): ${response.body}');
      }
    } catch (e) {
      print('Response Body (Raw): ${response.body}');
      print('Error formatting response: $e');
    }
  }

  // Token storage key
  static const String _tokenKey = 'auth_token';

  // Singleton instance
  static final ApiService _instance = ApiService._internal();

  factory ApiService() {
    return _instance;
  }

  // Private constructor for singleton
  ApiService._internal();

  // Default constructor for subclasses
  ApiService.create();

  // Get the stored auth token
  Future<String?> getToken() async {
    try {
      // Try to get token from FlutterSecureStorage first
      final secureToken = await storage.read(key: 'auth_token');
      if (secureToken != null) {
        // Check if token is a JWT token (contains two dots)
        if (secureToken.split('.').length == 3) {
          // Parse the JWT token to check expiration
          try {
            final parts = secureToken.split('.');
            final payload = parts[1];
            // Add padding to base64 string if needed
            String normalized = payload.replaceAll('-', '+').replaceAll('_', '/');
            normalized = normalized + '=' * ((4 - normalized.length % 4) % 4);

            final decoded = utf8.decode(base64Url.decode(normalized));
            final Map<String, dynamic> data = jsonDecode(decoded);

            // Check if token is expired
            if (data.containsKey('exp')) {
              final expiry = DateTime.fromMillisecondsSinceEpoch(data['exp'] * 1000);
              if (expiry.isBefore(DateTime.now())) {
                print('JWT token has expired, clearing it');
                await clearToken();
                return null;
              }
            }
          } catch (e) {
            print('Error parsing JWT token: $e');
            // Continue using the token even if we can't parse it
          }
        }

        print('Token loaded from secure storage: ${secureToken.substring(0, 20)}...');
        return secureToken;
      }

      // Fall back to SharedPreferences for backward compatibility
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString(_tokenKey);

      // If token exists in SharedPreferences, migrate it to secure storage
      if (token != null) {
        print('Migrating token from SharedPreferences to secure storage');
        await storage.write(key: 'auth_token', value: token);
        await prefs.remove(_tokenKey); // Remove from SharedPreferences after migration
      }

      if (token != null) {
        print('Token loaded from SharedPreferences: ${token.substring(0, 20)}...');
      } else {
        print('No token found');
      }
      return token;
    } catch (e) {
      print('Error getting token: $e');
      return null;
    }
  }

  // Save the auth token
  Future<void> saveToken(String token) async {
    try {
      // Save to FlutterSecureStorage
      await storage.write(key: 'auth_token', value: token);
      print('Token saved to secure storage: ${token.substring(0, 20)}...');
    } catch (e) {
      print('Error saving token to secure storage: $e');
      // Fall back to SharedPreferences if secure storage fails
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_tokenKey, token);
      print('Token saved to SharedPreferences as fallback: ${token.substring(0, 20)}...');
    }
  }

  // Clear the auth token (logout)
  Future<void> clearToken() async {
    try {
      // Clear from both storage mechanisms
      await storage.delete(key: 'auth_token');
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_tokenKey);
      print('Token cleared from all storage');
    } catch (e) {
      print('Error clearing token: $e');
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Attempt to refresh the token
  Future<bool> refreshToken() async {
    final token = await getToken();

    if (token == null) {
      print('No token found to refresh');
      return false;
    }

    try {
      // Try to validate the token with a lightweight API call
      final response = await http.get(
        Uri.parse('$baseUrl/profile.php'),
        headers: {
          'Authorization': 'Bearer $token',
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      );

      // If the token is valid, return true
      if (response.statusCode == 200) {
        print('Token is still valid');

        // Re-save the token to ensure it's stored correctly in secure storage
        await saveToken(token);

        return true;
      }

      // If we get a 401, try to get a new token by logging in again
      if (response.statusCode == 401) {
        print('Token is expired, attempting to get a new token');

        // Try to get a new token from the server
        // This would typically involve a refresh token API call
        // For now, we'll just clear the token and return false
        await clearToken();
        return false;
      }

      // If the token is invalid for other reasons, clear it and return false
      print('Token is invalid (status ${response.statusCode}), clearing it');
      await clearToken();
      return false;
    } catch (e) {
      print('Error refreshing token: $e');
      return false;
    }
  }

  // Login with phone number and PIN
  Future<Map<String, dynamic>> login(String phoneNumber, {required String pin, required String deviceId, required String baseUrl}) async {
    try {
      final url = 'login.php';
      final data = {
        'phone_number': phoneNumber,
        'pin': pin,
        'device_id': deviceId,
      };

      final response = await makeApiRequest(
        url,
        method: 'POST',
        data: data,
        requiresAuth: false,
      );

      if (response['success'] == true) {
        // Defensive null checks
        if (response['token'] == null) {
          throw Exception('Login failed: Missing token from server. Please try again or contact support.');
        }

        // Save the token
        await saveToken(response['token']);
        return response;
      }

      // For specific error types, pass the entire error response
      if (response['error'] == 'already_logged_in' ||
          response['error'] == 'already_logged_in ' || // Handle extra space
          response['error'] == 'device_mismatch') {
        throw Exception(jsonEncode(response));
      } else {
        throw Exception(response['message'] ?? response['error'] ?? 'Login failed');
      }
    } catch (e) {
      print('Login error: $e');
      if (e is Exception) {
        rethrow;
      }
      throw Exception('Network error: $e');
    }
  }

  // Get user profile from API
  Future<UserProfile> getUserProfile() async {
    try {
      final response = await makeApiRequest('profile.php');

      if (response['success'] == true && response['profile'] != null) {
        // Create UserProfile from API data
        final userProfile = UserProfile(
          name: response['profile']['name'] ?? 'Unknown',
          username: response['profile']['username'] ?? '',
          email: response['profile']['email'] ?? '',
          phoneNumber: response['profile']['phone_number'] ?? response['profile']['phone'] ?? '',
          height: _parseDouble(response['profile']['height'], 170.0),
          weight: _parseDouble(response['profile']['weight'], 70.0),
          isPremium: response['profile']['is_premium'] == 1,
          profileImageUrl: response['profile']['profile_image_url'] ?? '',
          assignedStaffId: response['profile']['assigned_staff_id'],
        );

        print('Loaded profile with image URL: ${userProfile.profileImageUrl}');

        // Add BMI records if available
        if (response['bmi_records'] != null) {
          for (var record in response['bmi_records']) {
            userProfile.bmiHistory.add(BMIRecord(
              date: DateTime.parse(record['recorded_at']),
              bmi: _parseDouble(record['bmi'], 0.0),
              weight: _parseDouble(record['weight'], 0.0),
            ));
          }
        }

        // Add streak days if available
        if (response['streak_days'] != null) {
          for (var day in response['streak_days']) {
            userProfile.streakDays.add(DateTime.parse(day));
          }
        }

        return userProfile;
      } else {
        throw Exception('Invalid profile data structure');
      }
    } catch (e) {
      throw Exception('Error loading profile: $e');
    }
  }

  // Helper method to parse double values from API
  double _parseDouble(dynamic value, double defaultValue) {
    if (value == null) {
      return defaultValue;
    }

    if (value is num) {
      return value.toDouble();
    }

    if (value is String) {
      try {
        return double.parse(value);
      } catch (e) {
        print('Error parsing double from string: $value');
        return defaultValue;
      }
    }

    print('Unknown type for double value: ${value.runtimeType}');
    return defaultValue;
  }

  // Generic API request method
  Future<Map<String, dynamic>> makeApiRequest(String endpoint, {String method = 'GET', Map<String, dynamic>? data, bool requiresAuth = true, bool isRetry = false}) async {
    try {
      String cleanBaseUrl = _baseUrl;
      if (!cleanBaseUrl.endsWith('/')) cleanBaseUrl += '/';
      final url = Uri.parse('${cleanBaseUrl}${endpoint.startsWith('/') ? endpoint.substring(1) : endpoint}');
      final headers = <String, String>{
        'Content-Type': 'application/json',
      };

      // Add authorization header if required
      if (requiresAuth) {
        final token = await getToken();
        if (token != null) {
          headers['Authorization'] = 'Bearer $token';
          print('Using token for $endpoint: ${token.substring(0, 20)}...');
        } else {
          print('Token is null for $endpoint');
          if (!isRetry) {
            // Try to refresh the token
            print('Attempting to refresh token...');
            final refreshed = await refreshToken();
            if (refreshed) {
              // Retry the request with the new token
              return makeApiRequest(endpoint, method: method, data: data, requiresAuth: requiresAuth, isRetry: true);
            } else {
              print('Token refresh failed');
              throw Exception('Not authenticated');
            }
          } else {
            throw Exception('Not authenticated');
          }
        }
      }

      http.Response response;

      print('API Request: $method $url');
      if (data != null) {
        print('Request Body: ${jsonEncode(data)}');
      }
      print('Request Headers: ${headers.entries.map((e) => '${e.key}: ${e.value}').join(', ')}');

      if (method == 'GET') {
        response = await http.get(url, headers: headers);
      } else if (method == 'POST') {
        response = await http.post(url, headers: headers, body: data != null ? jsonEncode(data) : null);
      } else if (method == 'PUT') {
        response = await http.put(url, headers: headers, body: data != null ? jsonEncode(data) : null);
      } else if (method == 'DELETE') {
        response = await http.delete(url, headers: headers);
      } else {
        throw Exception('Unsupported HTTP method: $method');
      }

      print('API Response: ${response.statusCode}');
      print('Response Headers: ${response.headers}');

      // For debugging
      if (response.body.isNotEmpty) {
        try {
          print('Response Body (JSON): ${jsonDecode(response.body)}');
        } catch (e) {
          print('Response Body (raw): ${response.body}');
        }
      }

      // Handle authentication errors
      if (response.statusCode == 401) {
        print('Authentication failed (401) for $endpoint. Retry attempt: ${isRetry ? '2' : '1'}');

        if (!isRetry) {
          // Try to refresh the token
          print('Attempting to refresh token...');
          final refreshed = await refreshToken();
          if (refreshed) {
            // Retry the request with the new token
            return makeApiRequest(endpoint, method: method, data: data, requiresAuth: requiresAuth, isRetry: true);
          } else {
            print('Token refresh failed');
            print('Attempting to re-authenticate...');
            await clearToken();
            throw Exception('Authentication token lost during request');
          }
        } else {
          // Already retried once, clear token and force re-login
          await clearToken();
          throw Exception('Authentication failed after retry');
        }
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        if (response.body.isEmpty) {
          return {'success': true};
        }

        try {
          final responseData = jsonDecode(response.body);

          // Check if the response contains a new token
          if (responseData is Map && responseData.containsKey('token')) {
            final newToken = responseData['token'];
            if (newToken != null && newToken is String) {
              print('Received new token from server, saving it');
              await saveToken(newToken);
            }
          }

          return responseData;
        } catch (e) {
          print('Error parsing JSON response: $e');
          return {'success': false, 'error': 'Invalid response format'};
        }
      } else {
        print('API error: ${response.statusCode} - ${response.body}');

        try {
          return jsonDecode(response.body);
        } catch (e) {
          return {
            'success': false,
            'error': 'Request failed with status: ${response.statusCode}',
            'status': response.statusCode,
          };
        }
      }
    } catch (e) {
      print('Error making API request: $e');
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<http.Response> _makeRequestWithDeviceId(Future<http.Response> Function(Map<String, String> headers) requestFn, {Map<String, String>? headers}) async {
    final deviceId = await PlatformStorageImpl.getDeviceId();
    final requestHeaders = {
      ...?headers,
      if (deviceId != null) 'X-Device-ID': deviceId,
    };
    return await requestFn(requestHeaders);
  }
}
