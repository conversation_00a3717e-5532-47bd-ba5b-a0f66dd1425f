import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import '../services/api_service.dart';

class DailyStreakService {
  static const String _currentStreakKey = 'daily_streak_current';
  static const String _highestStreakKey = 'daily_streak_highest';
  static const String _lastOpenDateKey = 'daily_streak_last_open';
  static const String _streakHistoryKey = 'daily_streak_history';
  static const String _lastSyncKey = 'daily_streak_last_sync';

  static final DailyStreakService _instance = DailyStreakService._internal();
  factory DailyStreakService() => _instance;
  DailyStreakService._internal();

  int _currentStreak = 0;
  int _highestStreak = 0;
  DateTime? _lastOpenDate;
  Map<String, bool> _streakHistory = {};
  bool _isInitialized = false;

  int get currentStreak => _currentStreak;
  int get highestStreak => _highestStreak;
  DateTime? get lastOpenDate => _lastOpenDate;
  Map<String, bool> get streakHistory => Map.unmodifiable(_streakHistory);
  bool get isInitialized => _isInitialized;

  /// Initialize the service and load existing data
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await loadStreak();
      _isInitialized = true;
      print('🔥 DailyStreakService initialized - Current: $_currentStreak, Highest: $_highestStreak');
    } catch (e) {
      print('❌ Error initializing DailyStreakService: $e');
      _isInitialized = true; // Mark as initialized even on error to prevent infinite loops
    }
  }

  /// Call this on app open to update streak
  Future<void> updateStreakOnAppOpen() async {
    await initialize();

    final now = DateTime.now();
    final today = _formatDateKey(now);

    // Load last open date from memory (already loaded in initialize)
    bool streakUpdated = false;
    String updateReason = '';

    if (_lastOpenDate == null) {
      // First time opening the app
      _currentStreak = 1;
      _highestStreak = 1;
      _streakHistory[today] = true;
      streakUpdated = true;
      updateReason = 'First app open';
    } else {
      final daysSinceLastOpen = _daysBetween(_lastOpenDate!, now);

      if (daysSinceLastOpen == 0 && _streakHistory[today] == true) {
        // Same day, already counted
        updateReason = 'Same day, no update needed';
      } else if (daysSinceLastOpen == 0 && _streakHistory[today] != true) {
        // Same day, but not yet counted (edge case)
        _streakHistory[today] = true;
        updateReason = 'Same day, marked as completed';
      } else if (daysSinceLastOpen == 1) {
        // Next day - continue streak
        _currentStreak += 1;
        _streakHistory[today] = true;
        if (_currentStreak > _highestStreak) {
          _highestStreak = _currentStreak;
        }
        streakUpdated = true;
        updateReason = 'Next day - streak continued';
      } else if (daysSinceLastOpen > 1) {
        // Missed days - reset streak
        _currentStreak = 1;
        _streakHistory[today] = true;
        streakUpdated = true;
        updateReason = 'Missed $daysSinceLastOpen days - streak reset';
      }
    }

    // Save updated data
    _lastOpenDate = now;
    await _saveStreakData();

    if (streakUpdated) {
      HapticFeedback.lightImpact();
      print('🔥 Streak updated: $_currentStreak days ($updateReason)');
    } else {
      print('🔥 Streak checked: $_currentStreak days ($updateReason)');
    }

    // Sync with backend
    await _syncWithBackend();
  }

  /// Load streak data from local storage
  Future<void> loadStreak() async {
    final prefs = await SharedPreferences.getInstance();
    _currentStreak = prefs.getInt(_currentStreakKey) ?? 0;
    _highestStreak = prefs.getInt(_highestStreakKey) ?? 0;

    final lastOpenStr = prefs.getString(_lastOpenDateKey);
    if (lastOpenStr != null) {
      _lastOpenDate = DateTime.tryParse(lastOpenStr);
    }

    // Load streak history
    final historyStr = prefs.getString(_streakHistoryKey);
    if (historyStr != null) {
      try {
        final historyMap = jsonDecode(historyStr) as Map<String, dynamic>;
        _streakHistory = historyMap.map((key, value) => MapEntry(key, value as bool));
      } catch (e) {
        print('❌ Error loading streak history: $e');
        _streakHistory = {};
      }
    }
  }

  /// Save streak data to local storage
  Future<void> _saveStreakData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_currentStreakKey, _currentStreak);
    await prefs.setInt(_highestStreakKey, _highestStreak);

    if (_lastOpenDate != null) {
      await prefs.setString(_lastOpenDateKey, _lastOpenDate!.toIso8601String());
    }

    // Save streak history
    final historyStr = jsonEncode(_streakHistory);
    await prefs.setString(_streakHistoryKey, historyStr);
  }

  bool isMilestone() {
    return _currentStreak == 7 || _currentStreak == 30 || _currentStreak == 100;
  }

  int nextMilestone() {
    if (_currentStreak < 7) return 7;
    if (_currentStreak < 30) return 30;
    if (_currentStreak < 100) return 100;
    return ((_currentStreak ~/ 100) + 1) * 100;
  }

  /// Sync streak data with backend
  Future<void> _syncWithBackend() async {
    try {
      final apiService = ApiService();

      final streakData = {
        'current_streak': _currentStreak,
        'highest_streak': _highestStreak,
        'last_open_date': _lastOpenDate?.toIso8601String(),
        'streak_history': _streakHistory,
      };

      await apiService.makeApiRequest(
        'user_streak.php',
        method: 'POST',
        data: streakData,
      );

      // Update last sync time
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastSyncKey, DateTime.now().toIso8601String());

      print('🔄 Streak data synced with backend');
    } catch (e) {
      print('❌ Error syncing streak with backend: $e');
      // Continue without backend sync - local data is preserved
    }
  }

  /// Backup streak to preferences (legacy method)
  Future<void> backupStreakToPreferences() async {
    await _syncWithBackend();
  }

  /// Reset streak (for testing or user request)
  Future<void> resetStreak() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_currentStreakKey);
    await prefs.remove(_highestStreakKey);
    await prefs.remove(_lastOpenDateKey);
    await prefs.remove(_streakHistoryKey);

    _currentStreak = 0;
    _highestStreak = 0;
    _lastOpenDate = null;
    _streakHistory.clear();

    print('🔥 Streak reset successfully');
  }

  /// Get streak for a specific date
  bool getStreakForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _streakHistory[dateKey] ?? false;
  }

  /// Get current week's streak days
  List<bool> getCurrentWeekStreak() {
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));

    return List.generate(7, (index) {
      final date = weekStart.add(Duration(days: index));
      return getStreakForDate(date);
    });
  }

  /// Helper methods
  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  int _daysBetween(DateTime from, DateTime to) {
    from = DateTime(from.year, from.month, from.day);
    to = DateTime(to.year, to.month, to.day);
    return to.difference(from).inDays;
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }
} 