import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'api_service.dart';

class VideoStreakService {
  static const String _currentStreakKey = 'video_current_streak';
  static const String _highestStreakKey = 'video_highest_streak';
  static const String _lastActivityDateKey = 'video_last_activity_date';
  static const String _todayStreakEarnedKey = 'video_today_streak_earned';
  static const String _lastStreakMessageShownKey = 'video_last_streak_message_shown';

  final ApiService _apiService = ApiService();

  // Get current streak count
  Future<int> getCurrentStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_currentStreakKey) ?? 0;
    } catch (e) {
      debugPrint('Error getting current streak: $e');
      return 0;
    }
  }

  // Get highest streak achieved
  Future<int> getHighestStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getInt(_highestStreakKey) ?? 0;
    } catch (e) {
      debugPrint('Error getting highest streak: $e');
      return 0;
    }
  }

  // Get last activity date
  Future<DateTime?> getLastActivityDate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dateString = prefs.getString(_lastActivityDateKey);
      if (dateString != null) {
        return DateTime.parse(dateString);
      }
      return null;
    } catch (e) {
      debugPrint('Error getting last activity date: $e');
      return null;
    }
  }

  // Check if streak was earned today
  Future<bool> isStreakEarnedToday() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      final earnedDateString = prefs.getString(_todayStreakEarnedKey);
      return earnedDateString == todayString;
    } catch (e) {
      debugPrint('Error checking if streak earned today: $e');
      return false;
    }
  }

  // Mark streak as earned for today
  Future<void> markStreakEarnedToday() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      await prefs.setString(_todayStreakEarnedKey, todayString);
    } catch (e) {
      debugPrint('Error marking streak earned today: $e');
    }
  }

  // Check if video completion qualifies for streak (50% completion)
  Future<bool> checkVideoStreakQualification(int videoId, int watchDurationSeconds, int videoDurationMinutes) async {
    try {
      // Convert video duration from minutes to seconds
      final videoDurationSeconds = videoDurationMinutes * 60;

      // Check if watched at least 50% of the video
      if (videoDurationSeconds > 0) {
        final completionPercentage = (watchDurationSeconds / videoDurationSeconds) * 100;
        return completionPercentage >= 50.0;
      }

      return false;
    } catch (e) {
      debugPrint('Error checking video streak qualification: $e');
      return false;
    }
  }

  // Process video completion for streak tracking (AUTOMATIC - DEFAULT BEHAVIOR)
  Future<StreakUpdateResult> processVideoCompletion(int videoId, int watchDurationSeconds, int videoDurationMinutes) async {
    try {
      // Check if video qualifies for streak (50% completion)
      final qualifies = await checkVideoStreakQualification(videoId, watchDurationSeconds, videoDurationMinutes);

      if (!qualifies) {
        return StreakUpdateResult(
          streakUpdated: false,
          newStreak: await getCurrentStreak(),
          highestStreak: await getHighestStreak(),
          message: null,
        );
      }

      // Check if streak already earned today (only one streak per day)
      final alreadyEarnedToday = await isStreakEarnedToday();
      if (alreadyEarnedToday) {
        // Return current streak info but indicate no update needed
        return StreakUpdateResult(
          streakUpdated: false,
          newStreak: await getCurrentStreak(),
          highestStreak: await getHighestStreak(),
          message: "🎯 Great job! You've already earned your streak for today!",
        );
      }

      // Update streak automatically
      return await _updateStreak();
    } catch (e) {
      debugPrint('Error processing video completion: $e');
      return StreakUpdateResult(
        streakUpdated: false,
        newStreak: await getCurrentStreak(),
        highestStreak: await getHighestStreak(),
        message: null,
      );
    }
  }

  // Update streak logic (AUTOMATIC - NO MANUAL OPTIONS)
  Future<StreakUpdateResult> _updateStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      final lastActivityDate = await getLastActivityDate();
      final currentStreak = await getCurrentStreak();
      final highestStreak = await getHighestStreak();

      int newStreak = currentStreak;
      String? message;

      if (lastActivityDate == null) {
        // First time user - start streak at 1
        newStreak = 1;
        message = "🎉 Great start! You've begun your video learning streak! Watch 50% of any video daily to maintain it!";
      } else {
        final lastActivityDay = DateTime(lastActivityDate.year, lastActivityDate.month, lastActivityDate.day);
        final hoursDifference = now.difference(lastActivityDate).inHours;

        if (hoursDifference < 24) {
          // Within 24 hours - increment streak
          newStreak = currentStreak + 1;
          if (newStreak == 1) {
            message = "🎯 Perfect! You've started your video learning streak! Keep watching daily!";
          } else if (newStreak <= 3) {
            message = "🔥 Amazing! You're on a $newStreak-day streak! Keep the momentum going!";
          } else if (newStreak <= 7) {
            message = "⭐ Incredible! $newStreak days in a row! You're building a great habit!";
          } else if (newStreak <= 30) {
            message = "🏆 Outstanding! $newStreak-day streak! You're a learning champion!";
          } else {
            message = "👑 LEGENDARY! $newStreak days! You're an absolute learning master!";
          }
        } else {
          // More than 24 hours - reset to 1 (fresh start)
          message = currentStreak > 0
              ? "💪 Your streak was reset after 24 hours, but you're back! Starting fresh at day 1!"
              : "🎯 Welcome back! Starting your video learning streak at day 1!";
          newStreak = 1;
        }
      }

      // Update highest streak if needed
      final newHighestStreak = newStreak > highestStreak ? newStreak : highestStreak;

      // Save to local storage
      await prefs.setInt(_currentStreakKey, newStreak);
      await prefs.setInt(_highestStreakKey, newHighestStreak);
      await prefs.setString(_lastActivityDateKey, now.toIso8601String());
      await markStreakEarnedToday();

      // Sync with backend
      await _syncWithBackend(newStreak, newHighestStreak);

      return StreakUpdateResult(
        streakUpdated: true,
        newStreak: newStreak,
        highestStreak: newHighestStreak,
        message: message,
      );
    } catch (e) {
      debugPrint('Error updating streak: $e');
      return StreakUpdateResult(
        streakUpdated: false,
        newStreak: await getCurrentStreak(),
        highestStreak: await getHighestStreak(),
        message: null,
      );
    }
  }

  // Sync streak data with backend
  Future<void> _syncWithBackend(int currentStreak, int highestStreak) async {
    try {
      await _apiService.makeApiRequest(
        'update_video_streak.php',
        method: 'POST',
        data: {
          'current_streak': currentStreak,
          'highest_streak': highestStreak,
          'last_activity_date': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('Error syncing streak with backend: $e');
      // Don't throw error - local storage is primary
    }
  }

  // Load streak data from backend
  Future<void> loadStreakFromBackend() async {
    try {
      final response = await _apiService.makeApiRequest('get_video_streak.php');

      if (response['success'] == true && response['streak'] != null) {
        final streakData = response['streak'];
        final prefs = await SharedPreferences.getInstance();

        await prefs.setInt(_currentStreakKey, streakData['current_streak'] ?? 0);
        await prefs.setInt(_highestStreakKey, streakData['highest_streak'] ?? 0);

        if (streakData['last_activity_date'] != null) {
          await prefs.setString(_lastActivityDateKey, streakData['last_activity_date']);
        }
      }
    } catch (e) {
      debugPrint('Error loading streak from backend: $e');
      // Continue with local data
    }
  }

  // Check and update streak status (call this when homepage loads)
  Future<StreakStatusResult> checkStreakStatus() async {
    try {
      final now = DateTime.now();

      final lastActivityDate = await getLastActivityDate();
      final currentStreak = await getCurrentStreak();
      final highestStreak = await getHighestStreak();

      if (lastActivityDate == null) {
        return StreakStatusResult(
          currentStreak: currentStreak,
          highestStreak: highestStreak,
          streakBroken: false,
          message: null,
        );
      }

      final hoursDifference = now.difference(lastActivityDate).inHours;

      // Reset streak if more than 24 hours have passed since last activity
      if (hoursDifference >= 24 && currentStreak > 0) {
        // Streak broken - reset to 0
        final prefs = await SharedPreferences.getInstance();
        await prefs.setInt(_currentStreakKey, 0);

        final message = currentStreak > 1
            ? "💔 Your $currentStreak-day streak was reset after 24 hours! Your highest streak of $highestStreak days shows your dedication. Start fresh today! 💪"
            : "⏰ Your streak was reset after 24 hours! Start fresh by watching 50% of any video today! 🎯";

        // Sync with backend
        await _syncWithBackend(0, highestStreak);

        return StreakStatusResult(
          currentStreak: 0,
          highestStreak: highestStreak,
          streakBroken: true,
          message: message,
        );
      }

      return StreakStatusResult(
        currentStreak: currentStreak,
        highestStreak: highestStreak,
        streakBroken: false,
        message: null,
      );
    } catch (e) {
      debugPrint('Error checking streak status: $e');
      return StreakStatusResult(
        currentStreak: await getCurrentStreak(),
        highestStreak: await getHighestStreak(),
        streakBroken: false,
        message: null,
      );
    }
  }

  // Check if we should show streak message
  Future<bool> shouldShowStreakMessage(String message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastShownMessage = prefs.getString(_lastStreakMessageShownKey);
      return lastShownMessage != message;
    } catch (e) {
      return true;
    }
  }

  // Mark streak message as shown
  Future<void> markStreakMessageShown(String message) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastStreakMessageShownKey, message);
    } catch (e) {
      debugPrint('Error marking streak message as shown: $e');
    }
  }

  // Reset streak to zero
  Future<void> resetStreak() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_currentStreakKey, 0);
      await prefs.setString(_lastActivityDateKey, '');
      await prefs.setString(_todayStreakEarnedKey, '');
      await prefs.setString(_lastStreakMessageShownKey, '');
      debugPrint('Video streak reset successfully');
    } catch (e) {
      debugPrint('Error resetting video streak: $e');
      rethrow;
    }
  }
}

// Result classes
class StreakUpdateResult {
  final bool streakUpdated;
  final int newStreak;
  final int highestStreak;
  final String? message;

  StreakUpdateResult({
    required this.streakUpdated,
    required this.newStreak,
    required this.highestStreak,
    this.message,
  });
}

class StreakStatusResult {
  final int currentStreak;
  final int highestStreak;
  final bool streakBroken;
  final String? message;

  StreakStatusResult({
    required this.currentStreak,
    required this.highestStreak,
    required this.streakBroken,
    this.message,
  });
}
