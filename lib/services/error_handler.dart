import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'connectivity_service.dart';

/// Production-grade error handling and recovery service
/// Provides comprehensive error handling, logging, and user-friendly recovery options
class ErrorHandler {
  static final ErrorHandler _instance = ErrorHandler._internal();
  factory ErrorHandler() => _instance;
  ErrorHandler._internal();

  // Error tracking
  final List<ErrorReport> _errorReports = [];
  final Map<String, int> _errorCounts = {};
  final ConnectivityService _connectivityService = ConnectivityService();
  
  // Configuration
  static const int _maxErrorReports = 100;
  static const int _maxRetryAttempts = 3;
  static const Duration _errorCooldown = Duration(minutes: 5);

  /// Initialize error handler
  Future<void> initialize() async {
    // Set up global error handlers
    FlutterError.onError = _handleFlutterError;
    PlatformDispatcher.instance.onError = _handlePlatformError;
    
    debugPrint('🛡️ ErrorHandler initialized');
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    final errorReport = ErrorReport(
      timestamp: DateTime.now(),
      type: ErrorType.flutter,
      error: details.exception,
      stackTrace: details.stack,
      context: details.context?.toString(),
      library: details.library,
    );
    
    _recordError(errorReport);
    
    // In debug mode, use default error handling
    if (kDebugMode) {
      FlutterError.presentError(details);
    }
  }

  /// Handle platform errors
  bool _handlePlatformError(Object error, StackTrace stackTrace) {
    final errorReport = ErrorReport(
      timestamp: DateTime.now(),
      type: ErrorType.platform,
      error: error,
      stackTrace: stackTrace,
    );
    
    _recordError(errorReport);
    
    // Return true to indicate error was handled
    return true;
  }

  /// Record error for tracking and analysis
  void _recordError(ErrorReport errorReport) {
    _errorReports.add(errorReport);
    
    // Keep only recent errors
    if (_errorReports.length > _maxErrorReports) {
      _errorReports.removeAt(0);
    }
    
    // Track error frequency
    final errorKey = _getErrorKey(errorReport.error);
    _errorCounts[errorKey] = (_errorCounts[errorKey] ?? 0) + 1;
    
    // Log error
    _logError(errorReport);
    
    // Send to crash reporting service in production
    if (kReleaseMode) {
      _sendToCrashReporting(errorReport);
    }
  }

  /// Generate error key for tracking
  String _getErrorKey(Object error) {
    final errorString = error.toString();
    if (errorString.length > 100) {
      return errorString.substring(0, 100);
    }
    return errorString;
  }

  /// Log error with appropriate level
  void _logError(ErrorReport errorReport) {
    final severity = _getErrorSeverity(errorReport);
    final prefix = _getSeverityPrefix(severity);
    
    debugPrint('$prefix ${errorReport.type.name.toUpperCase()} ERROR: ${errorReport.error}');
    
    if (errorReport.stackTrace != null && kDebugMode) {
      debugPrint('Stack trace: ${errorReport.stackTrace}');
    }
  }

  /// Get error severity level
  ErrorSeverity _getErrorSeverity(ErrorReport errorReport) {
    final errorString = errorReport.error.toString().toLowerCase();
    
    // Critical errors
    if (errorString.contains('out of memory') ||
        errorString.contains('stackoverflow') ||
        errorString.contains('segmentation fault')) {
      return ErrorSeverity.critical;
    }
    
    // High severity errors
    if (errorString.contains('null pointer') ||
        errorString.contains('index out of range') ||
        errorString.contains('assertion failed')) {
      return ErrorSeverity.high;
    }
    
    // Network errors are usually medium severity
    if (errorString.contains('socket') ||
        errorString.contains('timeout') ||
        errorString.contains('connection')) {
      return ErrorSeverity.medium;
    }
    
    // Default to low severity
    return ErrorSeverity.low;
  }

  /// Get severity prefix for logging
  String _getSeverityPrefix(ErrorSeverity severity) {
    switch (severity) {
      case ErrorSeverity.critical:
        return '🔴';
      case ErrorSeverity.high:
        return '🟠';
      case ErrorSeverity.medium:
        return '🟡';
      case ErrorSeverity.low:
        return '🔵';
    }
  }

  /// Send error to crash reporting service
  void _sendToCrashReporting(ErrorReport errorReport) {
    // Implementation would depend on your crash reporting service
    // e.g., Firebase Crashlytics, Sentry, etc.
  }

  /// Handle API errors with user-friendly messages
  Future<T?> handleApiError<T>(
    Future<T> Function() apiCall, {
    String? userMessage,
    bool showUserError = true,
    BuildContext? context,
    VoidCallback? onRetry,
  }) async {
    try {
      return await apiCall();
    } catch (error, stackTrace) {
      final errorReport = ErrorReport(
        timestamp: DateTime.now(),
        type: ErrorType.api,
        error: error,
        stackTrace: stackTrace,
        userMessage: userMessage,
      );
      
      _recordError(errorReport);
      
      if (showUserError && context != null) {
        await _showUserFriendlyError(context, error, onRetry);
      }
      
      return null;
    }
  }

  /// Show user-friendly error dialog
  Future<void> _showUserFriendlyError(
    BuildContext context,
    Object error, [
    VoidCallback? onRetry,
  ]) async {
    final errorInfo = _analyzeError(error);
    
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(errorInfo.icon, color: errorInfo.color),
              const SizedBox(width: 8),
              Text(errorInfo.title),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(errorInfo.message),
              if (errorInfo.suggestion != null) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.lightbulb_outline, color: Colors.blue),
                      const SizedBox(width: 8),
                      Expanded(child: Text(errorInfo.suggestion!)),
                    ],
                  ),
                ),
              ],
            ],
          ),
          actions: [
            if (onRetry != null)
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onRetry();
                },
                child: const Text('Retry'),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Analyze error and provide user-friendly information
  ErrorInfo _analyzeError(Object error) {
    final errorString = error.toString().toLowerCase();
    
    // Network errors
    if (errorString.contains('socket') ||
        errorString.contains('timeout') ||
        errorString.contains('connection refused') ||
        errorString.contains('network error')) {
      return ErrorInfo(
        title: 'Connection Problem',
        message: 'Unable to connect to the server. Please check your internet connection.',
        suggestion: 'Try connecting to a different network or check if the server is accessible.',
        icon: Icons.wifi_off,
        color: Colors.orange,
      );
    }
    
    // Authentication errors
    if (errorString.contains('401') ||
        errorString.contains('unauthorized') ||
        errorString.contains('authentication')) {
      return ErrorInfo(
        title: 'Authentication Error',
        message: 'Your session has expired. Please log in again.',
        suggestion: 'You will be redirected to the login screen.',
        icon: Icons.lock_outline,
        color: Colors.red,
      );
    }
    
    // Server errors
    if (errorString.contains('500') ||
        errorString.contains('502') ||
        errorString.contains('503') ||
        errorString.contains('server error')) {
      return ErrorInfo(
        title: 'Server Error',
        message: 'The server is experiencing issues. Please try again later.',
        suggestion: 'This is usually temporary. Try again in a few minutes.',
        icon: Icons.error_outline,
        color: Colors.red,
      );
    }
    
    // Storage errors
    if (errorString.contains('storage') ||
        errorString.contains('disk') ||
        errorString.contains('space')) {
      return ErrorInfo(
        title: 'Storage Error',
        message: 'Unable to save data. Your device may be running low on storage.',
        suggestion: 'Free up some space on your device and try again.',
        icon: Icons.storage,
        color: Colors.orange,
      );
    }
    
    // Default error
    return ErrorInfo(
      title: 'Unexpected Error',
      message: 'Something went wrong. Please try again.',
      suggestion: 'If this problem persists, please contact support.',
      icon: Icons.error_outline,
      color: Colors.red,
    );
  }

  /// Execute operation with automatic retry
  Future<T> executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxAttempts = _maxRetryAttempts,
    Duration delay = const Duration(seconds: 1),
    bool Function(Object error)? shouldRetry,
  }) async {
    Object? lastError;
    
    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error, stackTrace) {
        lastError = error;
        
        final errorReport = ErrorReport(
          timestamp: DateTime.now(),
          type: ErrorType.retry,
          error: error,
          stackTrace: stackTrace,
          context: 'Attempt $attempt of $maxAttempts',
        );
        
        _recordError(errorReport);
        
        // Check if we should retry
        if (attempt == maxAttempts || (shouldRetry != null && !shouldRetry(error))) {
          break;
        }
        
        // Wait before retrying
        await Future.delayed(delay * attempt);
        
        // Check connectivity for network errors
        if (_isNetworkError(error)) {
          await _connectivityService.waitForConnectivity(
            timeout: const Duration(seconds: 30),
          );
        }
      }
    }
    
    throw lastError!;
  }

  /// Check if error is network-related
  bool _isNetworkError(Object error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('socket') ||
           errorString.contains('timeout') ||
           errorString.contains('connection') ||
           errorString.contains('network');
  }

  /// Get error statistics
  Map<String, dynamic> getErrorStats() {
    final now = DateTime.now();
    final recentErrors = _errorReports.where(
      (report) => now.difference(report.timestamp) < const Duration(hours: 24),
    ).toList();
    
    return {
      'totalErrors': _errorReports.length,
      'recentErrors': recentErrors.length,
      'errorTypes': _getErrorTypeStats(),
      'topErrors': _getTopErrors(),
      'errorRate': recentErrors.length / 24.0, // Errors per hour
    };
  }

  /// Get error type statistics
  Map<String, int> _getErrorTypeStats() {
    final stats = <String, int>{};
    for (final report in _errorReports) {
      final type = report.type.name;
      stats[type] = (stats[type] ?? 0) + 1;
    }
    return stats;
  }

  /// Get top errors by frequency
  List<Map<String, dynamic>> _getTopErrors() {
    final sortedErrors = _errorCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedErrors.take(5).map((entry) => {
      'error': entry.key,
      'count': entry.value,
    }).toList();
  }

  /// Clear error history
  void clearErrorHistory() {
    _errorReports.clear();
    _errorCounts.clear();
    debugPrint('🧹 Error history cleared');
  }
}

/// Error report model
class ErrorReport {
  final DateTime timestamp;
  final ErrorType type;
  final Object error;
  final StackTrace? stackTrace;
  final String? context;
  final String? library;
  final String? userMessage;

  ErrorReport({
    required this.timestamp,
    required this.type,
    required this.error,
    this.stackTrace,
    this.context,
    this.library,
    this.userMessage,
  });
}

/// Error type enumeration
enum ErrorType {
  flutter,
  platform,
  api,
  network,
  storage,
  retry,
  user,
}

/// Error severity enumeration
enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}

/// Error information for user display
class ErrorInfo {
  final String title;
  final String message;
  final String? suggestion;
  final IconData icon;
  final Color color;

  ErrorInfo({
    required this.title,
    required this.message,
    this.suggestion,
    required this.icon,
    required this.color,
  });
}
