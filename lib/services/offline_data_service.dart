import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'network_resilience_service.dart';

/// Offline data management service with comprehensive caching and sync capabilities
class OfflineDataService {
  static final OfflineDataService _instance = OfflineDataService._internal();
  factory OfflineDataService() => _instance;
  OfflineDataService._internal();

  // Database and storage
  Database? _database;
  bool _isInitialized = false;
  final NetworkResilienceService _networkService = NetworkResilienceService();

  // Sync management
  Timer? _syncTimer;
  bool _isSyncing = false;
  final Set<String> _pendingSync = {};

  // Configuration
  static const String _dbName = 'kft_offline.db';
  static const int _dbVersion = 1;
  static const Duration _syncInterval = Duration(minutes: 5);
  static const int _maxCacheAge = 7; // days

  /// Initialize offline data service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      print('💾 Initializing OfflineDataService...');

      // Initialize database
      await _initializeDatabase();

      // Start periodic sync
      _startPeriodicSync();

      // Clean old cache data
      await _cleanOldCache();

      _isInitialized = true;
      print('✅ OfflineDataService initialized');
    } catch (e) {
      print('❌ OfflineDataService initialization failed: $e');
      _isInitialized = true; // Continue with degraded functionality
    }
  }

  /// Initialize SQLite database
  Future<void> _initializeDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _dbName);

      _database = await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _createDatabase,
        onUpgrade: _upgradeDatabase,
      );

      print('💾 Database initialized at: $path');
    } catch (e) {
      print('❌ Database initialization failed: $e');
      throw e;
    }
  }

  /// Create database tables
  Future<void> _createDatabase(Database db, int version) async {
    await db.execute('''
      CREATE TABLE cache_data (
        key TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        expiry INTEGER,
        sync_status INTEGER DEFAULT 0,
        data_type TEXT NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE sync_queue (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation TEXT NOT NULL,
        data TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3
      )
    ''');

    await db.execute('''
      CREATE TABLE user_data (
        key TEXT PRIMARY KEY,
        data TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        is_dirty INTEGER DEFAULT 0
      )
    ''');

    // --- OPTIMIZATION: Add indices for faster queries ---
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_timestamp ON cache_data(timestamp);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_sync_status ON cache_data(sync_status);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_data_type ON cache_data(data_type);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_sync_queue_timestamp ON sync_queue(timestamp);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_user_data_is_dirty ON user_data(is_dirty);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_user_data_timestamp ON user_data(timestamp);');

    print('💾 Database tables and indices created');
  }

  /// Upgrade database schema
  Future<void> _upgradeDatabase(Database db, int oldVersion, int newVersion) async {
    // Handle database schema upgrades
    print('💾 Database upgraded from v$oldVersion to v$newVersion');

    // --- OPTIMIZATION: Ensure indices exist for all users (existing installs) ---
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_timestamp ON cache_data(timestamp);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_sync_status ON cache_data(sync_status);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_cache_data_data_type ON cache_data(data_type);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_sync_queue_timestamp ON sync_queue(timestamp);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_user_data_is_dirty ON user_data(is_dirty);');
    await db.execute('CREATE INDEX IF NOT EXISTS idx_user_data_timestamp ON user_data(timestamp);');
  }

  /// Cache data with optional expiry
  Future<void> cacheData(
    String key,
    Map<String, dynamic> data, {
    Duration? expiry,
    String dataType = 'general',
    bool requiresSync = false,
  }) async {
    if (_database == null) return;

    try {
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final expiryTimestamp = expiry != null 
          ? timestamp + expiry.inMilliseconds 
          : null;

      await _database!.insert(
        'cache_data',
        {
          'key': key,
          'data': json.encode(data),
          'timestamp': timestamp,
          'expiry': expiryTimestamp,
          'sync_status': requiresSync ? 1 : 0,
          'data_type': dataType,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      if (requiresSync) {
        _pendingSync.add(key);
      }

      print('💾 Cached data: $key (Type: $dataType)');
    } catch (e) {
      print('❌ Failed to cache data for $key: $e');
    }
  }

  /// Retrieve cached data
  Future<Map<String, dynamic>?> getCachedData(String key) async {
    if (_database == null) return null;

    try {
      final result = await _database!.query(
        'cache_data',
        where: 'key = ?',
        whereArgs: [key],
        limit: 1,
      );

      if (result.isEmpty) return null;

      final row = result.first;
      final expiry = row['expiry'] as int?;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Check if data has expired
      if (expiry != null && now > expiry) {
        await _database!.delete(
          'cache_data',
          where: 'key = ?',
          whereArgs: [key],
        );
        return null;
      }

      final data = json.decode(row['data'] as String) as Map<String, dynamic>;
      print('💾 Retrieved cached data: $key');
      return data;
    } catch (e) {
      print('❌ Failed to retrieve cached data for $key: $e');
      return null;
    }
  }

  /// Store user data with dirty flag for sync
  Future<void> storeUserData(String key, Map<String, dynamic> data) async {
    if (_database == null) return;

    try {
      await _database!.insert(
        'user_data',
        {
          'key': key,
          'data': json.encode(data),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          'is_dirty': 1, // Mark as needing sync
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      _pendingSync.add(key);
      print('💾 Stored user data: $key (marked for sync)');
    } catch (e) {
      print('❌ Failed to store user data for $key: $e');
    }
  }

  /// Retrieve user data
  Future<Map<String, dynamic>?> getUserData(String key) async {
    if (_database == null) return null;

    try {
      final result = await _database!.query(
        'user_data',
        where: 'key = ?',
        whereArgs: [key],
        limit: 1,
      );

      if (result.isEmpty) return null;

      final data = json.decode(result.first['data'] as String) as Map<String, dynamic>;
      print('💾 Retrieved user data: $key');
      return data;
    } catch (e) {
      print('❌ Failed to retrieve user data for $key: $e');
      return null;
    }
  }

  /// Queue operation for sync when online
  Future<void> queueForSync(String operation, Map<String, dynamic> data) async {
    if (_database == null) return;

    try {
      await _database!.insert('sync_queue', {
        'operation': operation,
        'data': json.encode(data),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'retry_count': 0,
        'max_retries': 3,
      });

      print('📤 Queued for sync: $operation');
    } catch (e) {
      print('❌ Failed to queue for sync: $e');
    }
  }

  /// Start periodic sync
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(_syncInterval, (_) async {
      if (_networkService.isOnline && !_isSyncing) {
        await _performSync();
      }
    });
  }

  /// Perform comprehensive data sync
  Future<void> _performSync() async {
    if (_isSyncing || !_networkService.isOnline) return;

    _isSyncing = true;
    print('🔄 Starting data sync...');

    try {
      // Sync queued operations
      await _syncQueuedOperations();

      // Sync dirty user data
      await _syncDirtyUserData();

      // Sync pending cache data
      await _syncPendingCacheData();

      print('✅ Data sync completed');
    } catch (e) {
      print('❌ Data sync failed: $e');
    } finally {
      _isSyncing = false;
    }
  }

  /// Sync queued operations
  Future<void> _syncQueuedOperations() async {
    if (_database == null) return;

    try {
      final operations = await _database!.query(
        'sync_queue',
        orderBy: 'timestamp ASC',
      );

      for (final operation in operations) {
        try {
          final success = await _executeSyncOperation(operation);
          
          if (success) {
            await _database!.delete(
              'sync_queue',
              where: 'id = ?',
              whereArgs: [operation['id']],
            );
            print('✅ Synced operation: ${operation['operation']}');
          } else {
            // Increment retry count
            final retryCount = (operation['retry_count'] as int) + 1;
            final maxRetries = operation['max_retries'] as int;

            if (retryCount >= maxRetries) {
              // Remove failed operation after max retries
              await _database!.delete(
                'sync_queue',
                where: 'id = ?',
                whereArgs: [operation['id']],
              );
              print('❌ Removed failed operation after $maxRetries retries: ${operation['operation']}');
            } else {
              // Update retry count
              await _database!.update(
                'sync_queue',
                {'retry_count': retryCount},
                where: 'id = ?',
                whereArgs: [operation['id']],
              );
            }
          }
        } catch (e) {
          print('❌ Failed to sync operation ${operation['operation']}: $e');
        }
      }
    } catch (e) {
      print('❌ Failed to sync queued operations: $e');
    }
  }

  /// Execute a sync operation
  Future<bool> _executeSyncOperation(Map<String, dynamic> operation) async {
    try {
      final operationType = operation['operation'] as String;
      final data = json.decode(operation['data'] as String) as Map<String, dynamic>;

      // This would integrate with your API service
      // For now, simulate successful sync
      await Future.delayed(const Duration(milliseconds: 100));
      
      return true;
    } catch (e) {
      print('❌ Sync operation execution failed: $e');
      return false;
    }
  }

  /// Sync dirty user data
  Future<void> _syncDirtyUserData() async {
    if (_database == null) return;

    try {
      final dirtyData = await _database!.query(
        'user_data',
        where: 'is_dirty = ?',
        whereArgs: [1],
      );

      for (final data in dirtyData) {
        try {
          // Sync with server
          final success = await _syncUserDataToServer(data);
          
          if (success) {
            // Mark as clean
            await _database!.update(
              'user_data',
              {'is_dirty': 0},
              where: 'key = ?',
              whereArgs: [data['key']],
            );
            print('✅ Synced user data: ${data['key']}');
          }
        } catch (e) {
          print('❌ Failed to sync user data ${data['key']}: $e');
        }
      }
    } catch (e) {
      print('❌ Failed to sync dirty user data: $e');
    }
  }

  /// Sync user data to server
  Future<bool> _syncUserDataToServer(Map<String, dynamic> data) async {
    try {
      // This would integrate with your API service
      // For now, simulate successful sync
      await Future.delayed(const Duration(milliseconds: 100));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Sync pending cache data
  Future<void> _syncPendingCacheData() async {
    for (final key in _pendingSync.toList()) {
      try {
        final data = await getCachedData(key);
        if (data != null) {
          // Sync with server
          final success = await _syncCacheDataToServer(key, data);
          if (success) {
            _pendingSync.remove(key);
          }
        }
      } catch (e) {
        print('❌ Failed to sync cache data $key: $e');
      }
    }
  }

  /// Sync cache data to server
  Future<bool> _syncCacheDataToServer(String key, Map<String, dynamic> data) async {
    try {
      // This would integrate with your API service
      // For now, simulate successful sync
      await Future.delayed(const Duration(milliseconds: 100));
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Clean old cache data
  Future<void> _cleanOldCache() async {
    if (_database == null) return;

    try {
      final cutoffTime = DateTime.now()
          .subtract(Duration(days: _maxCacheAge))
          .millisecondsSinceEpoch;

      final deletedCount = await _database!.delete(
        'cache_data',
        where: 'timestamp < ? AND sync_status = 0',
        whereArgs: [cutoffTime],
      );

      if (deletedCount > 0) {
        print('🧹 Cleaned $deletedCount old cache entries');
      }
    } catch (e) {
      print('❌ Failed to clean old cache: $e');
    }
  }

  /// Get cache statistics
  Future<CacheStatistics> getCacheStatistics() async {
    if (_database == null) {
      return CacheStatistics(
        totalEntries: 0,
        cacheSize: 0,
        pendingSyncCount: 0,
        queuedOperations: 0,
      );
    }

    try {
      final cacheCount = Sqflite.firstIntValue(
        await _database!.rawQuery('SELECT COUNT(*) FROM cache_data')
      ) ?? 0;

      final queueCount = Sqflite.firstIntValue(
        await _database!.rawQuery('SELECT COUNT(*) FROM sync_queue')
      ) ?? 0;

      // Estimate cache size (rough calculation)
      final sizeResult = await _database!.rawQuery(
        'SELECT SUM(LENGTH(data)) FROM cache_data'
      );
      final cacheSize = Sqflite.firstIntValue(sizeResult) ?? 0;

      return CacheStatistics(
        totalEntries: cacheCount,
        cacheSize: cacheSize,
        pendingSyncCount: _pendingSync.length,
        queuedOperations: queueCount,
      );
    } catch (e) {
      print('❌ Failed to get cache statistics: $e');
      return CacheStatistics(
        totalEntries: 0,
        cacheSize: 0,
        pendingSyncCount: 0,
        queuedOperations: 0,
      );
    }
  }

  /// Force sync now
  Future<void> forceSyncNow() async {
    if (_networkService.isOnline) {
      await _performSync();
    } else {
      print('⚠️ Cannot sync: device is offline');
    }
  }

  /// Clear all cache data
  Future<void> clearAllCache() async {
    if (_database == null) return;

    try {
      await _database!.delete('cache_data');
      await _database!.delete('sync_queue');
      _pendingSync.clear();
      print('🧹 All cache data cleared');
    } catch (e) {
      print('❌ Failed to clear cache: $e');
    }
  }

  /// Check if data exists in cache
  Future<bool> hasCachedData(String key) async {
    final data = await getCachedData(key);
    return data != null;
  }

  /// Get sync status
  bool get isSyncing => _isSyncing;

  /// Get pending sync count
  int get pendingSyncCount => _pendingSync.length;

  /// Dispose the service
  void dispose() {
    _syncTimer?.cancel();
    _database?.close();
    _isInitialized = false;
  }
}

/// Cache statistics model
class CacheStatistics {
  final int totalEntries;
  final int cacheSize;
  final int pendingSyncCount;
  final int queuedOperations;

  CacheStatistics({
    required this.totalEntries,
    required this.cacheSize,
    required this.pendingSyncCount,
    required this.queuedOperations,
  });
}
