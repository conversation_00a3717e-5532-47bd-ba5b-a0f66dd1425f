import 'dart:async';
import 'package:flutter/material.dart';
import 'single_session_auth_service.dart';
import 'persistent_auth_service.dart';
import 'auth_service.dart';
import '../widgets/session_logout_dialog.dart';

/// Manages single-session authentication and coordinates with existing auth services
class SessionManager {
  static final SessionManager _instance = SessionManager._internal();
  factory SessionManager() => _instance;
  SessionManager._internal();

  // Services
  final SingleSessionAuthService _singleSessionService = SingleSessionAuthService();
  late PersistentAuthService _persistentAuthService;
  late AuthService _authService;

  // State
  bool _isInitialized = false;
  StreamSubscription<SessionEvent>? _sessionEventSubscription;
  StreamSubscription<bool>? _sessionValiditySubscription;
  BuildContext? _currentContext;

  // Getters
  bool get isInitialized => _isInitialized;
  SingleSessionAuthService get singleSessionService => _singleSessionService;

  /// Initialize the session manager
  Future<void> initialize({
    required PersistentAuthService persistentAuthService,
    required AuthService authService,
  }) async {
    if (_isInitialized) return;

    try {
      debugPrint('🔐 Initializing Session Manager...');

      _persistentAuthService = persistentAuthService;
      _authService = authService;

      // Initialize single session service
      await _singleSessionService.initialize();

      // Listen to session events
      _sessionEventSubscription = _singleSessionService.sessionEventStream.listen(
        _handleSessionEvent,
        onError: (error) => debugPrint('❌ Session event error: $error'),
      );

      // Listen to session validity changes
      _sessionValiditySubscription = _singleSessionService.sessionValidityStream.listen(
        _handleSessionValidityChange,
        onError: (error) => debugPrint('❌ Session validity error: $error'),
      );

      _isInitialized = true;
      debugPrint('✅ Session Manager initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize Session Manager: $e');
      rethrow;
    }
  }

  /// Set current context for showing dialogs
  void setContext(BuildContext context) {
    _currentContext = context;
  }

  /// Handle successful login
  Future<void> onLoginSuccess({
    required String userId,
    required String token,
    Map<String, dynamic>? serverResponse,
  }) async {
    try {
      debugPrint('🔐 Session Manager handling login success...');
      
      await _singleSessionService.onLoginSuccess(
        userId: userId,
        token: token,
        serverResponse: serverResponse,
      );
      
      debugPrint('✅ Session Manager login success handled');
    } catch (e) {
      debugPrint('❌ Error handling login success: $e');
    }
  }

  /// Validate current session
  Future<SessionValidationResult> validateSession() async {
    try {
      final token = await _persistentAuthService.getToken();
      if (token == null) {
        debugPrint('❌ No token available for session validation');
        return SessionValidationResult.invalidated;
      }

      return await _singleSessionService.validateSession(token);
    } catch (e) {
      debugPrint('❌ Error validating session: $e');
      return SessionValidationResult.error;
    }
  }

  /// Force session validation
  Future<SessionValidationResult> forceValidation() async {
    try {
      final token = await _persistentAuthService.getToken();
      if (token == null) {
        debugPrint('❌ No token available for forced validation');
        return SessionValidationResult.invalidated;
      }

      return await _singleSessionService.forceValidation(token);
    } catch (e) {
      debugPrint('❌ Error in forced validation: $e');
      return SessionValidationResult.error;
    }
  }

  /// Handle session events
  void _handleSessionEvent(SessionEvent event) {
    debugPrint('🔔 Session event: ${event.type} - ${event.message}');

    switch (event.type) {
      case SessionEventType.deviceReplaced:
        _showDeviceReplacedNotification(event.message);
        break;
      case SessionEventType.sessionInvalidated:
        _handleSessionInvalidated(event.message);
        break;
      case SessionEventType.adminRevocation:
        _handleAdminRevocation(event.message, event.data);
        break;
      case SessionEventType.offlineLogoutPending:
        _showOfflineLogoutPendingNotification(event.message);
        break;
      case SessionEventType.sessionRestored:
        _showSessionRestoredNotification(event.message);
        break;
      case SessionEventType.validationFailed:
        _handleValidationFailed(event.message);
        break;
    }
  }

  /// Handle session validity changes
  void _handleSessionValidityChange(bool isValid) {
    debugPrint('🔔 Session validity changed: $isValid');

    if (!isValid) {
      // Only trigger logout dialog if we have a specific admin revocation reason
      // For general session invalidity, we'll let the specific event handlers deal with it
      debugPrint('⚠️ Session validity changed to false, but letting specific handlers deal with it');
    }
  }

  /// Show device replaced notification
  void _showDeviceReplacedNotification(String message) {
    if (_currentContext != null && Navigator.of(_currentContext!).mounted) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.info_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Handle session invalidated
  void _handleSessionInvalidated(String reason) {
    debugPrint('🚨 Session invalidated: $reason');
    
    // Only show logout dialog for admin revocation
    if (reason.toLowerCase().contains('device access revoked by administrator')) {
      _showSessionInvalidatedDialog(reason);
    } else {
      // For all other session issues, show notification but don't logout
      debugPrint('⚠️ Non-admin session issue, handling gracefully: $reason');
      _showSessionErrorNotification('Session issue: $reason. Please try again.');
    }
  }

  /// Handle admin revocation with special UX
  void _handleAdminRevocation(String message, Map<String, dynamic>? revocationInfo) {
    debugPrint('🚨 Admin revocation detected: $message');

    // Show a more graceful notification first
    _showAdminRevocationNotification(message);

    // Delay the logout dialog to allow user to finish current activity
    Future.delayed(const Duration(seconds: 3), () {
      _showAdminRevocationDialog(message, revocationInfo);
    });
  }

  /// Show admin revocation notification
  void _showAdminRevocationNotification(String message) {
    if (_currentContext != null && Navigator.of(_currentContext!).mounted) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.admin_panel_settings, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(
                      'Session Terminated by Administrator',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const Text(
                      'You will be logged out shortly. Please save your work.',
                      style: TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 8),
          behavior: SnackBarBehavior.floating,
          action: SnackBarAction(
            label: 'OK',
            textColor: Colors.white,
            onPressed: () {
              ScaffoldMessenger.of(_currentContext!).hideCurrentSnackBar();
            },
          ),
        ),
      );
    }
  }

  /// Show admin revocation dialog
  void _showAdminRevocationDialog(String message, Map<String, dynamic>? revocationInfo) {
    if (_currentContext == null || !Navigator.of(_currentContext!).mounted) {
      // If no valid context, perform logout immediately
      _performLogout();
      return;
    }

    AnimatedSessionLogoutDialog.show(
      _currentContext!,
      reason: message,
      onConfirm: () {
        _performLogout();
      },
    );
  }

  /// Handle validation failed
  void _handleValidationFailed(String reason) {
    debugPrint('🚨 Session validation failed: $reason');
    // Instead of logging out directly, show the invalidated dialog
    _showSessionInvalidatedDialog(reason);
  }

  /// Show a dialog for session invalidated state with retry/logout options
  Future<void> _showSessionInvalidatedDialog(String reason) async {
    if (_currentContext == null || !Navigator.of(_currentContext!).mounted) {
      debugPrint('🔵 PLATFORM ERROR: Cannot show session invalidated dialog, context is invalid.');
      _performLogout(); // Fallback to immediate logout if context is invalid
      return;
    }

    // Ensure only one dialog is shown at a time
    if (_isDialogShowing) {
      debugPrint('⚠️ Session invalidated dialog already showing, skipping.');
      return;
    }

    _isDialogShowing = true;

    try {
      final result = await showDialog<SessionDialogAction>(
        context: Navigator.of(_currentContext!).overlay!.context, // Use overlay context for stability
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return PopScope(
            canPop: false, // Prevent dialog from being dismissed by back button
            child: AlertDialog(
              title: const Text('Session Invalidated'),
              content: Text('Your session has expired or is no longer valid. Reason: $reason'),
              actions: <Widget>[
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop(SessionDialogAction.retry);
                  },
                  child: const Text('Retry'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(dialogContext).pop(SessionDialogAction.logout);
                  },
                  child: const Text('Logout'),
                ),
              ],
            ),
          );
        },
      );

      _isDialogShowing = false;

      if (result == SessionDialogAction.retry) {
        debugPrint('🔄 User chose to retry session validation...');
        final validationResult = await forceValidation();
        if (validationResult != SessionValidationResult.valid) {
          debugPrint('❌ Retry validation failed. Performing logout.');
          _performLogout();
        } else {
          debugPrint('✅ Session re-validated successfully!');
          // Optionally show a success snackbar or navigate away from login
        }
      } else {
        // User chose to logout or dialog was dismissed (though we set barrierDismissible to false)
        debugPrint('🚪 User chose to logout or dialog dismissed. Performing logout.');
        _performLogout();
      }
    } catch (e) {
      debugPrint('❌ Error showing session invalidated dialog: $e');
      _performLogout(); // Fallback to immediate logout on error
    } finally {
      _isDialogShowing = false;
    }
  }

  // To prevent multiple dialogs from showing
  bool _isDialogShowing = false;

  /// Show offline logout pending notification
  void _showOfflineLogoutPendingNotification(String message) {
    if (_currentContext != null && Navigator.of(_currentContext!).mounted) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.cloud_off, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 5),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Show session restored notification
  void _showSessionRestoredNotification(String message) {
    if (_currentContext != null && Navigator.of(_currentContext!).mounted) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.cloud_done, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Perform logout (clears tokens, navigates to login)
  Future<void> _performLogout({String? reason}) async {
    debugPrint('🚪 Session Manager performing logout...');
    
    // Only logout for admin revocation - all other cases should be handled gracefully
    if (reason != null && reason.toLowerCase().contains('device access revoked by administrator')) {
      debugPrint('🚨 Admin revocation detected - performing logout');
      // await _authService.logout();
      debugPrint('✅ Admin revocation logout completed');
    } else {
      // For all other errors, show a notification but do not log out
      final errorMessage = reason ?? 'Temporary session issue';
      _showSessionErrorNotification('Session issue: $errorMessage. Please try again.');
      debugPrint('⚠️ Logout skipped for non-admin revocation: $errorMessage');
    }
  }

  void _showSessionErrorNotification(String message) {
    if (_currentContext != null && Navigator.of(_currentContext!).mounted) {
      ScaffoldMessenger.of(_currentContext!).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(child: Text(message)),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  /// Handle external logout (e.g., from another device or admin action)
  Future<void> handleExternalLogout() async {
    debugPrint('🚪 Handling external logout...');
    await _singleSessionService.clearSessionData();
    // Notify about logout reason
    _singleSessionService.emitSessionEvent(SessionEvent(
      type: SessionEventType.sessionInvalidated,
      message: 'You have been logged out from another device or by an administrator.',
      timestamp: DateTime.now(),
    ));
    debugPrint('✅ External logout handled');
  }

  /// Dispose resources
  void dispose() {
    _sessionEventSubscription?.cancel();
    _sessionValiditySubscription?.cancel();
    debugPrint('🗑️ Session Manager disposed');
  }
}

enum SessionDialogAction { retry, logout }
