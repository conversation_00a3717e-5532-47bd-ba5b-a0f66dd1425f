import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/motivational_quote.dart';
import 'api_service.dart';

class QuoteService {
  final ApiService _apiService = ApiService();

  // Cache keys
  static const String _quoteKey = 'current_quote';
  static const String _quotesKey = 'cached_quotes';
  static const String _preferencesKey = 'quote_preferences';
  static const String _lastFetchTimeKey = 'quotes_last_fetch_time';
  static const String _viewedQuotesKey = 'viewed_quotes';

  // Cache expiration time (1 hour)
  static const Duration _cacheExpiration = Duration(hours: 1);

  // Cached data
  MotivationalQuote? _cachedQuote;
  List<MotivationalQuote>? _cachedQuotes;
  QuotePreferences? _cachedPreferences;

  // Default quotes in case API fails
  static final List<MotivationalQuote> _defaultQuotes = [
    MotivationalQuote(
      id: 1,
      quote: "The only bad workout is the one that didn't happen.",
      author: "Unknown",
      category: "workout",
    ),
    MotivationalQuote(
      id: 2,
      quote: "Your body can stand almost anything. It's your mind that you have to convince.",
      author: "Unknown",
      category: "mental",
    ),
    MotivationalQuote(
      id: 3,
      quote: "Weight loss is not a physical challenge. It's a mental one.",
      author: "Unknown",
      category: "weight_loss",
    ),
    MotivationalQuote(
      id: 4,
      quote: "The hardest lift of all is lifting your butt off the couch.",
      author: "Unknown",
      category: "workout",
    ),
    MotivationalQuote(
      id: 5,
      quote: "Fitness is not about being better than someone else. It's about being better than you used to be.",
      author: "Unknown",
      category: "fitness",
    ),
  ];

  // Singleton instance
  static final QuoteService _instance = QuoteService._internal();

  factory QuoteService() {
    return _instance;
  }

  QuoteService._internal();

  // This method has been moved to line 520

  // Get a motivational quote
  Future<MotivationalQuote> getQuote({bool forceRefresh = false}) async {
    try {
      // If we have a cached quote and don't need to refresh, return it
      if (!forceRefresh && _cachedQuote != null) {
        return _cachedQuote!;
      }

      // Try to get from API
      final response = await _apiService.getQuote();
      final quote = response['quote'] as MotivationalQuote;

      // Cache the quote
      _cachedQuote = quote;
      _saveQuoteToLocal(quote);

      return quote;
    } catch (e) {
      print('Error getting quote from API: $e');

      // Try to get from local storage
      final localQuote = await _getQuoteFromLocal();
      if (localQuote != null) {
        return localQuote;
      }

      // Return a default quote if all else fails
      return MotivationalQuote(
        quote: 'The only bad workout is the one that didn\'t happen.',
        author: 'Unknown',
        category: 'fitness',
      );
    }
  }

  // Fetch all quotes from the backend
  Future<List<MotivationalQuote>> fetchAllQuotes({bool forceRefresh = false}) async {
    try {
      // Check if we need to fetch new quotes
      final shouldFetch = forceRefresh || await _shouldFetchNewQuotes();
      if (!shouldFetch && _cachedQuotes != null && _cachedQuotes!.isNotEmpty) {
        // Return cached quotes
        return _cachedQuotes!;
      }

      // Get token for authentication
      final token = await _apiService.getToken();
      if (token == null) {
        throw Exception('Not authenticated');
      }

      // Make API request to get all quotes
      final response = await _apiService.getAllQuotes();

      if (response.containsKey('quotes') && response['quotes'] != null) {
        // Parse quotes
        final List<dynamic> quotesJson = response['quotes'] as List<dynamic>;
        final quotes = quotesJson
            .map((json) => MotivationalQuote.fromJson(json))
            .toList();

        // Cache the quotes
        _cachedQuotes = quotes;
        _saveQuotesToLocal(quotes);
        _updateLastFetchTime();

        return quotes;
      } else {
        // Try to load from local storage if API fails
        final localQuotes = await _getQuotesFromLocal();
        if (localQuotes.isNotEmpty) {
          _cachedQuotes = localQuotes;
          return localQuotes;
        }

        // Return default quotes if all else fails
        _cachedQuotes = _defaultQuotes;
        return _defaultQuotes;
      }
    } catch (e) {
      print('Error fetching all quotes: $e');

      // Try to load from local storage if API fails
      final localQuotes = await _getQuotesFromLocal();
      if (localQuotes.isNotEmpty) {
        _cachedQuotes = localQuotes;
        return localQuotes;
      }

      // Return default quotes if all else fails
      _cachedQuotes = _defaultQuotes;
      return _defaultQuotes;
    }
  }

  // Get a random quote that hasn't been viewed yet
  Future<MotivationalQuote> getRandomNonRepeatingQuote() async {
    try {
      // Get all quotes
      final allQuotes = await fetchAllQuotes();
      if (allQuotes.isEmpty) {
        throw Exception('No quotes available');
      }

      // Get user preferences
      final preferences = await getPreferences();

      // Get viewed quote IDs
      final viewedQuoteIds = await _getViewedQuoteIds();

      // Filter quotes by category if preferences exist
      List<MotivationalQuote> filteredQuotes = allQuotes;
      if (preferences.preferredCategories != null && preferences.preferredCategories!.isNotEmpty) {
        final categories = preferences.categoriesList;
        if (categories.isNotEmpty) {
          filteredQuotes = allQuotes.where((quote) {
            return quote.category != null &&
                   categories.any((cat) => quote.category!.toLowerCase().contains(cat.toLowerCase()));
          }).toList();
        }
      }

      // If no quotes match the categories, use all quotes
      if (filteredQuotes.isEmpty) {
        filteredQuotes = allQuotes;
      }

      // Filter out quotes that have been viewed (if there are any unviewed quotes)
      final unviewedQuotes = filteredQuotes.where((quote) {
        return quote.id != null && !viewedQuoteIds.contains(quote.id);
      }).toList();

      // If all quotes have been viewed, reset the viewed list and use all quotes
      if (unviewedQuotes.isEmpty) {
        // Reset viewed quotes
        await _resetViewedQuoteIds();

        // Use all filtered quotes
        unviewedQuotes.addAll(filteredQuotes);
      }

      // Get a random quote
      final random = Random();
      final randomIndex = random.nextInt(unviewedQuotes.length);
      final selectedQuote = unviewedQuotes[randomIndex];

      // Mark the quote as viewed
      if (selectedQuote.id != null) {
        await _markQuoteAsViewed(selectedQuote.id!);

        // Update the quote's view count
        final updatedQuote = selectedQuote.markAsViewed();
        _cachedQuote = updatedQuote;
        _saveQuoteToLocal(updatedQuote);

        return updatedQuote;
      }

      // Cache the selected quote
      _cachedQuote = selectedQuote;
      _saveQuoteToLocal(selectedQuote);

      return selectedQuote;
    } catch (e) {
      print('Error getting random non-repeating quote: $e');

      // Try to get a cached quote
      if (_cachedQuote != null) {
        return _cachedQuote!;
      }

      // Try to get from local storage
      final localQuote = await _getQuoteFromLocal();
      if (localQuote != null) {
        return localQuote;
      }

      // Return a default quote if all else fails
      return _defaultQuotes[0];
    }
  }

  // Generate an AI quote
  Future<MotivationalQuote> generateAiQuote({String? theme, String? category}) async {
    try {
      final response = await _apiService.generateAiQuote(theme: theme, category: category);
      final quote = response['quote'] as MotivationalQuote;

      // Cache the quote
      _cachedQuote = quote;
      _saveQuoteToLocal(quote);

      return quote;
    } catch (e) {
      print('Error generating AI quote: $e');
      rethrow;
    }
  }

  // Get user quote preferences
  Future<QuotePreferences> getPreferences() async {
    try {
      // Try to get from API
      final response = await _apiService.getQuotePreferences();

      if (response['success'] == true && response['preferences'] != null) {
        // Convert the Map to QuotePreferences
        final Map<String, dynamic> preferencesMap = response['preferences'] as Map<String, dynamic>;
        final preferences = QuotePreferences.fromJson(preferencesMap);

        // Cache the preferences
        _cachedPreferences = preferences;
        _savePreferencesToLocal(preferences);

        return preferences;
      } else {
        throw Exception('Invalid preferences response');
      }
    } catch (e) {
      print('Error getting quote preferences from API: $e');

      // Try to get from cache
      if (_cachedPreferences != null) {
        return _cachedPreferences!;
      }

      // Try to get from local storage
      final localPreferences = await _getPreferencesFromLocal();
      if (localPreferences != null) {
        return localPreferences;
      }

      // Return default preferences if all else fails
      return QuotePreferences(
        userId: 0,
        preferredCategories: 'fitness,motivation,health,mindfulness',
        personalizationEnabled: true,
      );
    }
  }

  // Update user quote preferences
  Future<QuotePreferences> updatePreferences({
    String? preferredCategories,
    bool? personalizationEnabled,
    bool? deepSeekEnabled,
  }) async {
    try {
      // Update preferences via API
      final response = await _apiService.updateQuotePreferences(
        preferredCategories: preferredCategories != null ? [preferredCategories] : null,
        personalizationEnabled: personalizationEnabled,
        deepSeekEnabled: deepSeekEnabled,
      );

      if (response['success'] == true && response['preferences'] != null) {
        // Convert the Map to QuotePreferences
        final Map<String, dynamic> preferencesMap = response['preferences'] as Map<String, dynamic>;
        final preferences = QuotePreferences.fromJson(preferencesMap);

        // Cache the preferences
        _cachedPreferences = preferences;
        _savePreferencesToLocal(preferences);

        return preferences;
      } else {
        // If API call fails, return current preferences
        final current = await getPreferences();
        return current;
      }
    } catch (e) {
      print('Error updating quote preferences: $e');
      // Return current preferences on error
      try {
        return await getPreferences();
      } catch (_) {
        // Return default preferences if all else fails
        return QuotePreferences(
          userId: 0,
          preferredCategories: 'fitness,motivation,health,mindfulness',
          personalizationEnabled: true,
        );
      }
    }
  }

  // Save quote to local storage
  Future<void> _saveQuoteToLocal(MotivationalQuote quote) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_quoteKey, jsonEncode(quote.toJson()));
    } catch (e) {
      print('Error saving quote to local storage: $e');
    }
  }

  // Get quote from local storage
  Future<MotivationalQuote?> _getQuoteFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final quoteJson = prefs.getString(_quoteKey);

      if (quoteJson == null) {
        return null;
      }

      return MotivationalQuote.fromJson(jsonDecode(quoteJson));
    } catch (e) {
      print('Error getting quote from local storage: $e');
      return null;
    }
  }

  // Save quotes to local storage
  Future<void> _saveQuotesToLocal(List<MotivationalQuote> quotes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = quotes.map((quote) => jsonEncode(quote.toJson())).toList();
      await prefs.setStringList(_quotesKey, jsonList);
    } catch (e) {
      print('Error saving quotes to local storage: $e');
    }
  }

  // Get quotes from local storage
  Future<List<MotivationalQuote>> _getQuotesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = prefs.getStringList(_quotesKey);

      if (jsonList != null && jsonList.isNotEmpty) {
        return jsonList
            .map((jsonStr) => MotivationalQuote.fromJson(jsonDecode(jsonStr)))
            .toList();
      }

      return [];
    } catch (e) {
      print('Error loading quotes from local storage: $e');
      return [];
    }
  }

  // Update last fetch time
  Future<void> _updateLastFetchTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_lastFetchTimeKey, DateTime.now().toIso8601String());
    } catch (e) {
      print('Error updating last fetch time: $e');
    }
  }

  // Check if we should fetch new quotes
  Future<bool> _shouldFetchNewQuotes() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastFetchTimeStr = prefs.getString(_lastFetchTimeKey);

      if (lastFetchTimeStr == null) {
        return true;
      }

      final lastFetchTime = DateTime.parse(lastFetchTimeStr);
      final now = DateTime.now();

      return now.difference(lastFetchTime) > _cacheExpiration;
    } catch (e) {
      print('Error checking if should fetch new quotes: $e');
      return true;
    }
  }

  // Get viewed quote IDs
  Future<List<int>> _getViewedQuoteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final viewedIdsStr = prefs.getString(_viewedQuotesKey);

      if (viewedIdsStr == null || viewedIdsStr.isEmpty) {
        return [];
      }

      return viewedIdsStr
          .split(',')
          .where((id) => id.isNotEmpty)
          .map((id) => int.parse(id))
          .toList();
    } catch (e) {
      print('Error getting viewed quote IDs: $e');
      return [];
    }
  }

  // Mark a quote as viewed
  Future<void> _markQuoteAsViewed(int quoteId) async {
    try {
      final viewedIds = await _getViewedQuoteIds();

      if (!viewedIds.contains(quoteId)) {
        viewedIds.add(quoteId);

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_viewedQuotesKey, viewedIds.join(','));
      }
    } catch (e) {
      print('Error marking quote as viewed: $e');
    }
  }

  // Reset viewed quote IDs
  Future<void> _resetViewedQuoteIds() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_viewedQuotesKey, '');
    } catch (e) {
      print('Error resetting viewed quote IDs: $e');
    }
  }

  // Save preferences to local storage
  Future<void> _savePreferencesToLocal(QuotePreferences preferences) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_preferencesKey, jsonEncode(preferences.toJson()));
    } catch (e) {
      print('Error saving preferences to local storage: $e');
    }
  }

  // Get preferences from local storage
  Future<QuotePreferences?> _getPreferencesFromLocal() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = prefs.getString(_preferencesKey);

      if (preferencesJson == null) {
        return null;
      }

      return QuotePreferences.fromJson(jsonDecode(preferencesJson));
    } catch (e) {
      print('Error getting preferences from local storage: $e');
      return null;
    }
  }

  // Get whether DeepSeek is enabled for the user
  Future<bool> getDeepSeekEnabledForUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      // Try to get from local storage first
      final local = prefs.getBool('deepseek_enabled');
      if (local != null) return local;
      // Try to get from preferences API
      final preferences = await getPreferences();
      return preferences.deepSeekEnabled;
    } catch (e) {
      print('Error getting DeepSeek enabled: $e');
      return false;
    }
  }

  // Check if quotes are globally enabled
  Future<bool> areQuotesGloballyEnabled() async {
    try {
      // Try to get from API
      final response = await _apiService.getAppSettings();

      if (response.containsKey('settings') &&
          response['settings'] != null &&
          response['settings'].containsKey('quotes_enabled')) {
        return response['settings']['quotes_enabled'] == true ||
               response['settings']['quotes_enabled'] == 1;
      }

      // Default to true if not specified
      return true;
    } catch (e) {
      print('Error checking if quotes are globally enabled: $e');
      // Default to true if API fails
      return true;
    }
  }

  // Set whether DeepSeek is enabled for the user
  Future<void> setDeepSeekEnabledForUser(bool enabled) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('deepseek_enabled', enabled);
      // Optionally, update backend user preferences
      final current = await getPreferences();
      final List<String> categories = current.categoriesList;
      await updatePreferences(
        preferredCategories: categories.isNotEmpty ? categories[0] : null,
        personalizationEnabled: current.personalizationEnabled,
        deepSeekEnabled: enabled,
      );
    } catch (e) {
      print('Error setting DeepSeek enabled: $e');
    }
  }

  // Get a quote only if DeepSeek is enabled
  Future<MotivationalQuote?> getDeepSeekQuoteIfEnabled({bool forceRefresh = false}) async {
    if (await getDeepSeekEnabledForUser()) {
      return await generateAiQuote();
    }
    return null;
  }

  // Toggle favorite status for a quote
  Future<MotivationalQuote> toggleFavorite(MotivationalQuote quote) async {
    try {
      // Toggle favorite status
      final updatedQuote = quote.copyWith(
        isFavorite: !quote.isFavorite,
      );

      // Get token for authentication
      final token = await _apiService.getToken();
      if (token == null) {
        throw Exception('Not authenticated');
      }

      // Make API request (if available)
      try {
        await _apiService.toggleFavoriteQuote(
          quoteId: quote.id,
          isFavorite: updatedQuote.isFavorite,
        );
      } catch (e) {
        print('API call failed, but continuing with local update: $e');
      }

      // Update quote in cache
      if (quote.id == _cachedQuote?.id) {
        _cachedQuote = updatedQuote;
        _saveQuoteToLocal(updatedQuote);
      }

      // Update quote in quotes list if it exists
      if (_cachedQuotes != null) {
        final index = _cachedQuotes!.indexWhere((q) => q.id == quote.id);
        if (index >= 0) {
          _cachedQuotes![index] = updatedQuote;
          _saveQuotesToLocal(_cachedQuotes!);
        }
      }

      return updatedQuote;
    } catch (e) {
      print('Error toggling favorite status: $e');

      // Toggle favorite status locally even if API fails
      final updatedQuote = quote.copyWith(
        isFavorite: !quote.isFavorite,
      );

      // Update quote in cache
      if (quote.id == _cachedQuote?.id) {
        _cachedQuote = updatedQuote;
        _saveQuoteToLocal(updatedQuote);
      }

      // Update quote in quotes list if it exists
      if (_cachedQuotes != null) {
        final index = _cachedQuotes!.indexWhere((q) => q.id == quote.id);
        if (index >= 0) {
          _cachedQuotes![index] = updatedQuote;
          _saveQuotesToLocal(_cachedQuotes!);
        }
      }

      return updatedQuote;
    }
  }
}
