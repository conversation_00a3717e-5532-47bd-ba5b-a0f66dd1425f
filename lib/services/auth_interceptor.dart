import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'auth_service.dart';
import '../utils/platform_storage_io.dart';

class AuthInterceptor {
  static final AuthInterceptor _instance = AuthInterceptor._internal();
  factory AuthInterceptor() => _instance;
  AuthInterceptor._internal();

  final AuthService _authService = AuthService();
  
  // Global context for navigation (set this in main.dart)
  static BuildContext? globalContext;

  // Maximum number of retries for failed requests
  static const int maxRetries = 2;

  // Intercepted HTTP client
  Future<http.Response> get(Uri url, {Map<String, String>? headers}) async {
    return _makeRequest((h) => http.get(url, headers: h), headers: headers);
  }

  Future<http.Response> post(Uri url, {Map<String, String>? headers, Object? body}) async {
    return _makeRequest((h) => http.post(url, headers: h, body: body), headers: headers);
  }

  Future<http.Response> put(Uri url, {Map<String, String>? headers, Object? body}) async {
    return _makeRequest((h) => http.put(url, headers: h, body: body), headers: headers);
  }

  Future<http.Response> delete(Uri url, {Map<String, String>? headers}) async {
    return _makeRequest((h) => http.delete(url, headers: h), headers: headers);
  }

  // Generic request handler with auth interception
  Future<http.Response> _makeRequest(
    Future<http.Response> Function(Map<String, String> headers) request,
    {Map<String, String>? headers}
  ) async {
    int retryCount = 0;
    
    while (retryCount <= maxRetries) {
      try {
        // Check if token is expired before making the request
        if (await _authService.isTokenExpired()) {
          debugPrint('Token expired, attempting refresh...');
          final refreshSuccess = await _authService.refreshToken();
          if (!refreshSuccess) {
            await _handleAuthError(http.Response('Token refresh failed', 401));
            throw Exception('Token refresh failed');
          }
        }

      final deviceId = await PlatformStorageImpl.getDeviceId();
      final requestHeaders = {
        ...?headers,
        if (deviceId != null) 'X-Device-ID': deviceId,
      };

        // Add auth header if needed
        if (needsAuth(requestHeaders['url'] as Uri)) {
          final authHeaders = await addAuthHeader(requestHeaders);
          final response = await request(authHeaders);
      
          // Handle authentication errors
      if (response.statusCode == 401) {
            if (retryCount < maxRetries) {
              debugPrint('Auth failed, attempting token refresh... (attempt ${retryCount + 1})');
              final refreshSuccess = await _authService.refreshToken();
              if (refreshSuccess) {
                retryCount++;
                continue;
              }
            }
        await _handleAuthError(response);
      }
      
      return response;
        } else {
          return await request(requestHeaders);
        }
    } catch (e) {
        if (retryCount >= maxRetries) {
          debugPrint('Max retries exceeded: $e');
      rethrow;
        }
        retryCount++;
        await Future.delayed(Duration(milliseconds: 500 * retryCount)); // Exponential backoff
      }
    }

    throw Exception('Max retries exceeded');
  }

  // Handle authentication errors
  Future<void> _handleAuthError(http.Response response) async {
    debugPrint('Authentication error detected: ${response.statusCode}');
    debugPrint('Response body: ${response.body}');

    try {
      // Try to parse the response to get more details
      final responseData = jsonDecode(response.body);
      final message = responseData['message'] ?? responseData['error'] ?? 'Authentication failed';
      
      debugPrint('Auth error message: $message');
    } catch (e) {
      debugPrint('Could not parse auth error response: $e');
    }

    // Clear authentication data
    // await _authService.logout();
    debugPrint('Logout called from AuthInterceptor, but logout is disabled in this app.');

    // Navigate to login if context is available
    if (globalContext != null && globalContext!.mounted) {
      _showAuthErrorDialog();
    }
  }

  // Show authentication error dialog
  void _showAuthErrorDialog() {
    if (globalContext == null || !globalContext!.mounted) return;

    showDialog(
      context: globalContext!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('Session Expired'),
          ],
        ),
        content: const Text(
          'Your session has expired. Please log in again to continue.',
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin();
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Navigate to login screen
  void _navigateToLogin() {
    if (globalContext == null || !globalContext!.mounted) return;

    Navigator.of(globalContext!).pushNamedAndRemoveUntil(
      '/login',
      (route) => false,
    );
  }

  // Add authentication header to existing headers
  Future<Map<String, String>> addAuthHeader(Map<String, String>? headers) async {
    final token = await _authService.getToken();
    final authHeaders = headers ?? <String, String>{};
    
    if (token != null) {
      authHeaders['Authorization'] = 'Bearer $token';
    }
    
    return authHeaders;
  }

  // Check if request needs authentication
  bool needsAuth(Uri url) {
    // List of endpoints that don't require authentication
    final noAuthEndpoints = [
      'login.php',
      'register.php',
      'forgot-password.php',
      'reset-password.php',
      'refresh_token.php',
    ];

    return !noAuthEndpoints.any((endpoint) => url.path.contains(endpoint));
  }
}
