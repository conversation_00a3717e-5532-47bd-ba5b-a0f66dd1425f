import 'package:flutter/foundation.dart';
import '../models/course_video.dart';
import '../utils/video_security_helper.dart';
import 'api_service.dart';

/// Service for managing hosted Vimeo player functionality
/// Provides stable video playback by hosting the player on our domain
class HostedPlayerService {
  static const String _hostedPlayerBaseUrl = 'https://mycloudforge.com/hosted_player/';
  static const String _fallbackPlayerUrl = 'https://mycloudforge.com/kft_vimeo_player.html';
  static const String _localTestUrl = 'file:///android_asset/flutter_assets/assets/hosted_vimeo_player.html';

  final ApiService _apiService = ApiService();

  /// Generate hosted player URL for a video
  Future<String> generateHostedPlayerUrl({
    required CourseVideo video,
    bool autoplay = false,
    String? authToken,
    String? secureHash,
  }) async {
    try {
      debugPrint('🎬 HostedPlayerService: Generating hosted player URL for video ${video.id}');

      // Extract Vimeo ID from video URL
      final vimeoId = VideoSecurityHelper.extractVimeoId(video.videoUrl);
      if (vimeoId == null) {
        throw Exception('Invalid Vimeo URL: ${video.videoUrl}');
      }

      // Get secure embed URL if needed
      String? embedHash;
      if (secureHash != null) {
        embedHash = secureHash;
      } else {
        try {
          // TODO: Implement secure embed API call when available
          debugPrint('🔐 HostedPlayerService: Secure embed API not implemented yet');
          /*
          final secureEmbedResponse = await _apiService.getSecureVimeoEmbed(
            vimeoId: vimeoId,
            videoId: video.id,
            domain: 'mycloudforge.com',
            autoplay: autoplay,
          );

          if (secureEmbedResponse['success'] == true) {
            final secureUrl = secureEmbedResponse['secure_embed_url'] as String?;
            if (secureUrl != null) {
              final uri = Uri.parse(secureUrl);
              embedHash = uri.queryParameters['h'];
            }
          }
          */
        } catch (e) {
          debugPrint('⚠️ HostedPlayerService: Could not get secure embed, using standard embed: $e');
        }
      }

      // Build hosted player URL with parameters
      final hostedUrl = await _buildHostedPlayerUrl(
        vimeoId: vimeoId,
        videoId: video.id,
        autoplay: autoplay,
        authToken: authToken,
        secureHash: embedHash,
      );

      debugPrint('✅ HostedPlayerService: Generated hosted player URL: $hostedUrl');
      return hostedUrl;

    } catch (e) {
      debugPrint('❌ HostedPlayerService: Error generating hosted player URL: $e');

      // Fallback to basic hosted player
      final vimeoId = VideoSecurityHelper.extractVimeoId(video.videoUrl);
      if (vimeoId != null) {
        return await _buildHostedPlayerUrl(
          vimeoId: vimeoId,
          videoId: video.id,
          autoplay: autoplay,
        );
      }

      throw Exception('Failed to generate hosted player URL: $e');
    }
  }

  /// Build hosted player URL with query parameters
  Future<String> _buildHostedPlayerUrl({
    required String vimeoId,
    required int videoId,
    bool autoplay = false,
    String? authToken,
    String? secureHash,
  }) async {
    final params = <String, String>{
      'vimeo_id': vimeoId,
      'video_id': videoId.toString(),
      'domain': 'mycloudforge.com',
      'autoplay': autoplay ? '1' : '0',
    };

    if (authToken != null && authToken.isNotEmpty) {
      params['auth_token'] = authToken;
    }

    if (secureHash != null && secureHash.isNotEmpty) {
      params['h'] = secureHash;
    }

    // Add timestamp for cache busting
    params['t'] = DateTime.now().millisecondsSinceEpoch.toString();

    // Use the enhanced hosted player with proper domain verification
    try {
      debugPrint('🌐 HostedPlayerService: Using domain-verified hosted player');

      // Build hosted player URL with domain verification
      final hostedPlayerParams = <String, String>{
        'vimeo_id': vimeoId,
        'video_id': videoId.toString(),
        'domain': 'mycloudforge.com',
        'autoplay': autoplay ? '1' : '0',
      };

      // Add authentication parameters
      if (authToken != null && authToken.isNotEmpty) {
        hostedPlayerParams['auth_token'] = authToken;
      }

      if (secureHash != null && secureHash.isNotEmpty) {
        hostedPlayerParams['h'] = secureHash;
      }

      // Add timestamp for cache busting
      hostedPlayerParams['t'] = DateTime.now().millisecondsSinceEpoch.toString();

      // Use the hosted player URL
      final hostedPlayerUri = Uri.parse(_fallbackPlayerUrl).replace(queryParameters: hostedPlayerParams);

      debugPrint('🔗 HostedPlayerService: Using domain-verified hosted player URL: ${hostedPlayerUri.toString()}');
      return hostedPlayerUri.toString();

    } catch (e) {
      debugPrint('⚠️ HostedPlayerService: Error creating hosted player URL: $e');

      // Fallback to enhanced direct embed
      final enhancedEmbedUrl = 'https://player.vimeo.com/video/$vimeoId';
      final enhancedParams = <String, String>{
        'autoplay': autoplay ? '1' : '0',
        'muted': autoplay ? '1' : '0',
        'title': '0',
        'byline': '0',
        'portrait': '0',
        'responsive': '1',
        'dnt': '1',
        'controls': '1',
        'sharing': '0',
        'download': '0',
        'fullscreen': '1',
        'autopause': '0',
        'background': '0',
        'playsinline': '1',
        'keyboard': '1',
        'pip': '0',
        'quality': 'auto',
        'transparent': '0',
        'color': 'ffffff',
        'referrer': 'https://mycloudforge.com',
        'origin': 'https://mycloudforge.com',
        'app_id': '122963',
      };

      if (authToken != null && authToken.isNotEmpty) {
        enhancedParams['auth_token'] = authToken;
      }

      if (secureHash != null && secureHash.isNotEmpty) {
        enhancedParams['h'] = secureHash;
      }

      enhancedParams['t'] = DateTime.now().millisecondsSinceEpoch.toString();

      final fallbackUri = Uri.parse(enhancedEmbedUrl).replace(queryParameters: enhancedParams);
      debugPrint('🔄 HostedPlayerService: Using enhanced direct embed fallback: ${fallbackUri.toString()}');
      return fallbackUri.toString();
    }
  }

  /// Validate hosted player domain
  bool isValidHostedDomain(String url) {
    try {
      final uri = Uri.parse(url);
      final allowedDomains = [
        'mycloudforge.com',
        'localhost',
        '127.0.0.1',
      ];

      return allowedDomains.any((domain) =>
        uri.host.contains(domain) || uri.host == domain
      );
    } catch (e) {
      debugPrint('❌ HostedPlayerService: Invalid URL for domain validation: $url');
      return false;
    }
  }

  /// Get hosted player configuration for debugging
  Map<String, dynamic> getHostedPlayerConfig({
    required CourseVideo video,
    bool autoplay = false,
  }) {
    final vimeoId = VideoSecurityHelper.extractVimeoId(video.videoUrl);

    return {
      'video_id': video.id,
      'vimeo_id': vimeoId,
      'video_title': video.title,
      'video_url': video.videoUrl,
      'autoplay': autoplay,
      'hosted_player_base_url': _hostedPlayerBaseUrl,
      'fallback_player_url': _fallbackPlayerUrl,
      'domain': 'mycloudforge.com',
      'is_unlocked': video.isUnlocked,
      'week_number': video.weekNumber,
      'sequence_number': video.sequenceNumber,
    };
  }

  /// Handle authentication for hosted player
  Future<Map<String, dynamic>> handleHostedPlayerAuth({
    required String vimeoId,
    required int videoId,
    int retryCount = 0,
  }) async {
    try {
      debugPrint('🔐 HostedPlayerService: Handling authentication for video $videoId (retry: $retryCount)');

      // TODO: Implement Vimeo Pro authentication when API is available
      debugPrint('🔐 HostedPlayerService: Vimeo Pro auth API not implemented yet');
      /*
      final authResponse = await _apiService.getSecureVimeoEmbed(
        vimeoId: vimeoId,
        videoId: videoId,
        domain: 'mycloudforge.com',
        vimeoProAuth: true,
      );
      */
      final authResponse = {'success': false, 'error': 'API not implemented'};

      if (authResponse['success'] == true) {
        debugPrint('✅ HostedPlayerService: Authentication successful');
        return {
          'success': true,
          'secure_embed_url': authResponse['secure_embed_url'],
          'message': 'Authentication successful',
        };
      } else {
        debugPrint('❌ HostedPlayerService: Authentication failed');
        return {
          'success': false,
          'error': authResponse['error'] ?? 'Authentication failed',
          'retry_count': retryCount,
        };
      }
    } catch (e) {
      debugPrint('❌ HostedPlayerService: Authentication error: $e');
      return {
        'success': false,
        'error': e.toString(),
        'retry_count': retryCount,
      };
    }
  }

  /// Upload hosted player HTML to server (for deployment)
  Future<bool> uploadHostedPlayerToServer(String htmlContent) async {
    try {
      debugPrint('📤 HostedPlayerService: Uploading hosted player to server');

      // This would typically upload the HTML file to your server
      // For now, we'll just validate the content and return success
      if (htmlContent.contains('KFT Vimeo Player') &&
          htmlContent.contains('throttledSeek') &&
          htmlContent.contains('mycloudforge.com')) {
        debugPrint('✅ HostedPlayerService: Hosted player content validated');
        return true;
      } else {
        debugPrint('❌ HostedPlayerService: Invalid hosted player content');
        return false;
      }
    } catch (e) {
      debugPrint('❌ HostedPlayerService: Upload error: $e');
      return false;
    }
  }

  /// Test hosted player connectivity
  Future<bool> testHostedPlayerConnectivity() async {
    try {
      debugPrint('🔍 HostedPlayerService: Testing hosted player connectivity');

      // Test basic connectivity to hosted player URL
      final testUrl = await _buildHostedPlayerUrl(
        vimeoId: '76979871', // Test video
        videoId: 999999,
        autoplay: false,
      );

      debugPrint('🌐 HostedPlayerService: Test URL: $testUrl');

      // For now, just validate URL structure
      final uri = Uri.parse(testUrl);
      final isValid = uri.hasQuery &&
                     uri.queryParameters.containsKey('vimeo_id') &&
                     uri.queryParameters.containsKey('video_id');

      if (isValid) {
        debugPrint('✅ HostedPlayerService: Connectivity test passed');
        return true;
      } else {
        debugPrint('❌ HostedPlayerService: Connectivity test failed - invalid URL structure');
        return false;
      }
    } catch (e) {
      debugPrint('❌ HostedPlayerService: Connectivity test error: $e');
      return false;
    }
  }

  /// Get hosted player status for debugging
  Map<String, dynamic> getHostedPlayerStatus() {
    return {
      'service_name': 'HostedPlayerService',
      'hosted_player_base_url': _hostedPlayerBaseUrl,
      'fallback_player_url': _fallbackPlayerUrl,
      'supported_domains': ['mycloudforge.com', 'localhost', '127.0.0.1'],
      'features': [
        'seek_throttling',
        'privacy_error_handling',
        'domain_verification',
        'authentication_support',
        'visual_feedback',
        'error_recovery',
      ],
      'version': '1.0.0',
      'last_updated': DateTime.now().toIso8601String(),
    };
  }
}
