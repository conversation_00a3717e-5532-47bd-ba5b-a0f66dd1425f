import 'dart:async';
import 'dart:convert';
import 'dart:math'; // Import for min function
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:http/http.dart' as http;
import '../config/app_config.dart';
import '../utils/platform_storage.dart';

/// Single-session authentication service that ensures only one active session per user
class SingleSessionAuthService {
  static final SingleSessionAuthService _instance = SingleSessionAuthService._internal();
  factory SingleSessionAuthService() => _instance;
  SingleSessionAuthService._internal();

  // Storage
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Storage keys
  static const String _deviceIdKey = 'single_session_device_id';
  static const String _sessionIdKey = 'single_session_id';
  static const String _lastValidationKey = 'last_session_validation';
  // static const String _pendingLogoutKey = 'pending_logout_flag'; // Reserved for future use
  static const String _logoutReasonKey = 'logout_reason';
  static const String _offlineLogoutKey = 'offline_logout_pending';

  // Session management
  String? _currentDeviceId;
  String? _currentSessionId;
  Timer? _validationTimer;
  Timer? _offlineCheckTimer;
  bool _isValidating = false;
  bool _isInitialized = false;

  // Configuration
  static const Duration _validationInterval = Duration(minutes: 5);
  static const Duration _offlineCheckInterval = Duration(seconds: 30);
  // static const Duration _sessionTimeout = Duration(hours: 24); // Reserved for future use

  // Stream controllers
  final StreamController<SessionEvent> _sessionEventController = StreamController<SessionEvent>.broadcast();
  final StreamController<bool> _sessionValidityController = StreamController<bool>.broadcast();

  // Getters
  Stream<SessionEvent> get sessionEventStream => _sessionEventController.stream;
  Stream<bool> get sessionValidityStream => _sessionValidityController.stream;
  String? get currentDeviceId => _currentDeviceId;
  String? get currentSessionId => _currentSessionId;
  bool get isInitialized => _isInitialized;

  /// Initialize the single session service
  Future<void> initialize({bool force = false}) async {
    if (_isInitialized && !force) return;

    try {
      debugPrint('🔐 Initializing Single Session Auth Service...');

      // Load stored device ID or generate new one
      await _loadOrGenerateDeviceId();

      // Load current session ID
      await _loadSessionId();

      // Check for pending offline logout
      await _checkPendingOfflineLogout();

      // Start periodic validation
      _startPeriodicValidation();

      // Start offline monitoring
      _startOfflineMonitoring();

      _isInitialized = true;
      debugPrint('✅ Single Session Auth Service initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize Single Session Auth Service: $e');
      rethrow;
    }
  }

  /// Load or generate device ID
  Future<void> _loadOrGenerateDeviceId() async {
    try {
      // Try to load from secure storage first
      _currentDeviceId = await _secureStorage.read(key: _deviceIdKey);

      if (_currentDeviceId == null) {
        // Generate new device ID using platform-specific method
        _currentDeviceId = await PlatformStorage.getDeviceId();
        
        if (_currentDeviceId != null) {
          await _secureStorage.write(key: _deviceIdKey, value: _currentDeviceId!);
          debugPrint('🆔 Generated new device ID: ${_currentDeviceId!.substring(0, 8)}...');
        }
      } else {
        debugPrint('🆔 Loaded existing device ID: ${_currentDeviceId!.substring(0, 8)}...');
      }
    } catch (e) {
      debugPrint('❌ Error loading/generating device ID: $e');
      // Fallback to timestamp-based ID
      _currentDeviceId = 'device_${DateTime.now().millisecondsSinceEpoch}';
      await _secureStorage.write(key: _deviceIdKey, value: _currentDeviceId!);
    }
  }

  /// Load current session ID
  Future<void> _loadSessionId() async {
    try {
      _currentSessionId = await _secureStorage.read(key: _sessionIdKey);
      if (_currentSessionId != null) {
        debugPrint('🔑 Loaded existing session ID: ${_currentSessionId!.substring(0, 8)}...');
      }
    } catch (e) {
      debugPrint('❌ Error loading session ID: $e');
    }
  }

  /// Generate new session ID for login
  Future<String> _generateSessionId() async {
    final sessionId = 'session_${DateTime.now().millisecondsSinceEpoch}_${_currentDeviceId?.substring(0, 8) ?? 'unknown'}';
    _currentSessionId = sessionId;
    
    try {
      await _secureStorage.write(key: _sessionIdKey, value: sessionId);
      debugPrint('🔑 Generated new session ID: ${sessionId.substring(0, 16)}...');
    } catch (e) {
      debugPrint('❌ Error saving session ID: $e');
    }
    
    return sessionId;
  }

  /// Handle successful login - create new session
  Future<void> onLoginSuccess({
    required String userId,
    required String token,
    Map<String, dynamic>? serverResponse,
  }) async {
    try {
      debugPrint('🔐 Handling successful login for user: $userId');

      // Generate new session ID
      await _generateSessionId();

      // Check if server indicated forced logout occurred
      final forcedLogout = serverResponse?['forced_logout'] == true;

      if (forcedLogout) {
        debugPrint('⚠️ Server indicated forced logout occurred on another device');
        _sessionEventController.add(SessionEvent(
          type: SessionEventType.deviceReplaced,
          message: 'Previous session terminated - logged in from new device',
          timestamp: DateTime.now(),
        ));
      }

      // Update last validation timestamp
      await _updateLastValidation();

      // Clear any pending logout flags
      await _clearPendingLogout();

      // Notify session validity
      _sessionValidityController.add(true);

      debugPrint('✅ Login session established successfully');
    } catch (e) {
      debugPrint('❌ Error handling login success: $e');
    }
  }

  /// Validate current session with server
  Future<SessionValidationResult> validateSession(String token) async {
    if (_isValidating) return SessionValidationResult.valid;
    _isValidating = true;
    try {
      final response = await http.post(
        Uri.parse('${AppConfig.defaultApiBaseUrl}/session_validate.php'),
        body: {'token': token, 'device_id': await PlatformStorage.getDeviceId()},
        headers: {'Accept': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        try {
          final Map<String, dynamic> responseBody = jsonDecode(response.body);

          if (responseBody['success'] == true) {
            debugPrint('✅ Session validated successfully');
            
            // Check if this is a never-expiring token
            final tokenType = responseBody['token_type'];
            final expiresAt = responseBody['expires_at'];
            
            if (tokenType == 'never_expiring' || expiresAt == null) {
              debugPrint('🔓 Never-expiring token detected - no expiry validation needed');
            } else {
              debugPrint('⏰ Expiring token detected - expiry: $expiresAt');
            }
            
            await _updateLastValidation();
            _sessionValidityController.add(true);
            return SessionValidationResult.valid;
          } else {
            final String error = responseBody['error'] ?? 'Unknown validation error';
            debugPrint('❌ Session validation failed: $error');

            // Only logout for admin revocation (403 status with admin revocation message)
            if (response.statusCode == 403 && error.toLowerCase().contains('device access revoked by administrator')) {
              debugPrint('🚨 Admin revocation detected, invalidating session');
              await clearSessionData();
              emitSessionEvent(SessionEvent(
                type: SessionEventType.adminRevocation,
                message: error,
                timestamp: DateTime.now(),
              ));
              _sessionValidityController.add(false);
              return SessionValidationResult.invalidated;
            } else {
              // For all other errors, keep session valid and show notification
              debugPrint('⚠️ Session error, but keeping session valid: $error');
              _sessionValidityController.add(true);
              return SessionValidationResult.valid;
            }
          }
        } catch (e) {
          debugPrint('Error parsing session validation response: $e');
          // Keep session valid on parsing errors
          _sessionValidityController.add(true);
          return SessionValidationResult.valid;
        }
      } else if (response.statusCode == 403) {
        // Handle 403 status specifically for admin revocation
        try {
          final Map<String, dynamic> responseBody = jsonDecode(response.body);
          final String error = responseBody['error'] ?? 'Access forbidden';
          
          if (error.toLowerCase().contains('device access revoked by administrator')) {
            debugPrint('🚨 Admin revocation detected (403), invalidating session');
            await clearSessionData();
            emitSessionEvent(SessionEvent(
              type: SessionEventType.adminRevocation,
              message: error,
              timestamp: DateTime.now(),
            ));
            _sessionValidityController.add(false);
            return SessionValidationResult.invalidated;
          } else {
            // Other 403 errors - keep session valid
            debugPrint('⚠️ 403 error but not admin revocation, keeping session valid: $error');
            _sessionValidityController.add(true);
            return SessionValidationResult.valid;
          }
        } catch (e) {
          debugPrint('Error parsing 403 response: $e');
          // Keep session valid on parsing errors
          _sessionValidityController.add(true);
          return SessionValidationResult.valid;
        }
      } else {
        // On any other non-200 status (network error, 401, etc.), keep session valid
        debugPrint('⚠️ Non-200 status (${response.statusCode}), keeping session valid');
        _sessionValidityController.add(true);
        return SessionValidationResult.valid;
      }
    } on TimeoutException catch (e) {
      debugPrint('❌ Timeout during session validation: $e');
      // Keep session valid on timeout
      _sessionValidityController.add(true);
      return SessionValidationResult.valid;
    } catch (e) {
      debugPrint('❌ Error in validateSession: $e');
      // Keep session valid on unexpected errors
      _sessionValidityController.add(true);
      return SessionValidationResult.valid;
    } finally {
      _isValidating = false;
    }
  }

  /// Handle session invalidation
  Future<void> _handleSessionInvalidation(String reason) async {
    try {
      debugPrint('🚨 Handling session invalidation: $reason');

      // Only handle admin revocation - all other cases should be handled gracefully
      if (reason.toLowerCase().contains('device access revoked by administrator')) {
        // Check if we're offline
        final connectivityResults = await Connectivity().checkConnectivity();
        if (connectivityResults.contains(ConnectivityResult.none)) {
          // Store pending logout for when we come back online
          await _setPendingOfflineLogout(reason);
          debugPrint('📱 Offline - admin revocation deferred until reconnection');
          return;
        }

        // Immediate logout for admin revocation
        await _performLogout(reason);
      } else {
        // For all other session issues, show notification but don't logout
        debugPrint('⚠️ Non-admin session issue, handling gracefully: $reason');
        _showSessionErrorNotification('Session issue: $reason. Please try again.');
      }
    } catch (e) {
      debugPrint('❌ Error handling session invalidation: $e');
    }
  }

  /// Handle admin revocation with special handling
  Future<void> _handleAdminRevocation(String message, Map<String, dynamic>? revocationInfo) async {
    try {
      debugPrint('🚨 Handling admin revocation: $message');

      // Check if we're offline
      final connectivityResults = await Connectivity().checkConnectivity();
      if (connectivityResults.contains(ConnectivityResult.none)) {
        // Store pending logout for when we come back online
        await _setPendingOfflineLogout(message);
        debugPrint('📱 Offline - admin revocation deferred until reconnection');
        return;
      }

      // For admin revocation, we want to be more graceful
      // Don't interrupt critical user actions immediately
      await _performGracefulLogout(message, revocationInfo);
    } catch (e) {
      debugPrint('❌ Error handling admin revocation: $e');
    }
  }

  /// Perform graceful logout for admin revocation
  Future<void> _performGracefulLogout(String reason, Map<String, dynamic>? revocationInfo) async {
    try {
      debugPrint('🚪 Performing graceful logout for admin revocation: $reason');

      // Store logout reason for user notification
      await _secureStorage.write(key: _logoutReasonKey, value: reason);

      // Clear session data
      await _clearSessionData();

      // Notify about admin revocation with special event type
      _sessionEventController.add(SessionEvent(
        type: SessionEventType.adminRevocation,
        message: reason,
        timestamp: DateTime.now(),
        data: revocationInfo,
      ));

      // Notify session invalidity
      _sessionValidityController.add(false);

      debugPrint('✅ Graceful logout completed for admin revocation');
    } catch (e) {
      debugPrint('❌ Error during graceful logout: $e');
    }
  }

  /// Perform logout with reason
  Future<void> _performLogout(String reason) async {
    try {
      debugPrint('🚪 Performing logout: $reason');
      
      // Only logout for admin revocation - all other cases should be handled gracefully
      if (reason.toLowerCase().contains('device access revoked by administrator')) {
        debugPrint('🚨 Admin revocation detected - performing logout');
        // Store logout reason for user notification
        await _secureStorage.write(key: _logoutReasonKey, value: reason);
        // Clear session data
        await _clearSessionData();
        // Notify about admin revocation
        _sessionEventController.add(SessionEvent(
          type: SessionEventType.adminRevocation,
          message: reason,
          timestamp: DateTime.now(),
        ));
        _sessionValidityController.add(false);
        debugPrint('✅ Admin revocation logout completed');
      } else {
        // For all other errors, show a notification but do not log out
        _showSessionErrorNotification('Session issue: $reason. Please try again.');
        debugPrint('⚠️ Logout skipped for non-admin revocation: $reason');
      }
    } catch (e) {
      debugPrint('❌ Error during logout: $e');
    }
  }

  void _showSessionErrorNotification(String message) {
    // You can implement a global error handler or use a callback to show this in the UI
    debugPrint('Session error: ' + message);
  }

  /// Clear all session data
  Future<void> _clearSessionData() async {
    try {
      await _secureStorage.delete(key: _sessionIdKey);
      await _secureStorage.delete(key: _lastValidationKey);
      _currentSessionId = null;
      debugPrint('🧹 Session data cleared');
    } catch (e) {
      debugPrint('❌ Error clearing session data: $e');
    }
  }

  /// Update last validation timestamp
  Future<void> _updateLastValidation() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastValidationKey, DateTime.now().millisecondsSinceEpoch);
      debugPrint('🕒 Last validation timestamp updated.');
    } catch (e) {
      debugPrint('❌ Error updating last validation timestamp: $e');
    }
  }

  /// Set pending offline logout
  Future<void> _setPendingOfflineLogout(String reason) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_offlineLogoutKey, true);
      await _secureStorage.write(key: _logoutReasonKey, value: reason);
      debugPrint('📱 Pending offline logout set: $reason');
    } catch (e) {
      debugPrint('❌ Error setting pending offline logout: $e');
    }
  }

  /// Clear pending logout flags
  Future<void> _clearPendingLogout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_offlineLogoutKey);
      await _secureStorage.delete(key: _logoutReasonKey);
    } catch (e) {
      debugPrint('❌ Error clearing pending logout: $e');
    }
  }

  /// Check for pending offline logout
  Future<void> _checkPendingOfflineLogout() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isPending = prefs.getBool(_offlineLogoutKey) ?? false;
      if (isPending) {
        debugPrint('⚠️ Offline logout was pending. Notifying user.');
        _sessionEventController.add(SessionEvent(
          type: SessionEventType.offlineLogoutPending,
          message: 'You were logged out while offline. Please re-authenticate.',
          timestamp: DateTime.now(),
        ));
        await _clearPendingLogout(); // Clear the flag after notifying
      }
    } catch (e) {
      debugPrint('❌ Error checking pending offline logout: $e');
    }
  }

  /// Start periodic session validation
  void _startPeriodicValidation() {
    _validationTimer?.cancel();
    _validationTimer = Timer.periodic(_validationInterval, (timer) async {
      final token = await _secureStorage.read(key: 'auth_token'); // Assuming token is here
      if (token != null) {
        await validateSession(token);
      } else {
        debugPrint('⚠️ No token found, stopping validation timer.');
        _validationTimer?.cancel();
        _sessionValidityController.add(false); // No token means invalid session
      }
    });
    debugPrint('⏰ Periodic session validation started.');
  }

  /// Start offline monitoring
  void _startOfflineMonitoring() {
    _offlineCheckTimer?.cancel();
    _offlineCheckTimer = Timer.periodic(_offlineCheckInterval, (_) async {
      await _checkPendingOfflineLogout();
    });
    debugPrint('📱 Offline monitoring started');
  }

  /// Get logout reason for user notification
  Future<String?> getLogoutReason() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_logoutReasonKey);
    } catch (e) {
      debugPrint('❌ Error getting logout reason: $e');
      return null;
    }
  }

  /// Check if session validation is needed
  Future<bool> needsValidation() async {
    try {
      final lastValidationStr = await _secureStorage.read(key: _lastValidationKey);
      if (lastValidationStr == null) return true;

      final lastValidation = DateTime.parse(lastValidationStr);
      final timeSinceValidation = DateTime.now().difference(lastValidation);

      return timeSinceValidation > _validationInterval;
    } catch (e) {
      debugPrint('❌ Error checking validation need: $e');
      return true;
    }
  }

  /// Force session validation
  Future<SessionValidationResult> forceValidation(String token) async {
    debugPrint('🌟 Forcing session validation...');
    // Temporarily disable validation timer to avoid conflicts
    _validationTimer?.cancel(); 
    final result = await validateSession(token);
    _startPeriodicValidation(); // Restart timer after forced validation
    return result;
  }

  /// Handle logout from external source (user initiated)
  Future<void> onLogout() async {
    try {
      debugPrint('🚪 Handling external logout...');

      // Cancel timers
      _validationTimer?.cancel();
      _offlineCheckTimer?.cancel();

      // Clear session data
      await _clearSessionData();
      await _clearPendingLogout();

      // Notify session end
      _sessionEventController.add(SessionEvent(
        type: SessionEventType.sessionInvalidated,
        message: 'User logged out',
        timestamp: DateTime.now(),
      ));

      _sessionValidityController.add(false);

      debugPrint('✅ External logout handled');
    } catch (e) {
      debugPrint('❌ Error handling external logout: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _validationTimer?.cancel();
    _offlineCheckTimer?.cancel();
    _sessionEventController.close();
    _sessionValidityController.close();
    _isInitialized = false;
    debugPrint('🗑️ Single Session Auth Service disposed');
  }

  /// Set logout reason for display after re-login
  Future<void> _setLogoutReason(String reason) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_logoutReasonKey, reason);
      debugPrint('📝 Logout reason set: $reason');
    } catch (e) {
      debugPrint('❌ Error setting logout reason: $e');
    }
  }

  /// Clear all session data (device ID, session ID, last validation, etc.)
  Future<void> clearSessionData() async {
    debugPrint('🧹 Clearing single session data...');
    try {
      await _secureStorage.delete(key: _deviceIdKey);
      await _secureStorage.delete(key: _sessionIdKey);
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastValidationKey);
      await prefs.remove(_logoutReasonKey);
      await prefs.remove(_offlineLogoutKey);
      _currentDeviceId = null;
      _currentSessionId = null;
      _sessionValidityController.add(false); // Notify that session is no longer valid
      debugPrint('✅ Single session data cleared.');
    } catch (e) {
      debugPrint('❌ Error clearing single session data: $e');
    }
  }

  /// Emit a session event
  void emitSessionEvent(SessionEvent event) {
    _sessionEventController.add(event);
  }
}

/// Session validation result
enum SessionValidationResult {
  valid,
  invalidated,
  failed,
  error,
  offline,
  validating,
}

/// Session event types
enum SessionEventType {
  deviceReplaced,
  sessionInvalidated,
  adminRevocation,
  offlineLogoutPending,
  sessionRestored,
  validationFailed,
}

/// Session event data
class SessionEvent {
  final SessionEventType type;
  final String message;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  SessionEvent({
    required this.type,
    required this.message,
    required this.timestamp,
    this.data,
  });

  @override
  String toString() => 'SessionEvent(type: $type, message: $message, timestamp: $timestamp)';
}
