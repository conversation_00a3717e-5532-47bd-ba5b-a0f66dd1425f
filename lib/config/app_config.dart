import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'network_config.dart';

class AppConfig {
  // Development mode flag
  static const bool isDevelopmentMode = true;

  // Default API base URL - now uses NetworkConfig
  static String get defaultApiBaseUrl => NetworkConfig.getCurrentEndpoint();

  // App version
  static const String appVersion = '1.0.0';

  // Theme mode
  static ThemeMode _themeMode = ThemeMode.light;

  // Theme change listener
  static VoidCallback? _themeChangeListener;

  // Getter for theme mode
  static ThemeMode get themeMode => _themeMode;

  // Setter for theme mode
  static set themeMode(ThemeMode mode) {
    _themeMode = mode;
    _notifyThemeChangeListener();
  }

  // Check if dark mode is enabled
  static bool get isDarkMode => _themeMode == ThemeMode.dark;

  // Check if system theme is used
  static bool get isSystemTheme => _themeMode == ThemeMode.system;

  // Add theme change listener
  static void addThemeChangeListener(VoidCallback listener) {
    _themeChangeListener = listener;
  }

  // Remove theme change listener
  static void removeThemeChangeListener() {
    _themeChangeListener = null;
  }

  // Notify theme change listener
  static void _notifyThemeChangeListener() {
    if (_themeChangeListener != null) {
      _themeChangeListener!();
    }
  }

  // Save theme mode to SharedPreferences
  static Future<void> saveThemeMode(ThemeMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('theme_mode', mode.toString());
      _themeMode = mode;
      _notifyThemeChangeListener();
    } catch (e) {
      print('Error saving theme mode: $e');
    }
  }

  // Initialize from SharedPreferences
  static Future<void> initFromPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode
      final savedThemeMode = prefs.getString('theme_mode');
      if (savedThemeMode != null) {
        if (savedThemeMode == ThemeMode.dark.toString()) {
          _themeMode = ThemeMode.dark;
        } else if (savedThemeMode == ThemeMode.light.toString()) {
          _themeMode = ThemeMode.light;
        } else {
          _themeMode = ThemeMode.system;
        }
      }
    } catch (e) {
      print('Error initializing AppConfig from preferences: $e');
    }
  }

  // Other configuration settings can be added here
}
