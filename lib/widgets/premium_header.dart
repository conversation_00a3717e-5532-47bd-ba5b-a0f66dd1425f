import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';

/// A premium gradient header component that can be used across different pages
/// for design consistency.
class PremiumHeader extends StatelessWidget {
  /// The title to display in the header
  final String title;

  /// Optional subtitle to display below the title
  final String? subtitle;

  /// Optional action buttons to display in the header
  final List<Widget>? actions;

  /// Height of the header
  final double height;

  /// Whether to show a back button
  final bool showBackButton;

  /// Callback when back button is pressed
  final VoidCallback? onBackPressed;

  /// Optional leading widget to display instead of the back button
  final Widget? leading;

  /// Optional decoration to override the default gradient
  final BoxDecoration? decoration;

  /// Optional bottom widget to display at the bottom of the header
  final Widget? bottomWidget;

  /// Optional padding for the content
  final EdgeInsetsGeometry? padding;

  const PremiumHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.actions,
    this.height = 180.0,
    this.showBackButton = false,
    this.onBackPressed,
    this.leading,
    this.decoration,
    this.bottomWidget,
    this.padding,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      height: height,
      decoration: decoration ?? BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [Colors.black, Colors.grey[900]!]
              : [KFTDesignSystem.primaryColor, KFTDesignSystem.primaryDarkColor],
        ),
      ),
      child: Stack(
        children: [
          // Decorative elements
          Positioned(
            top: -20,
            right: -20,
            child: Container(
              width: 150,
              height: 150,
              decoration: BoxDecoration(
                color: isDark ? Colors.white.withOpacity(0.03) : Colors.white.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
            ),
          ),
          Positioned(
            bottom: -50,
            left: -30,
            child: Container(
              width: 180,
              height: 180,
              decoration: BoxDecoration(
                color: isDark ? Colors.white.withOpacity(0.02) : Colors.white.withOpacity(0.08),
                shape: BoxShape.circle,
              ),
            ),
          ),

          // Content
          Positioned.fill(
            child: SafeArea(
              child: Padding(
                padding: padding ?? const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top row with back button/leading widget and actions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        if (showBackButton)
                          IconButton(
                            icon: Icon(
                              Icons.arrow_back_ios_rounded,
                              color: isDark ? KFTDesignSystem.getTextPrimaryColor(context) : Colors.white,
                            ),
                            onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
                          )
                        else if (leading != null)
                          leading!
                        else
                          const SizedBox(width: 40),

                        if (actions != null && actions!.isNotEmpty)
                          Row(
                            children: actions!,
                          )
                        else
                          const SizedBox(width: 40),
                      ],
                    ),

                    // Title and subtitle - using Flexible instead of Spacer + Expanded
                    Flexible(
                      flex: 1,
                      child: Align(
                        alignment: Alignment.bottomLeft,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                color: isDark ? KFTDesignSystem.getTextPrimaryColor(context) : Colors.white,
                                fontSize: 24, // Further reduced to save space
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            if (subtitle != null) ...[
                              const SizedBox(height: 1), // Reduced from 2 to 1 to save space
                              Text(
                                subtitle!,
                                style: TextStyle(
                                  color: isDark ? KFTDesignSystem.getTextSecondaryColor(context) : Colors.white.withOpacity(0.8),
                                  fontSize: 12, // Further reduced from 13 to 12 to save space
                                  fontWeight: FontWeight.w500,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),

                    if (bottomWidget != null) ...[
                      const SizedBox(height: 4), // Reduced from 8 to 4 to eliminate overflow
                      Flexible(
                        child: bottomWidget!,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// A sliver version of the premium header for use in CustomScrollView
class SliverPremiumHeader extends StatelessWidget {
  /// The title to display in the header
  final String title;

  /// Optional subtitle to display below the title
  final String? subtitle;

  /// Optional action buttons to display in the header
  final List<Widget>? actions;

  /// Height of the header when expanded
  final double expandedHeight;

  /// Whether to show a back button
  final bool showBackButton;

  /// Callback when back button is pressed
  final VoidCallback? onBackPressed;

  /// Optional leading widget to display instead of the back button
  final Widget? leading;

  /// Optional decoration to override the default gradient
  final BoxDecoration? decoration;

  /// Optional bottom widget to display at the bottom of the header
  final Widget? bottomWidget;

  /// Whether the app bar should remain visible at the start of the scroll view
  final bool floating;

  /// Whether the app bar should remain visible during scrolling
  final bool pinned;

  const SliverPremiumHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.actions,
    this.expandedHeight = 180.0,
    this.showBackButton = false,
    this.onBackPressed,
    this.leading,
    this.decoration,
    this.bottomWidget,
    this.floating = false,
    this.pinned = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SliverAppBar(
      expandedHeight: expandedHeight,
      floating: floating,
      pinned: pinned,
      elevation: 0,
      backgroundColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      flexibleSpace: FlexibleSpaceBar(
        background: PremiumHeader(
          title: title,
          subtitle: subtitle,
          actions: actions,
          height: expandedHeight,
          showBackButton: showBackButton,
          onBackPressed: onBackPressed,
          leading: leading,
          decoration: decoration,
          bottomWidget: bottomWidget,
        ),
      ),
    );
  }
}
