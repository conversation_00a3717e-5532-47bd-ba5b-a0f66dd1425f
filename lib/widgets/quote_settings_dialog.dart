import 'package:flutter/material.dart';
import '../models/motivational_quote.dart';
import '../services/quote_service.dart';
import '../design_system/kft_design_system.dart';

class QuoteSettingsDialog extends StatefulWidget {
  final QuotePreferences preferences;
  
  const QuoteSettingsDialog({
    Key? key,
    required this.preferences,
  }) : super(key: key);

  @override
  _QuoteSettingsDialogState createState() => _QuoteSettingsDialogState();
}

class _QuoteSettingsDialogState extends State<QuoteSettingsDialog> {
  late QuotePreferences _preferences;
  final QuoteService _quoteService = QuoteService();
  bool _isLoading = false;
  String? _errorMessage;
  bool _deepSeekEnabled = false;
  
  // Available categories
  final List<String> _availableCategories = [
    'fitness',
    'motivation',
    'health',
    'mindfulness',
    'success',
    'discipline',
    'nutrition',
    'strength',
    'endurance',
    'recovery',
  ];
  
  // Selected categories
  late List<String> _selectedCategories;
  
  @override
  void initState() {
    super.initState();
    _preferences = widget.preferences;
    _selectedCategories = _preferences.categoriesList;
    _loadDeepSeekSetting();
  }
  
  Future<void> _loadDeepSeekSetting() async {
    final enabled = await QuoteService().getDeepSeekEnabledForUser();
    setState(() {
      _deepSeekEnabled = enabled;
    });
  }
  
  Future<void> _savePreferences() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    
    try {
      final preferredCategories = _selectedCategories.join(',');
      
      await _quoteService.updatePreferences(
        preferredCategories: preferredCategories,
        personalizationEnabled: _preferences.personalizationEnabled,
      );
      
      if (mounted) {
        Navigator.of(context).pop(true); // Return true to indicate success
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to save preferences: $e';
        _isLoading = false;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.format_quote, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Quote Preferences',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (_errorMessage != null)
              Container(
                margin: const EdgeInsets.only(bottom: 16),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _errorMessage!,
                  style: TextStyle(color: Colors.red.shade800),
                ),
              ),
            
            Text(
              'Select Quote Categories',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Choose the categories of quotes you want to see:',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _availableCategories.map((category) {
                final isSelected = _selectedCategories.contains(category);
                return FilterChip(
                  label: Text(
                    category.substring(0, 1).toUpperCase() + category.substring(1),
                    style: TextStyle(
                      color: isSelected ? Colors.white : null,
                      fontWeight: isSelected ? FontWeight.bold : null,
                    ),
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      if (selected) {
                        _selectedCategories.add(category);
                      } else {
                        _selectedCategories.remove(category);
                      }
                    });
                  },
                  backgroundColor: Colors.grey.shade200,
                  selectedColor: theme.colorScheme.primary,
                  checkmarkColor: Colors.white,
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
            
            SwitchListTile(
              title: const Text('AI Personalization'),
              subtitle: const Text('Allow AI to personalize quotes based on your fitness goals'),
              value: _preferences.personalizationEnabled,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(
                    personalizationEnabled: value,
                  );
                });
              },
              activeColor: theme.colorScheme.primary,
            ),
            
            const SizedBox(height: 24),
            
            SwitchListTile(
              title: const Text('Enable DeepSeek AI Quotes'),
              subtitle: const Text('Get personalized, AI-generated quotes'),
              value: _deepSeekEnabled,
              onChanged: (value) async {
                setState(() => _deepSeekEnabled = value);
                await QuoteService().setDeepSeekEnabledForUser(value);
              },
            ),
            
            const SizedBox(height: 24),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(false),
                  child: const Text('Cancel'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _isLoading ? null : _savePreferences,
                  child: _isLoading
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Save Preferences'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
