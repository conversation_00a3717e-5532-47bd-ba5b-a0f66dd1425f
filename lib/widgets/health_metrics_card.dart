import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../design_system/kft_design_system.dart';

class HealthMetricsCard extends StatelessWidget {
  final double bmi;
  final double bodyFat;
  final double muscleMass;
  final double waterPercentage;

  const HealthMetricsCard({
    Key? key,
    required this.bmi,
    required this.bodyFat,
    required this.muscleMass,
    required this.waterPercentage,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
        side: BorderSide(
          color: KFTDesignSystem.borderColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
                const Text(
                  'Health Metrics',
                style: TextStyle(
                    fontSize: 20,
                  fontWeight: FontWeight.bold,
                    letterSpacing: -0.5,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    // Show detailed metrics info
                  },
                  icon: Icon(
                    Icons.info_outline,
                    color: KFTDesignSystem.primaryColor,
                ),
              ),
            ],
          ),
            const SizedBox(height: 24),
            Row(
            children: [
                Expanded(
                  child: _buildMetricItem(
                    title: 'BMI',
                    value: bmi.toStringAsFixed(1),
                    icon: Icons.monitor_weight,
                    color: KFTDesignSystem.primaryColor,
                    status: _getBMIStatus(bmi),
              ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMetricItem(
                    title: 'Body Fat',
                    value: '${bodyFat.toStringAsFixed(1)}%',
                    icon: Icons.pie_chart,
                    color: KFTDesignSystem.secondaryColor,
                    status: _getBodyFatStatus(bodyFat),
                  ),
              ),
            ],
          ),
          const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    title: 'Muscle Mass',
                    value: '${muscleMass.toStringAsFixed(1)}%',
                    icon: Icons.fitness_center,
                    color: KFTDesignSystem.accentColor,
                    status: _getMuscleMassStatus(muscleMass),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildMetricItem(
                    title: 'Water',
                    value: '${waterPercentage.toStringAsFixed(1)}%',
                    icon: Icons.water_drop,
                    color: KFTDesignSystem.primaryColor,
                    status: _getWaterStatus(waterPercentage),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildProgressBar(bmi, bodyFat, muscleMass, waterPercentage),
        ],
        ),
      ),
    );
  }

  Widget _buildMetricItem({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String status,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                  icon,
                size: 20,
                  color: color,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                  style: TextStyle(
                  fontSize: 14,
                  color: KFTDesignSystem.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            status,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressBar(
    double bmi,
    double bodyFat,
    double muscleMass,
    double waterPercentage,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Overall Health Score',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: LinearProgressIndicator(
            value: _calculateOverallScore(bmi, bodyFat, muscleMass, waterPercentage),
            backgroundColor: KFTDesignSystem.primaryColor.withOpacity(0.1),
            valueColor: AlwaysStoppedAnimation<Color>(KFTDesignSystem.primaryColor),
            minHeight: 8,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          '${(_calculateOverallScore(bmi, bodyFat, muscleMass, waterPercentage) * 100).toInt()}%',
            style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: KFTDesignSystem.primaryColor,
          ),
        ),
      ],
    );
  }

  String _getBMIStatus(double bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal';
    if (bmi < 30) return 'Overweight';
    return 'Obese';
  }

  String _getBodyFatStatus(double bodyFat) {
    if (bodyFat < 10) return 'Very Low';
    if (bodyFat < 20) return 'Low';
    if (bodyFat < 30) return 'Normal';
    return 'High';
  }

  String _getMuscleMassStatus(double muscleMass) {
    if (muscleMass < 30) return 'Low';
    if (muscleMass < 40) return 'Normal';
    return 'High';
  }

  String _getWaterStatus(double waterPercentage) {
    if (waterPercentage < 45) return 'Low';
    if (waterPercentage < 60) return 'Normal';
    return 'High';
  }

  double _calculateOverallScore(
    double bmi,
    double bodyFat,
    double muscleMass,
    double waterPercentage,
  ) {
    // Normalize BMI (ideal range 18.5-24.9)
    double bmiScore = 1.0;
    if (bmi < 18.5) {
      bmiScore = bmi / 18.5;
    } else if (bmi > 24.9) {
      bmiScore = 24.9 / bmi;
    }

    // Normalize body fat (ideal range 10-20%)
    double bodyFatScore = 1.0;
    if (bodyFat < 10) {
      bodyFatScore = bodyFat / 10;
    } else if (bodyFat > 20) {
      bodyFatScore = 20 / bodyFat;
    }

    // Normalize muscle mass (ideal range 30-40%)
    double muscleMassScore = 1.0;
    if (muscleMass < 30) {
      muscleMassScore = muscleMass / 30;
    } else if (muscleMass > 40) {
      muscleMassScore = 40 / muscleMass;
    }

    // Normalize water percentage (ideal range 45-60%)
    double waterScore = 1.0;
    if (waterPercentage < 45) {
      waterScore = waterPercentage / 45;
    } else if (waterPercentage > 60) {
      waterScore = 60 / waterPercentage;
    }

    // Calculate weighted average
    return (bmiScore * 0.3 + bodyFatScore * 0.3 + muscleMassScore * 0.2 + waterScore * 0.2);
  }
}
