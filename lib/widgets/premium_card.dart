import 'package:flutter/material.dart';
import '../design_system/kft_design_system.dart';

/// A premium styled card component that can be used across different pages
/// for design consistency.
class PremiumCard extends StatelessWidget {
  /// The child widget to display inside the card
  final Widget child;

  /// Optional padding for the card content
  final EdgeInsetsGeometry padding;

  /// Optional margin for the card
  final EdgeInsetsGeometry? margin;

  /// Optional border radius for the card
  final BorderRadius borderRadius;

  /// Optional color for the card
  final Color? color;

  /// Optional gradient for the card
  final Gradient? gradient;

  /// Optional elevation for the card
  final double elevation;

  /// Optional onTap callback
  final VoidCallback? onTap;

  /// Whether to use a gradient background
  final bool useGradient;

  /// Optional shadow color
  final Color? shadowColor;

  const PremiumCard({
    Key? key,
    required this.child,
    this.padding = const EdgeInsets.all(20),
    this.margin,
    this.borderRadius = const BorderRadius.all(Radius.circular(20)),
    this.color,
    this.gradient,
    this.elevation = 2,
    this.onTap,
    this.useGradient = false,
    this.shadowColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cardColor = color ?? theme.colorScheme.surface;
    final cardShadowColor = shadowColor ?? theme.shadowColor.withOpacity(0.05);
    final primaryColor = theme.colorScheme.primary;
    final primaryDarkColor = theme.colorScheme.primary.withOpacity(0.8);
    final dividerColor = theme.dividerColor;

    final isDarkMode = theme.brightness == Brightness.dark;

    final cardDecoration = BoxDecoration(
      color: useGradient ? null : (isDarkMode ? const Color(0xFF2C2C2C) : cardColor),
      gradient: useGradient
          ? gradient ?? LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isDarkMode
                  ? [
                      const Color(0xFF2C3E50).withOpacity(0.9),
                      const Color(0xFF1E272E),
                    ]
                  : [
                      primaryColor.withOpacity(0.9),
                      primaryDarkColor,
                    ],
            )
          : null,
      borderRadius: borderRadius,
      boxShadow: [
        BoxShadow(
          color: theme.shadowColor.withOpacity(isDarkMode ? 0.3 : 0.05),
          blurRadius: 10,
          offset: const Offset(0, 4),
          spreadRadius: 0,
        ),
      ],
      border: useGradient
          ? null
          : Border.all(
              color: isDarkMode
                  ? Colors.grey.shade800
                  : dividerColor.withOpacity(0.5),
              width: KFTDesignSystem.borderWidthThin,
            ),
    );

    final cardContent = Container(
      padding: padding,
      decoration: cardDecoration,
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: Container(
          margin: margin,
          child: cardContent,
        ),
      );
    }

    return Container(
      margin: margin,
      child: cardContent,
    );
  }
}

/// A gradient card with white text, useful for featured content
class GradientCard extends StatelessWidget {
  /// The child widget to display inside the card
  final Widget child;

  /// Optional padding for the card content
  final EdgeInsetsGeometry padding;

  /// Optional margin for the card
  final EdgeInsetsGeometry? margin;

  /// Optional border radius for the card
  final BorderRadius borderRadius;

  /// Optional gradient for the card
  final Gradient? gradient;

  /// Optional onTap callback
  final VoidCallback? onTap;

  /// Optional shadow color
  final Color? shadowColor;

  /// Which color to use as the base for the gradient
  final Color baseColor;

  const GradientCard({
    Key? key,
    required this.child,
    this.padding = const EdgeInsets.all(KFTDesignSystem.spacingLg),
    this.margin,
    this.borderRadius = const BorderRadius.all(Radius.circular(KFTDesignSystem.borderRadiusLg)),
    this.gradient,
    this.onTap,
    this.shadowColor,
    this.baseColor = KFTDesignSystem.primaryColor, // Default to mint green primary color
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PremiumCard(
      padding: padding,
      margin: margin,
      borderRadius: borderRadius,
      useGradient: true,
      gradient: gradient ?? LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          baseColor.withOpacity(0.95),
          baseColor.withGreen((baseColor.green - 25).clamp(0, 255)),
        ],
        stops: const [0.3, 1.0],
      ),
      shadowColor: shadowColor ?? baseColor.withOpacity(0.3),
      onTap: onTap,
      child: child,
    );
  }
}

/// A feature card with icon, title and description
class FeatureCard extends StatelessWidget {
  /// The icon to display
  final IconData icon;

  /// The title of the feature
  final String title;

  /// The description of the feature
  final String description;

  /// The color of the feature
  final Color color;

  /// Optional onTap callback
  final VoidCallback? onTap;

  /// Optional margin for the card
  final EdgeInsetsGeometry? margin;

  const FeatureCard({
    Key? key,
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
    this.onTap,
    this.margin,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PremiumCard(
      margin: margin ?? const EdgeInsets.only(bottom: 16),
      onTap: onTap,
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios_rounded,
            size: 16,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
          ),
        ],
      ),
    );
  }
}
