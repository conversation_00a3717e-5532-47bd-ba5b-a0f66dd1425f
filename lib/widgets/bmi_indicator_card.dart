import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/user_profile.dart';
import '../design_system/kft_design_system.dart';

class BMIIndicatorCard extends StatelessWidget {
  final UserProfile userProfile;
  final Function(double) onWeightUpdated;

  const BMIIndicatorCard({
    Key? key,
    required this.userProfile,
    required this.onWeightUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final bmi = userProfile.currentBMI;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics,
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'BMI Analysis',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: KFTDesignSystem.getTextPrimaryColor(context),
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () => _showUpdateWeightDialog(context),
                icon: const Icon(Icons.edit, size: 16),
                label: const Text('Update'),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // BMI value display
          Center(
            child: Column(
              children: [
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: _getBMIColor(bmi).withOpacity(0.1),
                    border: Border.all(
                      color: _getBMIColor(bmi),
                      width: 3,
                    ),
                  ),
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          bmi.toStringAsFixed(1),
                          style: TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                            color: _getBMIColor(bmi),
                          ),
                        ),
                        Text(
                          'BMI',
                          style: TextStyle(
                            fontSize: 12,
                            color: KFTDesignSystem.getTextSecondaryColor(context),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: _getBMIColor(bmi).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: _getBMIColor(bmi).withOpacity(0.3),
                    ),
                  ),
                  child: Text(
                    userProfile.bmiCategory,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: _getBMIColor(bmi),
                    ),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // BMI scale indicator
          _buildBMIScale(context, bmi),


        ],
      ),
    );
  }

  Widget _buildBMIScale(BuildContext context, double bmi) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'BMI Scale',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: KFTDesignSystem.getTextPrimaryColor(context),
          ),
        ),
        const SizedBox(height: 12),

        // BMI scale bar
        Container(
          height: 8,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: const LinearGradient(
              colors: [
                Colors.blue,    // Underweight
                Colors.green,   // Normal
                Colors.orange,  // Overweight
                Colors.red,     // Obese
              ],
              stops: [0.0, 0.33, 0.66, 1.0],
            ),
          ),
        ),

        const SizedBox(height: 8),

        // BMI scale labels
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildScaleLabel('Under\n18.5', Colors.blue, bmi < 18.5),
            _buildScaleLabel('Normal\n18.5-24.9', Colors.green, bmi >= 18.5 && bmi < 25),
            _buildScaleLabel('Over\n25-29.9', Colors.orange, bmi >= 25 && bmi < 30),
            _buildScaleLabel('Obese\n30+', Colors.red, bmi >= 30),
          ],
        ),

        // Current BMI indicator
        const SizedBox(height: 8),
        _buildBMIIndicator(context, bmi),
      ],
    );
  }

  Widget _buildScaleLabel(String label, Color color, bool isActive) {
    return Column(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: isActive ? color : color.withOpacity(0.3),
            border: isActive ? Border.all(color: color, width: 2) : null,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 10,
            color: isActive ? color : color.withOpacity(0.6),
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }

  Widget _buildBMIIndicator(BuildContext context, double bmi) {
    // Calculate position on scale (0-1)
    double position;
    if (bmi < 18.5) {
      position = (bmi / 18.5) * 0.33;
    } else if (bmi < 25) {
      position = 0.33 + ((bmi - 18.5) / (25 - 18.5)) * 0.33;
    } else if (bmi < 30) {
      position = 0.66 + ((bmi - 25) / (30 - 25)) * 0.34;
    } else {
      position = 1.0;
    }

    return Stack(
      children: [
        Container(
          height: 2,
          color: Colors.transparent,
        ),
        Positioned(
          left: MediaQuery.of(context).size.width * 0.7 * position - 6,
          child: Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: _getBMIColor(bmi),
              border: Border.all(color: Colors.white, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }



  Color _getBMIColor(double bmi) {
    if (bmi < 18.5) {
      return Colors.blue; // Underweight
    } else if (bmi < 25) {
      return Colors.green; // Normal
    } else if (bmi < 30) {
      return Colors.orange; // Overweight
    } else {
      return Colors.red; // Obese
    }
  }

  void _showUpdateWeightDialog(BuildContext context) {
    final weightController = TextEditingController(
      text: userProfile.weight.toStringAsFixed(1),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Update Weight'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: weightController,
              decoration: const InputDecoration(
                labelText: 'Weight (kg)',
                border: OutlineInputBorder(),
                suffixText: 'kg',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,1}')),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Current BMI will be recalculated automatically',
              style: TextStyle(
                fontSize: 12,
                color: KFTDesignSystem.getTextSecondaryColor(context),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              final newWeight = double.tryParse(weightController.text);
              if (newWeight != null && newWeight > 0) {
                onWeightUpdated(newWeight);
                Navigator.of(context).pop();
              }
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
