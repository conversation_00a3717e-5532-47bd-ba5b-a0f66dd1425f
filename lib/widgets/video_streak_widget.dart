import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/video_streak_service.dart';

class VideoStreakWidget extends StatefulWidget {
  final VoidCallback? onStreakUpdated;

  const VideoStreakWidget({
    Key? key,
    this.onStreakUpdated,
  }) : super(key: key);

  @override
  State<VideoStreakWidget> createState() => VideoStreakWidgetState();
}

class VideoStreakWidgetState extends State<VideoStreakWidget>
    with TickerProviderStateMixin {
  final VideoStreakService _streakService = VideoStreakService();

  int _currentStreak = 0;
  int _highestStreak = 0;
  bool _isLoading = true;

  late AnimationController _pulseController;
  late AnimationController _countController;
  late Animation<double> _pulseAnimation;
  late Animation<int> _countAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStreakData();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _countController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _countAnimation = IntTween(
      begin: 0,
      end: _currentStreak,
    ).animate(CurvedAnimation(
      parent: _countController,
      curve: Curves.easeOutCubic,
    ));

    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _countController.dispose();
    super.dispose();
  }

  Future<void> _loadStreakData() async {
    try {
      final currentStreak = await _streakService.getCurrentStreak();
      final highestStreak = await _streakService.getHighestStreak();

      if (mounted) {
        setState(() {
          _currentStreak = currentStreak;
          _highestStreak = highestStreak;
          _isLoading = false;
        });

        // Update count animation
        _countAnimation = IntTween(
          begin: 0,
          end: _currentStreak,
        ).animate(CurvedAnimation(
          parent: _countController,
          curve: Curves.easeOutCubic,
        ));

        _countController.forward();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return _buildLoadingWidget(theme);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDarkMode
              ? [
                  const Color(0xFF1E3A8A).withOpacity(0.8),
                  const Color(0xFF3B82F6).withOpacity(0.6),
                ]
              : [
                  const Color(0xFF3B82F6).withOpacity(0.9),
                  const Color(0xFF1E40AF).withOpacity(0.7),
                ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          // Streak Icon and Count
          Expanded(
            flex: 2,
            child: _buildStreakDisplay(theme),
          ),

          const SizedBox(width: 20),

          // Streak Information
          Expanded(
            flex: 3,
            child: _buildStreakInfo(theme),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface.withOpacity(0.5),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          const CircularProgressIndicator(),
          const SizedBox(width: 16),
          Text(
            'Loading video streak...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStreakDisplay(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // Animated Fire Icon
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _currentStreak > 0 ? _pulseAnimation.value : 1.0,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.white.withOpacity(0.2),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  _currentStreak > 0 ? Icons.local_fire_department : Icons.play_circle_outline,
                  size: 32,
                  color: _currentStreak > 0 ? Colors.orange : Colors.white70,
                ),
              ),
            );
          },
        ),

        const SizedBox(height: 12),

        // Animated Streak Count
        AnimatedBuilder(
          animation: _countAnimation,
          builder: (context, child) {
            return Text(
              '${_countAnimation.value}',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 32,
              ),
            );
          },
        ),

        Text(
          _currentStreak == 1 ? 'Day' : 'Days',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: Colors.white70,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStreakInfo(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                'Video Learning Streak',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(width: 6),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green.withOpacity(0.3)),
              ),
              child: Text(
                'DEFAULT',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                  fontSize: 9,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        if (_currentStreak > 0) ...[
          Text(
            'Watch 50% of any video daily to maintain streak. Resets after 24 hours.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white70,
              height: 1.3,
              fontSize: 12,
            ),
          ),
        ] else ...[
          Text(
            'Watch 50% of any video to earn daily streak. Automatically tracked.',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white70,
              height: 1.3,
              fontSize: 12,
            ),
          ),
        ],

        const SizedBox(height: 12),

        // Highest Streak Badge
        if (_highestStreak > 0)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  size: 16,
                  color: Colors.amber,
                ),
                const SizedBox(width: 6),
                Text(
                  'Best: $_highestStreak ${_highestStreak == 1 ? 'day' : 'days'}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  // Method to update streak from external sources
  void updateStreak(int newStreak, int newHighest) {
    if (mounted) {
      setState(() {
        _currentStreak = newStreak;
        _highestStreak = newHighest;
      });

      // Trigger haptic feedback for streak updates
      if (newStreak > 0) {
        HapticFeedback.lightImpact();
      }

      // Update animations
      _countAnimation = IntTween(
        begin: _countAnimation.value,
        end: newStreak,
      ).animate(CurvedAnimation(
        parent: _countController,
        curve: Curves.easeOutCubic,
      ));

      _countController.reset();
      _countController.forward();

      // Notify parent
      widget.onStreakUpdated?.call();
    }
  }

  // Method to refresh streak data
  Future<void> refreshStreak() async {
    await _loadStreakData();
  }
}
