import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;

/// Hydrated theme colors for progress components
class HydratedTheme {
  // Primary hydrated colors
  static const Color primaryBlue = Color(0xFF0891B2); // Cyan-600
  static const Color lightBlue = Color(0xFF06B6D4); // Cyan-500
  static const Color darkBlue = Color(0xFF0E7490); // Cyan-700
  
  // Secondary hydrated colors
  static const Color aqua = Color(0xFF22D3EE); // Cyan-400
  static const Color teal = Color(0xFF14B8A6); // Teal-500
  static const Color lightTeal = Color(0xFF5EEAD4); // Teal-300
  
  // Progress states
  static const Color progressBackground = Color(0xFFE0F7FA); // Very light cyan
  static const Color progressBackgroundDark = Color(0xFF164E63); // Dark cyan
  static const Color completedGreen = Color(0xFF10B981); // Emerald-500
  static const Color warningAmber = Color(0xFFF59E0B); // Amber-500
  
  // Gradients
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [lightBlue, primaryBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient completedGradient = LinearGradient(
    colors: [completedGreen, teal],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient aquaGradient = LinearGradient(
    colors: [aqua, lightBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}

/// Enhanced progress bar with hydrated theme and animations
class HydratedProgressBar extends StatefulWidget {
  final double progress; // 0.0 to 1.0
  final double height;
  final Color? backgroundColor;
  final Gradient? progressGradient;
  final BorderRadius? borderRadius;
  final bool showPercentage;
  final bool animated;
  final Duration animationDuration;
  final String? label;
  final bool showShimmer;

  const HydratedProgressBar({
    Key? key,
    required this.progress,
    this.height = 8.0,
    this.backgroundColor,
    this.progressGradient,
    this.borderRadius,
    this.showPercentage = false,
    this.animated = true,
    this.animationDuration = const Duration(milliseconds: 800),
    this.label,
    this.showShimmer = true,
  }) : super(key: key);

  @override
  State<HydratedProgressBar> createState() => _HydratedProgressBarState();
}

class _HydratedProgressBarState extends State<HydratedProgressBar>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _shimmerController;
  late Animation<double> _progressAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    _shimmerAnimation = Tween<double>(
      begin: -1.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.easeInOut,
    ));

    if (widget.animated) {
      _progressController.forward();
    }

    if (widget.showShimmer && widget.progress > 0) {
      _shimmerController.repeat();
    }
  }

  @override
  void didUpdateWidget(HydratedProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeOutCubic,
      ));
      
      if (widget.animated) {
        _progressController.reset();
        _progressController.forward();
      }
    }

    if (widget.showShimmer && widget.progress > 0 && !_shimmerController.isAnimating) {
      _shimmerController.repeat();
    } else if (!widget.showShimmer || widget.progress == 0) {
      _shimmerController.stop();
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final backgroundColor = widget.backgroundColor ?? 
        (isDark ? HydratedTheme.progressBackgroundDark : HydratedTheme.progressBackground);
    
    final progressGradient = widget.progressGradient ?? 
        (widget.progress >= 1.0 ? HydratedTheme.completedGradient : HydratedTheme.primaryGradient);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.label!,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: HydratedTheme.primaryBlue,
                ),
              ),
              if (widget.showPercentage)
                Text(
                  '${(widget.progress * 100).round()}%',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: HydratedTheme.primaryBlue,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
        ],
        Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: widget.borderRadius ?? BorderRadius.circular(widget.height / 2),
          ),
          child: AnimatedBuilder(
            animation: widget.animated ? _progressAnimation : AlwaysStoppedAnimation(widget.progress),
            builder: (context, child) {
              final currentProgress = widget.animated ? _progressAnimation.value : widget.progress;
              
              return Stack(
                children: [
                  // Progress bar
                  FractionallySizedBox(
                    widthFactor: currentProgress.clamp(0.0, 1.0),
                    child: Container(
                      height: widget.height,
                      decoration: BoxDecoration(
                        gradient: progressGradient,
                        borderRadius: widget.borderRadius ?? BorderRadius.circular(widget.height / 2),
                        boxShadow: [
                          BoxShadow(
                            color: HydratedTheme.lightBlue.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Shimmer effect
                  if (widget.showShimmer && currentProgress > 0)
                    AnimatedBuilder(
                      animation: _shimmerAnimation,
                      builder: (context, child) {
                        return FractionallySizedBox(
                          widthFactor: currentProgress.clamp(0.0, 1.0),
                          child: Container(
                            height: widget.height,
                            decoration: BoxDecoration(
                              borderRadius: widget.borderRadius ?? BorderRadius.circular(widget.height / 2),
                            ),
                            child: ClipRRect(
                              borderRadius: widget.borderRadius ?? BorderRadius.circular(widget.height / 2),
                              child: Transform.translate(
                                offset: Offset(_shimmerAnimation.value * widget.height * 4, 0),
                                child: Container(
                                  width: widget.height * 2,
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      colors: [
                                        Colors.transparent,
                                        Colors.white.withOpacity(0.4),
                                        Colors.transparent,
                                      ],
                                      stops: const [0.0, 0.5, 1.0],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}

/// Circular progress indicator with hydrated theme
class HydratedCircularProgress extends StatefulWidget {
  final double progress; // 0.0 to 1.0
  final double size;
  final double strokeWidth;
  final Color? backgroundColor;
  final Gradient? progressGradient;
  final Widget? child;
  final bool showPercentage;
  final bool animated;
  final Duration animationDuration;

  const HydratedCircularProgress({
    Key? key,
    required this.progress,
    this.size = 80.0,
    this.strokeWidth = 8.0,
    this.backgroundColor,
    this.progressGradient,
    this.child,
    this.showPercentage = true,
    this.animated = true,
    this.animationDuration = const Duration(milliseconds: 1000),
  }) : super(key: key);

  @override
  State<HydratedCircularProgress> createState() => _HydratedCircularProgressState();
}

class _HydratedCircularProgressState extends State<HydratedCircularProgress>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;

  @override
  void initState() {
    super.initState();
    
    _progressController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOutCubic,
    ));

    if (widget.animated) {
      _progressController.forward();
    }
  }

  @override
  void didUpdateWidget(HydratedCircularProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: _progressAnimation.value,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeOutCubic,
      ));
      
      if (widget.animated) {
        _progressController.reset();
        _progressController.forward();
      }
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    final backgroundColor = widget.backgroundColor ?? 
        (isDark ? HydratedTheme.progressBackgroundDark : HydratedTheme.progressBackground);
    
    final progressGradient = widget.progressGradient ?? 
        (widget.progress >= 1.0 ? HydratedTheme.completedGradient : HydratedTheme.primaryGradient);

    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: AnimatedBuilder(
        animation: widget.animated ? _progressAnimation : AlwaysStoppedAnimation(widget.progress),
        builder: (context, child) {
          final currentProgress = widget.animated ? _progressAnimation.value : widget.progress;
          
          return Stack(
            alignment: Alignment.center,
            children: [
              // Background circle
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: _CircularProgressPainter(
                  progress: 1.0,
                  strokeWidth: widget.strokeWidth,
                  color: backgroundColor,
                  gradient: null,
                ),
              ),
              // Progress circle
              CustomPaint(
                size: Size(widget.size, widget.size),
                painter: _CircularProgressPainter(
                  progress: currentProgress,
                  strokeWidth: widget.strokeWidth,
                  color: null,
                  gradient: progressGradient,
                ),
              ),
              // Content
              if (widget.child != null)
                widget.child!
              else if (widget.showPercentage)
                Text(
                  '${(currentProgress * 100).round()}%',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: HydratedTheme.primaryBlue,
                  ),
                ),
            ],
          );
        },
      ),
    );
  }
}

/// Custom painter for circular progress
class _CircularProgressPainter extends CustomPainter {
  final double progress;
  final double strokeWidth;
  final Color? color;
  final Gradient? gradient;

  _CircularProgressPainter({
    required this.progress,
    required this.strokeWidth,
    this.color,
    this.gradient,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width - strokeWidth) / 2;
    
    final paint = Paint()
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    if (gradient != null) {
      paint.shader = gradient!.createShader(
        Rect.fromCircle(center: center, radius: radius),
      );
    } else if (color != null) {
      paint.color = color!;
    }

    const startAngle = -math.pi / 2;
    final sweepAngle = 2 * math.pi * progress;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      startAngle,
      sweepAngle,
      false,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// Progress stats card with hydrated theme
class HydratedProgressCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final double? progress;
  final VoidCallback? onTap;

  const HydratedProgressCard({
    Key? key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    this.progress,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () {
        if (onTap != null) {
          HapticFeedback.lightImpact();
          onTap!();
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: isDark 
              ? LinearGradient(
                  colors: [
                    HydratedTheme.darkBlue.withOpacity(0.3),
                    HydratedTheme.primaryBlue.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [
                    HydratedTheme.progressBackground,
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: HydratedTheme.lightBlue.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: HydratedTheme.lightBlue.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    gradient: HydratedTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: HydratedTheme.primaryBlue,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        value,
                        style: theme.textTheme.headlineSmall?.copyWith(
                          color: HydratedTheme.darkBlue,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 8),
              Text(
                subtitle!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: HydratedTheme.primaryBlue.withOpacity(0.7),
                ),
              ),
            ],
            if (progress != null) ...[
              const SizedBox(height: 12),
              HydratedProgressBar(
                progress: progress!,
                height: 4,
                animated: true,
                showShimmer: true,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
