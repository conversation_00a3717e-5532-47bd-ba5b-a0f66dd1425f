import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../design_system/kft_design_system.dart';

/// A modern, minimal app bar that changes appearance on scroll
/// - Transparent at the top of the page
/// - Blurred background when scrolled
/// - Reduced padding and more elegant typography
class ScrollAwareAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final String? subtitle;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final ScrollController scrollController;
  final double scrollThreshold;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final TextStyle? titleTextStyle;
  final TextStyle? subtitleTextStyle;
  final double elevation;
  final double height;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const ScrollAwareAppBar({
    Key? key,
    required this.title,
    this.subtitle,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = false,
    required this.scrollController,
    this.scrollThreshold = 20.0,
    this.backgroundColor,
    this.foregroundColor,
    this.titleTextStyle,
    this.subtitleTextStyle,
    this.elevation = 0,
    this.height = 72.0,
    this.showBackButton = false,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Default colors
    final defaultBgColor = theme.colorScheme.primary;
    final defaultFgColor = Colors.white;

    // Use provided colors or defaults
    final bgColor = backgroundColor ?? defaultBgColor;
    final fgColor = foregroundColor ?? defaultFgColor;

    return AnimatedBuilder(
      animation: scrollController,
      builder: (context, child) {
        // Calculate scroll percentage for animations
        final scrollOffset = scrollController.hasClients ? scrollController.offset : 0;
        final scrollPercentage = (scrollOffset / scrollThreshold).clamp(0.0, 1.0);

        // Calculate background opacity based on scroll
        final backgroundOpacity = scrollPercentage * 0.85;

        // Calculate elevation based on scroll
        final currentElevation = scrollPercentage * elevation;

        // Calculate padding reduction based on scroll
        final paddingReduction = scrollPercentage * 8.0;
        final topPadding = 10.0 - (paddingReduction / 2);
        final bottomPadding = 10.0 - (paddingReduction / 2);

        // Calculate font size reduction based on scroll
        final titleFontSizeReduction = scrollPercentage * 4.0;
        final subtitleFontSizeReduction = scrollPercentage * 2.0;

        // Determine system overlay style based on background color
        final overlayStyle = bgColor.computeLuminance() > 0.5
            ? SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent)
            : SystemUiOverlayStyle.light.copyWith(statusBarColor: Colors.transparent);

        return AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          systemOverlayStyle: overlayStyle,
          automaticallyImplyLeading: automaticallyImplyLeading,
          leading: _getLeadingWidget(context),
          actions: actions,
          titleSpacing: 20 - (paddingReduction * 0.5),
          flexibleSpace: ClipRect(
            child: Stack(
              children: [
                // Blurred background that appears on scroll
                BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 15 * scrollPercentage,
                    sigmaY: 15 * scrollPercentage,
                  ),
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          bgColor.withOpacity(backgroundOpacity),
                          bgColor.withOpacity(backgroundOpacity * 0.95),
                        ],
                      ),
                      boxShadow: scrollPercentage > 0.1
                          ? [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.05 * scrollPercentage),
                                blurRadius: 8 * scrollPercentage,
                                offset: Offset(0, 2 * scrollPercentage),
                              ),
                            ]
                          : null,
                    ),
                  ),
                ),
              ],
            ),
          ),
          title: Padding(
            padding: EdgeInsets.symmetric(
              vertical: scrollPercentage > 0.5 ? 8 : topPadding,
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (subtitle != null) ...[
                    Text(
                      subtitle!,
                      style: (subtitleTextStyle ??
                        TextStyle(
                          fontSize: 14 - subtitleFontSizeReduction,
                          fontWeight: FontWeight.w300,
                          color: fgColor.withOpacity(0.8),
                          letterSpacing: 0.5,
                          fontFamily: 'Poppins',
                          height: 1.1,
                        )
                      ),
                    ),
                    SizedBox(height: 2 - (scrollPercentage * 1)),
                  ],
                  Text(
                    title,
                    style: (titleTextStyle ??
                      TextStyle(
                        fontSize: 26 - titleFontSizeReduction,
                        fontWeight: FontWeight.w300, // Thinner font weight
                        color: fgColor,
                        letterSpacing: -0.5,
                        fontFamily: 'Poppins',
                        height: 1.1,
                      )
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget? _getLeadingWidget(BuildContext context) {
    if (leading != null) {
      return leading;
    }

    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios_new_rounded, size: 20),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }

    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(height);
}
