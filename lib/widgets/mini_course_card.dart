import 'package:flutter/material.dart';
import '../utils/image_utils.dart';
import '../theme/app_theme.dart';

class MiniCourseCard extends StatelessWidget {
  final String title;
  final String? imageUrl;
  final VoidCallback onTap;

  const MiniCourseCard({
    Key? key,
    required this.title,
    this.imageUrl,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        final cardHeight = cardWidth * 0.35;
        return Card(
          elevation: 0.5,
          margin: EdgeInsets.symmetric(
            horizontal: cardWidth * 0.01,
            vertical: cardWidth * 0.01,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppTheme.borderRadius12),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(AppTheme.borderRadius12),
            onTap: onTap,
            child: Container(
              height: cardHeight,
              padding: EdgeInsets.all(cardWidth * 0.03),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  if (imageUrl != null)
                    ClipRRect(
                      borderRadius: BorderRadius.circular(AppTheme.borderRadius8),
                      child: Image.network(
                        getFullImageUrl(imageUrl),
                        width: cardHeight,
                        height: cardHeight,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: cardHeight,
                            height: cardHeight,
                            color: Colors.grey[300],
                          );
                        },
                      ),
                    ),
                  if (imageUrl != null)
                    SizedBox(width: cardWidth * 0.04),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        fontSize: cardWidth * 0.04,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
} 