import 'package:flutter/material.dart';

class SkeletonWidget extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;

  const SkeletonWidget({
    Key? key,
    this.width = double.infinity,
    this.height = 20,
    this.borderRadius = 4,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}

class SkeletonContainer extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final EdgeInsets margin;

  const SkeletonContainer({
    Key? key,
    this.width = double.infinity,
    this.height = 20,
    this.borderRadius = 4,
    this.margin = EdgeInsets.zero,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}

class SkeletonCourseCard extends StatelessWidget {
  const SkeletonCourseCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey[300],
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}

class SkeletonProfile extends StatelessWidget {
  const SkeletonProfile({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SkeletonContainer(
          width: 100,
          height: 100,
          borderRadius: 50,
          margin: const EdgeInsets.only(bottom: 16),
        ),
        SkeletonContainer(
          width: 150,
          height: 24,
          margin: const EdgeInsets.only(bottom: 8),
        ),
        SkeletonContainer(
          width: 100,
          height: 16,
        ),
      ],
    );
  }
} 