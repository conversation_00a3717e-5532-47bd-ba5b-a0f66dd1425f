import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../design_system/kft_design_system.dart';

/// A sliver header that expands and collapses on scroll
/// - Stays visible during scrolling (pinned)
/// - Expands to show more content when at the top
/// - Collapses to a minimal header when scrolled
class SliverExpandableHeader extends StatelessWidget {
  /// The title to display in the header
  final String title;
  
  /// Optional subtitle to display below the title
  final String? subtitle;
  
  /// Optional action buttons to display in the header
  final List<Widget>? actions;
  
  /// Height of the header when expanded
  final double expandedHeight;
  
  /// Height of the header when collapsed
  final double collapsedHeight;
  
  /// Whether to show a back button
  final bool showBackButton;
  
  /// Callback when back button is pressed
  final VoidCallback? onBackPressed;
  
  /// Optional leading widget to display instead of the back button
  final Widget? leading;
  
  /// Optional bottom widget to display at the bottom of the expanded header
  final Widget? bottomWidget;
  
  /// Whether the app bar should remain visible during scrolling
  final bool pinned;
  
  /// Background color for the header
  final Color? backgroundColor;
  
  /// Text style for the title in expanded state
  final TextStyle? expandedTitleStyle;
  
  /// Text style for the subtitle in expanded state
  final TextStyle? expandedSubtitleStyle;
  
  /// Text style for the title in collapsed state
  final TextStyle? collapsedTitleStyle;

  const SliverExpandableHeader({
    Key? key,
    required this.title,
    this.subtitle,
    this.actions,
    this.expandedHeight = 180.0,
    this.collapsedHeight = 72.0,
    this.showBackButton = false,
    this.onBackPressed,
    this.leading,
    this.bottomWidget,
    this.pinned = true,
    this.backgroundColor,
    this.expandedTitleStyle,
    this.expandedSubtitleStyle,
    this.collapsedTitleStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Default colors
    final bgColor = backgroundColor ?? theme.colorScheme.primary;
    final textColor = isDark ? KFTDesignSystem.getTextPrimaryColor(context) : Colors.white;
    
    // Default text styles
    final defaultExpandedTitleStyle = TextStyle(
      color: textColor,
      fontSize: 28,
      fontWeight: FontWeight.w200,
      letterSpacing: -0.5,
      height: 1.1,
    );
    
    final defaultExpandedSubtitleStyle = TextStyle(
      color: textColor.withOpacity(0.85),
      fontSize: 16,
      fontWeight: FontWeight.w200,
      letterSpacing: 0.5,
      height: 1.1,
    );
    
    final defaultCollapsedTitleStyle = TextStyle(
      color: textColor,
      fontSize: 20,
      fontWeight: FontWeight.w300,
      letterSpacing: -0.5,
      height: 1.1,
    );

    return SliverAppBar(
      expandedHeight: expandedHeight,
      collapsedHeight: collapsedHeight,
      pinned: pinned,
      elevation: 0,
      backgroundColor: Colors.transparent,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      leading: _getLeadingWidget(context, textColor),
      actions: actions,
      flexibleSpace: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          // Calculate scroll percentage
          final expandedHeight = this.expandedHeight;
          final scrollPercentage = (1.0 - (constraints.maxHeight / expandedHeight)).clamp(0.0, 1.0);
          
          // Calculate background opacity based on scroll
          final backgroundOpacity = 0.15 + (scrollPercentage * 0.85);
          
          return FlexibleSpaceBar(
            titlePadding: EdgeInsets.zero,
            title: AnimatedOpacity(
              duration: const Duration(milliseconds: 300),
              opacity: scrollPercentage > 0.5 ? 1.0 : 0.0,
              child: Padding(
                padding: const EdgeInsets.only(left: 20),
                child: Text(
                  title,
                  style: collapsedTitleStyle ?? defaultCollapsedTitleStyle,
                ),
              ),
            ),
            background: Stack(
              children: [
                // Gradient background
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        bgColor,
                        bgColor.withOpacity(0.9),
                      ],
                    ),
                  ),
                ),
                
                // Decorative elements
                Positioned(
                  top: -20,
                  right: -20,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Positioned(
                  bottom: -50,
                  left: -30,
                  child: Container(
                    width: 180,
                    height: 180,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.08),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                
                // Content
                SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Top row with back button/leading widget and actions
                        SizedBox(height: 40),
                        
                        const Spacer(),
                        
                        // Title and subtitle
                        Text(
                          title,
                          style: expandedTitleStyle ?? defaultExpandedTitleStyle,
                        ),
                        if (subtitle != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            subtitle!,
                            style: expandedSubtitleStyle ?? defaultExpandedSubtitleStyle,
                          ),
                        ],
                        
                        if (bottomWidget != null) ...[
                          const SizedBox(height: 16),
                          bottomWidget!,
                        ],
                      ],
                    ),
                  ),
                ),
                
                // Blurred overlay that appears when scrolling
                Positioned.fill(
                  child: IgnorePointer(
                    child: AnimatedOpacity(
                      opacity: scrollPercentage,
                      duration: const Duration(milliseconds: 200),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(
                          sigmaX: 10 * scrollPercentage,
                          sigmaY: 10 * scrollPercentage,
                        ),
                        child: Container(
                          color: bgColor.withOpacity(backgroundOpacity * 0.5),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget? _getLeadingWidget(BuildContext context, Color textColor) {
    if (leading != null) {
      return leading;
    }

    if (showBackButton) {
      return IconButton(
        icon: Icon(
          Icons.arrow_back_ios_rounded,
          color: textColor,
          size: 20,
        ),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }

    return null;
  }
}
