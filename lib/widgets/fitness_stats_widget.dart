import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import 'package:intl/intl.dart';

class FitnessStatsWidget extends StatelessWidget {
  final UserProfile? userProfile;
  final VoidCallback? onUpdateWeight;

  const FitnessStatsWidget({
    Key? key,
    required this.userProfile,
    this.onUpdateWeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Calculate stats
    final totalWorkouts = userProfile?.totalWorkouts ?? 0;
    final totalMinutes = userProfile?.totalWorkoutTime ?? 0;
    final currentStreak = userProfile?.currentStreak ?? 0;
    final weight = userProfile?.weight ?? 0;
    final bmi = userProfile?.currentBMI ?? 0;
    final bmiCategory = userProfile?.bmiCategory ?? 'Unknown';
    
    // Get BMI category color
    Color categoryColor;
    if (bmi < 18.5) {
      categoryColor = Colors.blue;
    } else if (bmi < 25) {
      categoryColor = Colors.green;
    } else if (bmi < 30) {
      categoryColor = Colors.orange;
    } else {
      categoryColor = Colors.red;
    }
    
    // Calculate calories burned (estimated based on workout time)
    // Average calorie burn rate: ~7 calories per minute for moderate exercise
    final caloriesBurned = totalMinutes * 7;
    
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.fitness_center, color: theme.colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  'Fitness Stats',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (onUpdateWeight != null)
                  TextButton.icon(
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Update'),
                    onPressed: onUpdateWeight,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      minimumSize: Size.zero,
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Key metrics in a grid
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              childAspectRatio: 1.5,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              children: [
                // Weight card
                _buildMetricCard(
                  context,
                  'Weight',
                  '${weight.toStringAsFixed(1)} kg',
                  Icons.monitor_weight,
                  Colors.purple,
                  subtitle: _getLastUpdatedText(),
                ),
                
                // BMI card
                _buildMetricCard(
                  context,
                  'BMI',
                  bmi.toStringAsFixed(1),
                  Icons.health_and_safety,
                  categoryColor,
                  subtitle: bmiCategory,
                ),
                
                // Calories burned card
                _buildMetricCard(
                  context,
                  'Calories Burned',
                  '${NumberFormat.compact().format(caloriesBurned)}',
                  Icons.local_fire_department,
                  Colors.orange,
                  subtitle: 'Estimated total',
                ),
                
                // Workout frequency card
                _buildMetricCard(
                  context,
                  'Workout Streak',
                  '$currentStreak days',
                  Icons.calendar_today,
                  Colors.blue,
                  subtitle: 'Current streak',
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            
            // Summary row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildSummaryItem(
                  context,
                  '$totalWorkouts',
                  'Total Workouts',
                  Icons.fitness_center,
                ),
                _buildSummaryItem(
                  context,
                  _formatDuration(totalMinutes),
                  'Workout Time',
                  Icons.timer,
                ),
                _buildSummaryItem(
                  context,
                  userProfile?.averageWorkoutTime.toStringAsFixed(0) ?? '0',
                  'Avg. Minutes',
                  Icons.show_chart,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildMetricCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
    {String? subtitle}
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade100,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade700 : Colors.grey.shade300,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 18),
                const SizedBox(width: 6),
                Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: isDarkMode ? Colors.grey.shade300 : Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            Text(
              value,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            if (subtitle != null)
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
                ),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildSummaryItem(
    BuildContext context,
    String value,
    String label,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.primary,
            size: 16,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
            fontSize: 10,
          ),
        ),
      ],
    );
  }
  
  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    
    if (hours > 0) {
      return '$hours hr ${mins > 0 ? '$mins min' : ''}';
    } else {
      return '$mins min';
    }
  }
  
  String _getLastUpdatedText() {
    if (userProfile == null || userProfile!.bmiHistory.isEmpty) {
      return 'Not recorded';
    }
    
    final latestRecord = userProfile!.bmiHistory.last;
    return 'Updated ${DateFormat('MMM d').format(latestRecord.date)}';
  }
}
