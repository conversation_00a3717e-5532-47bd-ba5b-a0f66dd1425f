import 'package:flutter/material.dart';
import '../design_system/kft_design_system.dart';

/// A widget that generates a default avatar with user initials
/// Replaces profile picture functionality with a consistent letter-based avatar
class DefaultAvatarWidget extends StatelessWidget {
  final String name;
  final double radius;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;

  const DefaultAvatarWidget({
    Key? key,
    required this.name,
    this.radius = 20,
    this.backgroundColor,
    this.foregroundColor,
    this.fontSize,
    this.fontWeight,
    this.onTap,
    this.showBorder = false,
    this.borderColor,
    this.borderWidth = 2.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    
    // Generate initials from name
    final initials = _generateInitials(name);
    
    // Generate background color based on name for consistency
    final avatarBackgroundColor = backgroundColor ?? _generateBackgroundColor(name, isDarkMode);
    
    // Determine text color for good contrast
    final textColor = foregroundColor ?? _getContrastColor(avatarBackgroundColor);
    
    // Calculate font size based on radius
    final calculatedFontSize = fontSize ?? (radius * 0.6);

    Widget avatar = CircleAvatar(
      radius: radius,
      backgroundColor: avatarBackgroundColor,
      child: Text(
        initials,
        style: TextStyle(
          color: textColor,
          fontSize: calculatedFontSize,
          fontWeight: fontWeight ?? FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );

    // Add border if requested
    if (showBorder) {
      avatar = Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          border: Border.all(
            color: borderColor ?? theme.colorScheme.outline,
            width: borderWidth,
          ),
        ),
        child: avatar,
      );
    }

    // Add tap functionality if provided
    if (onTap != null) {
      avatar = GestureDetector(
        onTap: onTap,
        child: avatar,
      );
    }

    return avatar;
  }

  /// Generate initials from a name
  /// Returns first letter of first name and first letter of last name
  /// Falls back to 'U' for empty names
  String _generateInitials(String name) {
    if (name.isEmpty) {
      return 'U';
    }

    // Clean the name and split into words
    final cleanName = name.trim().replaceAll(RegExp(r'[^a-zA-Z\s]'), '');
    final words = cleanName.split(RegExp(r'\s+'));
    
    if (words.isEmpty || words.first.isEmpty) {
      return 'U';
    }

    // Get first letter of first word
    String initials = words.first[0].toUpperCase();
    
    // Add first letter of last word if available and different from first
    if (words.length > 1 && words.last.isNotEmpty) {
      final lastInitial = words.last[0].toUpperCase();
      if (lastInitial != initials) {
        initials += lastInitial;
      }
    }

    return initials;
  }

  /// Generate a consistent background color based on the name
  /// Uses a hash of the name to ensure the same name always gets the same color
  Color _generateBackgroundColor(String name, bool isDarkMode) {
    if (name.isEmpty) {
      return isDarkMode 
          ? KFTDesignSystem.primaryColor.withOpacity(0.8)
          : KFTDesignSystem.primaryColor;
    }

    // Create a simple hash from the name
    int hash = 0;
    for (int i = 0; i < name.length; i++) {
      hash = name.codeUnitAt(i) + ((hash << 5) - hash);
    }
    hash = hash.abs();

    // Define a palette of colors that work well with the app design
    final colors = isDarkMode ? _darkModeColors : _lightModeColors;
    
    // Use hash to select a color from the palette
    final colorIndex = hash % colors.length;
    return colors[colorIndex];
  }

  /// Light mode color palette
  static const List<Color> _lightModeColors = [
    KFTDesignSystem.primaryColor,
    Color(0xFF6366F1), // Indigo
    Color(0xFF8B5CF6), // Purple
    Color(0xFF06B6D4), // Cyan
    Color(0xFF10B981), // Emerald
    Color(0xFFF59E0B), // Amber
    Color(0xFFEF4444), // Red
    Color(0xFFEC4899), // Pink
    Color(0xFF84CC16), // Lime
    Color(0xFF3B82F6), // Blue
  ];

  /// Dark mode color palette (slightly muted versions)
  static const List<Color> _darkModeColors = [
    Color(0xFF4F46E5), // Darker indigo
    Color(0xFF7C3AED), // Darker purple
    Color(0xFF0891B2), // Darker cyan
    Color(0xFF059669), // Darker emerald
    Color(0xFFD97706), // Darker amber
    Color(0xFFDC2626), // Darker red
    Color(0xFFDB2777), // Darker pink
    Color(0xFF65A30D), // Darker lime
    Color(0xFF2563EB), // Darker blue
    Color(0xFF7C2D12), // Brown
  ];

  /// Get contrasting text color for the given background
  Color _getContrastColor(Color backgroundColor) {
    // Calculate luminance to determine if we need light or dark text
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? Colors.black87 : Colors.white;
  }
}

/// Extension to provide easy access to default avatar generation
extension UserProfileAvatar on String {
  /// Generate a default avatar widget for this name
  Widget toDefaultAvatar({
    double radius = 20,
    Color? backgroundColor,
    Color? foregroundColor,
    VoidCallback? onTap,
    bool showBorder = false,
  }) {
    return DefaultAvatarWidget(
      name: this,
      radius: radius,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      onTap: onTap,
      showBorder: showBorder,
    );
  }
}
