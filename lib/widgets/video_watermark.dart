import 'package:flutter/material.dart';
import '../models/user_profile.dart';

/// A watermark widget that displays user information over video content
/// for piracy prevention
class VideoWatermark extends StatefulWidget {
  final UserProfile? userProfile;
  final bool isVisible;
  final WatermarkPosition position;
  final double opacity;
  final bool isFullscreen;

  const VideoWatermark({
    Key? key,
    required this.userProfile,
    this.isVisible = true,
    this.position = WatermarkPosition.bottomRight,
    this.opacity = 0.25,
    this.isFullscreen = false,
  }) : super(key: key);

  @override
  State<VideoWatermark> createState() => _VideoWatermarkState();
}

class _VideoWatermarkState extends State<VideoWatermark>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _moveController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _positionAnimation;

  @override
  void initState() {
    super.initState();

    // Fade animation for subtle appearance/disappearance
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Movement animation for dynamic positioning (anti-screenshot)
    _moveController = AnimationController(
      duration: const Duration(seconds: 30),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: widget.opacity,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    // Subtle movement to prevent easy removal
    _positionAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(0.1, 0.1),
    ).animate(CurvedAnimation(
      parent: _moveController,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _fadeController.forward();
    }

    // Start subtle movement animation
    _moveController.repeat(reverse: true);
  }

  @override
  void didUpdateWidget(VideoWatermark oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _fadeController.forward();
      } else {
        _fadeController.reverse();
      }
    }
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _moveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.userProfile == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: Listenable.merge([_fadeAnimation, _positionAnimation]),
      builder: (context, child) {
        final positionProps = widget.position.getPositionProperties(context, widget.isFullscreen);
        return Positioned(
          top: positionProps['top'],
          left: positionProps['left'],
          right: positionProps['right'],
          bottom: positionProps['bottom'],
          child: Transform.translate(
            offset: _positionAnimation.value * 20, // Subtle movement
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: _buildWatermarkContent(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWatermarkContent() {
    final userProfile = widget.userProfile!;
    final isFullscreen = widget.isFullscreen;

    // Adjust size based on screen mode
    final fontSize = isFullscreen ? 14.0 : 12.0;
    final padding = isFullscreen ? 12.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // User name
          Text(
            userProfile.name,
            style: TextStyle(
              color: Colors.white,
              fontSize: fontSize,
              fontWeight: FontWeight.w600,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.8),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),

          // Phone number (if available)
          if (userProfile.phoneNumber.isNotEmpty) ...[
            const SizedBox(height: 2),
            Text(
              userProfile.phoneNumber,
              style: TextStyle(
                color: Colors.white.withOpacity(0.9),
                fontSize: fontSize - 1,
                fontWeight: FontWeight.w500,
                shadows: [
                  Shadow(
                    color: Colors.black.withOpacity(0.8),
                    offset: const Offset(1, 1),
                    blurRadius: 2,
                  ),
                ],
              ),
            ),
          ],

          // Timestamp for additional security
          Text(
            _getCurrentTimestamp(),
            style: TextStyle(
              color: Colors.white.withOpacity(0.7),
              fontSize: fontSize - 2,
              fontWeight: FontWeight.w400,
              shadows: [
                Shadow(
                  color: Colors.black.withOpacity(0.8),
                  offset: const Offset(1, 1),
                  blurRadius: 2,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getCurrentTimestamp() {
    final now = DateTime.now();
    return '${now.day}/${now.month}/${now.year} ${now.hour}:${now.minute.toString().padLeft(2, '0')}';
  }
}

/// Enum for watermark positioning
enum WatermarkPosition {
  topLeft,
  topRight,
  bottomLeft,
  bottomRight,
  center,
}

extension WatermarkPositionExtension on WatermarkPosition {
  Map<String, double?> getPositionProperties(BuildContext context, bool isFullscreen) {
    final screenSize = MediaQuery.of(context).size;
    final margin = isFullscreen ? 24.0 : 16.0;

    switch (this) {
      case WatermarkPosition.topLeft:
        return {
          'top': margin,
          'left': margin,
          'right': null,
          'bottom': null,
        };
      case WatermarkPosition.topRight:
        return {
          'top': margin,
          'right': margin,
          'left': null,
          'bottom': null,
        };
      case WatermarkPosition.bottomLeft:
        return {
          'bottom': margin,
          'left': margin,
          'top': null,
          'right': null,
        };
      case WatermarkPosition.bottomRight:
        return {
          'bottom': margin,
          'right': margin,
          'top': null,
          'left': null,
        };
      case WatermarkPosition.center:
        return {
          'top': screenSize.height * 0.5 - 30,
          'left': screenSize.width * 0.5 - 60,
          'right': null,
          'bottom': null,
        };
    }
  }
}
