import 'package:flutter/material.dart';
import '../config/app_config.dart';

/// A button that toggles between light, dark, and system theme modes.
class ThemeToggleButton extends StatelessWidget {
  /// Whether to show the button as an icon button (true) or a regular button with text (false).
  final bool iconOnly;

  /// Optional callback to be called when the theme is changed.
  final VoidCallback? onThemeChanged;

  const ThemeToggleButton({
    Key? key,
    this.iconOnly = true,
    this.onThemeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return iconOnly ? _buildIconButton(context) : _buildTextButton(context);
  }

  Widget _buildIconButton(BuildContext context) {
    final ThemeMode currentMode = AppConfig.themeMode;
    final IconData icon = _getThemeIcon(currentMode);
    final Color iconColor = Theme.of(context).brightness == Brightness.dark
        ? Colors.white
        : Colors.white;

    return IconButton(
      icon: Icon(icon, color: iconColor),
      tooltip: _getThemeTooltip(currentMode),
      onPressed: () => _cycleThemeMode(context),
    );
  }

  Widget _buildTextButton(BuildContext context) {
    final ThemeMode currentMode = AppConfig.themeMode;
    final IconData icon = _getThemeIcon(currentMode);
    final String label = _getThemeLabel(currentMode);

    return TextButton.icon(
      icon: Icon(icon),
      label: Text(label),
      onPressed: () => _cycleThemeMode(context),
    );
  }

  IconData _getThemeIcon(ThemeMode mode) {
    return mode == ThemeMode.light ? Icons.light_mode : Icons.dark_mode;
  }

  String _getThemeLabel(ThemeMode mode) {
    return mode == ThemeMode.light ? 'Light Mode' : 'Dark Mode';
  }

  String _getThemeTooltip(ThemeMode mode) {
    return mode == ThemeMode.light ? 'Switch to Dark Mode' : 'Switch to Light Mode';
  }

  Future<void> _cycleThemeMode(BuildContext context) async {
    final ThemeMode currentMode = AppConfig.themeMode;

    // Toggle between light and dark modes only
    final ThemeMode newMode = currentMode == ThemeMode.light ? ThemeMode.dark : ThemeMode.light;

    // Save the new theme mode
    await AppConfig.saveThemeMode(newMode);

    // Show a snackbar to confirm the change
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Switched to ${_getThemeLabel(newMode)}',
            style: const TextStyle(color: Colors.white),
          ),
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    // Call the callback if provided
    onThemeChanged?.call();
  }
}
