import 'package:flutter/material.dart';
import '../design_system/kft_design_system.dart';

/// KFT Button
/// A custom button widget that follows the KFT design system
/// Supports primary, secondary, and tertiary button styles

enum KFTButtonType {
  primary,
  secondary,
  tertiary,
}

enum KFTButtonSize {
  small,
  medium,
  large,
}

class KFTButton extends StatelessWidget {
  final String label;
  final VoidCallback? onPressed;
  final KFTButtonType type;
  final KFTButtonSize size;
  final IconData? icon;
  final bool isLoading;
  final bool isFullWidth;
  final bool isDisabled;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;

  const KFTButton({
    Key? key,
    required this.label,
    this.onPressed,
    this.type = KFTButtonType.primary,
    this.size = KFTButtonSize.medium,
    this.icon,
    this.isLoading = false,
    this.isFullWidth = false,
    this.isDisabled = false,
    this.padding,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final ButtonStyle buttonStyle = _getButtonStyle(context);
    final EdgeInsets buttonPadding = padding ?? _getButtonPadding();
    final TextStyle textStyle = _getTextStyle(context);
    Widget buttonContent = _buildButtonContent(textStyle);
    final bool isEnabled = !isDisabled && !isLoading && onPressed != null;

    Widget button = _buildAnimatedButton(
      context: context,
      isEnabled: isEnabled,
      buttonStyle: buttonStyle,
      buttonContent: buttonContent,
    );

    if (isFullWidth) {
      return SizedBox(width: double.infinity, child: button);
    }
    return button;
  }

  Widget _buildAnimatedButton({
    required BuildContext context,
    required bool isEnabled,
    required ButtonStyle buttonStyle,
    required Widget buttonContent,
  }) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 120),
      tween: Tween<double>(begin: 1.0, end: 1.0),
      builder: (context, scale, child) {
        return Listener(
          onPointerDown: isEnabled
              ? (_) => (context as Element).markNeedsBuild()
              : null,
          onPointerUp: isEnabled
              ? (_) => (context as Element).markNeedsBuild()
              : null,
          child: GestureDetector(
            onTapDown: isEnabled
                ? (_) => (context as Element).markNeedsBuild()
                : null,
            onTapUp: isEnabled
                ? (_) => (context as Element).markNeedsBuild()
                : null,
            onTapCancel: isEnabled
                ? () => (context as Element).markNeedsBuild()
                : null,
            child: AnimatedScale(
              scale: isEnabled ? scale : 1.0,
              duration: const Duration(milliseconds: 80),
              curve: Curves.easeOut,
              child: _buildButtonByType(
                isEnabled: isEnabled,
                buttonStyle: buttonStyle,
                buttonContent: buttonContent,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildButtonByType({
    required bool isEnabled,
    required ButtonStyle buttonStyle,
    required Widget buttonContent,
  }) {
    switch (type) {
      case KFTButtonType.primary:
        return Builder(
          builder: (context) {
            final theme = Theme.of(context);
            final primaryColor = theme.colorScheme.primary;
            final onPrimaryColor = theme.colorScheme.onPrimary;

            return Material(
              color: Colors.transparent,
              elevation: 0,
              child: InkWell(
                borderRadius: borderRadius ?? BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
                splashColor: primaryColor.withOpacity(0.08),
                highlightColor: primaryColor.withOpacity(0.04),
                onTap: isEnabled ? onPressed : null,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 120),
                  curve: Curves.easeOut,
                  decoration: BoxDecoration(
                    color: isEnabled ? primaryColor : primaryColor.withOpacity(0.5),
                    borderRadius: borderRadius ?? BorderRadius.circular(KFTDesignSystem.borderRadiusMd),
                    boxShadow: isEnabled ? [
                      BoxShadow(
                        color: primaryColor.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                        spreadRadius: 0,
                      ),
                    ] : null,
                  ),
                  padding: _getButtonPadding(),
                  child: Center(child: buttonContent),
                ),
              ),
            );
          }
        );
      case KFTButtonType.secondary:
        return OutlinedButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonContent,
        );
      case KFTButtonType.tertiary:
        return TextButton(
          onPressed: isEnabled ? onPressed : null,
          style: buttonStyle,
          child: buttonContent,
        );
    }
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final onPrimaryColor = theme.colorScheme.onPrimary;

    final BorderRadius buttonBorderRadius = borderRadius ??
        BorderRadius.circular(KFTDesignSystem.borderRadiusMd);

    switch (type) {
      case KFTButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: onPrimaryColor,
          disabledBackgroundColor: primaryColor.withOpacity(0.5),
          disabledForegroundColor: onPrimaryColor.withOpacity(0.7),
          padding: _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: buttonBorderRadius,
          ),
          elevation: 0,
        );
      case KFTButtonType.secondary:
        return OutlinedButton.styleFrom(
          foregroundColor: primaryColor,
          disabledForegroundColor: primaryColor.withOpacity(0.5),
          padding: _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: buttonBorderRadius,
          ),
          side: BorderSide(
            color: isDisabled
                ? primaryColor.withOpacity(0.5)
                : primaryColor,
            width: 1.5,
          ),
        );
      case KFTButtonType.tertiary:
        return TextButton.styleFrom(
          foregroundColor: primaryColor,
          disabledForegroundColor: primaryColor.withOpacity(0.5),
          padding: _getButtonPadding(),
          shape: RoundedRectangleBorder(
            borderRadius: buttonBorderRadius,
          ),
        );
    }
  }

  EdgeInsets _getButtonPadding() {
    switch (size) {
      case KFTButtonSize.small:
        return const EdgeInsets.symmetric(
          horizontal: KFTDesignSystem.spacingMd,
          vertical: KFTDesignSystem.spacingSm,
        );
      case KFTButtonSize.medium:
        return const EdgeInsets.symmetric(
          horizontal: KFTDesignSystem.spacingLg,
          vertical: KFTDesignSystem.spacingMd - 4, // 12px for pixel-perfect height
        );
      case KFTButtonSize.large:
        return const EdgeInsets.symmetric(
          horizontal: KFTDesignSystem.spacingXl,
          vertical: KFTDesignSystem.spacingMd, // 16px
        );
    }
  }

  TextStyle _getTextStyle(BuildContext context) {
    final theme = Theme.of(context);
    final Color textColor = type == KFTButtonType.primary
        ? theme.colorScheme.onPrimary
        : type == KFTButtonType.secondary
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface;

    switch (size) {
      case KFTButtonSize.small:
        return KFTDesignSystem.labelMedium.copyWith(
          color: textColor,
          fontWeight: KFTDesignSystem.fontWeightSemiBold,
          letterSpacing: 0.2,
        );
      case KFTButtonSize.medium:
        return KFTDesignSystem.labelLarge.copyWith(
          color: textColor,
          fontWeight: KFTDesignSystem.fontWeightSemiBold,
          letterSpacing: 0.2,
        );
      case KFTButtonSize.large:
        return KFTDesignSystem.titleMedium.copyWith(
          color: textColor,
          fontWeight: KFTDesignSystem.fontWeightSemiBold,
          letterSpacing: 0.2,
        );
    }
  }

  Widget _buildButtonContent(TextStyle textStyle) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        final primaryColor = theme.colorScheme.primary;
        final onPrimaryColor = theme.colorScheme.onPrimary;

        if (isLoading) {
          return SizedBox(
            height: size == KFTButtonSize.small ? 16 : 20,
            width: size == KFTButtonSize.small ? 16 : 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                type == KFTButtonType.primary ? onPrimaryColor : primaryColor,
              ),
            ),
          );
        }

        if (icon != null) {
          return Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: size == KFTButtonSize.small ? 16 : 20,
                color: textStyle.color,
              ),
              SizedBox(width: KFTDesignSystem.spacingSm),
              Text(label, style: textStyle),
            ],
          );
        }

        return Text(label, style: textStyle);
      }
    );
  }
}
