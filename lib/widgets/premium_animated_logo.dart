import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PremiumAnimated<PERSON>ogo extends StatefulWidget {
  final double height;
  final String title;
  final String subtitle;
  final bool enableInteraction;
  final VoidCallback? onTap;
  final bool enableAnimations;

  const PremiumAnimatedLogo({
    super.key,
    this.height = 110,
    this.title = 'KFT',
    this.subtitle = 'Martial Arts & Fitness',
    this.enableInteraction = true,
    this.onTap,
    this.enableAnimations = true,
  });

  @override
  State<PremiumAnimatedLogo> createState() => _PremiumAnimatedLogoState();
}

class _PremiumAnimatedLogoState extends State<PremiumAnimatedLogo>
    with TickerProviderStateMixin {

  // Animation Controllers
  late AnimationController _entryController;
  late AnimationController _interactionController;
  AnimationController? _subtleBreathingController;

  // Animations
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _interactionAnimation;
  Animation<double>? _subtleBreathingAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    // Entry Animation (600ms - faster and more professional)
    _entryController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Interaction Animation (150ms - snappy response)
    _interactionController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    // Entry Animations - smooth and professional
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _entryController,
      curve: const Interval(0.0, 0.7, curve: Curves.easeOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _entryController,
      curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
    ));

    // Interaction Animation - subtle scale
    _interactionAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _interactionController,
      curve: Curves.easeInOut,
    ));

    // Optional subtle breathing animation (only if enabled)
    if (widget.enableAnimations) {
      _subtleBreathingController = AnimationController(
        duration: const Duration(milliseconds: 4000),
        vsync: this,
      );

      _subtleBreathingAnimation = Tween<double>(
        begin: 1.0,
        end: 1.01, // Very subtle breathing effect
      ).animate(CurvedAnimation(
        parent: _subtleBreathingController!,
        curve: Curves.easeInOut,
      ));
    }
  }

  void _startAnimations() {
    // Start entry animation
    _entryController.forward();

    // Start subtle breathing animation if enabled
    if (widget.enableAnimations && _subtleBreathingController != null) {
      _entryController.addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          _subtleBreathingController!.repeat(reverse: true);
        }
      });
    }
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.enableInteraction) return;

    setState(() {
      _isPressed = true;
    });

    _interactionController.forward();

    // Subtle haptic feedback
    HapticFeedback.selectionClick();
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.enableInteraction) return;

    setState(() {
      _isPressed = false;
    });

    _interactionController.reverse();

    if (widget.onTap != null) {
      widget.onTap!();
    }
  }

  void _handleTapCancel() {
    if (!widget.enableInteraction) return;

    setState(() {
      _isPressed = false;
    });

    _interactionController.reverse();
  }

  @override
  void dispose() {
    _entryController.dispose();
    _interactionController.dispose();
    _subtleBreathingController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    // Build list of animations to listen to
    final List<Listenable> animations = [
      _fadeAnimation,
      _scaleAnimation,
      _interactionAnimation,
    ];

    // Add breathing animation if enabled
    if (_subtleBreathingAnimation != null) {
      animations.add(_subtleBreathingAnimation!);
    }

    return AnimatedBuilder(
      animation: Listenable.merge(animations),
      builder: (context, child) {
        // Calculate scale with optional breathing effect
        double scale = _scaleAnimation.value * _interactionAnimation.value;
        if (_subtleBreathingAnimation != null) {
          scale *= _subtleBreathingAnimation!.value;
        }

        return FadeTransition(
          opacity: _fadeAnimation,
          child: Transform.scale(
            scale: scale,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              child: Column(
                children: [
                  // Logo with minimalistic design
                  Container(
                    height: widget.height,
                    width: widget.height,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        // Subtle shadow with theme colors
                        BoxShadow(
                          color: theme.colorScheme.primary.withOpacity(0.08),
                          blurRadius: 12,
                          spreadRadius: 0,
                          offset: const Offset(0, 4),
                        ),
                        // Standard shadow
                        BoxShadow(
                          color: isDarkMode
                              ? Colors.black.withOpacity(0.2)
                              : Colors.grey.withOpacity(0.1),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: Stack(
                        children: [
                          // Clean background with theme consistency
                          Container(
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surface,
                              border: Border.all(
                                color: theme.colorScheme.outline.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                          ),

                          // Logo image
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Image.asset(
                              'assets/images/logo.webp',
                              fit: BoxFit.contain,
                            ),
                          ),

                          // Subtle interactive overlay
                          if (_isPressed)
                            Positioned.fill(
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(20),
                                  color: theme.colorScheme.primary.withOpacity(0.05),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Title with theme consistency
                  Text(
                    widget.title,
                    style: theme.textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      letterSpacing: 1.5,
                      color: theme.colorScheme.primary,
                    ),
                  ),

                  const SizedBox(height: 6),

                  // Subtitle with theme consistency
                  Text(
                    widget.subtitle,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withOpacity(0.7),
                      fontWeight: FontWeight.w400,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
