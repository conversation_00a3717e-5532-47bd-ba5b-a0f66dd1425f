import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import '../models/user_profile.dart';
import '../widgets/optimized_profile_image.dart';

class ProgressCard extends StatelessWidget {
  final UserProfile userProfile;
  final List<String> achievements; // List of achievement titles
  final double width;
  final double height;
  final VoidCallback? onShare;

  const ProgressCard({
    Key? key,
    required this.userProfile,
    this.achievements = const [],
    this.width = 340,
    this.height = 440,
    this.onShare,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final initials = userProfile.name.isNotEmpty ? userProfile.name[0].toUpperCase() : 'U';
    final defaultAvatarUrl = 'https://ui-avatars.com/api/?name=$initials&background=cccccc&color=ffffff&size=128';
    final imageUrl = userProfile.profileImageUrl.isNotEmpty ? userProfile.profileImageUrl : defaultAvatarUrl;
    final softBg = theme.brightness == Brightness.dark
        ? const Color(0xFF23272F)
        : const Color(0xFFF7F8FA);
    final accent = theme.brightness == Brightness.dark
        ? const Color(0xFF3A3F47)
        : const Color(0xFFE3E6EA);
    final textColor = theme.brightness == Brightness.dark
        ? Colors.white
        : const Color(0xFF222B45);
    final accentColor = theme.colorScheme.primary.withOpacity(0.12);

    return Center(
      child: Container(
        width: width,
        height: height,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 28),
        decoration: BoxDecoration(
          color: softBg,
          borderRadius: BorderRadius.circular(28),
          border: Border.all(color: accent, width: 1.2),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: accent,
              child: ClipOval(
                child: OptimizedProfileImage(
                  imageUrl: imageUrl,
                  initials: initials,
                  size: 80,
                ),
              ),
            ),
            const SizedBox(height: 18),
            Text(
              userProfile.name,
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.w700,
                color: textColor,
                letterSpacing: 0.2,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              'Fitness Journey',
              style: TextStyle(
                fontSize: 15,
                color: textColor.withOpacity(0.7),
                fontWeight: FontWeight.w400,
                letterSpacing: 0.1,
              ),
            ),
            const SizedBox(height: 18),
            Divider(color: accent, thickness: 1, height: 1),
            const SizedBox(height: 18),
            _buildMilestones(context, textColor, accentColor),
            const Spacer(),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onShare,
                icon: const Icon(Icons.share, size: 20),
                label: const Text('Share My Progress'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: theme.colorScheme.primary,
                  side: BorderSide(color: accent, width: 1.2),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  textStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 15),
                  backgroundColor: accentColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMilestones(BuildContext context, Color textColor, Color accentColor) {
    if (achievements.isEmpty) {
      return Column(
        children: [
          Icon(Icons.emoji_events, color: accentColor, size: 36),
          const SizedBox(height: 8),
          Text(
            'No milestones yet',
            style: TextStyle(color: textColor.withOpacity(0.6), fontSize: 15, fontWeight: FontWeight.w400),
          ),
        ],
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          'Achievements',
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.1,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 8,
          runSpacing: 8,
          children: achievements.take(5).map((a) => _AchievementPill(
            label: a,
            accentColor: accentColor,
            textColor: textColor,
          )).toList(),
        ),
        if (achievements.length > 5)
          Padding(
            padding: const EdgeInsets.only(top: 6),
            child: Text(
              '+${achievements.length - 5} more',
              style: TextStyle(color: textColor.withOpacity(0.5), fontSize: 13),
            ),
          ),
      ],
    );
  }
}

class _AchievementPill extends StatelessWidget {
  final String label;
  final Color accentColor;
  final Color textColor;
  const _AchievementPill({
    required this.label,
    required this.accentColor,
    required this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 14, vertical: 7),
      decoration: BoxDecoration(
        color: accentColor,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: accentColor.withOpacity(0.4)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.check_circle, color: textColor.withOpacity(0.7), size: 16),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              letterSpacing: 0.1,
            ),
          ),
        ],
      ),
    );
  }
}

/// Utility to export the card as an image
Future<ui.Image> exportProgressCardToImage(GlobalKey cardKey) async {
  RenderRepaintBoundary boundary = cardKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
  return await boundary.toImage(pixelRatio: 3.0);
} 