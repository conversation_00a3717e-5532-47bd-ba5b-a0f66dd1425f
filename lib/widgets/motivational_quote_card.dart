import 'package:flutter/material.dart';
import '../models/motivational_quote.dart';
import '../design_system/kft_design_system.dart';
import 'dart:math' as math;

class MotivationalQuoteCard extends StatelessWidget {
  final MotivationalQuote quote;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final VoidCallback? onSettings;
  final bool showActions;
  final bool showCategory;
  final Color? backgroundColor;
  final Color? textColor;

  const MotivationalQuoteCard({
    Key? key,
    required this.quote,
    this.isLoading = false,
    this.onRefresh,
    this.onSettings,
    this.showActions = true,
    this.showCategory = true,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.primary.withOpacity(0.1);
    final txtColor = textColor ?? theme.colorScheme.primary;

    return Card(
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: bgColor,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: isLoading
            ? _buildLoadingState(context)
            : _buildQuoteContent(context, txtColor),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading your daily inspiration...'),
        ],
      ),
    );
  }

  Widget _buildQuoteContent(BuildContext context, Color textColor) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showCategory && quote.category != null)
          Chip(
            label: Text(
              quote.displayCategory,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            ),
            backgroundColor: Colors.white.withOpacity(0.3),
            padding: EdgeInsets.zero,
            materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
        const SizedBox(height: 8),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '"',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                height: 0.8,
              ),
            ),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                quote.quote,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontStyle: FontStyle.italic,
                  color: textColor,
                ),
                maxLines: 4,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '— ${quote.displayAuthor}',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: textColor,
              ),
            ),
            if (showActions) ...[
              Row(
                children: [
                  if (onRefresh != null)
                    IconButton(
                      icon: const Icon(Icons.refresh),
                      color: textColor,
                      onPressed: onRefresh,
                      tooltip: 'Get a new quote',
                    ),
                  if (onSettings != null)
                    IconButton(
                      icon: const Icon(Icons.settings),
                      color: textColor,
                      onPressed: onSettings,
                      tooltip: 'Quote settings',
                    ),
                ],
              ),
            ],
          ],
        ),
      ],
    );
  }
}

class AnimatedMotivationalQuoteCard extends StatefulWidget {
  final MotivationalQuote quote;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final VoidCallback? onSettings;
  final bool showActions;
  final bool showCategory;

  const AnimatedMotivationalQuoteCard({
    Key? key,
    required this.quote,
    this.isLoading = false,
    this.onRefresh,
    this.onSettings,
    this.showActions = true,
    this.showCategory = true,
  }) : super(key: key);

  @override
  _AnimatedMotivationalQuoteCardState createState() => _AnimatedMotivationalQuoteCardState();
}

class _AnimatedMotivationalQuoteCardState extends State<AnimatedMotivationalQuoteCard> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  MotivationalQuote? _displayedQuote;
  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _displayedQuote = widget.quote;

    _controller = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _controller,
        curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
      ),
    );

    _controller.addStatusListener(_handleAnimationStatus);
  }

  @override
  void didUpdateWidget(AnimatedMotivationalQuoteCard oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (widget.quote.id != oldWidget.quote.id && !_isAnimating && !widget.isLoading) {
      _isAnimating = true;
      _controller.forward();
    }
  }

  void _handleAnimationStatus(AnimationStatus status) {
    if (status == AnimationStatus.completed) {
      setState(() {
        _displayedQuote = widget.quote;
      });
      _controller.reverse();
    } else if (status == AnimationStatus.dismissed) {
      setState(() {
        _isAnimating = false;
      });
    }
  }

  @override
  void dispose() {
    _controller.removeStatusListener(_handleAnimationStatus);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return MotivationalQuoteCard(
        quote: widget.quote,
        isLoading: true,
        showActions: widget.showActions,
        showCategory: widget.showCategory,
      );
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Opacity(
          opacity: 1.0 - _fadeAnimation.value,
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: MotivationalQuoteCard(
              quote: _displayedQuote ?? widget.quote,
              onRefresh: widget.onRefresh,
              onSettings: widget.onSettings,
              showActions: widget.showActions,
              showCategory: widget.showCategory,
              backgroundColor: _getRandomPastelColor(),
              textColor: KFTDesignSystem.textPrimaryColor,
            ),
          ),
        );
      },
    );
  }

  Color _getRandomPastelColor() {
    final List<Color> pastelColors = [
      const Color(0xFFE0F7FA), // Cyan
      const Color(0xFFE8F5E9), // Light Green - matches our mint theme
      const Color(0xFFE1F5FE), // Light Blue - matches our sky blue accent
      const Color(0xFFEDE7F6), // Light Purple - matches our purple accent
      const Color(0xFFFFF8E1), // Light Amber
      const Color(0xFFFCE4EC), // Light Coral - matches our coral accent
      const Color(0xFFE0F2F1), // Light Teal
      const Color(0xFFE3F2FD), // Very Light Blue
      const Color(0xFFE8F5E9), // Very Light Green
      const Color(0xFFF3E5F5), // Very Light Purple
    ];

    // Use the quote ID as a seed if available, otherwise use a random seed
    final seed = _displayedQuote?.id ?? math.Random().nextInt(1000);
    final random = math.Random(seed);

    return pastelColors[random.nextInt(pastelColors.length)];
  }
}
