import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';

import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../services/vimeo_pro_auth_service.dart';
import '../services/api_service.dart';
import '../utils/kft_design_system.dart';
import '../widgets/video_error_overlay.dart';

/// Vimeo Pro authenticated video player that handles domain protection legally
/// Uses proper Vimeo Pro API authentication to bypass privacy restrictions
class VimeoProAuthenticatedPlayer extends StatefulWidget {
  final CourseVideo video;
  final UserProfile? userProfile;
  final Function(int)? onProgress;
  final Function()? onCompleted;
  final Function(String)? onError;
  final Function()? onPlay;
  final Function()? onPause;
  final Function()? onReady;
  final bool autoPlay;
  final bool showControls;
  final bool enableFullscreen;
  final bool showProgressIndicator;
  final double aspectRatio;

  const VimeoProAuthenticatedPlayer({
    Key? key,
    required this.video,
    this.userProfile,
    this.onProgress,
    this.onCompleted,
    this.onError,
    this.onPlay,
    this.onPause,
    this.onReady,
    this.autoPlay = false,
    this.showControls = true,
    this.enableFullscreen = true,
    this.showProgressIndicator = true,
    this.aspectRatio = 16 / 9,
  }) : super(key: key);

  @override
  State<VimeoProAuthenticatedPlayer> createState() => _VimeoProAuthenticatedPlayerState();
}

class _VimeoProAuthenticatedPlayerState extends State<VimeoProAuthenticatedPlayer>
    with WidgetsBindingObserver {
  
  // Core services
  late VimeoProAuthService _vimeoProAuth;
  late ApiService _apiService;
  WebViewController? _webViewController;
  
  // State management
  bool _isLoading = true;
  bool _isPlaying = false;
  bool _hasError = false;
  bool _isAuthenticating = false;
  bool _isPlayerReady = false;
  bool _isDomainVerified = false;
  
  // Error handling
  String? _currentErrorMessage;
  String? _authenticationProgress;
  
  // Video state
  String? _authenticatedEmbedUrl;
  String? _vimeoId;
  int _currentPosition = 0;
  
  // Domain and authentication
  String _currentDomain = 'mycloudforge.com';
  Map<String, dynamic>? _videoPrivacySettings;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeVimeoProPlayer();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _initializeVimeoProPlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _isAuthenticating = true;
        _authenticationProgress = 'Initializing Vimeo Pro authentication...';
      });
      
      _vimeoProAuth = VimeoProAuthService();
      _apiService = ApiService();
      
      // Initialize Vimeo Pro authentication
      await _vimeoProAuth.initialize();
      
      _vimeoId = _extractVimeoId(widget.video.videoUrl);
      if (_vimeoId == null) {
        _handleError('Invalid video URL - could not extract Vimeo ID');
        return;
      }
      
      setState(() {
        _authenticationProgress = 'Verifying domain access...';
      });
      
      // Verify domain access
      _isDomainVerified = await _vimeoProAuth.verifyDomainAccess(
        vimeoId: _vimeoId!,
        videoId: widget.video.id,
        domain: _currentDomain,
      );
      
      if (!_isDomainVerified) {
        _handleError('Domain verification failed - video not accessible from this domain');
        return;
      }
      
      setState(() {
        _authenticationProgress = 'Getting authenticated embed URL...';
      });
      
      // Get authenticated embed URL
      _authenticatedEmbedUrl = await _vimeoProAuth.getAuthenticatedEmbedUrl(
        vimeoId: _vimeoId!,
        videoId: widget.video.id,
        domain: _currentDomain,
        autoPlay: widget.autoPlay,
      );
      
      if (_authenticatedEmbedUrl == null) {
        _handleError('Failed to get authenticated embed URL');
        return;
      }
      
      setState(() {
        _authenticationProgress = 'Setting up video player...';
      });
      
      // Setup WebView player
      await _setupAuthenticatedWebPlayer();
      
      setState(() {
        _isAuthenticating = false;
        _authenticationProgress = null;
      });
      
      debugPrint('✅ VimeoProAuthenticatedPlayer: Initialization completed for video: ${widget.video.id}');
      
    } catch (e) {
      debugPrint('🚫 VimeoProAuthenticatedPlayer: Initialization failed: $e');
      _handleError('Failed to initialize Vimeo Pro player: $e');
    }
  }

  Future<void> _setupAuthenticatedWebPlayer() async {
    try {
      // Create WebView controller with Vimeo Pro optimizations
      _webViewController = WebViewController()
        // Enable unrestricted JavaScript for Vimeo Pro API
        ..setJavaScriptMode(JavaScriptMode.unrestricted)
        
        // Set optimized user agent for Vimeo Pro
        ..setUserAgent(_buildVimeoProUserAgent())
        
        // Configure navigation delegate
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: _onPageStarted,
            onPageFinished: _onPageFinished,
            onWebResourceError: _onWebResourceError,
            onNavigationRequest: _onNavigationRequest,
          ),
        )
        
        // Enable inline media playback and gesture permissions
        ..enableZoom(false);
      
      // Load the authenticated video player
      await _loadAuthenticatedVideoPlayer();
      
    } catch (e) {
      debugPrint('🚫 VimeoProAuthenticatedPlayer: WebView setup failed: $e');
      _handleError('WebView setup failed: $e');
    }
  }

  String _buildVimeoProUserAgent() {
    return 'KFT-Fitness-App/1.0 (Flutter Mobile; Vimeo Pro Authenticated; Domain: $_currentDomain)';
  }

  Future<void> _loadAuthenticatedVideoPlayer() async {
    if (_webViewController == null || _authenticatedEmbedUrl == null) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final authenticatedHtml = _buildAuthenticatedEmbedHtml(_authenticatedEmbedUrl!);
      await _webViewController!.loadHtmlString(
        authenticatedHtml,
        baseUrl: 'https://$_currentDomain/',
      );
      
    } catch (e) {
      debugPrint('🚫 Failed to load authenticated video player: $e');
      _handleError('Failed to load video: $e');
    }
  }

  String _buildAuthenticatedEmbedHtml(String embedUrl) {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <meta name="referrer" content="origin">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            html, body { 
                height: 100%; 
                background: #000; 
                overflow: hidden;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                touch-action: manipulation;
            }
            .video-container {
                position: relative;
                width: 100%;
                height: 100vh;
                background: #000;
            }
            iframe { 
                width: 100%; 
                height: 100%; 
                border: none;
                background: #000;
            }
            .auth-indicator {
                position: absolute;
                top: 8px;
                right: 8px;
                background: rgba(0, 255, 0, 0.8);
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 10px;
                font-weight: bold;
                z-index: 1000;
            }
        </style>
    </head>
    <body>
        <div class="video-container">
            <div class="auth-indicator">VIMEO PRO</div>
            <iframe 
                src="$embedUrl"
                frameborder="0" 
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                allowfullscreen
                webkitallowfullscreen
                mozallowfullscreen
                playsinline
                webkit-playsinline
                referrerpolicy="origin"
                sandbox="allow-scripts allow-same-origin allow-presentation allow-forms allow-popups allow-popups-to-escape-sandbox allow-orientation-lock">
            </iframe>
        </div>
        
        <script src="https://player.vimeo.com/api/player.js"></script>
        <script>
            // Vimeo Pro authenticated player with enhanced error handling
            let player;
            let isPlayerReady = false;
            let lastPosition = 0;
            
            // Initialize Vimeo Pro authenticated player
            function initializeVimeoProPlayer() {
                try {
                    const iframe = document.querySelector('iframe');
                    player = new Vimeo.Player(iframe);
                    
                    // Apply inline playback optimizations
                    iframe.setAttribute('playsinline', 'true');
                    iframe.setAttribute('webkit-playsinline', 'true');
                    iframe.style.webkitPlaysinline = 'true';
                    
                    // Player ready event
                    player.ready().then(() => {
                        isPlayerReady = true;
                        console.log('✅ Vimeo Pro authenticated player ready');
                        window.flutter_inappwebview?.callHandler('onPlayerReady');
                    }).catch(handlePlayerError);
                    
                    // Enhanced event listeners
                    player.on('play', () => {
                        console.log('▶️ Vimeo Pro player: Video playing');
                        window.flutter_inappwebview?.callHandler('onPlay');
                    });
                    
                    player.on('pause', () => {
                        console.log('⏸️ Vimeo Pro player: Video paused');
                        window.flutter_inappwebview?.callHandler('onPause');
                    });
                    
                    player.on('ended', () => {
                        console.log('🏁 Vimeo Pro player: Video completed');
                        window.flutter_inappwebview?.callHandler('onEnded');
                    });
                    
                    player.on('timeupdate', (data) => {
                        lastPosition = data.seconds;
                        window.flutter_inappwebview?.callHandler('onTimeUpdate', data.seconds);
                    });
                    
                    // Enhanced error handling for Vimeo Pro
                    player.on('error', handlePlayerError);
                    
                } catch (error) {
                    handlePlayerError(error);
                }
            }
            
            function handlePlayerError(error) {
                const errorMessage = error.message || error.name || 'Unknown Vimeo Pro error';
                console.error('🚫 Vimeo Pro Player Error:', error);
                
                // Classify error type
                let errorType = 'unknownError';
                if (errorMessage.toLowerCase().includes('network') || 
                    errorMessage.toLowerCase().includes('connection')) {
                    errorType = 'networkError';
                } else if (errorMessage.toLowerCase().includes('privacy') || 
                          errorMessage.toLowerCase().includes('private') ||
                          errorMessage.toLowerCase().includes('restricted')) {
                    errorType = 'privacyError';
                } else if (errorMessage.toLowerCase().includes('password') || 
                          errorMessage.toLowerCase().includes('auth')) {
                    errorType = 'passwordError';
                }
                
                window.flutter_inappwebview?.callHandler('onError', {
                    type: errorType,
                    message: errorMessage,
                    position: lastPosition,
                    authenticated: true
                });
            }
            
            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeVimeoProPlayer);
            } else {
                initializeVimeoProPlayer();
            }
            
            // Expose functions for Flutter communication
            window.seekTo = function(position) {
                if (player && isPlayerReady) {
                    return player.setCurrentTime(position).catch(handlePlayerError);
                }
                return Promise.reject('Vimeo Pro player not ready');
            };
            
            window.playVideo = function() {
                if (player && isPlayerReady) {
                    return player.play().catch(handlePlayerError);
                }
                return Promise.reject('Vimeo Pro player not ready');
            };
            
            window.pauseVideo = function() {
                if (player && isPlayerReady) {
                    return player.pause().catch(handlePlayerError);
                }
                return Promise.reject('Vimeo Pro player not ready');
            };
        </script>
    </body>
    </html>
    ''';
  }

  void _onPageStarted(String url) {
    setState(() {
      _isLoading = true;
    });
    debugPrint('🎬 VimeoProAuthenticatedPlayer: Page started loading: $url');
  }

  void _onPageFinished(String url) {
    setState(() {
      _isLoading = false;
    });
    debugPrint('🎬 VimeoProAuthenticatedPlayer: Page finished loading: $url');
    
    // Setup JavaScript handlers
    _setupJavaScriptHandlers();
  }

  void _setupJavaScriptHandlers() {
    if (_webViewController == null) return;
    
    // Enhanced JavaScript channel setup
    _webViewController!.addJavaScriptChannel(
      'onPlayerReady',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlayerReady = true;
          _isLoading = false;
        });
        debugPrint('✅ VimeoProAuthenticatedPlayer: Player ready');
        widget.onReady?.call();
      },
    );
    
    _webViewController!.addJavaScriptChannel(
      'onPlay',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = true;
        });
        debugPrint('▶️ VimeoProAuthenticatedPlayer: Video playing');
        widget.onPlay?.call();
      },
    );
    
    _webViewController!.addJavaScriptChannel(
      'onPause',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = false;
        });
        debugPrint('⏸️ VimeoProAuthenticatedPlayer: Video paused');
        widget.onPause?.call();
      },
    );
    
    _webViewController!.addJavaScriptChannel(
      'onEnded',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = false;
        });
        debugPrint('🏁 VimeoProAuthenticatedPlayer: Video completed');
        widget.onCompleted?.call();
      },
    );
    
    _webViewController!.addJavaScriptChannel(
      'onTimeUpdate',
      onMessageReceived: (JavaScriptMessage message) {
        try {
          final position = double.parse(message.message).round();
          _currentPosition = position;
          widget.onProgress?.call(position);
        } catch (e) {
          debugPrint('⚠️ Failed to parse time update: $e');
        }
      },
    );
    
    _webViewController!.addJavaScriptChannel(
      'onError',
      onMessageReceived: (JavaScriptMessage message) {
        debugPrint('🚫 VimeoProAuthenticatedPlayer: JavaScript error: ${message.message}');
        _handleVimeoProError(message.message);
      },
    );
  }

  void _handleVimeoProError(String errorData) {
    // Handle privacy errors with Vimeo Pro authentication
    if (errorData.contains('privacy') || errorData.contains('restricted')) {
      _handlePrivacyErrorWithAuth();
    } else {
      _handleError('Vimeo Pro player error: $errorData');
    }
  }

  Future<void> _handlePrivacyErrorWithAuth() async {
    try {
      debugPrint('🔧 VimeoProAuthenticatedPlayer: Handling privacy error with authentication');
      
      setState(() {
        _isAuthenticating = true;
        _authenticationProgress = 'Resolving privacy restrictions...';
      });
      
      // Use Vimeo Pro auth service to handle privacy error
      final recoveryEmbedUrl = await _vimeoProAuth.handlePrivacyError(
        vimeoId: _vimeoId!,
        videoId: widget.video.id,
        domain: _currentDomain,
        errorMessage: 'Privacy settings prevent playback',
      );
      
      if (recoveryEmbedUrl != null) {
        setState(() {
          _authenticationProgress = 'Reloading with authenticated access...';
        });
        
        _authenticatedEmbedUrl = recoveryEmbedUrl;
        await _loadAuthenticatedVideoPlayer();
        
        setState(() {
          _hasError = false;
          _isAuthenticating = false;
          _authenticationProgress = null;
        });
        
        debugPrint('✅ VimeoProAuthenticatedPlayer: Privacy error resolved with authentication');
      } else {
        _handleError('Failed to resolve privacy restrictions with Vimeo Pro authentication');
      }
      
    } catch (e) {
      debugPrint('🚫 VimeoProAuthenticatedPlayer: Privacy error handling failed: $e');
      _handleError('Privacy error handling failed: $e');
    }
  }

  void _onWebResourceError(WebResourceError error) {
    debugPrint('🚫 VimeoProAuthenticatedPlayer: WebView resource error: ${error.description}');
    _handleError('WebView error: ${error.description}');
  }

  NavigationDecision _onNavigationRequest(NavigationRequest request) {
    debugPrint('🔗 VimeoProAuthenticatedPlayer: Navigation: ${request.url}');
    
    // Allow all Vimeo-related navigation
    if (request.url.contains('vimeo.com') || 
        request.url.contains('player.vimeo.com') ||
        request.url.contains('vimeocdn.com') ||
        request.url.contains(_currentDomain)) {
      return NavigationDecision.navigate;
    }
    
    return NavigationDecision.prevent;
  }

  void _handleError(String errorMessage) {
    setState(() {
      _hasError = true;
      _currentErrorMessage = errorMessage;
      _isLoading = false;
      _isAuthenticating = false;
      _authenticationProgress = null;
    });
    
    debugPrint('🚫 VimeoProAuthenticatedPlayer: Error: $errorMessage');
    widget.onError?.call(errorMessage);
  }

  void _retryPlayer() {
    setState(() {
      _hasError = false;
      _currentErrorMessage = null;
    });
    
    _initializeVimeoProPlayer();
  }

  String? _extractVimeoId(String url) {
    final regex = RegExp(r'vimeo\.com/(?:video/)?(\d+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Container(
        color: Colors.black,
        child: Stack(
          children: [
            // Main authenticated video player
            if (_webViewController != null && !_hasError && !_isAuthenticating)
              WebViewWidget(controller: _webViewController!),
            
            // Loading indicator
            if (_isLoading && !_hasError && !_isAuthenticating)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    KFTDesignSystem.primaryColor,
                  ),
                ),
              ),
            
            // Authentication progress
            if (_isAuthenticating)
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        KFTDesignSystem.primaryColor,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _authenticationProgress ?? 'Authenticating...',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            
            // Error overlay
            if (_hasError)
              VideoErrorOverlay(
                errorType: VideoErrorType.privacyError,
                errorMessage: _currentErrorMessage ?? 'Unknown error occurred',
                isRecovering: false,
                onRetry: _retryPlayer,
                showRetryButton: true,
                customIcon: Icon(
                  Icons.verified_user,
                  size: 32,
                  color: KFTDesignSystem.primaryColor,
                ),
              ),
            
            // Vimeo Pro authentication indicator (debug mode only)
            if (kDebugMode && _isDomainVerified)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'VIMEO PRO',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
