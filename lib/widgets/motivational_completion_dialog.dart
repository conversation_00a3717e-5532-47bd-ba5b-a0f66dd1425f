import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:ui';
import '../design_system/kft_design_system.dart';

class MotivationalCompletionDialog extends StatefulWidget {
  final String message;
  final int currentStreak;
  final VoidCallback onDismiss;

  const MotivationalCompletionDialog({
    super.key,
    required this.message,
    required this.currentStreak,
    required this.onDismiss,
  });

  @override
  State<MotivationalCompletionDialog> createState() => _MotivationalCompletionDialogState();
}

class _MotivationalCompletionDialogState extends State<MotivationalCompletionDialog>
    with TickerProviderStateMixin {
  late AnimationController _dialogAnimationController;
  late AnimationController _confettiAnimationController;
  late AnimationController _streakAnimationController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _blurAnimation;
  late Animation<double> _confettiAnimation;
  late Animation<double> _streakBounceAnimation;
  late Animation<double> _streakGlowAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _triggerHapticFeedback();
  }

  void _initializeAnimations() {
    // Dialog entrance animation
    _dialogAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // Confetti animation
    _confettiAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Streak number animation
    _streakAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Dialog scale animation with bounce
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dialogAnimationController,
      curve: Curves.elasticOut,
    ));

    // Fade animation for backdrop
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dialogAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Blur animation for premium backdrop
    _blurAnimation = Tween<double>(
      begin: 0.0,
      end: 15.0,
    ).animate(CurvedAnimation(
      parent: _dialogAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Confetti animation
    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _confettiAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Streak bounce animation
    _streakBounceAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _streakAnimationController,
      curve: Curves.elasticOut,
    ));

    // Streak glow animation
    _streakGlowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _streakAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _startAnimations() {
    _dialogAnimationController.forward();

    // Delay confetti and streak animations
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _confettiAnimationController.forward();
        _streakAnimationController.forward();
      }
    });
  }

  void _triggerHapticFeedback() {
    // Success haptic feedback
    HapticFeedback.heavyImpact();

    // Additional light impacts for celebration effect
    Future.delayed(const Duration(milliseconds: 200), () {
      if (mounted) HapticFeedback.lightImpact();
    });
    Future.delayed(const Duration(milliseconds: 400), () {
      if (mounted) HapticFeedback.lightImpact();
    });
  }

  @override
  void dispose() {
    _dialogAnimationController.dispose();
    _confettiAnimationController.dispose();
    _streakAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return AnimatedBuilder(
      animation: Listenable.merge([
        _dialogAnimationController,
        _confettiAnimationController,
        _streakAnimationController,
      ]),
      builder: (context, child) {
        return Stack(
          children: [
            // Premium backdrop with blur
            GestureDetector(
              onTap: _dismiss,
              child: BackdropFilter(
                filter: ImageFilter.blur(
                  sigmaX: _blurAnimation.value,
                  sigmaY: _blurAnimation.value,
                ),
                child: Container(
                  color: Colors.black.withOpacity(0.5 * _fadeAnimation.value),
                ),
              ),
            ),

            // Confetti effect
            if (_confettiAnimation.value > 0)
              _buildConfettiEffect(),

            // Main dialog
            Center(
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: _buildDialogContent(theme, isDarkMode),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildConfettiEffect() {
    return Positioned.fill(
      child: CustomPaint(
        painter: ConfettiPainter(_confettiAnimation.value),
      ),
    );
  }

  Widget _buildDialogContent(ThemeData theme, bool isDarkMode) {
    return Container(
      margin: const EdgeInsets.all(24),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode
            ? KFTDesignSystem.darkSurfaceColor
            : Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 30,
            offset: const Offset(0, 15),
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Celebration icon with streak
          _buildCelebrationHeader(theme, isDarkMode),

          const SizedBox(height: 24),

          // Motivational message
          _buildMotivationalMessage(theme, isDarkMode),

          const SizedBox(height: 32),

          // Action buttons
          _buildActionButtons(theme, isDarkMode),
        ],
      ),
    );
  }

  Widget _buildCelebrationHeader(ThemeData theme, bool isDarkMode) {
    return Transform.scale(
      scale: _streakBounceAnimation.value,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              KFTDesignSystem.primaryColor,
              KFTDesignSystem.primaryColor.withOpacity(0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: KFTDesignSystem.primaryColor.withOpacity(0.4 * _streakGlowAnimation.value),
              blurRadius: 25 * _streakGlowAnimation.value,
              spreadRadius: 5 * _streakGlowAnimation.value,
            ),
          ],
        ),
        child: Column(
          children: [
            const Icon(
              Icons.celebration,
              color: Colors.white,
              size: 48,
            ),
            const SizedBox(height: 12),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.local_fire_department,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  '${widget.currentStreak} Day Streak!',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMotivationalMessage(ThemeData theme, bool isDarkMode) {
    // Parse the message to separate quote from attribution
    final parts = widget.message.split("'");
    final beforeQuote = parts.isNotEmpty ? parts[0] : '';
    final quote = parts.length > 2 ? "'${parts[1]}'" : '';
    final afterQuote = parts.length > 2 ? parts[2] : '';

    return Column(
      children: [
        if (beforeQuote.isNotEmpty)
          Text(
            beforeQuote.trim(),
            style: theme.textTheme.titleMedium?.copyWith(
              color: isDarkMode
                  ? KFTDesignSystem.darkTextPrimaryColor
                  : KFTDesignSystem.textPrimaryColor,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),

        if (quote.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: KFTDesignSystem.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: KFTDesignSystem.primaryColor.withOpacity(0.3),
              ),
            ),
            child: Text(
              quote,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: isDarkMode
                    ? KFTDesignSystem.darkTextPrimaryColor
                    : KFTDesignSystem.textPrimaryColor,
                fontStyle: FontStyle.italic,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],

        if (afterQuote.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            afterQuote.trim(),
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? KFTDesignSystem.darkTextSecondaryColor
                  : KFTDesignSystem.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme, bool isDarkMode) {
    return Row(
      children: [
        Expanded(
          child: TextButton(
            onPressed: _dismiss,
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Continue',
              style: theme.textTheme.titleMedium?.copyWith(
                color: KFTDesignSystem.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton(
            onPressed: () {
              // TODO: Share achievement
              _dismiss();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: KFTDesignSystem.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: Text(
              'Share 🎉',
              style: theme.textTheme.titleMedium?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _dismiss() async {
    await _dialogAnimationController.reverse();
    widget.onDismiss();
  }
}

// Custom painter for confetti effect
class ConfettiPainter extends CustomPainter {
  final double animationValue;

  ConfettiPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();
    final colors = [
      KFTDesignSystem.primaryColor,
      Colors.orange,
      Colors.pink,
      Colors.purple,
      Colors.blue,
      Colors.green,
    ];

    // Draw confetti particles
    for (int i = 0; i < 50; i++) {
      final x = (size.width * (i % 10) / 10) + (animationValue * 100 * (i % 3 - 1));
      final y = size.height * (1 - animationValue) + (i * 20);

      if (y < size.height && y > 0 && x > 0 && x < size.width) {
        paint.color = colors[i % colors.length].withOpacity(0.8);

        // Draw different shapes
        if (i % 3 == 0) {
          // Circle
          canvas.drawCircle(Offset(x, y), 4, paint);
        } else if (i % 3 == 1) {
          // Square
          canvas.drawRect(Rect.fromCenter(center: Offset(x, y), width: 6, height: 6), paint);
        } else {
          // Triangle
          final path = Path();
          path.moveTo(x, y - 4);
          path.lineTo(x - 3, y + 2);
          path.lineTo(x + 3, y + 2);
          path.close();
          canvas.drawPath(path, paint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
