import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/motivational_quote.dart';
import '../services/quote_service.dart';
import 'daily_motivational_quote.dart';

class KFTAppScaffold extends StatefulWidget {
  final String title;
  final List<Widget>? actions;
  final Widget body;
  final Widget? floatingActionButton;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? bottomNavigationBar;
  final bool centerTitle;
  final Color? backgroundColor;
  final SystemUiOverlayStyle? systemUiOverlayStyle;
  final PreferredSizeWidget? bottom;
  final Widget? drawer;
  final bool extendBodyBehindAppBar;
  final bool resizeToAvoidBottomInset;
  final bool showQuote;

  const KFTAppScaffold({
    Key? key,
    required this.title,
    required this.body,
    this.actions,
    this.floatingActionButton,
    this.floatingActionButtonLocation,
    this.bottomNavigationBar,
    this.centerTitle = false,
    this.backgroundColor,
    this.systemUiOverlayStyle,
    this.bottom,
    this.drawer,
    this.extendBodyBehindAppBar = false,
    this.resizeToAvoidBottomInset = true,
    this.showQuote = false, // Default to not showing quotes
  }) : super(key: key);

  @override
  State<KFTAppScaffold> createState() => _KFTAppScaffoldState();
}

class _KFTAppScaffoldState extends State<KFTAppScaffold> {
  final QuoteService _quoteService = QuoteService();
  bool _isLoadingQuote = true;
  MotivationalQuote? _quote;
  String? _quoteError;
  bool _showQuotes = true; // User preference to show quotes

  @override
  void initState() {
    super.initState();
    _loadQuotePreferences().then((_) {
      if (widget.showQuote && _showQuotes) {
        _loadQuote();
      }
    });
  }

  // Load user's quote preferences
  Future<void> _loadQuotePreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userPref = prefs.getBool('show_quotes');

      // If user has explicitly set a preference, use that
      if (userPref != null) {
        setState(() {
          _showQuotes = userPref;
        });
        return;
      }

      // Otherwise, check the global setting
      final globallyEnabled = await _quoteService.areQuotesGloballyEnabled();
      setState(() {
        _showQuotes = globallyEnabled;
      });
    } catch (e) {
      print('Error loading quote preferences: $e');
      // Default to showing quotes if there's an error
      setState(() {
        _showQuotes = true;
      });
    }
  }

  // Save user's quote preferences
  Future<void> _saveQuotePreferences(bool showQuotes) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('show_quotes', showQuotes);
      setState(() {
        _showQuotes = showQuotes;
      });
    } catch (e) {
      print('Error saving quote preferences: $e');
    }
  }

  Future<void> _loadQuote({bool forceRefresh = false}) async {
    if (!widget.showQuote || !_showQuotes) return;

    setState(() {
      _isLoadingQuote = true;
      _quoteError = null;
    });

    try {
      // Load quote
      final quote = await _quoteService.getQuote(forceRefresh: forceRefresh);

      if (mounted) {
        setState(() {
          _quote = quote;
          _isLoadingQuote = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _quoteError = e.toString().replaceAll('Exception: ', '');
          _isLoadingQuote = false;
        });
      }
    }
  }

  Future<void> _showQuoteSettings() async {
    // Show quote settings dialog
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Motivational Quotes'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Personalized motivational quotes can help keep you motivated on your fitness journey.',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Show Daily Quotes'),
              subtitle: const Text('Get a new personalized quote each day'),
              value: _showQuotes,
              onChanged: (value) {
                Navigator.of(context).pop(value);
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(null);
            },
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(_showQuotes);
            },
            child: const Text('SAVE'),
          ),
        ],
      ),
    );

    // Update preferences if user made a change
    if (result != null && result != _showQuotes) {
      await _saveQuotePreferences(result);

      // If quotes were turned on, load a new quote
      if (result && widget.showQuote) {
        await _loadQuote(forceRefresh: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Default quote to show while loading
    final defaultQuote = MotivationalQuote(
      quote: 'The only bad workout is the one that didn\'t happen.',
      author: 'Unknown',
      category: 'fitness',
    );

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        centerTitle: widget.centerTitle,
        actions: widget.actions,
        backgroundColor: widget.backgroundColor,
        systemOverlayStyle: widget.systemUiOverlayStyle,
        bottom: widget.bottom,
        elevation: 0,
      ),
      body: Column(
        children: [
          // Show motivational quote if enabled
          if (widget.showQuote && _showQuotes && _quote != null)
            DailyMotivationalQuote(
              quote: _quote ?? defaultQuote,
              isLoading: _isLoadingQuote,
              onRefresh: () => _loadQuote(forceRefresh: true),
              onSettings: _showQuoteSettings,
              showActions: true,
            ),
          // Main content
          Expanded(child: widget.body),
        ],
      ),
      floatingActionButton: widget.floatingActionButton,
      floatingActionButtonLocation: widget.floatingActionButtonLocation,
      bottomNavigationBar: widget.bottomNavigationBar,
      drawer: widget.drawer,
      extendBodyBehindAppBar: widget.extendBodyBehindAppBar,
      resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset,
    );
  }
}
