import 'package:flutter/material.dart';
import '../design_system/kft_design_system.dart';
import 'dart:ui';

/// KFT Bottom Navigation
/// A custom bottom navigation bar widget that follows the KFT design system
/// Features a modern, animated design with support for badges and indicators

class KFTBottomNavigationItem {
  final String label;
  final IconData icon;
  final IconData activeIcon;
  final bool showBadge;
  final int? badgeCount;

  const KFTBottomNavigationItem({
    required this.label,
    required this.icon,
    required this.activeIcon,
    this.showBadge = false,
    this.badgeCount,
  });
}

class KFTBottomNavigation extends StatelessWidget {
  final List<KFTBottomNavigationItem> items;
  final int currentIndex;
  final Function(int) onTap;
  final Color? backgroundColor;
  final Color? selectedItemColor;
  final Color? unselectedItemColor;
  final double height;
  final bool showLabels;
  final bool showSelectedLabels;
  final bool showUnselectedLabels;

  const KFTBottomNavigation({
    Key? key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
    this.backgroundColor,
    this.selectedItemColor,
    this.unselectedItemColor,
    this.height = 70,
    this.showLabels = true,
    this.showSelectedLabels = true,
    this.showUnselectedLabels = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Use the correct background color based on theme
    final bgColor = backgroundColor ??
        (isDarkMode ? KFTDesignSystem.darkSurfaceColor : KFTDesignSystem.surfaceColor);

    // Use the correct colors based on theme
    final selectedColor = selectedItemColor ??
        (isDarkMode ? KFTDesignSystem.primaryLightColor : KFTDesignSystem.primaryColor);

    final unselectedColor = unselectedItemColor ??
        (isDarkMode ? KFTDesignSystem.darkTextTertiaryColor : KFTDesignSystem.textTertiaryColor);

    return Container(
      height: 60 + MediaQuery.of(context).padding.bottom, // Reduced height for more minimal look
      decoration: BoxDecoration(
        color: bgColor,
        // Removed shadow for cleaner look
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? KFTDesignSystem.darkDividerColor.withOpacity(0.3)
                : KFTDesignSystem.dividerColor.withOpacity(0.3),
            width: KFTDesignSystem.borderWidthThin,
          ),
        ),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(KFTDesignSystem.borderRadiusLg),
          topRight: Radius.circular(KFTDesignSystem.borderRadiusLg),
        ),
        child: Container(
            color: bgColor, // Simple solid color for cleaner look
            padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(items.length, (index) {
                final item = items[index];
                final isSelected = index == currentIndex;

                return _buildNavItem(
                  context,
                  item: item,
                  isSelected: isSelected,
                  selectedColor: selectedColor,
                  unselectedColor: unselectedColor,
                  index: index,
                );
              }),
            ),
          ),
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context, {
    required KFTBottomNavigationItem item,
    required bool isSelected,
    required Color selectedColor,
    required Color unselectedColor,
    required int index,
  }) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final showLabel = showLabels && (isSelected ? showSelectedLabels : showUnselectedLabels);

    // Get the correct background color for the badge border
    final scaffoldBgColor = isDarkMode
        ? KFTDesignSystem.darkSurfaceColor
        : Theme.of(context).scaffoldBackgroundColor;

    // Get the correct accent color for the badge
    final badgeColor = isDarkMode
        ? KFTDesignSystem.accentColor.withOpacity(0.9)
        : KFTDesignSystem.accentColor;

    return InkWell(
      onTap: () => onTap(index),
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      child: SizedBox(
        width: 60, // Fixed width for more consistent spacing
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(height: 8),

            // Icon with simple animation
            Stack(
              clipBehavior: Clip.none,
              alignment: Alignment.center,
              children: [
                // Icon with subtle animation
                TweenAnimationBuilder<double>(
                  tween: Tween<double>(
                    begin: 0.0,
                    end: isSelected ? 1.0 : 0.0,
                  ),
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeOutCubic,
                  builder: (context, value, child) {
                    return Icon(
                      isSelected ? item.activeIcon : item.icon,
                      color: Color.lerp(unselectedColor, selectedColor, value),
                      size: 24, // Consistent size
                    );
                  },
                ),

                // Badge - Simplified Airbnb-style badge
                if (item.showBadge)
                  Positioned(
                    top: -2,
                    right: -2,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: badgeColor,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: scaffoldBgColor,
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),

            // Label - Simplified Airbnb-style label
            if (showLabel) ...[
              const SizedBox(height: 4),
              AnimatedDefaultTextStyle(
                duration: const Duration(milliseconds: 200),
                style: TextStyle(
                  color: isSelected ? selectedColor : unselectedColor,
                  fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
                  fontSize: 10,
                  letterSpacing: 0.2,
                ),
                child: Text(item.label),
              ),
            ],
            const SizedBox(height: 4), // Add a bit of bottom padding
          ],
        ),
      ),
    );
  }
}
