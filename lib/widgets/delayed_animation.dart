import 'package:flutter/material.dart';

/// A widget that animates its child with a delay.
class DelayedAnimation extends StatefulWidget {
  /// The child to animate.
  final Widget child;

  /// The delay before the animation starts.
  final Duration delay;

  /// The duration of the animation.
  final Duration duration;

  /// The curve of the animation.
  final Curve curve;

  /// Creates a delayed animation.
  const DelayedAnimation({
    Key? key,
    required this.child,
    required this.delay,
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeOut,
  }) : super(key: key);

  @override
  _DelayedAnimationState createState() => _DelayedAnimationState();
}

class _DelayedAnimationState extends State<DelayedAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: widget.curve,
    );

    // Start the animation after the delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: Transform.translate(
            offset: Offset(0, 20 * (1 - _animation.value)),
            child: child,
          ),
        );
      },
      child: widget.child,
    );
  }
}

/// A widget that fades and slides in its child with a delay.
class DelayedFadeIn extends StatelessWidget {
  /// The child to animate.
  final Widget child;

  /// The delay before the animation starts.
  final Duration delay;

  /// The duration of the animation.
  final Duration duration;

  /// The curve of the animation.
  final Curve curve;

  /// Creates a delayed fade-in animation.
  const DelayedFadeIn({
    Key? key,
    required this.child,
    required this.delay,
    this.duration = const Duration(milliseconds: 500),
    this.curve = Curves.easeOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return DelayedAnimation(
      delay: delay,
      duration: duration,
      curve: curve,
      child: child,
    );
  }
}
