import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../services/video_streak_service.dart';

class WorkoutStreakRing extends StatefulWidget {
  final VoidCallback? onStreakUpdated;

  const WorkoutStreakRing({
    Key? key,
    this.onStreakUpdated,
  }) : super(key: key);

  @override
  State<WorkoutStreakRing> createState() => WorkoutStreakRingState();
}

class WorkoutStreakRingState extends State<WorkoutStreakRing>
    with TickerProviderStateMixin {
  final VideoStreakService _streakService = VideoStreakService();

  int _currentStreak = 0;
  int _highestStreak = 0;
  bool _isLoading = true;
  bool _isStreakActive = false;

  late AnimationController _ringController;
  late AnimationController _pulseController;
  late AnimationController _scaleController;

  late Animation<double> _ringAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStreakData();
  }

  void _initializeAnimations() {
    _ringController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _ringAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _ringController,
      curve: Curves.elasticOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.08,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _ringController.dispose();
    _pulseController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  Future<void> _loadStreakData() async {
    try {
      final currentStreak = await _streakService.getCurrentStreak();
      final highestStreak = await _streakService.getHighestStreak();
      final streakEarnedToday = await _streakService.isStreakEarnedToday();

      if (mounted) {
        setState(() {
          _currentStreak = currentStreak;
          _highestStreak = highestStreak;
          _isStreakActive = streakEarnedToday;
          _isLoading = false;
        });

        // Start animations
        _scaleController.forward();

        if (_isStreakActive) {
          _ringController.forward();
          _pulseController.repeat(reverse: true);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getMotivationalQuote() {
    if (_currentStreak == 0) {
      return "Start your journey today! 🎯";
    } else if (_currentStreak == 1) {
      return "Amazing! One day down! 🔥";
    } else if (_currentStreak == 2) {
      return "Building momentum! 💪";
    } else if (_currentStreak == 3) {
      return "Three days strong! 🚀";
    } else if (_currentStreak <= 7) {
      return "You're unstoppable! ⭐";
    } else if (_currentStreak <= 14) {
      return "Two weeks of power! 💎";
    } else if (_currentStreak <= 30) {
      return "Incredible dedication! 🏆";
    } else {
      return "Legendary streak! 👑";
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return _buildLoadingWidget(theme);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey[900]?.withOpacity(0.6)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withOpacity(0.3)
                : Colors.grey.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Centered Motivational Quote
          Center(
            child: Text(
              _getMotivationalQuote(),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDarkMode ? Colors.white : Colors.grey[800],
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 24),

          // Animated Ring
          ScaleTransition(
            scale: _scaleAnimation,
            child: SizedBox(
              width: 120,
              height: 120,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Ring
                  AnimatedBuilder(
                    animation: _pulseAnimation,
                    builder: (context, child) {
                      return Transform.scale(
                        scale: _isStreakActive ? _pulseAnimation.value : 1.0,
                        child: CustomPaint(
                          size: const Size(120, 120),
                          painter: StreakRingPainter(
                            progress: _ringAnimation.value,
                            isActive: _isStreakActive,
                            isDarkMode: isDarkMode,
                          ),
                        ),
                      );
                    },
                  ),

                  // Center Content
                  Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AnimatedBuilder(
                        animation: _scaleAnimation,
                        builder: (context, child) {
                          return Text(
                            '$_currentStreak',
                            style: theme.textTheme.headlineLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              fontSize: 32,
                              color: _isStreakActive
                                  ? (isDarkMode ? Colors.green[400] : Colors.green[600])
                                  : (isDarkMode ? Colors.grey[400] : Colors.grey[600]),
                            ),
                          );
                        },
                      ),
                      Text(
                        _currentStreak == 1 ? 'Day' : 'Days',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isDarkMode ? Colors.grey[500] : Colors.grey[600],
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Highest Streak Badge (if exists)
          if (_highestStreak > 0)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.amber.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.emoji_events,
                    size: 16,
                    color: Colors.amber[700],
                  ),
                  const SizedBox(width: 6),
                  Text(
                    'Best: $_highestStreak ${_highestStreak == 1 ? 'day' : 'days'}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.amber[700],
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget(ThemeData theme) {
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey[900]?.withOpacity(0.6)
            : Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(24),
      ),
      child: Column(
        children: [
          const SizedBox(
            width: 120,
            height: 120,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(height: 16),
          Text(
            'Loading streak data...',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDarkMode ? Colors.grey[400] : Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  // Method to update streak from external sources
  void updateStreak(int newStreak, int newHighest, bool isActive) {
    if (mounted) {
      setState(() {
        _currentStreak = newStreak;
        _highestStreak = newHighest;
        _isStreakActive = isActive;
      });

      // Trigger haptic feedback for streak updates
      if (isActive) {
        HapticFeedback.lightImpact();
      }

      // Update animations
      if (isActive) {
        _ringController.forward();
        _pulseController.repeat(reverse: true);
      } else {
        _ringController.reverse();
        _pulseController.stop();
      }

      // Notify parent
      widget.onStreakUpdated?.call();
    }
  }

  // Method to refresh streak data
  Future<void> refreshStreak() async {
    await _loadStreakData();
  }
}

class StreakRingPainter extends CustomPainter {
  final double progress;
  final bool isActive;
  final bool isDarkMode;

  StreakRingPainter({
    required this.progress,
    required this.isActive,
    required this.isDarkMode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 8;

    // Background ring
    final backgroundPaint = Paint()
      ..color = isDarkMode
          ? Colors.grey[800]!.withOpacity(0.3)
          : Colors.grey[300]!.withOpacity(0.5)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 8
      ..strokeCap = StrokeCap.round;

    canvas.drawCircle(center, radius, backgroundPaint);

    // Progress ring
    if (progress > 0) {
      final progressPaint = Paint()
        ..shader = LinearGradient(
          colors: isActive
              ? [Colors.green[400]!, Colors.green[600]!]
              : [Colors.grey[400]!, Colors.grey[600]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ).createShader(Rect.fromCircle(center: center, radius: radius))
        ..style = PaintingStyle.stroke
        ..strokeWidth = 8
        ..strokeCap = StrokeCap.round;

      final sweepAngle = 2 * math.pi * progress;
      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        -math.pi / 2,
        sweepAngle,
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
