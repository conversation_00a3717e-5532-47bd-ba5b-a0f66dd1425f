import 'package:flutter/material.dart';
import '../services/api_service.dart';
import '../models/user_profile.dart';
import '../utils/animations.dart';
import '../design_system/kft_design_system.dart';
import '../services/performance_monitor.dart';

/// Professional staff name animation widget for homepage
class StaffNameAnimationWidget extends StatefulWidget {
  final UserProfile? profile;
  final bool shouldAnimate;
  final VoidCallback? onAnimationComplete;

  const StaffNameAnimationWidget({
    Key? key,
    required this.profile,
    this.shouldAnimate = true,
    this.onAnimationComplete,
  }) : super(key: key);

  @override
  State<StaffNameAnimationWidget> createState() => _StaffNameAnimationWidgetState();
}

class _StaffNameAnimationWidgetState extends State<StaffNameAnimationWidget>
    with TickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  Map<String, String>? _staffData;
  bool _isLoading = true;
  bool _hasError = false;

  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStaffData();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
  }

  Future<void> _loadStaffData() async {
    if (widget.profile?.assignedStaffId == null) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    try {
      final stopwatch = Stopwatch()..start();

      final staffData = await _apiService.getStaffInfo(widget.profile!.assignedStaffId!);

      stopwatch.stop();
      PerformanceMonitor().logEvent('staff_name_animation_load', {
        'duration_ms': stopwatch.elapsedMilliseconds,
        'staff_id': widget.profile!.assignedStaffId!,
        'success': staffData != null,
      });

      if (mounted) {
        setState(() {
          _staffData = staffData;
          _isLoading = false;
          _hasError = staffData == null;
        });

        if (staffData != null && widget.shouldAnimate) {
          _startAnimationSequence();
        }
      }
    } catch (e) {
      print('Error loading staff data for animation: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  Future<void> _startAnimationSequence() async {
    if (!mounted) return;

    // Staggered animation sequence
    await Future.delayed(const Duration(milliseconds: 800)); // Wait for page to settle

    if (mounted) {
      _fadeController.forward();
      await Future.delayed(const Duration(milliseconds: 100));
    }

    if (mounted) {
      _slideController.forward();
      await Future.delayed(const Duration(milliseconds: 200));
    }

    if (mounted) {
      _scaleController.forward();
      await Future.delayed(const Duration(milliseconds: 300));
    }

    // Auto-hide after showing for a few seconds
    await Future.delayed(const Duration(seconds: 4));
    if (mounted) {
      _hideAnimation();
    }
  }

  Future<void> _hideAnimation() async {
    if (!mounted) return;

    await Future.wait([
      _fadeController.reverse(),
      _slideController.reverse(),
      _scaleController.reverse(),
    ]);

    widget.onAnimationComplete?.call();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.profile?.assignedStaffId == null || _isLoading || _hasError || _staffData == null) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final staffName = _staffData!['name'] ?? 'Your Coach';

    return Positioned(
      bottom: 120, // Position above the FAB
      right: 20,
      child: AnimatedBuilder(
        animation: Listenable.merge([_slideController, _fadeController, _scaleController]),
        builder: (context, child) {
          return SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: _buildAnimationContent(theme, isDarkMode, staffName),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAnimationContent(ThemeData theme, bool isDarkMode, String staffName) {
    return Container(
      constraints: const BoxConstraints(maxWidth: 280),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            isDarkMode
                ? Colors.grey.shade800.withOpacity(0.95)
                : Colors.white.withOpacity(0.95),
            isDarkMode
                ? Colors.grey.shade900.withOpacity(0.9)
                : Colors.white.withOpacity(0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.1),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: theme.colorScheme.primary.withOpacity(0.05),
            blurRadius: 12,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      theme.colorScheme.primary.withOpacity(0.15),
                      theme.colorScheme.primary.withOpacity(0.08),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.fitness_center,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Your Personal Trainer',
                  style: TextStyle(
                    color: theme.colorScheme.onSurface.withOpacity(0.7),
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.3,
                    fontFamily: KFTDesignSystem.fontFamily,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Main message
          RichText(
            text: TextSpan(
              style: TextStyle(
                color: theme.colorScheme.onSurface,
                fontSize: 16,
                fontWeight: FontWeight.w600,
                height: 1.3,
                fontFamily: KFTDesignSystem.fontFamily,
              ),
              children: [
                TextSpan(
                  text: staffName,
                  style: TextStyle(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const TextSpan(
                  text: ' is supporting your\nfitness journey',
                ),
              ],
            ),
          ),

          const SizedBox(height: 12),

          // Call to action
          Row(
            children: [
              Icon(
                Icons.chat_bubble_outline,
                color: theme.colorScheme.primary.withOpacity(0.7),
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Connect instantly for personalized guidance',
                  style: TextStyle(
                    color: theme.colorScheme.onSurface.withOpacity(0.6),
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    fontFamily: KFTDesignSystem.fontFamily,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Progress indicator
          Container(
            height: 3,
            decoration: BoxDecoration(
              color: theme.colorScheme.outline.withOpacity(0.1),
              borderRadius: BorderRadius.circular(2),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: 0.7,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.primary.withOpacity(0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
