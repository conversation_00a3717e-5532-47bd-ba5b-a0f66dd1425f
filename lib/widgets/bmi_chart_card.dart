import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../models/user_profile.dart';

class BMIChartCard extends StatelessWidget {
  final UserProfile userProfile;
  final Function(double) onWeightUpdated;

  const BMIChartCard({
    Key? key,
    required this.userProfile,
    required this.onWeightUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    // Get BMI data for chart
    final bmiData = userProfile.bmiHistory
        .take(7) // Show last 7 records
        .toList()
      ..sort((a, b) => a.date.compareTo(b.date));

    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      elevation: 2,
      shadowColor: isDarkMode ? Colors.black : Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isDarkMode
            ? BorderSide(color: Colors.grey.shade800, width: 0.5)
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.monitor_weight, color: primaryColor),
                const SizedBox(width: 8),
                Text(
                  'BMI Trend',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildBMIInfo(context),
            const SizedBox(height: 24),
            if (bmiData.isNotEmpty)
              SizedBox(
                height: 200,
                child: _buildBMIChart(context, bmiData),
              )
            else
              Center(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 32.0),
                  child: Text(
                    'No BMI data available',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isDarkMode
                          ? theme.colorScheme.onSurface.withOpacity(0.7)
                          : Colors.grey.shade600,
                    ),
                  ),
                ),
              ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildBMIInfo(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final isDarkMode = theme.brightness == Brightness.dark;

    // Get BMI category color
    Color categoryColor;
    final bmi = userProfile.currentBMI;

    if (bmi < 18.5) {
      categoryColor = Colors.blue;
    } else if (bmi < 25) {
      categoryColor = Colors.green;
    } else if (bmi < 30) {
      categoryColor = Colors.orange;
    } else {
      categoryColor = Colors.red;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Column(
          children: [
            Text(
              'Current BMI',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDarkMode
                    ? theme.colorScheme.onSurface.withOpacity(0.7)
                    : Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              bmi.toStringAsFixed(1),
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: primaryColor,
              ),
            ),
          ],
        ),
        Column(
          children: [
            Text(
              'Category',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDarkMode
                    ? theme.colorScheme.onSurface.withOpacity(0.7)
                    : Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: categoryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                userProfile.bmiCategory,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: categoryColor,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBMIChart(BuildContext context, List<BMIRecord> bmiData) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final isDarkMode = theme.brightness == Brightness.dark;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: false,
          horizontalInterval: 5,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: isDarkMode
                  ? Colors.grey.shade800
                  : Colors.grey.shade200,
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < bmiData.length) {
                  final date = bmiData[value.toInt()].date;
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      DateFormat('MMM d').format(date),
                      style: TextStyle(
                        color: isDarkMode
                            ? theme.colorScheme.onSurface.withOpacity(0.6)
                            : Colors.grey.shade600,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const SizedBox();
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 5,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: isDarkMode
                        ? theme.colorScheme.onSurface.withOpacity(0.6)
                        : Colors.grey.shade600,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 30,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: false,
        ),
        minX: 0,
        maxX: bmiData.length - 1.0,
        minY: _getMinY(bmiData),
        maxY: _getMaxY(bmiData),
        lineBarsData: [
          LineChartBarData(
            spots: List.generate(bmiData.length, (index) {
              return FlSpot(index.toDouble(), bmiData[index].bmi);
            }),
            isCurved: true,
            color: primaryColor,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: const FlDotData(
              show: true,
            ),
            belowBarData: BarAreaData(
              show: true,
              color: primaryColor.withOpacity(0.1),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get minimum Y value for chart
  double _getMinY(List<BMIRecord> data) {
    if (data.isEmpty) return 15;
    double min = data.map((e) => e.bmi).reduce((a, b) => a < b ? a : b);
    return (min - 2).clamp(15, 40); // Ensure minimum is at least 15
  }

  // Helper method to get maximum Y value for chart
  double _getMaxY(List<BMIRecord> data) {
    if (data.isEmpty) return 30;
    double max = data.map((e) => e.bmi).reduce((a, b) => a > b ? a : b);
    return (max + 2).clamp(15, 40); // Ensure maximum is at most 40
  }
}
