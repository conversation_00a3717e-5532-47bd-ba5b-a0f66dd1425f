import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:async';

import '../models/course_video.dart';
import '../models/user_profile.dart';
import '../services/video_error_handler.dart';
import '../services/api_service.dart';
import '../utils/kft_design_system.dart';
import '../widgets/video_error_overlay.dart';

/// Optimized Vimeo video player with comprehensive inline playback support
/// Implements all WebView best practices to prevent seeking validation errors
class OptimizedVimeoPlayerWithInlinePlayback extends StatefulWidget {
  final CourseVideo video;
  final UserProfile? userProfile;
  final Function(int)? onProgress;
  final Function()? onCompleted;
  final Function(String)? onError;
  final Function()? onPlay;
  final Function()? onPause;
  final Function()? onReady;
  final bool autoPlay;
  final bool showControls;
  final bool enableFullscreen;
  final bool showProgressIndicator;
  final double aspectRatio;

  const OptimizedVimeoPlayerWithInlinePlayback({
    Key? key,
    required this.video,
    this.userProfile,
    this.onProgress,
    this.onCompleted,
    this.onError,
    this.onPlay,
    this.onPause,
    this.onReady,
    this.autoPlay = false,
    this.showControls = true,
    this.enableFullscreen = true,
    this.showProgressIndicator = true,
    this.aspectRatio = 16 / 9,
  }) : super(key: key);

  @override
  State<OptimizedVimeoPlayerWithInlinePlayback> createState() => _OptimizedVimeoPlayerWithInlinePlaybackState();
}

class _OptimizedVimeoPlayerWithInlinePlaybackState extends State<OptimizedVimeoPlayerWithInlinePlayback>
    with WidgetsBindingObserver {

  // Core components
  late VideoErrorHandler _errorHandler;
  late ApiService _apiService;
  WebViewController? _webViewController;

  // State management
  bool _isLoading = true;
  bool _isPlaying = false;
  bool _hasError = false;
  bool _isRecovering = false;
  bool _isPlayerReady = false;

  // Error handling
  VideoErrorType? _currentErrorType;
  String? _currentErrorMessage;
  String? _recoveryProgress;

  // Video state
  String? _currentEmbedUrl;
  String? _vimeoId;
  int _currentPosition = 0;
  int _lastSafePosition = 0;

  // Inline playback optimization
  String _currentDomain = 'mycloudforge.com';
  bool _allowsInlineMediaPlayback = true;
  bool _gestureNavigationEnabled = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeOptimizedPlayer();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Future<void> _initializeOptimizedPlayer() async {
    try {
      _errorHandler = VideoErrorHandler();
      _apiService = ApiService();

      await _errorHandler.initialize();

      _vimeoId = _extractVimeoId(widget.video.videoUrl);
      if (_vimeoId == null) {
        _handleError(VideoErrorType.loadingError, 'Invalid video URL');
        return;
      }

      await _setupOptimizedWebPlayer();

    } catch (e) {
      debugPrint('🚫 Optimized player initialization failed: $e');
      _handleError(VideoErrorType.webViewError, 'Failed to initialize player: $e');
    }
  }

  Future<void> _setupOptimizedWebPlayer() async {
    try {
      // Create WebView controller with comprehensive inline playback support
      _webViewController = WebViewController()
        // Enable unrestricted JavaScript for Vimeo API
        ..setJavaScriptMode(JavaScriptMode.unrestricted)

        // Set optimized user agent with domain information
        ..setUserAgent(_buildOptimizedUserAgent())

        // Configure navigation delegate with proper error handling
        ..setNavigationDelegate(
          NavigationDelegate(
            onPageStarted: _onPageStarted,
            onPageFinished: _onPageFinished,
            onWebResourceError: _onWebResourceError,
            onNavigationRequest: _onNavigationRequest,
          ),
        )

        // CRITICAL: Enable inline media playback and gesture permissions
        ..enableZoom(false);

      // Apply platform-specific optimizations
      await _applyPlatformOptimizations();

      // Generate optimized embed URL with inline playback parameters
      _currentEmbedUrl = await _generateOptimizedEmbedUrl();

      // Load the optimized video player
      await _loadOptimizedVideoPlayer();

    } catch (e) {
      debugPrint('🚫 Optimized WebView setup failed: $e');
      _handleError(VideoErrorType.webViewError, 'WebView setup failed: $e');
    }
  }

  Future<void> _applyPlatformOptimizations() async {
    if (_webViewController == null) return;

    try {
      // Platform-specific WebView optimizations
      if (defaultTargetPlatform == TargetPlatform.iOS) {
        // iOS-specific optimizations for inline playback
        await _webViewController!.runJavaScript('''
          // Enable inline playback on iOS
          document.addEventListener('DOMContentLoaded', function() {
            var videos = document.querySelectorAll('video');
            videos.forEach(function(video) {
              video.setAttribute('playsinline', 'true');
              video.setAttribute('webkit-playsinline', 'true');
            });
          });
        ''');
      } else if (defaultTargetPlatform == TargetPlatform.android) {
        // Android-specific optimizations
        await _webViewController!.runJavaScript('''
          // Enable gesture navigation and media playback on Android
          document.addEventListener('DOMContentLoaded', function() {
            document.body.style.touchAction = 'manipulation';
            document.body.style.userSelect = 'none';
          });
        ''');
      }
    } catch (e) {
      debugPrint('⚠️ Platform optimizations failed: $e');
      // Continue without platform optimizations
    }
  }

  String _buildOptimizedUserAgent() {
    // Build user agent that identifies as a legitimate mobile app
    final platform = defaultTargetPlatform == TargetPlatform.iOS ? 'iOS' : 'Android';
    return 'KFT-Fitness-App/1.0 ($platform; Flutter; Domain: $_currentDomain; Vimeo-Optimized)';
  }

  Future<String> _generateOptimizedEmbedUrl() async {
    if (_vimeoId == null) throw Exception('No Vimeo ID available');

    try {
      // Try to get secure embed URL from backend
      final response = await _apiService.makeApiRequest(
        'get_secure_vimeo_embed.php',
        method: 'POST',
        data: {
          'vimeo_id': _vimeoId!,
          'video_id': widget.video.id,
          'domain': _currentDomain,
          'autoplay': widget.autoPlay ? '1' : '0',
          'inline_playback': 'true',
          'gesture_navigation': 'true',
        },
      );

      if (response['success'] == true && response['secure_embed_url'] != null) {
        String embedUrl = response['secure_embed_url'];
        return _optimizeEmbedUrlForInlinePlayback(embedUrl);
      }
    } catch (e) {
      debugPrint('🚫 Secure embed URL generation failed: $e');
    }

    // Fallback to optimized standard embed URL
    return _buildOptimizedStandardEmbedUrl();
  }

  String _optimizeEmbedUrlForInlinePlayback(String embedUrl) {
    // Optimize embed URL with all inline playback parameters
    final optimizations = {
      'playsinline': '1',
      'webkit-playsinline': '1',
      'allowsInlineMediaPlayback': 'true',
      'gestureNavigationEnabled': 'true',
      'controls': '1',
      'keyboard': '1',
      'pip': '1',
      'dnt': '1',
      'responsive': '1',
      'referrer': 'https://$_currentDomain/',
      'origin': 'https://$_currentDomain',
    };

    // Apply optimizations to embed URL
    for (final entry in optimizations.entries) {
      if (!embedUrl.contains('${entry.key}=')) {
        embedUrl += '&${entry.key}=${entry.value}';
      }
    }

    // Ensure autoplay settings are correct
    if (widget.autoPlay) {
      embedUrl = embedUrl.replaceAll('autoplay=0', 'autoplay=1');
      if (!embedUrl.contains('muted=')) {
        embedUrl += '&muted=0';
      }
    }

    return embedUrl;
  }

  String _buildOptimizedStandardEmbedUrl() {
    final params = {
      'title': '0',
      'byline': '0',
      'portrait': '0',
      'autoplay': widget.autoPlay ? '1' : '0',
      'muted': widget.autoPlay ? '0' : '0', // Try unmuted autoplay
      'controls': '1',
      'playsinline': '1',
      'webkit-playsinline': '1',
      'allowsInlineMediaPlayback': 'true',
      'gestureNavigationEnabled': 'true',
      'keyboard': '1',
      'pip': '1',
      'dnt': '1',
      'responsive': '1',
      'fullscreen': widget.enableFullscreen ? '1' : '0',
      'referrer': 'https://$_currentDomain/',
      'origin': 'https://$_currentDomain',
    };

    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    return 'https://player.vimeo.com/video/$_vimeoId?$queryString';
  }

  Future<void> _loadOptimizedVideoPlayer() async {
    if (_webViewController == null || _currentEmbedUrl == null) return;

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final optimizedHtml = _buildOptimizedEmbedHtml(_currentEmbedUrl!);
      await _webViewController!.loadHtmlString(
        optimizedHtml,
        baseUrl: 'https://$_currentDomain/',
      );

    } catch (e) {
      debugPrint('🚫 Failed to load optimized video player: $e');
      _handleError(VideoErrorType.loadingError, 'Failed to load video: $e');
    }
  }

  String _buildOptimizedEmbedHtml(String embedUrl) {
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <meta name="referrer" content="origin">
        <style>
            * { margin: 0; padding: 0; box-sizing: border-box; }
            html, body {
                height: 100%;
                background: #000;
                overflow: hidden;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                touch-action: manipulation;
            }
            .video-container {
                position: relative;
                width: 100%;
                height: 100vh;
                background: #000;
            }
            iframe {
                width: 100%;
                height: 100%;
                border: none;
                background: #000;
            }
        </style>
    </head>
    <body>
        <div class="video-container">
            <iframe
                src="$embedUrl"
                frameborder="0"
                allow="autoplay; fullscreen; picture-in-picture; accelerometer; gyroscope; microphone; camera; encrypted-media; web-share"
                allowfullscreen
                webkitallowfullscreen
                mozallowfullscreen
                playsinline
                webkit-playsinline
                referrerpolicy="origin"
                sandbox="allow-scripts allow-same-origin allow-presentation allow-forms allow-popups allow-popups-to-escape-sandbox allow-orientation-lock">
            </iframe>
        </div>

        <script src="https://player.vimeo.com/api/player.js"></script>
        <script>
            // Optimized Vimeo player with comprehensive error handling
            let player;
            let isPlayerReady = false;
            let lastPosition = 0;
            let errorCount = 0;

            // Initialize player with inline playback optimizations
            function initializeOptimizedPlayer() {
                try {
                    const iframe = document.querySelector('iframe');
                    player = new Vimeo.Player(iframe);

                    // Apply inline playback optimizations
                    iframe.setAttribute('playsinline', 'true');
                    iframe.setAttribute('webkit-playsinline', 'true');
                    iframe.style.webkitPlaysinline = 'true';

                    // Player ready event
                    player.ready().then(() => {
                        isPlayerReady = true;
                        console.log('✅ Optimized Vimeo player ready with inline playback');
                        window.flutter_inappwebview?.callHandler('onPlayerReady');
                    }).catch(handlePlayerError);

                    // Enhanced event listeners with error handling
                    player.on('play', () => {
                        window.flutter_inappwebview?.callHandler('onPlay');
                    });

                    player.on('pause', () => {
                        window.flutter_inappwebview?.callHandler('onPause');
                    });

                    player.on('ended', () => {
                        window.flutter_inappwebview?.callHandler('onEnded');
                    });

                    player.on('timeupdate', (data) => {
                        lastPosition = data.seconds;
                        window.flutter_inappwebview?.callHandler('onTimeUpdate', data.seconds);
                    });

                    // Comprehensive error handling
                    player.on('error', handlePlayerError);

                    // Periodic health check
                    setInterval(healthCheck, 5000);

                } catch (error) {
                    handlePlayerError(error);
                }
            }

            function handlePlayerError(error) {
                errorCount++;
                const errorMessage = error.message || error.name || 'Unknown error';

                console.error('🚫 Optimized Vimeo Player Error:', error);

                // Classify error type for enhanced recovery
                let errorType = 'unknownError';
                if (errorMessage.toLowerCase().includes('network') ||
                    errorMessage.toLowerCase().includes('connection')) {
                    errorType = 'networkError';
                } else if (errorMessage.toLowerCase().includes('privacy') ||
                          errorMessage.toLowerCase().includes('private') ||
                          errorMessage.toLowerCase().includes('restricted')) {
                    errorType = 'privacyError';
                } else if (errorMessage.toLowerCase().includes('password') ||
                          errorMessage.toLowerCase().includes('auth')) {
                    errorType = 'passwordError';
                } else if (errorMessage.toLowerCase().includes('seek') ||
                          errorMessage.toLowerCase().includes('position')) {
                    errorType = 'seekError';
                }

                window.flutter_inappwebview?.callHandler('onError', {
                    type: errorType,
                    message: errorMessage,
                    position: lastPosition,
                    errorCount: errorCount
                });
            }

            function healthCheck() {
                if (!isPlayerReady) return;

                // Check if player is responsive
                player.getCurrentTime().then((time) => {
                    // Player is responsive
                }).catch((error) => {
                    handlePlayerError(error);
                });
            }

            // Initialize when DOM is ready
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initializeOptimizedPlayer);
            } else {
                initializeOptimizedPlayer();
            }

            // Expose optimized functions for Flutter communication
            window.seekTo = function(position) {
                if (player && isPlayerReady) {
                    return player.setCurrentTime(position).catch(handlePlayerError);
                }
                return Promise.reject('Player not ready');
            };

            window.playVideo = function() {
                if (player && isPlayerReady) {
                    return player.play().catch(handlePlayerError);
                }
                return Promise.reject('Player not ready');
            };

            window.pauseVideo = function() {
                if (player && isPlayerReady) {
                    return player.pause().catch(handlePlayerError);
                }
                return Promise.reject('Player not ready');
            };
        </script>
    </body>
    </html>
    ''';
  }

  void _onPageStarted(String url) {
    setState(() {
      _isLoading = true;
    });
    debugPrint('🎬 Optimized player: Page started loading: $url');
  }

  void _onPageFinished(String url) {
    setState(() {
      _isLoading = false;
    });
    debugPrint('🎬 Optimized player: Page finished loading: $url');

    // Setup JavaScript handlers for enhanced communication
    _setupOptimizedJavaScriptHandlers();
  }

  void _setupOptimizedJavaScriptHandlers() {
    if (_webViewController == null) return;

    // Enhanced JavaScript channel setup with comprehensive error handling
    _webViewController!.addJavaScriptChannel(
      'onPlayerReady',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlayerReady = true;
          _isLoading = false;
        });
        debugPrint('✅ Optimized player ready with inline playback support');
        widget.onReady?.call();
      },
    );

    _webViewController!.addJavaScriptChannel(
      'onPlay',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = true;
        });
        debugPrint('▶️ Optimized player: Video playing');
        widget.onPlay?.call();
      },
    );

    _webViewController!.addJavaScriptChannel(
      'onPause',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = false;
        });
        debugPrint('⏸️ Optimized player: Video paused');
        widget.onPause?.call();
      },
    );

    _webViewController!.addJavaScriptChannel(
      'onEnded',
      onMessageReceived: (JavaScriptMessage message) {
        setState(() {
          _isPlaying = false;
        });
        debugPrint('🏁 Optimized player: Video completed');
        widget.onCompleted?.call();
      },
    );

    _webViewController!.addJavaScriptChannel(
      'onTimeUpdate',
      onMessageReceived: (JavaScriptMessage message) {
        try {
          final position = double.parse(message.message).round();
          _currentPosition = position;

          // Update safe position periodically
          if (position > _lastSafePosition + 5) {
            _lastSafePosition = position;
          }

          widget.onProgress?.call(position);
        } catch (e) {
          debugPrint('⚠️ Failed to parse time update: $e');
        }
      },
    );

    _webViewController!.addJavaScriptChannel(
      'onError',
      onMessageReceived: (JavaScriptMessage message) {
        try {
          // Enhanced error handling with automatic recovery
          _handleOptimizedError(message.message);
        } catch (e) {
          _handleError(VideoErrorType.unknownError, 'JavaScript error: ${message.message}');
        }
      },
    );
  }

  void _handleOptimizedError(String errorData) {
    // Parse error data and determine recovery strategy
    VideoErrorType errorType = VideoErrorType.unknownError;
    String errorMessage = errorData;

    if (errorData.contains('network') || errorData.contains('connection')) {
      errorType = VideoErrorType.networkError;
    } else if (errorData.contains('privacy') || errorData.contains('restricted')) {
      errorType = VideoErrorType.privacyError;
    } else if (errorData.contains('password') || errorData.contains('auth')) {
      errorType = VideoErrorType.passwordError;
    } else if (errorData.contains('seek') || errorData.contains('position')) {
      errorType = VideoErrorType.seekError;
    }

    debugPrint('🚫 Optimized player error: $errorType - $errorMessage');
    _handleError(errorType, errorMessage);
  }

  void _onWebResourceError(WebResourceError error) {
    debugPrint('🚫 Optimized WebView resource error: ${error.description}');

    // Enhanced error classification for optimized recovery
    VideoErrorType errorType = VideoErrorType.networkError;

    if (error.description.contains('net::ERR_FAILED') ||
        error.description.contains('net::ERR_NETWORK_CHANGED') ||
        error.description.contains('net::ERR_INTERNET_DISCONNECTED')) {
      errorType = VideoErrorType.networkError;
    } else if (error.description.contains('net::ERR_BLOCKED_BY_CLIENT') ||
               error.description.contains('net::ERR_ACCESS_DENIED')) {
      errorType = VideoErrorType.privacyError;
    } else if (error.description.contains('net::ERR_INVALID_URL')) {
      errorType = VideoErrorType.loadingError;
    }

    _handleError(errorType, error.description);
  }

  NavigationDecision _onNavigationRequest(NavigationRequest request) {
    debugPrint('🔗 Optimized player navigation: ${request.url}');

    // Allow all Vimeo-related navigation with enhanced domain support
    if (request.url.contains('vimeo.com') ||
        request.url.contains('player.vimeo.com') ||
        request.url.contains('vimeocdn.com') ||
        request.url.contains(_currentDomain)) {
      return NavigationDecision.navigate;
    }

    return NavigationDecision.prevent;
  }

  void _handleError(VideoErrorType errorType, String errorMessage) {
    if (_isRecovering) return; // Don't handle errors during recovery

    setState(() {
      _hasError = true;
      _currentErrorType = errorType;
      _currentErrorMessage = errorMessage;
    });

    debugPrint('🚫 Optimized player error: $errorType - $errorMessage');

    // Start enhanced recovery process
    _startOptimizedErrorRecovery();

    // Notify parent widget
    widget.onError?.call(errorMessage);
  }

  Future<void> _startOptimizedErrorRecovery() async {
    if (_isRecovering) return;

    setState(() {
      _isRecovering = true;
      _recoveryProgress = 'Analyzing error with optimized recovery...';
    });

    try {
      final recovery = await _errorHandler.handleVideoError(
        videoId: widget.video.id.toString(),
        errorType: _currentErrorType!,
        errorMessage: _currentErrorMessage!,
        vimeoId: _vimeoId,
        context: {
          'current_position': _currentPosition,
          'last_safe_position': _lastSafePosition,
          'inline_playback_enabled': _allowsInlineMediaPlayback,
          'gesture_navigation_enabled': _gestureNavigationEnabled,
          'current_domain': _currentDomain,
        },
        onRecoveryStart: () {
          setState(() {
            _recoveryProgress = 'Starting optimized recovery...';
          });
        },
        onRecoveryProgress: (progress) {
          setState(() {
            _recoveryProgress = progress;
          });
        },
        onRecoveryComplete: (recovery) {
          _handleOptimizedRecoveryComplete(recovery);
        },
      );

      _handleOptimizedRecoveryComplete(recovery);

    } catch (e) {
      debugPrint('🚫 Optimized error recovery failed: $e');
      _handleRecoveryFailure('Optimized recovery process failed: $e');
    }
  }

  void _handleOptimizedRecoveryComplete(VideoErrorRecovery recovery) {
    setState(() {
      _isRecovering = false;
      _recoveryProgress = null;
    });

    if (recovery.success) {
      setState(() {
        _hasError = false;
        _currentErrorType = null;
        _currentErrorMessage = null;
      });

      debugPrint('✅ Optimized recovery successful: ${recovery.strategy}');
      _executeOptimizedRecoveryAction(recovery);

    } else {
      debugPrint('🚫 Optimized recovery failed: ${recovery.errorMessage}');
      _handleRecoveryFailure(recovery.errorMessage ?? 'Optimized recovery failed');
    }
  }

  Future<void> _executeOptimizedRecoveryAction(VideoErrorRecovery recovery) async {
    switch (recovery.actionRequired) {
      case VideoRecoveryAction.reloadPlayer:
        await _reloadOptimizedPlayer();
        break;

      case VideoRecoveryAction.updateEmbedUrl:
        if (recovery.newEmbedUrl != null) {
          await _updateOptimizedEmbedUrl(recovery.newEmbedUrl!);
        }
        break;

      case VideoRecoveryAction.seekToPosition:
        final position = recovery.metadata?['seek_position'] ?? _lastSafePosition;
        await _seekToOptimizedPosition(position);
        break;

      case VideoRecoveryAction.showError:
        // Error will remain visible
        break;

      case null:
        // No action required
        break;
    }
  }

  Future<void> _reloadOptimizedPlayer() async {
    setState(() {
      _isLoading = true;
      _isPlayerReady = false;
    });

    debugPrint('🔄 Reloading optimized player...');
    await Future.delayed(const Duration(milliseconds: 500));
    await _loadOptimizedVideoPlayer();
  }

  Future<void> _updateOptimizedEmbedUrl(String newEmbedUrl) async {
    debugPrint('🔄 Updating optimized embed URL: $newEmbedUrl');
    _currentEmbedUrl = _optimizeEmbedUrlForInlinePlayback(newEmbedUrl);
    await _reloadOptimizedPlayer();
  }

  Future<void> _seekToOptimizedPosition(int position) async {
    if (_webViewController == null || !_isPlayerReady) return;

    try {
      debugPrint('🎯 Seeking to optimized position: ${position}s');
      await _webViewController!.runJavaScript('window.seekTo($position)');
    } catch (e) {
      debugPrint('⚠️ Failed to seek to optimized position: $e');
    }
  }

  void _handleRecoveryFailure(String errorMessage) {
    setState(() {
      _isRecovering = false;
      _recoveryProgress = null;
      _hasError = true;
      _currentErrorMessage = errorMessage;
    });
  }

  void _retryOptimizedPlayer() {
    setState(() {
      _hasError = false;
      _currentErrorType = null;
      _currentErrorMessage = null;
    });

    _reloadOptimizedPlayer();
  }

  String? _extractVimeoId(String url) {
    final regex = RegExp(r'vimeo\.com/(?:video/)?(\d+)');
    final match = regex.firstMatch(url);
    return match?.group(1);
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: widget.aspectRatio,
      child: Container(
        color: Colors.black,
        child: Stack(
          children: [
            // Main optimized video player
            if (_webViewController != null && !_hasError)
              WebViewWidget(controller: _webViewController!),

            // Loading indicator
            if (_isLoading && !_hasError)
              const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    KFTDesignSystem.primaryColor,
                  ),
                ),
              ),

            // Enhanced error overlay with optimized recovery
            if (_hasError)
              VideoErrorOverlay(
                errorType: _currentErrorType ?? VideoErrorType.unknownError,
                errorMessage: _currentErrorMessage ?? 'Unknown error occurred',
                isRecovering: _isRecovering,
                recoveryProgress: _recoveryProgress,
                onRetry: _retryOptimizedPlayer,
                showRetryButton: !_isRecovering,
                estimatedRecoveryTime: const Duration(seconds: 8),
                customIcon: Icon(
                  Icons.play_circle_outline,
                  size: 32,
                  color: KFTDesignSystem.primaryColor,
                ),
              ),

            // Progress indicator overlay
            if (widget.showProgressIndicator && _isLoading && !_hasError)
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: LinearProgressIndicator(
                  backgroundColor: Colors.transparent,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    KFTDesignSystem.primaryColor,
                  ),
                ),
              ),

            // Optimized player status indicator (debug mode only)
            if (kDebugMode)
              Positioned(
                top: 8,
                left: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'OPTIMIZED',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
