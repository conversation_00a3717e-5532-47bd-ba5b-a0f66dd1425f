import 'package:flutter/material.dart';

class HomeStoriesWidget extends StatelessWidget {
  final List<Widget> stories;

  const HomeStoriesWidget({
    Key? key,
    required this.stories,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 100,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: stories.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: const EdgeInsets.only(right: 8.0),
            child: stories[index],
          );
        },
      ),
    );
  }
} 