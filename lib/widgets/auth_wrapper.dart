import 'package:flutter/material.dart';
import 'dart:async';
import '../services/auth_service.dart';
import '../services/api_service.dart';

class AuthWrapper extends StatefulWidget {
  final Widget child;
  final String loginRoute;

  const AuthWrapper({
    Key? key,
    required this.child,
    this.loginRoute = '/login',
  }) : super(key: key);

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> with WidgetsBindingObserver {
  final AuthService _authService = AuthService();
  final ApiService _apiService = ApiService();
  
  StreamSubscription<bool>? _authStateSubscription;
  Timer? _tokenCheckTimer;
  bool _isCheckingAuth = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeAuth();
    _startPeriodicTokenCheck();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _authStateSubscription?.cancel();
    _tokenCheckTimer?.cancel();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Check authentication when app comes to foreground
    if (state == AppLifecycleState.resumed) {
      _checkAuthenticationState();
    }
  }

  Future<void> _initializeAuth() async {
    await _authService.initialize();
    
    // Listen to authentication state changes
    _authStateSubscription = _authService.authStateStream.listen((isAuthenticated) {
      if (!isAuthenticated && mounted) {
        _handleAuthenticationLost();
      }
    });
  }

  void _startPeriodicTokenCheck() {
    // Check token validity every 5 minutes
    _tokenCheckTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _checkAuthenticationState();
    });
  }

  Future<void> _checkAuthenticationState() async {
    if (_isCheckingAuth) return;
    
    _isCheckingAuth = true;
    
    try {
      final needsReauth = await _authService.needsReauth();
      
      if (needsReauth && mounted) {
        _handleAuthenticationLost();
      }
    } catch (e) {
      debugPrint('Error checking authentication state: $e');
    } finally {
      _isCheckingAuth = false;
    }
  }

  void _handleAuthenticationLost() async {
    if (!mounted) return;
    // Try auto-login using persistent auth service
    final autoLoginSuccess = await _authService.autoLogin();
    if (autoLoginSuccess) {
      // Auto-login succeeded, do nothing (StreamBuilder will rebuild)
      return;
    } else {
      // Auto-login failed, navigate to login
      _navigateToLogin();
    }
  }

  void _navigateToLogin() {
    if (!mounted) return;

    Navigator.of(context).pushNamedAndRemoveUntil(
      widget.loginRoute,
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<bool>(
      stream: _authService.authStateStream,
      initialData: _authService.isAuthenticated,
      builder: (context, snapshot) {
        final isAuthenticated = snapshot.data ?? false;
        
        if (!isAuthenticated) {
          // If not authenticated, try auto-login
          WidgetsBinding.instance.addPostFrameCallback((_) async {
            if (mounted) {
              final autoLoginSuccess = await _authService.autoLogin();
              if (!autoLoginSuccess && mounted) {
                _navigateToLogin();
              }
            }
          });
          // Show loading while attempting auto-login
          return const Scaffold(
            body: Center(
              child: CircularProgressIndicator(),
            ),
          );
        }
        
        return widget.child;
      },
    );
  }
}

// Global error handler for authentication errors
class AuthErrorHandler {
  static void handleAuthError(BuildContext context, dynamic error) {
    if (error is AuthenticationException) {
      _showAuthErrorDialog(context, error.message);
    } else if (error.toString().contains('Authentication') || 
               error.toString().contains('Session expired') ||
               error.toString().contains('Token')) {
      _showAuthErrorDialog(context, 'Your session has expired. Please log in again.');
    }
  }

  static void _showAuthErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('Authentication Error'),
          ],
        ),
        content: Text(message),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToLogin(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Login Again'),
          ),
        ],
      ),
    );
  }

  static void _navigateToLogin(BuildContext context) {
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/login',
      (route) => false,
    );
  }
}

// Mixin for pages that need authentication error handling
mixin AuthErrorMixin<T extends StatefulWidget> on State<T> {
  void handleApiError(dynamic error) {
    if (mounted) {
      AuthErrorHandler.handleAuthError(context, error);
    }
  }

  Future<void> safeApiCall(Future<void> Function() apiCall) async {
    try {
      await apiCall();
    } catch (e) {
      handleApiError(e);
    }
  }

  Future<R?> safeApiCallWithResult<R>(Future<R> Function() apiCall) async {
    try {
      return await apiCall();
    } catch (e) {
      handleApiError(e);
      return null;
    }
  }
}
