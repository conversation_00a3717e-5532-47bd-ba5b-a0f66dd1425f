import 'package:flutter/material.dart';
import '../models/motivational_quote.dart';
import '../design_system/kft_design_system.dart';
import 'dart:math';
import 'dart:ui';

class DailyMotivationalQuote extends StatelessWidget {
  final MotivationalQuote quote;
  final bool isLoading;
  final VoidCallback? onRefresh;
  final VoidCallback? onSettings;
  final bool showActions;
  final bool showCard;

  const DailyMotivationalQuote({
    Key? key,
    required this.quote,
    this.isLoading = false,
    this.onRefresh,
    this.onSettings,
    this.showActions = false, // Default to false for minimalistic design
    this.showCard = true, // Whether to show the card or not
  }) : super(key: key);

  // Generate a subtle gradient based on the quote content
  List<Color> _getQuoteGradient(String quoteText) {
    // Use the hash of the quote to generate a consistent gradient
    final int hash = quoteText.hashCode.abs();
    final random = Random(hash);

    // Generate primary color (fitness-themed)
    final baseHue = 200 + random.nextDouble() * 40; // Blue to purple range

    // Create a gradient with two colors
    return [
      HSLColor.fromAHSL(
        0.8, // High opacity for gradient
        baseHue,
        0.6, // Medium saturation
        0.3, // Darker shade for gradient start
      ).toColor(),
      HSLColor.fromAHSL(
        0.8, // High opacity for gradient
        baseHue + 30,
        0.5, // Medium saturation
        0.5, // Lighter shade for gradient end
      ).toColor(),
    ];
  }

  // Get a contrasting text color based on background brightness
  Color _getTextColor(List<Color> gradientColors) {
    // Calculate the average brightness of the gradient
    final avgColor = Color.lerp(gradientColors[0], gradientColors[1], 0.5)!;
    final brightness = avgColor.computeLuminance();

    // Return white for dark backgrounds, dark for light backgrounds
    return brightness < 0.5 ? Colors.white : Colors.black87;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // If quotes are disabled, return an empty container
    if (!showCard) {
      return const SizedBox.shrink();
    }

    if (isLoading) {
      return Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: theme.dividerColor.withOpacity(0.1)),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Quote icon placeholder
              Container(
                width: 16,
                height: 16,
                color: theme.colorScheme.onSurface.withOpacity(0.05),
              ),
              const SizedBox(width: 8),

              // Content placeholders
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Quote text placeholder
                    Container(
                      width: double.infinity,
                      height: 14,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      width: MediaQuery.of(context).size.width * 0.6,
                      height: 14,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    const SizedBox(height: 8),

                    // Author placeholder
                    Container(
                      width: MediaQuery.of(context).size.width * 0.3,
                      height: 10,
                      decoration: BoxDecoration(
                        color: theme.colorScheme.onSurface.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Get a consistent gradient based on the quote
    final gradientColors = _getQuoteGradient(quote.quote);
    final textColor = _getTextColor(gradientColors);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: theme.dividerColor.withOpacity(0.1)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Quote icon
            Icon(
              Icons.format_quote,
              size: 16,
              color: theme.colorScheme.primary.withOpacity(0.5),
            ),
            const SizedBox(width: 8),

            // Quote content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '"${quote.quote}"',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '— ${quote.displayAuthor}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (showActions)
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (onRefresh != null)
                              InkWell(
                                onTap: onRefresh,
                                borderRadius: BorderRadius.circular(4),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Icon(
                                    Icons.refresh_rounded,
                                    size: 14,
                                    color: theme.colorScheme.primary.withOpacity(0.7),
                                  ),
                                ),
                              ),
                            if (onRefresh != null && onSettings != null)
                              const SizedBox(width: 4),
                            if (onSettings != null)
                              InkWell(
                                onTap: onSettings,
                                borderRadius: BorderRadius.circular(4),
                                child: Padding(
                                  padding: const EdgeInsets.all(4.0),
                                  child: Icon(
                                    Icons.settings,
                                    size: 14,
                                    color: theme.colorScheme.primary.withOpacity(0.7),
                                  ),
                                ),
                              ),
                          ],
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
