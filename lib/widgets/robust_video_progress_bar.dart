import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../services/robust_seek_service.dart';

/// Enhanced video progress bar with robust seeking capabilities
class RobustVideoProgressBar extends StatefulWidget {
  final int currentPosition;
  final int duration;
  final bool isPlaying;
  final bool isSeekInProgress;
  final Function(int position) onSeek;
  final Function(int position)? onSeekStart;
  final Function(int position)? onSeekEnd;
  final Color? progressColor;
  final Color? backgroundColor;
  final Color? thumbColor;
  final double height;
  final bool showTimeLabels;
  final bool enableHapticFeedback;
  final bool enableSeekPreview;

  const RobustVideoProgressBar({
    Key? key,
    required this.currentPosition,
    required this.duration,
    required this.isPlaying,
    required this.onSeek,
    this.isSeekInProgress = false,
    this.onSeekStart,
    this.onSeekEnd,
    this.progressColor,
    this.backgroundColor,
    this.thumbColor,
    this.height = 4.0,
    this.showTimeLabels = true,
    this.enableHapticFeedback = true,
    this.enableSeekPreview = true,
  }) : super(key: key);

  @override
  State<RobustVideoProgressBar> createState() => _RobustVideoProgressBarState();
}

class _RobustVideoProgressBarState extends State<RobustVideoProgressBar>
    with TickerProviderStateMixin {
  
  late RobustSeekService _seekService;
  late AnimationController _progressAnimationController;
  late AnimationController _thumbAnimationController;
  late Animation<double> _progressAnimation;
  late Animation<double> _thumbScaleAnimation;
  
  bool _isDragging = false;
  bool _isHovering = false;
  double _dragPosition = 0.0;
  int _previewPosition = 0;
  Timer? _progressUpdateTimer;
  Timer? _seekFeedbackTimer;
  
  // Visual feedback state
  bool _showSeekFeedback = false;
  String _seekFeedbackText = '';
  bool _seekFeedbackSuccess = false;
  
  // Performance optimization
  DateTime? _lastProgressUpdate;
  static const Duration _progressUpdateThrottle = Duration(milliseconds: 100);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeSeekService();
  }

  void _initializeAnimations() {
    _progressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _thumbAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressAnimationController,
      curve: Curves.easeInOut,
    ));
    
    _thumbScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _thumbAnimationController,
      curve: Curves.elasticOut,
    ));
  }

  void _initializeSeekService() {
    _seekService = RobustSeekService(
      onSeekStart: (position) {
        setState(() {
          _showSeekFeedback = true;
          _seekFeedbackText = 'Seeking to ${_formatTime(position)}...';
          _seekFeedbackSuccess = false;
        });
        widget.onSeekStart?.call(position);
        _thumbAnimationController.forward();
      },
      onSeekSuccess: (position) {
        setState(() {
          _seekFeedbackText = 'Seeked to ${_formatTime(position)}';
          _seekFeedbackSuccess = true;
        });
        widget.onSeekEnd?.call(position);
        _thumbAnimationController.reverse();
        _hideSeekFeedbackAfterDelay();
        
        if (widget.enableHapticFeedback) {
          HapticFeedback.lightImpact();
        }
      },
      onSeekFailure: (position, error) {
        setState(() {
          _seekFeedbackText = 'Seek failed';
          _seekFeedbackSuccess = false;
        });
        widget.onSeekEnd?.call(position);
        _thumbAnimationController.reverse();
        _hideSeekFeedbackAfterDelay();
        
        if (widget.enableHapticFeedback) {
          HapticFeedback.heavyImpact();
        }
      },
      onSeekThrottled: (position) {
        setState(() {
          _seekFeedbackText = 'Seeking throttled...';
          _seekFeedbackSuccess = false;
        });
      },
      onSeekDebounced: (position) {
        setState(() {
          _seekFeedbackText = 'Seeking...';
          _seekFeedbackSuccess = false;
        });
      },
      onSeekFeedback: (message, isSuccess) {
        setState(() {
          _seekFeedbackText = message;
          _seekFeedbackSuccess = isSuccess;
          _showSeekFeedback = true;
        });
        if (isSuccess) {
          _hideSeekFeedbackAfterDelay();
        }
      },
    );
  }

  void _hideSeekFeedbackAfterDelay() {
    _seekFeedbackTimer?.cancel();
    _seekFeedbackTimer = Timer(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _showSeekFeedback = false;
        });
      }
    });
  }

  @override
  void didUpdateWidget(RobustVideoProgressBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Throttle progress updates for performance
    final now = DateTime.now();
    if (_lastProgressUpdate == null || 
        now.difference(_lastProgressUpdate!) > _progressUpdateThrottle) {
      _lastProgressUpdate = now;
      _updateProgressAnimation();
    }
  }

  void _updateProgressAnimation() {
    if (!_isDragging && widget.duration > 0) {
      final progress = widget.currentPosition / widget.duration;
      _progressAnimationController.animateTo(progress.clamp(0.0, 1.0));
    }
  }

  void _handleDragStart(DragStartDetails details) {
    setState(() {
      _isDragging = true;
      _isHovering = true;
    });
    
    _thumbAnimationController.forward();
    
    if (widget.enableHapticFeedback) {
      HapticFeedback.selectionClick();
    }
  }

  void _handleDragUpdate(DragUpdateDetails details, BoxConstraints constraints) {
    if (!_isDragging) return;
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final progress = (localPosition.dx / constraints.maxWidth).clamp(0.0, 1.0);
    
    setState(() {
      _dragPosition = progress;
      _previewPosition = (progress * widget.duration).round();
    });
  }

  void _handleDragEnd(DragEndDetails details) {
    if (!_isDragging) return;
    
    setState(() {
      _isDragging = false;
      _isHovering = false;
    });
    
    final targetPosition = (_dragPosition * widget.duration).round();
    
    // Use robust seek service for the actual seek operation
    _seekService.seekToPosition(
      targetPosition,
      seekImplementation: (position) async {
        widget.onSeek(position);
        return true; // Assume success for now
      },
      videoDuration: widget.duration,
    );
  }

  void _handleTapSeek(TapUpDetails details, BoxConstraints constraints) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final progress = (localPosition.dx / constraints.maxWidth).clamp(0.0, 1.0);
    final targetPosition = (progress * widget.duration).round();
    
    if (widget.enableHapticFeedback) {
      HapticFeedback.lightImpact();
    }
    
    // Use robust seek service for tap seeks
    _seekService.seekToPosition(
      targetPosition,
      seekImplementation: (position) async {
        widget.onSeek(position);
        return true;
      },
      videoDuration: widget.duration,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Seek feedback overlay
        if (_showSeekFeedback)
          _buildSeekFeedback(),
        
        // Time labels
        if (widget.showTimeLabels)
          _buildTimeLabels(),
        
        const SizedBox(height: 8),
        
        // Progress bar
        _buildProgressBar(),
        
        // Seek preview
        if (widget.enableSeekPreview && _isDragging)
          _buildSeekPreview(),
      ],
    );
  }

  Widget _buildSeekFeedback() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: _seekFeedbackSuccess 
            ? Colors.green.withOpacity(0.9)
            : Colors.orange.withOpacity(0.9),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Text(
        _seekFeedbackText,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildTimeLabels() {
    final currentTime = _isDragging ? _previewPosition : widget.currentPosition;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          _formatTime(currentTime),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          _formatTime(widget.duration),
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return MouseRegion(
          onEnter: (_) => setState(() => _isHovering = true),
          onExit: (_) => setState(() => _isHovering = false),
          child: GestureDetector(
            onPanStart: _handleDragStart,
            onPanUpdate: (details) => _handleDragUpdate(details, constraints),
            onPanEnd: _handleDragEnd,
            onTapUp: (details) => _handleTapSeek(details, constraints),
            child: Container(
              height: max(widget.height * 3, 24), // Minimum touch target
              width: double.infinity,
              child: CustomPaint(
                painter: _ProgressBarPainter(
                  progress: _isDragging 
                      ? _dragPosition 
                      : _progressAnimation.value,
                  isHovering: _isHovering,
                  isDragging: _isDragging,
                  isSeekInProgress: widget.isSeekInProgress,
                  thumbScale: _thumbScaleAnimation.value,
                  progressColor: widget.progressColor ?? Theme.of(context).primaryColor,
                  backgroundColor: widget.backgroundColor ?? Colors.grey[300]!,
                  thumbColor: widget.thumbColor ?? Theme.of(context).primaryColor,
                  height: widget.height,
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSeekPreview() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        'Seeking to ${_formatTime(_previewPosition)}',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 11,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _progressAnimationController.dispose();
    _thumbAnimationController.dispose();
    _progressUpdateTimer?.cancel();
    _seekFeedbackTimer?.cancel();
    _seekService.dispose();
    super.dispose();
  }
}

/// Custom painter for the progress bar
class _ProgressBarPainter extends CustomPainter {
  final double progress;
  final bool isHovering;
  final bool isDragging;
  final bool isSeekInProgress;
  final double thumbScale;
  final Color progressColor;
  final Color backgroundColor;
  final Color thumbColor;
  final double height;

  _ProgressBarPainter({
    required this.progress,
    required this.isHovering,
    required this.isDragging,
    required this.isSeekInProgress,
    required this.thumbScale,
    required this.progressColor,
    required this.backgroundColor,
    required this.thumbColor,
    required this.height,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;
    
    final trackRect = Rect.fromLTWH(
      0,
      (size.height - height) / 2,
      size.width,
      height,
    );
    
    // Draw background track
    paint.color = backgroundColor;
    canvas.drawRRect(
      RRect.fromRectAndRadius(trackRect, Radius.circular(height / 2)),
      paint,
    );
    
    // Draw progress
    final progressWidth = size.width * progress;
    if (progressWidth > 0) {
      final progressRect = Rect.fromLTWH(
        0,
        (size.height - height) / 2,
        progressWidth,
        height,
      );
      
      paint.color = isSeekInProgress 
          ? progressColor.withOpacity(0.7)
          : progressColor;
      canvas.drawRRect(
        RRect.fromRectAndRadius(progressRect, Radius.circular(height / 2)),
        paint,
      );
    }
    
    // Draw thumb
    if (isHovering || isDragging) {
      final thumbRadius = (height * 2) * thumbScale;
      final thumbCenter = Offset(progressWidth, size.height / 2);
      
      // Thumb shadow
      paint.color = Colors.black26;
      canvas.drawCircle(
        thumbCenter.translate(1, 1),
        thumbRadius,
        paint,
      );
      
      // Thumb
      paint.color = thumbColor;
      canvas.drawCircle(thumbCenter, thumbRadius, paint);
      
      // Inner highlight
      paint.color = Colors.white.withOpacity(0.3);
      canvas.drawCircle(thumbCenter, thumbRadius * 0.6, paint);
    }
  }

  @override
  bool shouldRepaint(covariant _ProgressBarPainter oldDelegate) {
    return progress != oldDelegate.progress ||
           isHovering != oldDelegate.isHovering ||
           isDragging != oldDelegate.isDragging ||
           isSeekInProgress != oldDelegate.isSeekInProgress ||
           thumbScale != oldDelegate.thumbScale;
  }
}
