import 'package:flutter/material.dart';
import '../pages/home_page.dart';
import '../pages/workout_page.dart';
import '../pages/nutrition_page.dart';
import '../pages/profile_page_new.dart';
import 'kft_bottom_navigation.dart';

class KFTBottomNav extends StatefulWidget {
  const KFTBottomNav({Key? key}) : super(key: key);

  @override
  _KFTBottomNavState createState() => _KFTBottomNavState();
}

class _KFTBottomNavState extends State<KFTBottomNav> {
  int _currentIndex = 0;
  final List<Widget> _pages = [
    const HomePage(),
    const WorkoutPage(),
    const NutritionPage(),
    const ProfilePageNew(),
  ];

  final List<KFTBottomNavigationItem> _items = [
    const KFTBottomNavigationItem(
      label: 'Home',
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
    ),
    const KFTBottomNavigationItem(
      label: 'Workouts',
      icon: Icons.fitness_center_outlined,
      activeIcon: Icons.fitness_center,
    ),
    const KFTBottomNavigationItem(
      label: 'Nutrition',
      icon: Icons.restaurant_menu_outlined,
      activeIcon: Icons.restaurant_menu,
    ),
    const KFTBottomNavigationItem(
      label: 'Profile',
      icon: Icons.person_outline,
      activeIcon: Icons.person,
    ),
  ];

  void _onItemTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: KFTBottomNavigation(
        items: _items,
        currentIndex: _currentIndex,
        onTap: _onItemTapped,
      ),
    );
  }
}
