import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class DailyStreakRing extends StatefulWidget {
  final int currentStreak;
  final int highestStreak;
  final int nextMilestone;
  final bool isMilestone;
  final VoidCallback? onTap;

  const DailyStreakRing({
    Key? key,
    required this.currentStreak,
    required this.highestStreak,
    required this.nextMilestone,
    required this.isMilestone,
    this.onTap,
  }) : super(key: key);

  @override
  State<DailyStreakRing> createState() => _DailyStreakRingState();
}

class _DailyStreakRingState extends State<DailyStreakRing> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 900),
    );
    _progressAnimation = CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    );
    _glowAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOutCubic,
      ),
    );
    _controller.forward();
    if (widget.isMilestone) {
      HapticFeedback.heavyImpact();
    }
  }

  @override
  void didUpdateWidget(covariant DailyStreakRing oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStreak != widget.currentStreak) {
      _controller.forward(from: 0);
      if (widget.isMilestone) {
        HapticFeedback.heavyImpact();
      } else {
        HapticFeedback.lightImpact();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final ringColor = isDark
        ? [theme.colorScheme.primary, theme.colorScheme.secondary]
        : [theme.colorScheme.primary, theme.colorScheme.secondary];
    final backgroundColor = isDark
        ? Colors.white.withOpacity(0.05)
        : Colors.black.withOpacity(0.04);
    final milestoneGlow = widget.isMilestone ? 0.45 : 0.18;
    final progress = widget.currentStreak / widget.nextMilestone;

    return GestureDetector(
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _controller,
        builder: (context, child) {
          return Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: ringColor.first.withOpacity(milestoneGlow * _glowAnimation.value),
                  blurRadius: 32 * _glowAnimation.value,
                  spreadRadius: 2 * _glowAnimation.value,
                ),
              ],
            ),
            child: Stack(
              alignment: Alignment.center,
              children: [
                // Background ring
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: 1.0,
                    strokeWidth: 10,
                    valueColor: AlwaysStoppedAnimation<Color>(backgroundColor),
                  ),
                ),
                // Animated progress ring
                SizedBox(
                  width: 120,
                  height: 120,
                  child: CircularProgressIndicator(
                    value: _progressAnimation.value * progress,
                    strokeWidth: 10,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      _createGradientShader(ringColor, _progressAnimation.value * progress),
                    ),
                    backgroundColor: Colors.transparent,
                  ),
                ),
                // Center content
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '${widget.currentStreak}',
                      style: theme.textTheme.displaySmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: ringColor.first,
                        letterSpacing: -1.5,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'day streak',
                      style: theme.textTheme.labelMedium?.copyWith(
                        color: theme.hintColor,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.2,
                      ),
                    ),
                    if (widget.highestStreak > 1)
                      Padding(
                        padding: const EdgeInsets.only(top: 4.0),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.emoji_events, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              'Best: ${widget.highestStreak}',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: Colors.amber,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
                // Milestone badge
                if (widget.isMilestone)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.greenAccent.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.greenAccent.withOpacity(0.3),
                            blurRadius: 8,
                          ),
                        ],
                      ),
                      child: Text(
                        'Milestone!',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: Colors.green.shade900,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Color _createGradientShader(List<Color> colors, double progress) {
    if (colors.length == 1) return colors.first;
    if (progress <= 0.0) return colors.first;
    if (progress >= 1.0) return colors.last;
    final index = (progress * (colors.length - 1)).floor();
    final t = (progress * (colors.length - 1)) - index;
    return Color.lerp(colors[index], colors[index + 1], t)!;
  }
} 