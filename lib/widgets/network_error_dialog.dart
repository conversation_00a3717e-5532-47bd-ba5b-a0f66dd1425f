import 'package:flutter/material.dart';

class NetworkErrorDialog extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const NetworkErrorDialog({
    super.key,
    required this.error,
    this.onRetry,
  });

  static void show(BuildContext context, String error, {VoidCallback? onRetry}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => NetworkErrorDialog(
        error: error,
        onRetry: onRetry,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.wifi_off, color: Colors.red),
          const SizedBox(width: 8),
          const Text('Network Error'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          const Text(
            'Unable to connect to the server. Please check your internet connection and try again.',
            style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Troubleshooting:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• Check your internet connection'),
                Text('• Try switching between WiFi and mobile data'),
                Text('• Close and reopen the app'),
                Text('• Contact support if the problem persists'),
                  ],
                ),
              ),
            ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
        if (onRetry != null)
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              onRetry!();
            },
            icon: Icon(Icons.refresh, size: 16),
            label: const Text('Retry'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
      ],
    );
  }
}

/// Extension to easily show network error dialogs
extension NetworkErrorDialogExtension on BuildContext {
  void showNetworkError(String error, {VoidCallback? onRetry}) {
    NetworkErrorDialog.show(this, error, onRetry: onRetry);
  }
}
