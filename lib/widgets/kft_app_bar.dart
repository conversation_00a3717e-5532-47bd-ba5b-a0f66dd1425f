import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import '../widgets/theme_toggle_button.dart';
import 'dart:ui';

/// KFT App Bar
/// A custom app bar widget that follows the KFT design system
/// Features a modern, clean design with support for various configurations

enum KFTAppBarType {
  regular,
  transparent,
  blurred,
  modern,
}

class KFTAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final KFTAppBarType type;
  final List<Widget>? actions;
  final Widget? leading;
  final bool automaticallyImplyLeading;
  final bool centerTitle;
  final double elevation;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final SystemUiOverlayStyle? systemOverlayStyle;
  final PreferredSizeWidget? bottom;
  final Widget? flexibleSpace;
  final double? titleSpacing;
  final double? leadingWidth;
  final TextStyle? titleTextStyle;
  final bool showBackButton;
  final VoidCallback? onBackPressed;
  final bool showThemeToggle;

  const KFTAppBar({
    Key? key,
    required this.title,
    this.type = KFTAppBarType.regular,
    this.actions,
    this.leading,
    this.automaticallyImplyLeading = true,
    this.centerTitle = false,
    this.elevation = 0,
    this.backgroundColor,
    this.foregroundColor,
    this.systemOverlayStyle,
    this.bottom,
    this.flexibleSpace,
    this.titleSpacing,
    this.leadingWidth,
    this.titleTextStyle,
    this.showBackButton = false,
    this.onBackPressed,
    this.showThemeToggle = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final Color bgColor = _getBackgroundColor(context);
    final Color fgColor = foregroundColor ?? KFTDesignSystem.textPrimaryColor;

    // Determine system overlay style based on background color
    final SystemUiOverlayStyle overlayStyle = systemOverlayStyle ??
        (bgColor.computeLuminance() > 0.5
            ? SystemUiOverlayStyle.dark.copyWith(statusBarColor: Colors.transparent)
            : SystemUiOverlayStyle.light.copyWith(statusBarColor: Colors.transparent));

    // Determine leading widget
    Widget? leadingWidget = _getLeadingWidget(context);

    // Add theme toggle button to actions if requested
    List<Widget>? appBarActions = actions;
    if (showThemeToggle) {
      appBarActions = [
        const ThemeToggleButton(iconOnly: true),
        ...(actions ?? []),
      ];
    }

    // Build app bar based on type
    switch (type) {
      case KFTAppBarType.transparent:
        return AppBar(
          title: Text(title),
          titleTextStyle: titleTextStyle ?? KFTDesignSystem.headlineMedium,
          centerTitle: centerTitle,
          leading: leadingWidget,
          automaticallyImplyLeading: automaticallyImplyLeading,
          actions: appBarActions,
          backgroundColor: Colors.transparent,
          foregroundColor: fgColor,
          elevation: 0,
          systemOverlayStyle: overlayStyle,
          bottom: bottom,
          flexibleSpace: flexibleSpace,
          titleSpacing: titleSpacing,
          leadingWidth: leadingWidth,
        );

      case KFTAppBarType.blurred:
        return AppBar(
          title: Text(title),
          titleTextStyle: titleTextStyle ?? KFTDesignSystem.headlineMedium,
          centerTitle: centerTitle,
          leading: leadingWidget,
          automaticallyImplyLeading: automaticallyImplyLeading,
          actions: appBarActions,
          backgroundColor: Colors.transparent,
          foregroundColor: fgColor,
          elevation: 0,
          systemOverlayStyle: overlayStyle,
          bottom: bottom,
          flexibleSpace: ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color: bgColor.withOpacity(0.8),
              ),
            ),
          ),
          titleSpacing: titleSpacing,
          leadingWidth: leadingWidth,
        );

      case KFTAppBarType.modern:
        return AppBar(
          title: Text(title),
          titleTextStyle: titleTextStyle ??
              KFTDesignSystem.headlineMedium.copyWith(
                fontWeight: KFTDesignSystem.fontWeightSemiBold,
                letterSpacing: 0.2,
              ),
          centerTitle: centerTitle ?? true, // Default to centered for modern style
          leading: leadingWidget,
          automaticallyImplyLeading: automaticallyImplyLeading,
          actions: appBarActions,
          backgroundColor: Colors.transparent,
          foregroundColor: fgColor,
          elevation: 0,
          systemOverlayStyle: overlayStyle,
          bottom: PreferredSize(
            preferredSize: Size.fromHeight(bottom?.preferredSize.height ?? 1),
            child: Container(
              height: bottom?.preferredSize.height ?? 1,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: KFTDesignSystem.dividerColor.withOpacity(0.5),
                    width: KFTDesignSystem.borderWidthThin,
                  ),
                ),
              ),
              child: bottom,
            ),
          ),
          flexibleSpace: ClipRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).brightness == Brightness.dark
                      ? Theme.of(context).colorScheme.surface.withOpacity(0.85)
                      : Colors.white.withOpacity(0.85),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).shadowColor.withOpacity(0.03),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
              ),
            ),
          ),
          titleSpacing: titleSpacing,
          leadingWidth: leadingWidth,
          iconTheme: IconThemeData(
            color: fgColor,
            size: 22, // Slightly smaller icons for modern look
          ),
          actionsIconTheme: IconThemeData(
            color: fgColor,
            size: 22, // Slightly smaller icons for modern look
          ),
        );

      case KFTAppBarType.regular:
      default:
        return AppBar(
          title: Text(title),
          titleTextStyle: titleTextStyle ?? KFTDesignSystem.headlineMedium,
          centerTitle: centerTitle,
          leading: leadingWidget,
          automaticallyImplyLeading: automaticallyImplyLeading,
          actions: appBarActions,
          backgroundColor: bgColor,
          foregroundColor: fgColor,
          elevation: elevation,
          systemOverlayStyle: overlayStyle,
          bottom: bottom,
          flexibleSpace: flexibleSpace,
          titleSpacing: titleSpacing,
          leadingWidth: leadingWidth,
        );
    }
  }

  Color _getBackgroundColor(BuildContext context) {
    if (backgroundColor != null) {
      return backgroundColor!;
    }

    final theme = Theme.of(context);
    final surfaceColor = theme.colorScheme.surface;

    switch (type) {
      case KFTAppBarType.transparent:
        return Colors.transparent;
      case KFTAppBarType.blurred:
        return surfaceColor.withOpacity(0.5);
      case KFTAppBarType.regular:
      default:
        return surfaceColor;
    }
  }

  Widget? _getLeadingWidget(BuildContext context) {
    if (leading != null) {
      return leading;
    }

    if (showBackButton) {
      return IconButton(
        icon: const Icon(Icons.arrow_back_ios_new_rounded),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      );
    }

    return null;
  }

  @override
  Size get preferredSize => Size.fromHeight(bottom != null
      ? kToolbarHeight + bottom!.preferredSize.height
      : kToolbarHeight);
}
