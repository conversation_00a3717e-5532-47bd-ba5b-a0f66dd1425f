import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'dart:html' as html;
import 'dart:ui' as ui;
import '../models/course_video.dart';

/// Web-specific video player that works on Flutter web
/// Uses HTML5 video elements and iframe embeds for Vimeo
class WebVideoPlayer extends StatefulWidget {
  final CourseVideo video;
  final bool autoPlay;
  final VoidCallback? onReady;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onCompleted;
  final Function(int position)? onProgress;
  final Function(String error)? onError;

  const WebVideoPlayer({
    Key? key,
    required this.video,
    this.autoPlay = true,
    this.onReady,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
  }) : super(key: key);

  @override
  State<WebVideoPlayer> createState() => _WebVideoPlayerState();
}

class _WebVideoPlayerState extends State<WebVideoPlayer> {
  String? _viewId;
  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _initializeWebPlayer();
  }

  void _initializeWebPlayer() {
    try {
      _viewId = 'web-video-player-${widget.video.id}-${DateTime.now().millisecondsSinceEpoch}';
      
      // Register the view factory for web
      ui.platformViewRegistry.registerViewFactory(_viewId!, (int viewId) {
        return _createVideoElement();
      });

      setState(() {
        _isLoading = false;
      });

      // Call onReady after a short delay to ensure the element is created
      Future.delayed(const Duration(milliseconds: 100), () {
        widget.onReady?.call();
      });
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = 'Failed to initialize video player: $e';
      });
      widget.onError?.call(_errorMessage);
    }
  }

  html.Element _createVideoElement() {
    final vimeoId = _extractVimeoId(widget.video.videoUrl);
    
    if (vimeoId != null) {
      // Create Vimeo iframe for web with improved configuration
      final iframe = html.IFrameElement()
        ..style.border = 'none'
        ..style.height = '100%'
        ..style.width = '100%'
        ..allowFullscreen = true
        ..allow = 'autoplay; fullscreen; picture-in-picture; encrypted-media; gyroscope; accelerometer'
        ..setAttribute('webkitPlaysinline', 'true')
        ..setAttribute('playsinline', 'true')
        ..setAttribute('referrerpolicy', 'origin')
        ..setAttribute('frameborder', '0')
        ..src = _buildVimeoEmbedUrl(vimeoId);
      
      // Add event listeners for iframe
      iframe.onLoad.listen((_) {
        debugPrint('Vimeo iframe loaded successfully');
        widget.onReady?.call();
      });
      
      iframe.onError.listen((_) {
        debugPrint('Vimeo iframe failed to load');
        widget.onError?.call('Failed to load Vimeo video');
      });
      
      return iframe;
    } else {
      // Fallback to HTML5 video for direct URLs
      final video = html.VideoElement()
        ..style.height = '100%'
        ..style.width = '100%'
        ..controls = true
        ..autoplay = widget.autoPlay
        ..muted = widget.autoPlay // Mute is required for autoplay to work in most browsers
        ..setAttribute('playsinline', 'true')
        ..setAttribute('webkit-playsinline', 'true')
        ..setAttribute('preload', 'metadata')
        ..src = widget.video.videoUrl;
      
      // Add event listeners
      video.onPlay.listen((_) {
        debugPrint('Video started playing');
        widget.onPlay?.call();
      });
      
      video.onPause.listen((_) {
        debugPrint('Video paused');
        widget.onPause?.call();
      });
      
      video.onEnded.listen((_) {
        debugPrint('Video completed');
        widget.onCompleted?.call();
      });
      
      video.onTimeUpdate.listen((_) {
        if (widget.onProgress != null) {
          widget.onProgress!(video.currentTime.round());
        }
      });
      
      video.onError.listen((_) {
        debugPrint('Video error: ${video.error?.message}');
        widget.onError?.call('Video playback error: ${video.error?.message ?? 'Unknown error'}');
      });
      
      // Force play if autoplay is enabled
      if (widget.autoPlay) {
        video.play().catchError((error) {
          debugPrint('Autoplay failed: $error');
          // Don't show error for autoplay failure, just log it
        });
      }
      
      return video;
    }
  }

  String _buildVimeoEmbedUrl(String vimeoId) {
    final params = <String, String>{
      'autoplay': widget.autoPlay ? '1' : '0',
      'muted': widget.autoPlay ? '1' : '0', // Mute for autoplay to work
      'title': '0',
      'byline': '0',
      'portrait': '0',
      'responsive': '1',
      'controls': '1',
      'playsinline': '1',
      'background': '0', // Ensure video is not in background mode
      'dnt': '1', // Do not track
      'transparent': '0',
    };

    // Add private video hash if available (for the known working video)
    if (vimeoId == '1087487482') {
      params['h'] = 'ae75b6e329';
    }

    // Add referrer for domain verification
    params['referrer'] = 'https://mycloudforge.com/';

    final queryString = params.entries
        .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
        .join('&');

    final embedUrl = 'https://player.vimeo.com/video/$vimeoId?$queryString';
    debugPrint('Vimeo embed URL: $embedUrl');
    
    return embedUrl;
  }

  String? _extractVimeoId(String url) {
    // Handle various Vimeo URL formats
    final patterns = [
      RegExp(r'vimeo\.com/(\d+)'),
      RegExp(r'player\.vimeo\.com/video/(\d+)'),
      RegExp(r'vimeo\.com/video/(\d+)'),
    ];
    
    for (final pattern in patterns) {
      final match = pattern.firstMatch(url);
      if (match != null) {
        return match.group(1);
      }
    }
    
    return null;
  }

  @override
  Widget build(BuildContext context) {
    if (_hasError) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, color: Colors.white, size: 48),
              const SizedBox(height: 16),
              Text(
                'Video Error',
                style: const TextStyle(color: Colors.white, fontSize: 18),
              ),
              const SizedBox(height: 8),
              Text(
                _errorMessage,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _hasError = false;
                    _isLoading = true;
                  });
                  _initializeWebPlayer();
                },
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    if (_isLoading || _viewId == null) {
      return Container(
        color: Colors.black,
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Colors.white),
              SizedBox(height: 16),
              Text(
                'Loading video...',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ],
          ),
        ),
      );
    }

    return AspectRatio(
      aspectRatio: 16 / 9,
      child: HtmlElementView(viewType: _viewId!),
    );
  }
} 