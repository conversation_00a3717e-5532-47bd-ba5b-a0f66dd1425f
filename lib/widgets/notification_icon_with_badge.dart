import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import 'notification_panel.dart';
import '../services/awesome_notification_service.dart';

class NotificationIconWithBadge extends StatefulWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onTap;

  const NotificationIconWithBadge({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onTap,
  });

  @override
  State<NotificationIconWithBadge> createState() => _NotificationIconWithBadgeState();
}

class _NotificationIconWithBadgeState extends State<NotificationIconWithBadge>
    with TickerProviderStateMixin {
  late AnimationController _pressAnimationController;
  late AnimationController _glowAnimationController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  late Animation<double> _opacityAnimation;

  bool _isPressed = false;
  OverlayEntry? _overlayEntry;
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();

  @override
  void initState() {
    super.initState();

    // Initialize notification service
    _notificationService.initialize();

    // Press animation controller (for tap feedback)
    _pressAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: this,
    );

    // Glow animation controller (for premium press effect)
    _glowAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    // Premium scale animation with spring effect
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.0, end: 0.92)
            .chain(CurveTween(curve: Curves.easeInCubic)),
        weight: 30,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 0.92, end: 1.05)
            .chain(CurveTween(curve: Curves.elasticOut)),
        weight: 40,
      ),
      TweenSequenceItem(
        tween: Tween<double>(begin: 1.05, end: 1.0)
            .chain(CurveTween(curve: Curves.easeOutCubic)),
        weight: 30,
      ),
    ]).animate(_pressAnimationController);

    // Glow effect animation
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _glowAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // Opacity animation for press feedback
    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.8,
    ).animate(CurvedAnimation(
      parent: _pressAnimationController,
      curve: const Interval(0.0, 0.3, curve: Curves.easeInOut),
    ));
  }

  @override
  void dispose() {
    _pressAnimationController.dispose();
    _glowAnimationController.dispose();
    _removeOverlay();
    super.dispose();
  }

  // Premium haptic feedback
  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });

    // Trigger premium haptic feedback
    _triggerHapticFeedback();

    // Start press animations
    _pressAnimationController.forward();
    _glowAnimationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });

    // Reverse animations with delay for premium feel
    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        _pressAnimationController.reverse();
        _glowAnimationController.reverse();
      }
    });
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });

    _pressAnimationController.reverse();
    _glowAnimationController.reverse();
  }

  void _onTap() {
    print('DEBUG: Notification icon tapped!');

    if (widget.onTap != null) {
      print('DEBUG: Calling custom onTap callback');
      widget.onTap!();
    } else {
      print('DEBUG: Showing notification panel');
      _showNotificationPanel();
    }
  }

  void _showNotificationPanel() {
    if (_overlayEntry != null) {
      _removeOverlay();
      return;
    }

    _overlayEntry = OverlayEntry(
      builder: (context) => NotificationPanel(
        onClose: _removeOverlay,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = widget.iconColor ??
        (theme.brightness == Brightness.dark
            ? KFTDesignSystem.darkTextPrimaryColor
            : Colors.white);

    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _pressAnimationController,
          _glowAnimationController,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  // Premium glow effect when pressed
                  if (_glowAnimation.value > 0)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: effectiveIconColor.withOpacity(0.3 * _glowAnimation.value),
                              blurRadius: 20 * _glowAnimation.value,
                              spreadRadius: 2 * _glowAnimation.value,
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Notification Icon with opacity animation
                  Opacity(
                    opacity: _opacityAnimation.value,
                    child: Icon(
                      Icons.notifications_outlined,
                      color: effectiveIconColor,
                      size: widget.iconSize,
                    ),
                  ),

                  // Premium ripple effect when pressed
                  if (_isPressed)
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: effectiveIconColor.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(20),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}

// Enhanced notification icon for use in app bars
class EnhancedNotificationIcon extends StatelessWidget {
  final Color? iconColor;
  final double iconSize;
  final VoidCallback? onPressed;

  const EnhancedNotificationIcon({
    super.key,
    this.iconColor,
    this.iconSize = 24,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: NotificationIconWithBadge(
        iconColor: iconColor,
        iconSize: iconSize,
        onTap: onPressed,
      ),
    );
  }
}
