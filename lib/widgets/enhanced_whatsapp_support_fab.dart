import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/api_service.dart';
import '../models/user_profile.dart';
import '../utils/animations.dart';
import '../design_system/kft_design_system.dart';
import '../services/performance_monitor.dart';

/// Enhanced WhatsApp Support FAB with staff integration and animations
class EnhancedWhatsAppSupportFAB extends StatefulWidget {
  final UserProfile? profile;
  final VoidCallback? onStaffDataLoaded;

  const EnhancedWhatsAppSupportFAB({
    Key? key,
    required this.profile,
    this.onStaffDataLoaded,
  }) : super(key: key);

  @override
  State<EnhancedWhatsAppSupportFAB> createState() => _EnhancedWhatsAppSupportFABState();
}

class _EnhancedWhatsAppSupportFABState extends State<EnhancedWhatsAppSupportFAB>
    with TickerProviderStateMixin {
  final ApiService _apiService = ApiService();
  Map<String, String>? _staffData;
  bool _isLoading = true;
  bool _hasError = false;

  late AnimationController _fabController;
  late AnimationController _pulseController;
  late Animation<double> _fabScaleAnimation;
  late Animation<double> _fabOpacityAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStaffData();
  }

  @override
  void didUpdateWidget(EnhancedWhatsAppSupportFAB oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if profile changed and now has an assigned staff ID
    if (oldWidget.profile?.assignedStaffId != widget.profile?.assignedStaffId) {
      print('🔄 Profile changed! Old: ${oldWidget.profile?.assignedStaffId}, New: ${widget.profile?.assignedStaffId}');

      if (widget.profile?.assignedStaffId != null && _staffData == null) {
        print('✅ Profile now has assigned staff ID, loading staff data...');
        _loadStaffData();
      }
    }
  }

  void _initializeAnimations() {
    _fabController = AnimationController(
      duration: const Duration(milliseconds: 0), // Disable animation
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 0), // Disable animation
      vsync: this,
    );

    _fabScaleAnimation = Tween<double>(
      begin: 1.0, // Always visible
      end: 1.0,   // No scaling
    ).animate(CurvedAnimation(
      parent: _fabController,
      curve: Curves.linear,
    ));

    _fabOpacityAnimation = Tween<double>(
      begin: 1.0, // Always visible
      end: 1.0,   // No opacity change
    ).animate(CurvedAnimation(
      parent: _fabController,
      curve: Curves.linear,
    ));

    _pulseAnimation = Tween<double>(
      begin: 1.0, // No pulse
      end: 1.0,   // No pulse
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.linear,
    ));

    // No pulse animation - keep static
  }

  Future<void> _loadStaffData() async {
    print('🔄 Loading staff data...');
    print('👤 Profile assigned staff ID: ${widget.profile?.assignedStaffId}');

    if (widget.profile?.assignedStaffId == null) {
      print('❌ No assigned staff ID found');
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final stopwatch = Stopwatch()..start();

      final staffData = await _apiService.getStaffInfo(widget.profile!.assignedStaffId!);

      stopwatch.stop();
      PerformanceMonitor().logEvent('staff_data_load', {
        'duration_ms': stopwatch.elapsedMilliseconds,
        'staff_id': widget.profile!.assignedStaffId!,
        'success': staffData != null,
      });

      print('📊 Staff data loaded: $staffData');

      if (mounted) {
        setState(() {
          _staffData = staffData;
          _isLoading = false;
          _hasError = staffData == null;
        });

        if (staffData != null) {
          print('✅ Staff data loaded successfully, triggering callbacks');
          // No animation - just trigger callback
          widget.onStaffDataLoaded?.call();
        } else {
          print('❌ Staff data is null');
        }
      }
    } catch (e) {
      print('❌ Error loading staff data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  Future<void> _openWhatsAppChat() async {
    print('🎯 WhatsApp FAB tapped!');
    print('📊 Staff data: $_staffData');
    print('👤 Profile: ${widget.profile?.name}');

    if (_staffData == null) {
      print('❌ No staff data available');
      _showErrorSnackBar('Staff information not loaded yet. Please try again.');
      return;
    }

    final staffPhone = _staffData!['phone'] ?? '';
    final staffName = _staffData!['name'] ?? 'Your Coach';
    final userName = widget.profile?.name ?? 'User';

    print('📞 Staff phone: $staffPhone');
    print('👨‍💼 Staff name: $staffName');
    print('👤 User name: $userName');

    if (staffPhone.isEmpty) {
      print('❌ Staff phone is empty');
      _showErrorSnackBar('Staff contact information not available');
      return;
    }

    // Create a personalized support message
    final message = Uri.encodeComponent(
      'Hi $staffName! 👋\n\n'
      'I\'m $userName from the KFT Fitness app and I need some support with my workout and diet plan.\n\n'
      'Could you please help me? 💪'
    );

    final url = 'https://wa.me/$staffPhone?text=$message';

    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);

        // Track successful WhatsApp launch
        PerformanceMonitor().logEvent('whatsapp_support_opened', {
          'staff_id': widget.profile!.assignedStaffId!,
          'staff_name': staffName,
          'user_name': userName,
        });
      } else {
        _showErrorSnackBar('Could not open WhatsApp. Please make sure WhatsApp is installed.');
      }
    } catch (e) {
      print('Error opening WhatsApp: $e');
      _showErrorSnackBar('Could not open WhatsApp chat');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red.shade600,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      );
    }
  }

  @override
  void dispose() {
    _fabController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.profile?.assignedStaffId == null) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return _buildLoadingFAB(theme, isDarkMode);
    }

    if (_hasError || _staffData == null) {
      return _buildErrorFAB(theme, isDarkMode);
    }

    return AnimatedBuilder(
      animation: _fabController,
      builder: (context, child) {
        return Transform.scale(
          scale: _fabScaleAnimation.value,
          child: Opacity(
            opacity: _fabOpacityAnimation.value,
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: _buildEnhancedFAB(theme, isDarkMode),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedFAB(ThemeData theme, bool isDarkMode) {
    final staffName = _staffData!['name'] ?? 'Your Coach';
    final staffPhone = _staffData!['phone'] ?? '';

    return Material(
      color: Colors.transparent,
      elevation: 0,
      borderRadius: BorderRadius.circular(32),
      child: InkWell(
        borderRadius: BorderRadius.circular(32),
        onTap: _openWhatsAppChat,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                isDarkMode
                    ? Colors.grey.shade800.withOpacity(0.95)
                    : Colors.white.withOpacity(0.95),
                isDarkMode
                    ? Colors.grey.shade900.withOpacity(0.9)
                    : Colors.white.withOpacity(0.9),
              ],
            ),
            borderRadius: BorderRadius.circular(32),
            border: Border.all(
              color: theme.colorScheme.outline.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 16,
                offset: const Offset(0, 4),
                spreadRadius: 0,
              ),
              BoxShadow(
                color: const Color(0xFF25D366).withOpacity(0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Enhanced WhatsApp icon
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF25D366),
                      Color(0xFF128C7E),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: const Color(0xFF25D366).withOpacity(0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: const FaIcon(
                  FontAwesomeIcons.whatsapp,
                  color: Colors.white,
                  size: 18,
                ),
              ),

              // Enhanced support text with staff info
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Personal Trainer',
                      style: TextStyle(
                        color: isDarkMode ? Colors.white : Colors.black87,
                        fontWeight: FontWeight.w700,
                        fontSize: 11,
                        letterSpacing: 0.2,
                        fontFamily: KFTDesignSystem.fontFamily,
                      ),
                    ),
                    const SizedBox(height: 1),
                    Text(
                      staffName,
                      style: TextStyle(
                        color: const Color(0xFF25D366),
                        fontWeight: FontWeight.w600,
                        fontSize: 10,
                        letterSpacing: 0.1,
                        fontFamily: KFTDesignSystem.fontFamily,
                      ),
                    ),
                  ],
                ),
              ),

              // Online indicator
              Container(
                margin: const EdgeInsets.only(right: 6),
                width: 6,
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.green.shade400,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.green.shade400.withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingFAB(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.8)
            : Colors.white.withOpacity(0.8),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SizedBox(
        width: 24,
        height: 24,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            theme.colorScheme.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorFAB(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDarkMode
            ? Colors.grey.shade800.withOpacity(0.8)
            : Colors.white.withOpacity(0.8),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Icon(
        Icons.support_agent,
        color: theme.colorScheme.primary,
        size: 24,
      ),
    );
  }
}
