import 'package:flutter/material.dart';
import '../models/user_profile.dart';

class StreakCard extends StatelessWidget {
  final UserProfile userProfile;

  const StreakCard({
    Key? key,
    required this.userProfile,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    // Calculate streak
    final currentStreak = userProfile.currentStreak;

    // Calculate longest streak
    final streakDays = userProfile.streakDays;
    int longestStreak = 0;

    if (streakDays.isNotEmpty) {
      // Sort streak days
      final sortedDays = List<DateTime>.from(streakDays)
        ..sort((a, b) => a.compareTo(b));

      int currentRun = 1;
      int maxRun = 1;

      for (int i = 0; i < sortedDays.length - 1; i++) {
        final current = sortedDays[i];
        final next = sortedDays[i + 1];

        if (_dayDifference(current, next) == 1) {
          currentRun++;
          maxRun = currentRun > maxRun ? currentRun : maxRun;
        } else if (_dayDifference(current, next) > 1) {
          currentRun = 1;
        }
      }

      longestStreak = maxRun;
    }

    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isDarkMode
            ? BorderSide(color: Colors.grey.shade800, width: 0.5)
            : BorderSide.none,
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDarkMode
                ? [Colors.black, Colors.grey[900]!]
                : [Colors.white, Colors.grey[200]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.08)
                  : Colors.black.withOpacity(0.08),
              blurRadius: 16,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.local_fire_department, color: primaryColor),
                  const SizedBox(width: 8),
                  Text(
                    'Daily Streaks',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStreakDisplay(
                    context,
                    currentStreak,
                    'Current Streak',
                    currentStreak > 0 ? Colors.green : Colors.grey,
                  ),
                  _buildStreakDisplay(
                    context,
                    longestStreak,
                    'Longest Streak',
                    primaryColor,
                  ),
                ],
              ),
              const SizedBox(height: 24),
              _buildWeeklyStreak(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStreakDisplay(
    BuildContext context,
    int value,
    String label,
    Color color,
  ) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '$value',
                  style: theme.textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  'days',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildWeeklyStreak(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final isDarkMode = theme.brightness == Brightness.dark;

    // Get days of the week
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekDays = <DateTime>[];

    // Get the past 7 days
    for (int i = 6; i >= 0; i--) {
      weekDays.add(today.subtract(Duration(days: i)));
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Last 7 Days',
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: weekDays.map((day) {
            final dayName = _getDayName(day.weekday);
            final isActive = userProfile.streakDays.any((d) => _isSameDay(d, day));

            return Column(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: isActive
                        ? primaryColor
                        : isDarkMode
                            ? Colors.grey.shade800
                            : Colors.grey.shade200,
                    shape: BoxShape.circle,
                  ),
                  child: isActive
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 20,
                        )
                      : null,
                ),
                const SizedBox(height: 4),
                Text(
                  dayName,
                  style: TextStyle(
                    fontSize: 12,
                    color: isActive
                        ? primaryColor
                        : isDarkMode
                            ? theme.colorScheme.onSurface.withOpacity(0.7)
                            : Colors.grey.shade600,
                    fontWeight: isActive
                        ? FontWeight.w600
                        : FontWeight.normal,
                  ),
                ),
              ],
            );
          }).toList(),
        ),
      ],
    );
  }

  // Helper method to get day name
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'M';
      case 2: return 'T';
      case 3: return 'W';
      case 4: return 'T';
      case 5: return 'F';
      case 6: return 'S';
      case 7: return 'S';
      default: return '';
    }
  }

  // Helper method to check if two dates are the same day
  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  // Helper method to calculate day difference
  int _dayDifference(DateTime a, DateTime b) {
    final difference = b.difference(a).inDays;
    return difference;
  }
}
