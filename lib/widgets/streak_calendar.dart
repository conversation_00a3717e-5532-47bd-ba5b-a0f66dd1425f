import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';
import '../services/streak_service.dart';
import 'motivational_completion_dialog.dart';

class StreakCalendar extends StatefulWidget {
  final VoidCallback? onStreakUpdated;

  const StreakCalendar({
    super.key,
    this.onStreakUpdated,
  });

  @override
  State<StreakCalendar> createState() => _StreakCalendarState();
}

class _StreakCalendarState extends State<StreakCalendar>
    with TickerProviderStateMixin {
  final StreakService _streakService = StreakService();
  late AnimationController _streakAnimationController;
  late AnimationController _calendarAnimationController;
  late Animation<double> _streakScaleAnimation;
  late Animation<double> _streakGlowAnimation;

  List<StreakDay> _calendarDays = [];
  StreakStats? _streakStats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadStreakData();
  }

  void _initializeAnimations() {
    // Streak number animation controller
    _streakAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Calendar entrance animation controller
    _calendarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Streak scale animation with bounce effect
    _streakScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _streakAnimationController,
      curve: Curves.elasticOut,
    ));

    // Streak glow animation
    _streakGlowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _streakAnimationController,
      curve: Curves.easeOutCubic,
    ));
  }

  Future<void> _loadStreakData() async {
    try {
      await _streakService.initialize();
      setState(() {
        _calendarDays = _streakService.get30DayCalendar();
        _streakStats = _streakService.getStreakStats();
        _isLoading = false;
      });

      // Start animations
      _calendarAnimationController.forward();
      _streakAnimationController.forward();
    } catch (e) {
      print('Error loading streak data: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _markTodayCompleted(CompletionType type, String activity) async {
    // Haptic feedback
    HapticFeedback.mediumImpact();

    final success = await _streakService.markTodayCompleted(type, activity);

    if (success) {
      // Reload data
      await _loadStreakData();

      // Show motivational dialog
      if (mounted) {
        _showMotivationalDialog();
      }

      // Notify parent
      widget.onStreakUpdated?.call();

      // Restart animations for updated streak
      _streakAnimationController.reset();
      _streakAnimationController.forward();
    }
  }

  void _showMotivationalDialog() {
    final message = _streakService.getMotivationalMessage();
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => MotivationalCompletionDialog(
        message: message,
        currentStreak: _streakStats?.currentStreak ?? 0,
        onDismiss: () => Navigator.of(context).pop(),
      ),
    );
  }

  void _showTodayCompletionOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).brightness == Brightness.dark
              ? const Color(0xFF1C1C1E)
              : Colors.white,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey.shade300,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Complete Today\'s Goal',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildQuickActionButton(
                  'Complete Workout',
                  Icons.fitness_center,
                  () {
                    Navigator.pop(context);
                    _markTodayCompleted(CompletionType.workout, 'Workout completed');
                  },
                  Theme.of(context),
                ),
                _buildQuickActionButton(
                  'Log Water',
                  Icons.water_drop,
                  () {
                    Navigator.pop(context);
                    _markTodayCompleted(CompletionType.water, 'Water logged');
                  },
                  Theme.of(context),
                ),
                _buildQuickActionButton(
                  'Watch Course',
                  Icons.play_circle,
                  () {
                    Navigator.pop(context);
                    _markTodayCompleted(CompletionType.course, 'Course video watched');
                  },
                  Theme.of(context),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _streakAnimationController.dispose();
    _calendarAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (_isLoading) {
      return _buildLoadingState(theme);
    }

    return AnimatedBuilder(
      animation: Listenable.merge([
        _streakAnimationController,
        _calendarAnimationController,
      ]),
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: isDarkMode
                ? LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF1C1C1E),
                      const Color(0xFF2C2C2E),
                      const Color(0xFF1A1A1C),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  )
                : LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFFFAFAFA),
                      Colors.grey.shade50,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
            borderRadius: BorderRadius.circular(28),
            border: Border.all(
              color: isDarkMode
                  ? Colors.white.withOpacity(0.1)
                  : Colors.grey.withOpacity(0.2),
              width: 1,
            ),
            boxShadow: [
              // Primary shadow
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.4)
                    : Colors.black.withOpacity(0.12),
                blurRadius: 32,
                offset: const Offset(0, 16),
                spreadRadius: -4,
              ),
              // Secondary shadow for depth
              BoxShadow(
                color: isDarkMode
                    ? Colors.black.withOpacity(0.3)
                    : Colors.black.withOpacity(0.08),
                blurRadius: 16,
                offset: const Offset(0, 8),
                spreadRadius: -2,
              ),
              // Subtle inner glow
              BoxShadow(
                color: isDarkMode
                    ? Colors.white.withOpacity(0.05)
                    : Colors.white.withOpacity(0.8),
                blurRadius: 1,
                offset: const Offset(0, 1),
                spreadRadius: 0,
              ),
            ],
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildModernHeader(theme, isDarkMode),
                if (_streakStats?.currentStreak != null && _streakStats!.currentStreak > 0)
                  const SizedBox(height: 16),
                _buildModernCalendarGrid(theme, isDarkMode),
                const SizedBox(height: 18),
                _buildQuickActions(theme, isDarkMode),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      height: 300,
      decoration: BoxDecoration(
        color: theme.brightness == Brightness.dark
            ? KFTDesignSystem.darkSurfaceColor
            : Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildModernHeader(ThemeData theme, bool isDarkMode) {
    final currentStreak = _streakStats?.currentStreak ?? 0;

    return Transform.scale(
      scale: _streakScaleAnimation.value,
      child: FadeTransition(
        opacity: _streakGlowAnimation,
        child: currentStreak > 0 ? Container(
          margin: const EdgeInsets.only(bottom: 2),
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDarkMode
                  ? [
                      Colors.orange.withOpacity(0.2),
                      Colors.deepOrange.withOpacity(0.15),
                    ]
                  : [
                      Colors.orange.withOpacity(0.12),
                      Colors.deepOrange.withOpacity(0.08),
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.orange.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 42,
                height: 42,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [Colors.orange, Colors.deepOrange],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(21),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.orange.withOpacity(0.4),
                      blurRadius: 8,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.local_fire_department,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 14),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      '$currentStreak Day Streak!',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isDarkMode
                            ? Colors.white
                            : Colors.orange.shade800,
                        fontSize: 17,
                        height: 1.2,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      '${_streakStats?.completionRate ?? 0}% completion rate',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: isDarkMode
                            ? Colors.white.withOpacity(0.85)
                            : Colors.orange.shade700,
                        fontSize: 13,
                        height: 1.2,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ) : const SizedBox.shrink(),
      ),
    );
  }

  Widget _buildStreakHeader(ThemeData theme, bool isDarkMode) {
    final currentStreak = _streakStats?.currentStreak ?? 0;

    return Transform.scale(
      scale: _streakScaleAnimation.value,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              KFTDesignSystem.primaryColor.withOpacity(0.1),
              KFTDesignSystem.primaryColor.withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: KFTDesignSystem.primaryColor.withOpacity(0.3 * _streakGlowAnimation.value),
              blurRadius: 20 * _streakGlowAnimation.value,
              spreadRadius: 2 * _streakGlowAnimation.value,
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: KFTDesignSystem.primaryColor,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.local_fire_department,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '$currentStreak Day Streak!',
                    style: theme.textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isDarkMode
                          ? KFTDesignSystem.darkTextPrimaryColor
                          : KFTDesignSystem.textPrimaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${_streakStats?.completionRate ?? 0}% completion rate',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: isDarkMode
                          ? KFTDesignSystem.darkTextSecondaryColor
                          : KFTDesignSystem.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernCalendarGrid(ThemeData theme, bool isDarkMode) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 6,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
          childAspectRatio: 1.0,
        ),
        itemCount: 30, // Always show exactly 30 days
        itemBuilder: (context, index) {
          final dayNumber = index + 1; // Days 1-30
          final animationDelay = index * 40; // Slightly faster animation

          return _buildModern30DayItem(theme, isDarkMode, dayNumber, animationDelay);
        },
      ),
    );
  }

  Widget _buildCalendarGrid(ThemeData theme, bool isDarkMode) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Last 30 Days',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: isDarkMode
                ? KFTDesignSystem.darkTextPrimaryColor
                : KFTDesignSystem.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 7,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
            childAspectRatio: 1,
          ),
          itemCount: _calendarDays.length,
          itemBuilder: (context, index) {
            final day = _calendarDays[index];
            final animationDelay = index * 50; // 50ms delay between items

            return _buildCalendarDay(theme, isDarkMode, day, animationDelay);
          },
        ),
      ],
    );
  }

  Widget _buildModern30DayItem(ThemeData theme, bool isDarkMode, int dayNumber, int animationDelay) {
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 150) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOutCubic,
      ),
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 150) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOut,
      ),
    ));

    // Determine day state
    final isToday = dayNumber == 1; // Day 1 is always today
    final isCompleted = _isDayCompleted(dayNumber);
    final isPastDay = dayNumber < 1; // No past days in this 30-day view
    final isFutureDay = dayNumber > 1;

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: GestureDetector(
          onTap: () {
            if (isToday && !isCompleted) {
              HapticFeedback.lightImpact();
              _showTodayCompletionOptions();
            }
          },
          child: Container(
            decoration: BoxDecoration(
              color: _getModern30DayBackgroundColor(dayNumber, isDarkMode),
              shape: BoxShape.circle,
              border: isToday ? Border.all(
                color: isDarkMode
                    ? Colors.white
                    : KFTDesignSystem.primaryColor,
                width: 2.5,
              ) : null,
              boxShadow: _getModern30DayShadow(dayNumber, isDarkMode),
            ),
            child: Center(
              child: isCompleted
                  ? Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.green.withOpacity(0.3),
                            blurRadius: 3,
                            spreadRadius: 0,
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.check,
                        color: Colors.green.shade600,
                        size: 14,
                      ),
                    )
                  : Text(
                      dayNumber.toString(),
                      style: theme.textTheme.bodyLarge?.copyWith(
                        fontWeight: isToday ? FontWeight.bold : FontWeight.w600,
                        color: _getModern30DayTextColor(dayNumber, isDarkMode),
                        fontSize: isToday ? 16 : 15,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernCalendarDay(ThemeData theme, bool isDarkMode, StreakDay day, int animationDelay) {
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 200) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOutCubic,
      ),
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 200) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOut,
      ),
    ));

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: GestureDetector(
          onTap: () {
            if (!day.isCompleted && day.isToday) {
              HapticFeedback.lightImpact();
              // Could trigger completion dialog or action
            }
          },
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            decoration: BoxDecoration(
              color: _getModernDayBackgroundColor(day, isDarkMode),
              shape: BoxShape.circle,
              border: day.isToday ? Border.all(
                color: Colors.white,
                width: 2,
              ) : null,
              boxShadow: day.isCompleted ? [
                BoxShadow(
                  color: Colors.white.withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ] : null,
            ),
            child: Center(
              child: day.isCompleted
                  ? Container(
                      width: 20,
                      height: 20,
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.green,
                        size: 14,
                      ),
                    )
                  : Text(
                      day.date.day.toString(),
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: day.isToday ? FontWeight.bold : FontWeight.w500,
                        color: _getModernDayTextColor(day, isDarkMode),
                        fontSize: 16,
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCalendarDay(ThemeData theme, bool isDarkMode, StreakDay day, int animationDelay) {
    final slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 200) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOutCubic,
      ),
    ));

    final fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _calendarAnimationController,
      curve: Interval(
        (animationDelay / 1200).clamp(0.0, 1.0),
        ((animationDelay + 200) / 1200).clamp(0.0, 1.0),
        curve: Curves.easeOut,
      ),
    ));

    return SlideTransition(
      position: slideAnimation,
      child: FadeTransition(
        opacity: fadeAnimation,
        child: Container(
          decoration: BoxDecoration(
            color: _getDayBackgroundColor(day, isDarkMode),
            borderRadius: BorderRadius.circular(8),
            border: day.isToday ? Border.all(
              color: KFTDesignSystem.primaryColor,
              width: 2,
            ) : null,
            boxShadow: day.isCompleted ? [
              BoxShadow(
                color: KFTDesignSystem.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ] : null,
          ),
          child: Stack(
            children: [
              Center(
                child: Text(
                  day.date.day.toString(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: day.isToday ? FontWeight.bold : FontWeight.normal,
                    color: _getDayTextColor(day, isDarkMode),
                  ),
                ),
              ),
              if (day.isCompleted)
                Positioned(
                  top: 2,
                  right: 2,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: const Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 8,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getDayBackgroundColor(StreakDay day, bool isDarkMode) {
    if (day.isCompleted) {
      return KFTDesignSystem.primaryColor.withOpacity(0.2);
    } else if (day.isToday) {
      return isDarkMode
          ? KFTDesignSystem.darkSurfaceColor.withOpacity(0.8)
          : Colors.grey.shade100;
    } else {
      return isDarkMode
          ? KFTDesignSystem.darkSurfaceColor.withOpacity(0.3)
          : Colors.grey.shade50;
    }
  }

  Color _getModernDayBackgroundColor(StreakDay day, bool isDarkMode) {
    if (day.isCompleted) {
      return Colors.white;
    } else if (day.isToday) {
      return isDarkMode
          ? Colors.white.withOpacity(0.2)
          : Colors.grey.shade300;
    } else {
      return isDarkMode
          ? Colors.grey.shade700
          : Colors.grey.shade400;
    }
  }

  // Helper method to check if a day number is completed
  bool _isDayCompleted(int dayNumber) {
    if (dayNumber == 1) {
      // Day 1 is today - check if today is completed
      return _streakService.isTodayCompleted;
    } else if (dayNumber <= (_streakStats?.currentStreak ?? 0)) {
      // Past days within current streak are completed
      return true;
    }
    return false;
  }

  // Modern 30-day color methods
  Color _getModern30DayBackgroundColor(int dayNumber, bool isDarkMode) {
    final isToday = dayNumber == 1;
    final isCompleted = _isDayCompleted(dayNumber);

    if (isCompleted) {
      return isDarkMode
          ? Colors.green.shade600
          : Colors.green.shade500;
    } else if (isToday) {
      return isDarkMode
          ? KFTDesignSystem.primaryColor.withOpacity(0.8)
          : KFTDesignSystem.primaryColor;
    } else {
      // Future days
      return isDarkMode
          ? Colors.grey.shade700
          : Colors.grey.shade300;
    }
  }

  Color _getModern30DayTextColor(int dayNumber, bool isDarkMode) {
    final isToday = dayNumber == 1;
    final isCompleted = _isDayCompleted(dayNumber);

    if (isCompleted) {
      return Colors.white;
    } else if (isToday) {
      return Colors.white;
    } else {
      // Future days
      return isDarkMode
          ? Colors.white.withOpacity(0.9)
          : Colors.grey.shade700;
    }
  }

  List<BoxShadow> _getModern30DayShadow(int dayNumber, bool isDarkMode) {
    final isToday = dayNumber == 1;
    final isCompleted = _isDayCompleted(dayNumber);

    if (isCompleted) {
      return [
        BoxShadow(
          color: Colors.green.withOpacity(0.3),
          blurRadius: 8,
          offset: const Offset(0, 3),
          spreadRadius: 1,
        ),
      ];
    } else if (isToday) {
      return [
        BoxShadow(
          color: KFTDesignSystem.primaryColor.withOpacity(0.3),
          blurRadius: 8,
          offset: const Offset(0, 3),
          spreadRadius: 1,
        ),
      ];
    } else {
      return [
        BoxShadow(
          color: isDarkMode
              ? Colors.black.withOpacity(0.2)
              : Colors.black.withOpacity(0.08),
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ];
    }
  }

  Color _getModernDayTextColor(StreakDay day, bool isDarkMode) {
    if (day.isCompleted) {
      return Colors.green;
    } else if (day.isToday) {
      return Colors.white;
    } else {
      return isDarkMode
          ? Colors.white.withOpacity(0.8)
          : Colors.white;
    }
  }

  Color _getDayTextColor(StreakDay day, bool isDarkMode) {
    if (day.isCompleted || day.isToday) {
      return isDarkMode
          ? KFTDesignSystem.darkTextPrimaryColor
          : KFTDesignSystem.textPrimaryColor;
    } else {
      return isDarkMode
          ? KFTDesignSystem.darkTextSecondaryColor
          : KFTDesignSystem.textSecondaryColor;
    }
  }

  Widget _buildQuickActions(ThemeData theme, bool isDarkMode) {
    final isTodayCompleted = _streakService.isTodayCompleted;

    if (isTodayCompleted) {
      return Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.green.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Today\'s goal completed! Great job! 🎉',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: Colors.green.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildQuickActionButton(
          'Complete Workout',
          Icons.fitness_center,
          () => _markTodayCompleted(CompletionType.workout, 'Workout completed'),
          theme,
        ),
        _buildQuickActionButton(
          'Log Water',
          Icons.water_drop,
          () => _markTodayCompleted(CompletionType.water, 'Water logged'),
          theme,
        ),
        _buildQuickActionButton(
          'Watch Course',
          Icons.play_circle,
          () => _markTodayCompleted(CompletionType.course, 'Course video watched'),
          theme,
        ),
      ],
    );
  }

  Widget _buildQuickActionButton(
    String label,
    IconData icon,
    VoidCallback onTap,
    ThemeData theme,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: KFTDesignSystem.primaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: KFTDesignSystem.primaryColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: KFTDesignSystem.primaryColor,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: KFTDesignSystem.primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
