import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../design_system/kft_design_system.dart';

class ProfileStreakWidget extends StatelessWidget {
  final UserProfile? userProfile;
  final bool isLoading;

  const ProfileStreakWidget({
    Key? key,
    required this.userProfile,
    this.isLoading = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (isLoading) {
      return _buildLoadingState(context);
    }

    if (userProfile == null) {
      return const SizedBox.shrink();
    }

    // Calculate stats
    final totalWorkouts = userProfile!.totalWorkouts;
    final currentStreak = userProfile!.currentStreak;
    final totalMinutes = userProfile!.totalWorkoutTime;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isDarkMode ? Colors.grey.shade800 : Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.insights,
                  color: theme.colorScheme.primary,
                ),
                const Spacer(),
                _buildInfoButton(context),
              ],
            ),
            const SizedBox(height: 16),

            // Stats Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  context,
                  value: totalWorkouts.toString(),
                  label: 'Workouts',
                  icon: Icons.fitness_center,
                  color: theme.colorScheme.primary,
                ),
                _buildStatItem(
                  context,
                  value: currentStreak.toString(),
                  label: 'Day Streak',
                  icon: Icons.local_fire_department,
                  color: Colors.orange,
                ),
                _buildStatItem(
                  context,
                  value: _formatDuration(totalMinutes),
                  label: 'Active Minutes',
                  icon: Icons.timer,
                  color: Colors.green,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Weekly Progress
            _buildWeeklyProgress(context),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      height: 180,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? Colors.grey.shade800
              : Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  Widget _buildStatItem(
    BuildContext context, {
    required String value,
    required String label,
    required IconData icon,
    required Color color,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      children: [
        Container(
          width: 56,
          height: 56,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDarkMode
                ? theme.colorScheme.onSurface.withOpacity(0.7)
                : Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildWeeklyProgress(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final primaryColor = theme.colorScheme.primary;

    // Get days of the week
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekDays = <DateTime>[];

    // Get the past 7 days
    for (int i = 6; i >= 0; i--) {
      weekDays.add(today.subtract(Duration(days: i)));
    }

    // Check if there's at least one day of activity
    bool hasActivity = false;
    if (userProfile != null && userProfile!.streakDays.isNotEmpty) {
      for (final day in weekDays) {
        if (userProfile!.streakDays.any((d) => _isSameDay(d, day))) {
          hasActivity = true;
          break;
        }
      }
    }

    // If there's no activity, don't show the Last 7 Days section
    if (!hasActivity) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Last 7 Days',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            // Calculate streak percentage
            Text(
              '${_calculateActivePercentage(weekDays)}% active',
              style: theme.textTheme.bodySmall?.copyWith(
                color: primaryColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isDarkMode ? Colors.grey.shade800.withOpacity(0.3) : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: weekDays.map((day) {
              final dayName = _getDayName(day.weekday);
              final isActive = userProfile != null && userProfile!.streakDays.any((d) => _isSameDay(d, day));
              final isToday = _isSameDay(day, today);

              return Column(
                children: [
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isActive
                          ? primaryColor
                          : isDarkMode
                              ? Colors.grey.shade800
                              : Colors.grey.shade200,
                      shape: BoxShape.circle,
                      border: isToday
                          ? Border.all(
                              color: primaryColor,
                              width: 2,
                            )
                          : null,
                      boxShadow: isActive
                          ? [
                              BoxShadow(
                                color: primaryColor.withOpacity(0.3),
                                blurRadius: 6,
                                offset: const Offset(0, 2),
                              ),
                            ]
                          : null,
                    ),
                    child: isActive
                        ? Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    dayName,
                    style: TextStyle(
                      fontSize: 10,
                      color: isToday
                          ? primaryColor
                          : (isActive
                              ? primaryColor.withOpacity(0.8)
                              : isDarkMode
                                  ? theme.colorScheme.onSurface.withOpacity(0.7)
                                  : Colors.grey.shade600),
                      fontWeight: isToday || isActive ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  String _calculateActivePercentage(List<DateTime> weekDays) {
    if (userProfile == null) return '0';

    int activeDays = 0;
    for (final day in weekDays) {
      if (userProfile!.streakDays.any((d) => _isSameDay(d, day))) {
        activeDays++;
      }
    }

    final percentage = (activeDays / weekDays.length) * 100;
    return percentage.toStringAsFixed(0);
  }

  Widget _buildInfoButton(BuildContext context) {
    return IconButton(
      icon: const Icon(Icons.info_outline, size: 20),
      onPressed: () {
        _showInfoDialog(context);
      },
      tooltip: 'Streak Info',
    );
  }

  void _showInfoDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'About Your Streak',
          style: theme.textTheme.titleLarge,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoItem(
              context,
              icon: Icons.fitness_center,
              title: 'Workouts',
              description: 'Total number of workouts you have completed.',
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              context,
              icon: Icons.local_fire_department,
              title: 'Day Streak',
              description: 'Number of consecutive days you have been active.',
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              context,
              icon: Icons.timer,
              title: 'Active Minutes',
              description: 'Total time spent on workouts.',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
  }) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: theme.colorScheme.primary, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: theme.textTheme.bodySmall,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'M';
      case 2: return 'T';
      case 3: return 'W';
      case 4: return 'T';
      case 5: return 'F';
      case 6: return 'S';
      case 7: return 'S';
      default: return '';
    }
  }

  bool _isSameDay(DateTime a, DateTime b) {
    return a.year == b.year && a.month == b.month && a.day == b.day;
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes';
    } else {
      final hours = minutes ~/ 60;
      final mins = minutes % 60;
      if (mins == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${mins}m';
      }
    }
  }
}
