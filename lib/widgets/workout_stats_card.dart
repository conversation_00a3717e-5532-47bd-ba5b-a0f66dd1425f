import 'package:flutter/material.dart';
import '../models/user_profile.dart';
import '../models/course.dart';

class WorkoutStatsCard extends StatelessWidget {
  final UserProfile userProfile;
  final List<Course> enrolledCourses;

  const WorkoutStatsCard({
    Key? key,
    required this.userProfile,
    required this.enrolledCourses,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;

    // Calculate stats
    final totalWorkouts = userProfile.workoutHistory.length;
    final avgMinutes = userProfile.averageWorkoutTime.toStringAsFixed(1);

    // Calculate total unlocked video minutes (now used for Total Time)
    int unlockedVideoMinutes = 0;
    for (final course in enrolledCourses) {
      if (course.videos != null) {
        unlockedVideoMinutes += course.videos!
            .where((v) => v.isUnlocked && v.durationMinutes != null)
            .fold(0, (sum, v) => sum + (v.durationMinutes ?? 0));
      }
    }

    // Format total time (from unlocked videos only)
    final hours = unlockedVideoMinutes ~/ 60;
    final minutes = unlockedVideoMinutes % 60;
    final totalTimeText = hours > 0
        ? '$hours hr ${minutes > 0 ? '$minutes min' : ''}'
        : '$minutes min';

    final isDarkMode = theme.brightness == Brightness.dark;

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      elevation: 2,
      shadowColor: isDarkMode ? Colors.black : Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: isDarkMode
            ? BorderSide(color: Colors.grey.shade800, width: 0.5)
            : BorderSide.none,
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timer, color: primaryColor),
                const SizedBox(width: 8),
                Text(
                  'Workout Time Tracking',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatColumn(
                  context,
                  totalTimeText,
                  'Total Time',
                  Icons.access_time,
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 8),
            Text(
              'Recent Workouts',
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            _buildRecentWorkoutsList(context),
          ],
        ),
      ),
    );
  }

  Widget _buildStatColumn(
    BuildContext context,
    String value,
    String label,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    final primaryColor = theme.colorScheme.primary;
    final isDarkMode = theme.brightness == Brightness.dark;

    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: primaryColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: primaryColor,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: isDarkMode
                ? theme.colorScheme.onSurface.withOpacity(0.7)
                : Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildRecentWorkoutsList(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final recentWorkouts = userProfile.workoutHistory
        .take(3)
        .toList()
      ..sort((a, b) => b.date.compareTo(a.date));

    if (recentWorkouts.isEmpty) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        child: Center(
          child: Text(
            'No workouts recorded yet',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: isDarkMode
                  ? theme.colorScheme.onSurface.withOpacity(0.7)
                  : Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: recentWorkouts.length,
      itemBuilder: (context, index) {
        final workout = recentWorkouts[index];
        return ListTile(
          contentPadding: EdgeInsets.zero,
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.fitness_center,
              color: theme.colorScheme.primary,
            ),
          ),
          title: Text(
            workout.title,
            style: TextStyle(
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurface,
            ),
          ),
          subtitle: Text(
            workout.formattedDate,
            style: TextStyle(
              color: isDarkMode
                  ? theme.colorScheme.onSurface.withOpacity(0.6)
                  : Colors.grey.shade600,
              fontSize: 12
            ),
          ),
          trailing: Text(
            workout.formattedDuration,
            style: TextStyle(
              color: isDarkMode
                  ? theme.colorScheme.primary
                  : theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        );
      },
    );
  }
}
