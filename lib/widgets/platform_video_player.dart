import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../models/course_video.dart';

// Import platform-specific players
import 'web_video_player.dart';
import 'simple_vimeo_player.dart';

/// Platform-aware video player that automatically selects the right player
/// Uses WebVideoPlayer for web and SimpleVimeoPlayer for mobile platforms
class PlatformVideoPlayer extends StatelessWidget {
  final CourseVideo video;
  final bool autoPlay;
  final VoidCallback? onReady;
  final VoidCallback? onPlay;
  final VoidCallback? onPause;
  final VoidCallback? onCompleted;
  final Function(int position)? onProgress;
  final Function(String error)? onError;

  const PlatformVideoPlayer({
    Key? key,
    required this.video,
    this.autoPlay = false,
    this.onReady,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use WebVideoPlayer for web platform
    if (kIsWeb) {
      return WebVideoPlayer(
        video: video,
        autoPlay: autoPlay,
        onReady: onReady,
        onPlay: onPlay,
        onPause: onPause,
        onCompleted: onCompleted,
        onProgress: onProgress,
        onError: onError,
      );
    }
    
    // Use SimpleVimeoPlayer for mobile platforms
    return SimpleVimeoPlayer(
      video: video,
      autoPlay: autoPlay,
      onReady: onReady,
      onPlay: onPlay,
      onPause: onPause,
      onCompleted: onCompleted,
      onProgress: onProgress,
      onError: onError,
    );
  }
} 