import 'package:flutter/material.dart';
import '../utils/responsive_utils.dart';
import '../design_system/kft_design_system.dart';

/// A comprehensive responsive dashboard widget that adapts to any screen size
class ResponsiveDashboardWidget extends StatelessWidget {
  final String title;
  final List<DashboardCard> cards;
  final List<Widget>? additionalWidgets;
  final Widget? floatingActionButton;
  final bool showAppBar;
  final List<Widget>? appBarActions;

  const ResponsiveDashboardWidget({
    Key? key,
    required this.title,
    required this.cards,
    this.additionalWidgets,
    this.floatingActionButton,
    this.showAppBar = true,
    this.appBarActions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: showAppBar ? _buildResponsiveAppBar(context, deviceType) : null,
          body: _buildResponsiveBody(context, deviceType),
          floatingActionButton: floatingActionButton,
        );
      },
    );
  }

  PreferredSizeWidget _buildResponsiveAppBar(BuildContext context, DeviceType deviceType) {
    return AppBar(
      title: ResponsiveText(
        title,
        mobileFontSize: 18,
        tabletFontSize: 20,
        desktopFontSize: 22,
        fontWeight: FontWeight.w600,
      ),
      elevation: 0,
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      foregroundColor: Theme.of(context).textTheme.bodyLarge?.color,
      actions: appBarActions,
    );
  }

  Widget _buildResponsiveBody(BuildContext context, DeviceType deviceType) {
    return SingleChildScrollView(
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        largeMobile: const EdgeInsets.all(20),
        tablet: const EdgeInsets.all(24),
        desktop: const EdgeInsets.all(32),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Dashboard Cards Grid
          _buildResponsiveCardsGrid(context, deviceType),
          
          // Additional Widgets
          if (additionalWidgets != null) ...[
            SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
              context,
              mobile: 24,
              tablet: 32,
              desktop: 40,
            )),
            ...additionalWidgets!,
          ],
        ],
      ),
    );
  }

  Widget _buildResponsiveCardsGrid(BuildContext context, DeviceType deviceType) {
    return ResponsiveGrid(
      mobileColumns: 1,
      largeMobileColumns: 2,
      tabletColumns: 2,
      desktopColumns: cards.length >= 4 ? 4 : cards.length,
      largeDesktopColumns: cards.length >= 4 ? 4 : cards.length,
      spacing: ResponsiveUtils.getResponsiveSpacing(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      runSpacing: ResponsiveUtils.getResponsiveSpacing(
        context,
        mobile: 16,
        tablet: 20,
        desktop: 24,
      ),
      childAspectRatio: ResponsiveUtils.getResponsiveAspectRatio(
        context,
        mobile: 1.2,
        largeMobile: 1.0,
        tablet: 0.9,
        desktop: 0.8,
      ),
      children: cards.map((card) => _buildResponsiveCard(context, card, deviceType)).toList(),
    );
  }

  Widget _buildResponsiveCard(BuildContext context, DashboardCard card, DeviceType deviceType) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Container(
      decoration: BoxDecoration(
        color: isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        borderRadius: BorderRadius.circular(
          ResponsiveUtils.getResponsiveValue<double>(
            context,
            mobile: 12,
            tablet: 16,
            desktop: 20,
          ),
        ),
        boxShadow: [
          if (!isDarkMode)
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: ResponsiveUtils.getResponsiveValue<double>(
                context,
                mobile: 8,
                tablet: 10,
                desktop: 12,
              ),
              offset: const Offset(0, 4),
            ),
        ],
      ),
      padding: ResponsiveUtils.getResponsivePadding(
        context,
        mobile: const EdgeInsets.all(16),
        tablet: const EdgeInsets.all(20),
        desktop: const EdgeInsets.all(24),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (card.icon != null) ...[
                Container(
                  width: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    mobile: 40,
                    tablet: 44,
                    desktop: 48,
                  ),
                  height: ResponsiveUtils.getResponsiveValue<double>(
                    context,
                    mobile: 40,
                    tablet: 44,
                    desktop: 48,
                  ),
                  decoration: BoxDecoration(
                    color: card.iconBackgroundColor ?? KFTDesignSystem.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    card.icon,
                    color: card.iconColor ?? KFTDesignSystem.primaryColor,
                    size: ResponsiveUtils.getResponsiveValue<double>(
                      context,
                      mobile: 20,
                      tablet: 22,
                      desktop: 24,
                    ),
                  ),
                ),
                SizedBox(width: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 12,
                  tablet: 14,
                  desktop: 16,
                )),
              ],
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ResponsiveText(
                      card.title,
                      mobileFontSize: 12,
                      tabletFontSize: 14,
                      desktopFontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: isDarkMode ? Colors.white70 : Colors.black54,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                      context,
                      mobile: 4,
                      tablet: 6,
                      desktop: 8,
                    )),
                    ResponsiveText(
                      card.value,
                      mobileFontSize: 20,
                      tabletFontSize: 24,
                      desktopFontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: isDarkMode ? Colors.white : Colors.black,
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (card.subtitle != null) ...[
            const Spacer(),
            ResponsiveText(
              card.subtitle!,
              mobileFontSize: 10,
              tabletFontSize: 12,
              desktopFontSize: 14,
              color: isDarkMode ? Colors.white60 : Colors.black45,
            ),
          ],
        ],
      ),
    );
  }
}

/// Data class for dashboard cards
class DashboardCard {
  final String title;
  final String value;
  final String? subtitle;
  final IconData? icon;
  final Color? iconColor;
  final Color? iconBackgroundColor;
  final VoidCallback? onTap;

  const DashboardCard({
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.iconColor,
    this.iconBackgroundColor,
    this.onTap,
  });
}

/// Responsive section widget for organizing dashboard content
class ResponsiveDashboardSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final EdgeInsets? padding;
  final bool showDivider;

  const ResponsiveDashboardSection({
    Key? key,
    required this.title,
    required this.children,
    this.padding,
    this.showDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Container(
          padding: padding ?? ResponsiveUtils.getResponsivePadding(
            context,
            mobile: const EdgeInsets.all(16),
            tablet: const EdgeInsets.all(20),
            desktop: const EdgeInsets.all(24),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ResponsiveText(
                title,
                mobileFontSize: 18,
                tabletFontSize: 20,
                desktopFontSize: 22,
                fontWeight: FontWeight.w600,
              ),
              SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                context,
                mobile: 16,
                tablet: 20,
                desktop: 24,
              )),
              ...children,
              if (showDivider) ...[
                SizedBox(height: ResponsiveUtils.getResponsiveSpacing(
                  context,
                  mobile: 24,
                  tablet: 32,
                  desktop: 40,
                )),
                Divider(
                  color: Theme.of(context).dividerColor.withOpacity(0.1),
                  thickness: 1,
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
