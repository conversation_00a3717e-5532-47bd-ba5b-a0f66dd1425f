// CustomVimeoPlayer: A clean, stable Flutter video player for direct Vimeo MP4/HLS URLs
// Usage:
//   CustomVimeoPlayer(
//     videoUrl: 'https://your-backend.com/api/vimeo_video_url/123',
//     title: 'My Video',
//     onPlay: () {},
//     onPause: () {},
//     onCompleted: () {},
//     onProgress: (position) {},
//   )

import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class CustomVimeoPlayer extends StatefulWidget {
  final String videoUrl;
  final String? title;
  final void Function()? onPlay;
  final void Function()? onPause;
  final void Function()? onCompleted;
  final void Function(Duration position)? onProgress;
  final void Function(dynamic error)? onError;

  const CustomVimeoPlayer({
    Key? key,
    required this.videoUrl,
    this.title,
    this.onPlay,
    this.onPause,
    this.onCompleted,
    this.onProgress,
    this.onError,
  }) : super(key: key);

  @override
  State<CustomVimeoPlayer> createState() => _CustomVimeoPlayerState();
}

class _CustomVimeoPlayerState extends State<CustomVimeoPlayer> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;
  bool _isCompleted = false;
  bool _isInitialized = false;
  late VoidCallback _listener;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.network(widget.videoUrl)
      ..addListener(_onVideoEvent)
      ..setLooping(false)
      ..initialize().then((_) {
        setState(() {
          _isInitialized = true;
        });
      }).catchError((error) {
        widget.onError?.call(error);
      });
  }

  void _onVideoEvent() {
    if (!_controller.value.isInitialized) return;
    final position = _controller.value.position;
    final duration = _controller.value.duration;
    final isPlaying = _controller.value.isPlaying;

    if (widget.onProgress != null) {
      widget.onProgress!(position);
    }

    if (isPlaying != _isPlaying) {
      setState(() {
        _isPlaying = isPlaying;
      });
      if (isPlaying) {
        widget.onPlay?.call();
      } else {
        widget.onPause?.call();
      }
    }

    if (!_isCompleted && position >= duration && duration.inMilliseconds > 0) {
      setState(() {
        _isCompleted = true;
      });
      widget.onCompleted?.call();
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onVideoEvent);
    _controller.dispose();
    super.dispose();
  }

  void _onPlayPause() {
    try {
      if (_controller.value.isPlaying) {
        _controller.pause();
      } else {
        if (_isCompleted) {
          _controller.seekTo(Duration.zero);
          setState(() {
            _isCompleted = false;
          });
        }
        _controller.play();
      }
    } catch (error) {
      widget.onError?.call(error);
    }
  }

  void _onSeek(double value) {
    try {
      final position = Duration(milliseconds: value.toInt());
      _controller.seekTo(position);
    } catch (error) {
      widget.onError?.call(error);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AspectRatio(
      aspectRatio: _isInitialized ? _controller.value.aspectRatio : 16 / 9,
      child: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          _isInitialized
              ? VideoPlayer(_controller)
              : Container(
                  color: Colors.black,
                  child: const Center(
                    child: CircularProgressIndicator(),
                  ),
                ),
          if (_isInitialized) _buildControls(context),
        ],
      ),
    );
  }

  Widget _buildControls(BuildContext context) {
    final duration = _controller.value.duration;
    final position = _controller.value.position;
    final isPlaying = _controller.value.isPlaying;
    final isBuffering = _controller.value.isBuffering;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        if (widget.title != null)
          Container(
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Text(
              widget.title!,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
                shadows: [Shadow(blurRadius: 2, color: Colors.black)],
              ),
            ),
          ),
        Slider(
          value: position.inMilliseconds.clamp(0, duration.inMilliseconds).toDouble(),
          min: 0,
          max: duration.inMilliseconds.toDouble(),
          onChanged: (value) => _onSeek(value),
          activeColor: Colors.redAccent,
          inactiveColor: Colors.white24,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(_formatDuration(position), style: const TextStyle(color: Colors.white70, fontSize: 12)),
              Text(_formatDuration(duration), style: const TextStyle(color: Colors.white70, fontSize: 12)),
            ],
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: Icon(
                isPlaying ? Icons.pause_circle_filled : Icons.play_circle_filled,
                color: Colors.white,
                size: 48,
              ),
              onPressed: isBuffering ? null : _onPlayPause,
            ),
          ],
        ),
        const SizedBox(height: 8),
      ],
    );
  }

  String _formatDuration(Duration d) {
    final minutes = d.inMinutes.remainder(60).toString().padLeft(2, '0');
    final seconds = d.inSeconds.remainder(60).toString().padLeft(2, '0');
    return '$minutes:$seconds';
  }
}
