import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../design_system/kft_design_system.dart';
import '../models/user_profile.dart';
import '../services/user_service.dart';
import 'default_avatar_widget.dart';
import 'health_metrics_card.dart';

class ProfileAvatarEnhanced extends StatefulWidget {
  final UserProfile? userProfile;
  final bool isLoading;
  final double radius;
  final VoidCallback? onTap;
  final bool showDropdown;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const ProfileAvatarEnhanced({
    super.key,
    this.userProfile,
    this.isLoading = false,
    this.radius = 18,
    this.onTap,
    this.showDropdown = true,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  State<ProfileAvatarEnhanced> createState() => _ProfileAvatarEnhancedState();
}

class _ProfileAvatarEnhancedState extends State<ProfileAvatarEnhanced>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  OverlayEntry? _overlayEntry;
  final GlobalKey _avatarKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _removeOverlay();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _onTapCancel() {
    _animationController.reverse();
  }

  void _onTap() {
    print('DEBUG: Profile avatar tapped!');
    if (widget.onTap != null) {
      print('DEBUG: Calling custom onTap callback');
      widget.onTap!();
    } else if (widget.showDropdown) {
      print('DEBUG: Showing profile dropdown');
      _showProfileDropdown();
    } else {
      print('DEBUG: Navigating to profile page');
      Navigator.pushNamed(context, '/profile');
    }
  }

  void _showProfileDropdown() {
    if (_overlayEntry != null) {
      _removeOverlay();
      return;
    }

    final RenderBox renderBox = _avatarKey.currentContext!.findRenderObject() as RenderBox;
    final position = renderBox.localToGlobal(Offset.zero);
    final size = renderBox.size;

    _overlayEntry = OverlayEntry(
      builder: (context) => ProfileDropdownMenu(
        position: position,
        avatarSize: size,
        userProfile: widget.userProfile,
        onClose: _removeOverlay,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    if (widget.isLoading) {
      return _buildLoadingAvatar(theme, isDarkMode);
    }

    final userProfile = widget.userProfile;
    if (userProfile == null) {
      return _buildDefaultAvatar(theme, isDarkMode, 'U');
    }

    return GestureDetector(
      key: _avatarKey,
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: _onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: DefaultAvatarWidget(
                name: userProfile.name,
                radius: widget.radius,
                backgroundColor: widget.backgroundColor,
                foregroundColor: widget.foregroundColor,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLoadingAvatar(ThemeData theme, bool isDarkMode) {
    return Container(
      width: widget.radius * 2,
      height: widget.radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: widget.backgroundColor ??
            (isDarkMode
                ? KFTDesignSystem.darkTextPrimaryColor.withOpacity(0.2)
                : Colors.white.withOpacity(0.24)),
      ),
      child: Center(
        child: SizedBox(
          width: widget.radius,
          height: widget.radius,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              widget.foregroundColor ??
                  (isDarkMode
                      ? KFTDesignSystem.darkTextPrimaryColor
                      : Colors.white),
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildDefaultAvatar(ThemeData theme, bool isDarkMode, String initials) {
    return DefaultAvatarWidget(
      name: initials,
      radius: widget.radius,
      backgroundColor: widget.backgroundColor,
      foregroundColor: widget.foregroundColor,
    );
  }
}

// Profile dropdown menu
class ProfileDropdownMenu extends StatefulWidget {
  final Offset position;
  final Size avatarSize;
  final UserProfile? userProfile;
  final VoidCallback onClose;

  const ProfileDropdownMenu({
    super.key,
    required this.position,
    required this.avatarSize,
    required this.userProfile,
    required this.onClose,
  });

  @override
  State<ProfileDropdownMenu> createState() => _ProfileDropdownMenuState();
}

class _ProfileDropdownMenuState extends State<ProfileDropdownMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _closeMenu() async {
    await _animationController.reverse();
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;
    final screenSize = MediaQuery.of(context).size;

    // Calculate position for dropdown
    final dropdownWidth = 200.0;
    final dropdownHeight = 180.0;

    double left = widget.position.dx + widget.avatarSize.width - dropdownWidth;
    double top = widget.position.dy + widget.avatarSize.height + 8;

    // Ensure dropdown stays within screen bounds
    if (left < 16) left = 16;
    if (left + dropdownWidth > screenSize.width - 16) {
      left = screenSize.width - dropdownWidth - 16;
    }
    if (top + dropdownHeight > screenSize.height - 100) {
      top = widget.position.dy - dropdownHeight - 8;
    }

    return Stack(
      children: [
        // Backdrop
        GestureDetector(
          onTap: _closeMenu,
          child: Container(
            color: Colors.transparent,
            width: screenSize.width,
            height: screenSize.height,
          ),
        ),

        // Dropdown Menu
        Positioned(
          left: left,
          top: top,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                alignment: Alignment.topRight,
                child: Opacity(
                  opacity: _fadeAnimation.value,
                  child: Material(
                    elevation: 8,
                    borderRadius: BorderRadius.circular(12),
                    color: isDarkMode
                        ? KFTDesignSystem.darkSurfaceColor
                        : Colors.white,
                    child: Container(
                      width: dropdownWidth,
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.dividerColor.withOpacity(0.1),
                        ),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Profile Info
                          if (widget.userProfile != null) ...[
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              child: Row(
                                children: [
                                  DefaultAvatarWidget(
                                    name: widget.userProfile!.name,
                                    radius: 16,
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          widget.userProfile!.name,
                                          style: theme.textTheme.titleSmall?.copyWith(
                                            fontWeight: FontWeight.w600,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        Text(
                                          widget.userProfile!.phoneNumber,
                                          style: theme.textTheme.bodySmall?.copyWith(
                                            color: theme.colorScheme.onSurface.withOpacity(0.6),
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Divider(height: 1),
                          ],

                          // Menu Items
                          _buildMenuItem(
                            context,
                            icon: Icons.health_and_safety,
                            title: 'Health Metrics',
                            onTap: () {
                              _closeMenu();
                              showModalBottomSheet(
                                context: context,
                                shape: const RoundedRectangleBorder(
                                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                                ),
                                isScrollControlled: true,
                                builder: (context) => widget.userProfile == null
                                    ? const Center(child: Padding(padding: EdgeInsets.all(32), child: CircularProgressIndicator()))
                                    : SingleChildScrollView(
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                            bottom: MediaQuery.of(context).viewInsets.bottom,
                                          ),
                                          child: HealthMetricsCard(
                                            bmi: widget.userProfile!.currentBMI,
                                            bodyFat: widget.userProfile!.bodyFat ?? 0.0,
                                            muscleMass: widget.userProfile!.muscleMass ?? 0.0,
                                            waterPercentage: widget.userProfile!.waterPercentage ?? 0.0,
                                          ),
                                        ),
                                      ),
                              );
                            },
                          ),
                          _buildMenuItem(
                            context,
                            icon: Icons.person_outline,
                            title: 'Profile',
                            onTap: () {
                              _closeMenu();
                              Navigator.pushNamed(context, '/profile');
                            },
                          ),
                          _buildMenuItem(
                            context,
                            icon: Icons.settings_outlined,
                            title: 'Settings',
                            onTap: () {
                              _closeMenu();
                              Navigator.pushNamed(context, '/settings');
                            },
                          ),
                          _buildMenuItem(
                            context,
                            icon: Icons.bar_chart_outlined,
                            title: 'Progress',
                            onTap: () {
                              _closeMenu();
                              Navigator.pushNamed(context, '/progress');
                            },
                          ),
                          const Divider(height: 1),
                          // _buildMenuItem(
                          //   context,
                          //   icon: Icons.logout,
                          //   title: 'Sign Out',
                          //   onTap: () {
                          //     _closeMenu();
                          //     // Handle sign out
                          //     showDialog(
                          //       context: context,
                          //       builder: (context) => AlertDialog(
                          //         title: const Text('Sign Out'),
                          //         content: const Text('Are you sure you want to sign out?'),
                          //         actions: [
                          //           TextButton(
                          //             onPressed: () => Navigator.pop(context),
                          //             child: const Text('Cancel'),
                          //           ),
                          //           TextButton(
                          //             onPressed: () {
                          //               Navigator.pop(context);
                          //               Navigator.pushNamedAndRemoveUntil(
                          //                 context,
                          //                 '/login',
                          //                 (route) => false,
                          //               );
                          //             },
                          //             child: const Text('Sign Out'),
                          //           ),
                          //         ],
                          //       ),
                          //     );
                          //   },
                          //   isDestructive: true,
                          // ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    final theme = Theme.of(context);

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Icon(
              icon,
              size: 20,
              color: isDestructive
                  ? theme.colorScheme.error
                  : theme.colorScheme.onSurface.withOpacity(0.7),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isDestructive
                    ? theme.colorScheme.error
                    : theme.colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
