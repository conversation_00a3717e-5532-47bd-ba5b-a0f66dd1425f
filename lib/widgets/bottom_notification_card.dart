import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_system/kft_design_system.dart';

/// A bottom notification card that slides up from the bottom of the screen
/// Used to display notifications when users tap on system notifications
class BottomNotificationCard extends StatefulWidget {
  final String title;
  final String message;
  final IconData icon;
  final Color? iconColor;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final Duration duration;
  final bool showCloseButton;

  const BottomNotificationCard({
    super.key,
    required this.title,
    required this.message,
    required this.icon,
    this.iconColor,
    this.onTap,
    this.onDismiss,
    this.duration = const Duration(seconds: 4),
    this.showCloseButton = true,
  });

  @override
  State<BottomNotificationCard> createState() => _BottomNotificationCardState();
}

class _BottomNotificationCardState extends State<BottomNotificationCard>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animations
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start animations
    _showCard();

    // Auto-dismiss after duration
    Future.delayed(widget.duration, () {
      if (mounted) {
        _hideCard();
      }
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  void _showCard() {
    _slideController.forward();
    _fadeController.forward();
    _scaleController.forward();
    
    // Haptic feedback
    HapticFeedback.lightImpact();
  }

  void _hideCard() {
    _slideController.reverse().then((_) {
      if (mounted && widget.onDismiss != null) {
        widget.onDismiss!();
      }
    });
    _fadeController.reverse();
    _scaleController.reverse();
  }

  void _handleTap() {
    if (widget.onTap != null) {
      widget.onTap!();
    }
    _hideCard();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    return AnimatedBuilder(
      animation: Listenable.merge([_slideController, _fadeController, _scaleController]),
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                margin: const EdgeInsets.all(16),
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(16),
                  color: isDark 
                      ? KFTDesignSystem.darkSurfaceColor 
                      : Colors.white,
                  shadowColor: Colors.black.withOpacity(0.3),
                  child: InkWell(
                    onTap: _handleTap,
                    borderRadius: BorderRadius.circular(16),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: (widget.iconColor ?? KFTDesignSystem.primaryColor)
                              .withOpacity(0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          // Icon
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              color: (widget.iconColor ?? KFTDesignSystem.primaryColor)
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              widget.icon,
                              color: widget.iconColor ?? KFTDesignSystem.primaryColor,
                              size: 24,
                            ),
                          ),
                          
                          const SizedBox(width: 12),
                          
                          // Content
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  widget.title,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: isDark 
                                        ? KFTDesignSystem.darkTextPrimaryColor
                                        : KFTDesignSystem.textPrimaryColor,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  widget.message,
                                  style: theme.textTheme.bodyMedium?.copyWith(
                                    color: isDark 
                                        ? KFTDesignSystem.darkTextSecondaryColor
                                        : KFTDesignSystem.textSecondaryColor,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                          
                          // Close button
                          if (widget.showCloseButton)
                            IconButton(
                              onPressed: _hideCard,
                              icon: Icon(
                                Icons.close,
                                color: isDark 
                                    ? KFTDesignSystem.darkTextSecondaryColor
                                    : KFTDesignSystem.textSecondaryColor,
                                size: 20,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(
                                minWidth: 32,
                                minHeight: 32,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Helper class to show bottom notification cards
class BottomNotificationHelper {
  static OverlayEntry? _currentOverlay;

  /// Show a bottom notification card
  static void show({
    required BuildContext context,
    required String title,
    required String message,
    required IconData icon,
    Color? iconColor,
    VoidCallback? onTap,
    Duration duration = const Duration(seconds: 4),
    bool showCloseButton = true,
  }) {
    // Remove existing overlay if any
    dismiss();

    final overlay = Overlay.of(context);
    _currentOverlay = OverlayEntry(
      builder: (context) => Positioned(
        bottom: 0,
        left: 0,
        right: 0,
        child: BottomNotificationCard(
          title: title,
          message: message,
          icon: icon,
          iconColor: iconColor,
          onTap: onTap,
          duration: duration,
          showCloseButton: showCloseButton,
          onDismiss: () {
            dismiss();
          },
        ),
      ),
    );

    overlay.insert(_currentOverlay!);
  }

  /// Dismiss the current notification card
  static void dismiss() {
    _currentOverlay?.remove();
    _currentOverlay = null;
  }
}
