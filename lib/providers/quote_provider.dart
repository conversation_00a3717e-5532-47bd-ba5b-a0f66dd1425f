import 'dart:async';
import 'package:flutter/material.dart';
import '../models/motivational_quote.dart';
import '../services/quote_service.dart';

class QuoteProvider extends ChangeNotifier {
  final QuoteService _quoteService = QuoteService();

  MotivationalQuote? _currentQuote;
  List<MotivationalQuote> _allQuotes = [];
  QuotePreferences? _preferences;
  bool _isLoading = false;
  String? _error;
  Timer? _refreshTimer;

  // Getters
  MotivationalQuote? get currentQuote => _currentQuote;
  List<MotivationalQuote> get allQuotes => _allQuotes;
  QuotePreferences? get preferences => _preferences;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isEnabled => _preferences?.enabled ?? true;

  // Constructor
  QuoteProvider() {
    // Load initial data
    _initialize();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }

  // Initialize the provider
  Future<void> _initialize() async {
    await loadPreferences();
    await getNewQuote();
    await loadAllQuotes();

    // Set up a timer to refresh quotes periodically (once a day)
    _refreshTimer = Timer.periodic(const Duration(hours: 24), (_) {
      refreshQuotes();
    });
  }

  // Check if quotes are globally enabled
  Future<bool> areQuotesGloballyEnabled() async {
    return true; // Default to true until we have a backend setting
  }

  // Load user preferences
  Future<void> loadPreferences() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final prefs = await _quoteService.getPreferences();
      _preferences = prefs;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load preferences: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update user preferences
  Future<void> updatePreferences({
    bool? enabled,
    String? preferredCategories,
    bool? personalizationEnabled,
    bool? deepSeekEnabled,
    bool? showOnHomeScreen,
    bool? showOnLockScreen,
    int? frequencyPerDay,
  }) async {
    if (_preferences == null) {
      await loadPreferences();
      if (_preferences == null) {
        _error = 'Failed to load preferences';
        notifyListeners();
        return;
      }
    }

    _isLoading = true;
    notifyListeners();

    try {
      // Create updated preferences
      final updatedPrefs = _preferences!.copyWith(
        enabled: enabled,
        preferredCategories: preferredCategories,
        personalizationEnabled: personalizationEnabled,
        deepSeekEnabled: deepSeekEnabled,
        showOnHomeScreen: showOnHomeScreen,
        showOnLockScreen: showOnLockScreen,
        frequencyPerDay: frequencyPerDay,
      );

      // Update preferences via service
      await _quoteService.updatePreferences(
        preferredCategories: updatedPrefs.preferredCategories,
        personalizationEnabled: updatedPrefs.personalizationEnabled,
        deepSeekEnabled: updatedPrefs.deepSeekEnabled,
      );

      // Update local preferences
      _preferences = updatedPrefs;
      _isLoading = false;

      // If categories changed, refresh quotes
      if (preferredCategories != null) {
        await getNewQuote();
      }

      notifyListeners();
    } catch (e) {
      _error = 'Failed to update preferences: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Toggle enabled status
  Future<void> toggleEnabled() async {
    if (_preferences == null) {
      await loadPreferences();
    }

    if (_preferences != null) {
      await updatePreferences(enabled: !_preferences!.enabled);
    }
  }

  // Get a new quote
  Future<void> getNewQuote() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Check if quotes are globally enabled
      final quotesEnabled = await _quoteService.areQuotesGloballyEnabled();

      // Check if quotes are enabled for this user
      final userPrefs = await _quoteService.getPreferences();
      _preferences = userPrefs;

      if (!quotesEnabled || !userPrefs.enabled) {
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Get a random non-repeating quote
      final quote = await _quoteService.getRandomNonRepeatingQuote();
      _currentQuote = quote;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to get a new quote: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Load all quotes
  Future<void> loadAllQuotes() async {
    if (_isLoading) return;

    _isLoading = true;
    notifyListeners();

    try {
      final quotes = await _quoteService.fetchAllQuotes();
      _allQuotes = quotes;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = 'Failed to load all quotes: $e';
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh quotes
  Future<void> refreshQuotes() async {
    await loadAllQuotes();
    await getNewQuote();
  }

  // Toggle favorite status for a quote
  Future<void> toggleFavorite(MotivationalQuote quote) async {
    try {
      final updatedQuote = await _quoteService.toggleFavorite(quote);

      // Update current quote if it's the same
      if (_currentQuote != null && _currentQuote!.id == quote.id) {
        _currentQuote = updatedQuote;
      }

      // Update in all quotes list
      final index = _allQuotes.indexWhere((q) => q.id == quote.id);
      if (index >= 0) {
        _allQuotes[index] = updatedQuote;
      }

      notifyListeners();
    } catch (e) {
      _error = 'Failed to toggle favorite status: $e';
      notifyListeners();
    }
  }

  // Get a quote by category
  MotivationalQuote? getQuoteByCategory(String category) {
    final categoryQuotes = _allQuotes.where(
      (q) => q.category != null && q.category!.toLowerCase().contains(category.toLowerCase())
    ).toList();

    if (categoryQuotes.isEmpty) {
      return null;
    }

    // Return a random quote from the category
    categoryQuotes.shuffle();
    return categoryQuotes.first;
  }

  // Get favorite quotes
  List<MotivationalQuote> getFavoriteQuotes() {
    return _allQuotes.where((q) => q.isFavorite).toList();
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }
}
