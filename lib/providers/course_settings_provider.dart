import 'package:flutter/foundation.dart';
import '../models/course_settings.dart';

class CourseSettingsProvider with ChangeNotifier {
  CourseSettings? _settings;
  bool _isLoading = true;
  String _error = '';
  
  // Getters
  CourseSettings get settings => _settings ?? CourseSettings.defaults;
  bool get isLoading => _isLoading;
  String get error => _error;
  
  // Constructor
  CourseSettingsProvider() {
    _loadSettings();
  }
  
  // Load settings from local storage
  Future<void> _loadSettings() async {
    _isLoading = true;
    _error = '';
    notifyListeners();
    
    try {
      _settings = await CourseSettings.loadFromPrefs();
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _error = e.toString();
      _isLoading = false;
      notifyListeners();
    }
  }
  
  // Update settings
  Future<void> updateSettings({
    String? defaultWhatsappNumber,
    String? defaultMessagePrefix,
    bool? useCustomWhatsappNumbers,
  }) async {
    if (_settings == null) {
      _settings = CourseSettings.defaults;
    }
    
    _settings = _settings!.copyWith(
      defaultWhatsappNumber: defaultWhatsappNumber,
      defaultMessagePrefix: defaultMessagePrefix,
      useCustomWhatsappNumbers: useCustomWhatsappNumbers,
    );
    
    await _settings!.saveToPrefs();
    notifyListeners();
  }
  
  // Reset settings to defaults
  Future<void> resetToDefaults() async {
    _settings = CourseSettings.defaults;
    await _settings!.saveToPrefs();
    notifyListeners();
  }
}
