import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

final storage = FlutterSecureStorage();

final authProvider = riverpod.StateNotifierProvider<AuthNotifier, AuthState>((ref) => AuthNotifier());

class AuthState {
  final bool isAuthenticated;
  AuthState({required this.isAuthenticated});
}

class AuthNotifier extends riverpod.StateNotifier<AuthState> {
  AuthNotifier() : super(AuthState(isAuthenticated: false)) {
    checkAuth();
  }

  Future<void> checkAuth() async {
    final token = await storage.read(key: 'auth_token');
    state = AuthState(isAuthenticated: token != null);
  }

  Future<void> logout() async {
    await storage.delete(key: 'auth_token');
    state = AuthState(isAuthenticated: false);
  }

  Future<void> login(String token) async {
    await storage.write(key: 'auth_token', value: token);
    state = AuthState(isAuthenticated: true);
  }
} 