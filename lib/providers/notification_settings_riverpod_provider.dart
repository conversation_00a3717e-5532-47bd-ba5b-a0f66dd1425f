import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/material.dart';
import '../services/awesome_notification_service.dart';
import '../services/workout_video_notification_service.dart';

class NotificationSettingsState {
  final bool workoutNotificationsEnabled;
  final String workoutTime;
  final bool waterNotificationsEnabled;
  final int waterInterval;
  final bool hasPermissions;
  final bool videoNotificationsEnabled;
  final bool newVideoNotificationsEnabled;
  final bool unlockNotificationsEnabled;
  final bool smartSuggestionsEnabled;
  final bool isLoading;
  final String? error;

  NotificationSettingsState({
    this.workoutNotificationsEnabled = true,
    this.workoutTime = '07:00',
    this.waterNotificationsEnabled = true,
    this.waterInterval = 2,
    this.hasPermissions = false,
    this.videoNotificationsEnabled = true,
    this.newVideoNotificationsEnabled = true,
    this.unlockNotificationsEnabled = true,
    this.smartSuggestionsEnabled = true,
    this.isLoading = true,
    this.error,
  });

  NotificationSettingsState copyWith({
    bool? workoutNotificationsEnabled,
    String? workoutTime,
    bool? waterNotificationsEnabled,
    int? waterInterval,
    bool? hasPermissions,
    bool? videoNotificationsEnabled,
    bool? newVideoNotificationsEnabled,
    bool? unlockNotificationsEnabled,
    bool? smartSuggestionsEnabled,
    bool? isLoading,
    String? error,
  }) {
    return NotificationSettingsState(
      workoutNotificationsEnabled: workoutNotificationsEnabled ?? this.workoutNotificationsEnabled,
      workoutTime: workoutTime ?? this.workoutTime,
      waterNotificationsEnabled: waterNotificationsEnabled ?? this.waterNotificationsEnabled,
      waterInterval: waterInterval ?? this.waterInterval,
      hasPermissions: hasPermissions ?? this.hasPermissions,
      videoNotificationsEnabled: videoNotificationsEnabled ?? this.videoNotificationsEnabled,
      newVideoNotificationsEnabled: newVideoNotificationsEnabled ?? this.newVideoNotificationsEnabled,
      unlockNotificationsEnabled: unlockNotificationsEnabled ?? this.unlockNotificationsEnabled,
      smartSuggestionsEnabled: smartSuggestionsEnabled ?? this.smartSuggestionsEnabled,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

final notificationSettingsProvider = StateNotifierProvider<NotificationSettingsNotifier, NotificationSettingsState>((ref) {
  return NotificationSettingsNotifier();
});

class NotificationSettingsNotifier extends StateNotifier<NotificationSettingsState> {
  final EnhancedNotificationService _notificationService = EnhancedNotificationService();
  final WorkoutVideoNotificationService _videoNotificationService = WorkoutVideoNotificationService();

  NotificationSettingsNotifier() : super(NotificationSettingsState()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final hasPerms = await _notificationService.hasPermissions();
      final workoutEnabled = await _notificationService.isWorkoutNotificationEnabled();
      final waterEnabled = await _notificationService.isWaterNotificationEnabled();
      final TimeOfDay? workoutTimeOfDay = await _notificationService.getWorkoutReminderTime();
      final String workoutTimeString = workoutTimeOfDay != null 
          ? '${workoutTimeOfDay.hour.toString().padLeft(2, '0')}:${workoutTimeOfDay.minute.toString().padLeft(2, '0')}'
          : '07:00';
      final waterInterval = await _notificationService.getWaterReminderInterval();

      final videoNotificationsEnabled = await _videoNotificationService.areVideoNotificationsEnabled();
      final newVideoNotificationsEnabled = await _videoNotificationService.areNewVideoNotificationsEnabled();
      final unlockNotificationsEnabled = await _videoNotificationService.areUnlockNotificationsEnabled();
      final smartSuggestionsEnabled = await _videoNotificationService.areSmartSuggestionsEnabled();

      state = state.copyWith(
        hasPermissions: hasPerms,
        workoutNotificationsEnabled: workoutEnabled,
        waterNotificationsEnabled: waterEnabled,
        workoutTime: workoutTimeString,
        waterInterval: waterInterval,
        videoNotificationsEnabled: videoNotificationsEnabled,
        newVideoNotificationsEnabled: newVideoNotificationsEnabled,
        unlockNotificationsEnabled: unlockNotificationsEnabled,
        smartSuggestionsEnabled: smartSuggestionsEnabled,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'Error loading settings: $e');
    }
  }

  Future<void> requestPermissions() async {
    try {
      final granted = await _notificationService.requestPermissions();
      state = state.copyWith(hasPermissions: granted);
      if (!granted) {
        state = state.copyWith(error: 'Notification permissions denied. Some features may not work.');
      } else {
        state = state.copyWith(error: null);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to request permissions: $e');
    }
  }

  Future<void> setWorkoutNotificationsEnabled(bool enabled) async {
    try {
      await _notificationService.setWorkoutNotificationEnabled(enabled);
      state = state.copyWith(workoutNotificationsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update workout reminders: $e');
    }
  }

  Future<void> setWorkoutReminderTime(String time) async {
    try {
      await _notificationService.setWorkoutReminderTime(time);
      state = state.copyWith(workoutTime: time);
    } catch (e) {
      state = state.copyWith(error: 'Failed to set workout reminder time: $e');
    }
  }

  Future<void> setWaterNotificationsEnabled(bool enabled) async {
    try {
      await _notificationService.setWaterNotificationEnabled(enabled);
      state = state.copyWith(waterNotificationsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update water reminders: $e');
    }
  }

  Future<void> setWaterReminderInterval(int hours) async {
    try {
      await _notificationService.setWaterReminderInterval(hours);
      state = state.copyWith(waterInterval: hours);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update water interval: $e');
    }
  }

  Future<void> setVideoNotificationsEnabled(bool enabled) async {
    try {
      await _videoNotificationService.setVideoNotificationsEnabled(enabled);
      state = state.copyWith(videoNotificationsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update video notifications: $e');
    }
  }

  Future<void> setNewVideoNotificationsEnabled(bool enabled) async {
    try {
      await _videoNotificationService.setNewVideoNotificationsEnabled(enabled);
      state = state.copyWith(newVideoNotificationsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update new video notifications: $e');
    }
  }

  Future<void> setUnlockNotificationsEnabled(bool enabled) async {
    try {
      await _videoNotificationService.setUnlockNotificationsEnabled(enabled);
      state = state.copyWith(unlockNotificationsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update unlock notifications: $e');
    }
  }

  Future<void> setSmartSuggestionsEnabled(bool enabled) async {
    try {
      await _videoNotificationService.setSmartSuggestionsEnabled(enabled);
      state = state.copyWith(smartSuggestionsEnabled: enabled);
    } catch (e) {
      state = state.copyWith(error: 'Failed to update smart suggestions: $e');
    }
  }
} 