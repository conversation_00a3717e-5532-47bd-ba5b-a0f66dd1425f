-- Create streak tables for KFT fitness app
-- Run this SQL to create the necessary tables for the streak feature

-- Table to store user streak summary data
CREATE TABLE IF NOT EXISTS user_streaks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    current_streak INT DEFAULT 0,
    best_streak INT DEFAULT 0,
    total_completions INT DEFAULT 0,
    last_completion_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_streak (user_id)
);

-- Table to store individual day completions
CREATE TABLE IF NOT EXISTS streak_completions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    completion_date DATE NOT NULL,
    completion_type ENUM('workout', 'water', 'course', 'nutrition', 'general') DEFAULT 'general',
    activities <PERSON>SO<PERSON> NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, completion_date),
    INDEX idx_user_completion_date (user_id, completion_date),
    INDEX idx_completion_date (completion_date)
);

-- Insert some sample data for testing (optional)
-- This will create sample streak data for user ID 29 (jafer sadik)
INSERT IGNORE INTO user_streaks (user_id, current_streak, best_streak, total_completions, last_completion_date) 
VALUES (29, 3, 7, 15, NOW());

-- Insert sample completions for the last few days
INSERT IGNORE INTO streak_completions (user_id, completion_date, completion_type, activities) VALUES
(29, CURDATE(), 'workout', '["Morning workout completed"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 1 DAY), 'water', '["Water logged", "Workout completed"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 2 DAY), 'course', '["Course video watched"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 4 DAY), 'workout', '["Evening workout"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 5 DAY), 'general', '["Daily goal achieved"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 8 DAY), 'workout', '["HIIT workout completed"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 9 DAY), 'water', '["Hydration goal met"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 12 DAY), 'course', '["Advanced HIIT EP01 watched"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 15 DAY), 'workout', '["Full body workout"]'),
(29, DATE_SUB(CURDATE(), INTERVAL 18 DAY), 'nutrition', '["Meal plan followed"]');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_streaks_user_id ON user_streaks(user_id);
CREATE INDEX IF NOT EXISTS idx_streak_completions_user_date ON streak_completions(user_id, completion_date);
CREATE INDEX IF NOT EXISTS idx_streak_completions_date ON streak_completions(completion_date);

-- Update user_streaks table with calculated values based on completions
UPDATE user_streaks us
SET 
    total_completions = (
        SELECT COUNT(*) 
        FROM streak_completions sc 
        WHERE sc.user_id = us.user_id
    ),
    last_completion_date = (
        SELECT MAX(completed_at) 
        FROM streak_completions sc 
        WHERE sc.user_id = us.user_id
    )
WHERE us.user_id = 29;
