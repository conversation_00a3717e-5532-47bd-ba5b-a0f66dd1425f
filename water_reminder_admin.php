<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');
header('Access-Control-Allow-Credentials: true');
header('Access-Control-Expose-Headers: Authorization');
header('Access-Control-Max-Age: 86400');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once 'config.php';
require_once 'auth.php';

// Verify admin authentication
$auth = verifyAdminToken();
if (!$auth['valid']) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access',
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
    exit();
}

$admin_id = $auth['admin_id'];
$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'GET':
            handleGetWaterReminders($pdo);
            break;
        case 'POST':
            handleCreateWaterReminder($pdo, $admin_id);
            break;
        case 'PUT':
            handleUpdateWaterReminder($pdo, $admin_id);
            break;
        case 'DELETE':
            handleDeleteWaterReminder($pdo, $admin_id);
            break;
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'message' => 'Method not allowed',
                '_server_time' => date('Y-m-d H:i:s'),
                '_api_version' => '1.0',
                '_dev_mode' => true
            ]);
            break;
    }
} catch (Exception $e) {
    error_log("Water Reminder Admin Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage(),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
}

function handleGetWaterReminders($pdo) {
    $user_id = $_GET['user_id'] ?? null;
    
    if ($user_id) {
        // Get specific user's water reminder settings
        $stmt = $pdo->prepare("
            SELECT wr.*, u.name as user_name, u.phone as user_phone
            FROM water_reminders wr
            JOIN users u ON wr.user_id = u.id
            WHERE wr.user_id = ?
        ");
        $stmt->execute([$user_id]);
        $reminder = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($reminder) {
            echo json_encode([
                'success' => true,
                'water_reminder' => formatWaterReminderForAdmin($reminder),
                '_server_time' => date('Y-m-d H:i:s'),
                '_api_version' => '1.0',
                '_dev_mode' => true
            ]);
        } else {
            echo json_encode([
                'success' => true,
                'water_reminder' => null,
                'message' => 'No water reminder settings found for this user',
                '_server_time' => date('Y-m-d H:i:s'),
                '_api_version' => '1.0',
                '_dev_mode' => true
            ]);
        }
    } else {
        // Get all users with water reminder settings
        $stmt = $pdo->prepare("
            SELECT wr.*, u.name as user_name, u.phone as user_phone
            FROM water_reminders wr
            JOIN users u ON wr.user_id = u.id
            ORDER BY u.name ASC
        ");
        $stmt->execute();
        $reminders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $formatted_reminders = array_map('formatWaterReminderForAdmin', $reminders);
        
        echo json_encode([
            'success' => true,
            'water_reminders' => $formatted_reminders,
            'total_count' => count($formatted_reminders),
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
    }
}

function handleCreateWaterReminder($pdo, $admin_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    $required_fields = ['user_id', 'daily_target', 'reminder_interval', 'start_time', 'end_time'];
    foreach ($required_fields as $field) {
        if (!isset($input[$field])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => "Missing required field: $field",
                '_server_time' => date('Y-m-d H:i:s'),
                '_api_version' => '1.0',
                '_dev_mode' => true
            ]);
            return;
        }
    }
    
    // Validate user exists
    $stmt = $pdo->prepare("SELECT id, name FROM users WHERE id = ?");
    $stmt->execute([$input['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'User not found',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Check if water reminder already exists for this user
    $stmt = $pdo->prepare("SELECT id FROM water_reminders WHERE user_id = ?");
    $stmt->execute([$input['user_id']]);
    if ($stmt->fetch()) {
        http_response_code(409);
        echo json_encode([
            'success' => false,
            'message' => 'Water reminder already exists for this user. Use PUT to update.',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Create water reminder
    $stmt = $pdo->prepare("
        INSERT INTO water_reminders (
            user_id, daily_target, reminder_interval, start_time, end_time, 
            is_enabled, admin_managed, created_by_admin, created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, 1, ?, NOW(), NOW())
    ");
    
    $is_enabled = $input['is_enabled'] ?? true;
    
    $stmt->execute([
        $input['user_id'],
        $input['daily_target'],
        $input['reminder_interval'],
        $input['start_time'],
        $input['end_time'],
        $is_enabled ? 1 : 0,
        $admin_id
    ]);
    
    $reminder_id = $pdo->lastInsertId();
    
    // Get the created reminder with user info
    $stmt = $pdo->prepare("
        SELECT wr.*, u.name as user_name, u.phone as user_phone
        FROM water_reminders wr
        JOIN users u ON wr.user_id = u.id
        WHERE wr.id = ?
    ");
    $stmt->execute([$reminder_id]);
    $reminder = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Water reminder created successfully',
        'water_reminder' => formatWaterReminderForAdmin($reminder),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
}

function handleUpdateWaterReminder($pdo, $admin_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['user_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Missing required field: user_id',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Check if water reminder exists
    $stmt = $pdo->prepare("SELECT id FROM water_reminders WHERE user_id = ?");
    $stmt->execute([$input['user_id']]);
    $existing = $stmt->fetch();
    
    if (!$existing) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Water reminder not found for this user',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Build update query dynamically
    $update_fields = [];
    $params = [];
    
    $allowed_fields = ['daily_target', 'reminder_interval', 'start_time', 'end_time', 'is_enabled'];
    foreach ($allowed_fields as $field) {
        if (isset($input[$field])) {
            $update_fields[] = "$field = ?";
            $params[] = $field === 'is_enabled' ? ($input[$field] ? 1 : 0) : $input[$field];
        }
    }
    
    if (empty($update_fields)) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'No valid fields to update',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Add admin tracking fields
    $update_fields[] = "admin_managed = 1";
    $update_fields[] = "updated_by_admin = ?";
    $update_fields[] = "updated_at = NOW()";
    $params[] = $admin_id;
    $params[] = $input['user_id'];
    
    $sql = "UPDATE water_reminders SET " . implode(', ', $update_fields) . " WHERE user_id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    // Get the updated reminder with user info
    $stmt = $pdo->prepare("
        SELECT wr.*, u.name as user_name, u.phone as user_phone
        FROM water_reminders wr
        JOIN users u ON wr.user_id = u.id
        WHERE wr.user_id = ?
    ");
    $stmt->execute([$input['user_id']]);
    $reminder = $stmt->fetch(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'message' => 'Water reminder updated successfully',
        'water_reminder' => formatWaterReminderForAdmin($reminder),
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
}

function handleDeleteWaterReminder($pdo, $admin_id) {
    $user_id = $_GET['user_id'] ?? null;
    
    if (!$user_id) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Missing required parameter: user_id',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Check if water reminder exists
    $stmt = $pdo->prepare("SELECT id FROM water_reminders WHERE user_id = ?");
    $stmt->execute([$user_id]);
    if (!$stmt->fetch()) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'message' => 'Water reminder not found for this user',
            '_server_time' => date('Y-m-d H:i:s'),
            '_api_version' => '1.0',
            '_dev_mode' => true
        ]);
        return;
    }
    
    // Delete water reminder
    $stmt = $pdo->prepare("DELETE FROM water_reminders WHERE user_id = ?");
    $stmt->execute([$user_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Water reminder deleted successfully',
        '_server_time' => date('Y-m-d H:i:s'),
        '_api_version' => '1.0',
        '_dev_mode' => true
    ]);
}

function formatWaterReminderForAdmin($reminder) {
    return [
        'id' => (int)$reminder['id'],
        'user_id' => (int)$reminder['user_id'],
        'user_name' => $reminder['user_name'],
        'user_phone' => $reminder['user_phone'],
        'daily_target' => (int)$reminder['daily_target'],
        'reminder_interval' => (int)$reminder['reminder_interval'],
        'start_time' => $reminder['start_time'],
        'end_time' => $reminder['end_time'],
        'is_enabled' => (bool)$reminder['is_enabled'],
        'admin_managed' => (bool)$reminder['admin_managed'],
        'created_by_admin' => $reminder['created_by_admin'] ? (int)$reminder['created_by_admin'] : null,
        'updated_by_admin' => $reminder['updated_by_admin'] ? (int)$reminder['updated_by_admin'] : null,
        'created_at' => $reminder['created_at'],
        'updated_at' => $reminder['updated_at']
    ];
}
?>
