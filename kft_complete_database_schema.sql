-- KFT Fitness Complete Database Schema
-- This file contains the complete database structure for the KFT Fitness application
-- Created: 2025-01-27

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS kft_fitness;
USE kft_fitness;

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    pin CHAR(4) DEFAULT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    role ENUM('admin', 'editor', 'viewer') NOT NULL DEFAULT 'editor',
    last_login TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    name VARCHAR(100) NOT NULL
);

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username <PERSON><PERSON><PERSON><PERSON>(50) UNIQUE,
    password VARCHAR(255),
    pin CHAR(4) DEFAULT NULL,
    name VA<PERSON><PERSON>R(100) NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone_number VARCHAR(20) UNIQUE,
    gender ENUM('male', 'female', 'other'),
    fitness_goal VARCHAR(50),
    age INT,
    height DECIMAL(5,2) DEFAULT NULL,
    weight DECIMAL(5,2) DEFAULT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    verification_code VARCHAR(10),
    verification_expires_at TIMESTAMP,
    last_login TIMESTAMP NULL DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- API tokens table for mobile app authentication
CREATE TABLE IF NOT EXISTS api_tokens (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    token VARCHAR(255) NOT NULL UNIQUE,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- BMI records table
CREATE TABLE IF NOT EXISTS bmi_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    weight DECIMAL(5,2) NOT NULL,
    height DECIMAL(5,2) NOT NULL,
    bmi DECIMAL(4,2) NOT NULL,
    recorded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_recorded_at (recorded_at)
);

-- Workout records table
CREATE TABLE IF NOT EXISTS workout_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(100) NOT NULL,
    duration_minutes INT NOT NULL,
    recorded_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Workout programs table
CREATE TABLE IF NOT EXISTS workout_programs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    duration_weeks INT NOT NULL,
    difficulty ENUM('beginner', 'intermediate', 'advanced') NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Program enrollments table
CREATE TABLE IF NOT EXISTS program_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    program_id INT NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (program_id) REFERENCES workout_programs(id) ON DELETE CASCADE
);

-- Water reminder settings table
CREATE TABLE IF NOT EXISTS water_reminders (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    interval_hours INT NOT NULL DEFAULT 2,
    start_time TIME NOT NULL DEFAULT '08:00:00',
    end_time TIME NOT NULL DEFAULT '22:00:00',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Session access table
CREATE TABLE IF NOT EXISTS session_access (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_type VARCHAR(50) NOT NULL,
    access_level ENUM('none', 'view', 'full') NOT NULL DEFAULT 'none',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY (user_id, session_type),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- User streak summary data
CREATE TABLE IF NOT EXISTS user_streaks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    current_streak INT DEFAULT 0,
    best_streak INT DEFAULT 0,
    total_completions INT DEFAULT 0,
    last_completion_date DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_streak (user_id)
);

-- Individual day completions for streak tracking
CREATE TABLE IF NOT EXISTS streak_completions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    completion_date DATE NOT NULL,
    completion_type ENUM('workout', 'water', 'course', 'nutrition', 'general') DEFAULT 'general',
    activities JSON NULL,
    completed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_date (user_id, completion_date),
    INDEX idx_user_completion_date (user_id, completion_date),
    INDEX idx_completion_date (completion_date)
);

-- Motivational quotes table
CREATE TABLE IF NOT EXISTS motivational_quotes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    quote TEXT NOT NULL,
    author VARCHAR(255) DEFAULT NULL,
    category VARCHAR(100) DEFAULT NULL,
    is_ai_generated TINYINT(1) NOT NULL DEFAULT 0,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY category (category),
    KEY is_active (is_active)
);

-- User quote preferences
CREATE TABLE IF NOT EXISTS user_quote_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    preferred_categories JSON DEFAULT NULL,
    frequency ENUM('daily', 'weekly', 'custom') DEFAULT 'daily',
    is_enabled TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preference (user_id)
);

-- Quote settings for admin configuration
CREATE TABLE IF NOT EXISTS quote_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY setting_key (setting_key)
);

-- Settings table for application configuration
CREATE TABLE IF NOT EXISTS settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    `key` VARCHAR(100) NOT NULL,
    `value` TEXT DEFAULT NULL,
    `group` VARCHAR(50) DEFAULT 'general',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key (`key`)
);

-- Insert default admin user (password: admin123, pin: 1234)
INSERT INTO admin_users (username, password, pin, email, role, name) VALUES
('admin', '$2y$10$8zUkFXRsqFvgQa.Yl9VDOeQJPb0zrQGwXcaVCPqPHbCiGWYXwwRne', '1234', '<EMAIL>', 'admin', 'Admin')
ON DUPLICATE KEY UPDATE id=id;

-- Insert default quote settings
INSERT INTO quote_settings (setting_key, setting_value) VALUES
('ai_provider', 'deepseek'),
('deepseek_api_key', ''),
('ai_model', 'deepseek-chat'),
('personalization_enabled', '1'),
('quotes_enabled', '1'),
('personalization_factors', 'workout_history,fitness_goals,streak_days,bmi_progress'),
('personalization_prompt', 'Generate a motivational fitness quote for a user who {user_context}. The quote should be encouraging, positive, and tailored to their current fitness journey. Make the quote concise and impactful.'),
('quote_refresh_frequency', 'daily'),
('default_categories', 'fitness,motivation,health,mindfulness'),
('max_ai_quotes_per_day', '1')
ON DUPLICATE KEY UPDATE setting_value=VALUES(setting_value);

-- Courses table
CREATE TABLE IF NOT EXISTS courses (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    thumbnail_url VARCHAR(500),
    duration_weeks INT DEFAULT 1,
    difficulty ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'beginner',
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Course videos table
CREATE TABLE IF NOT EXISTS course_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    vimeo_id VARCHAR(50),
    vimeo_password VARCHAR(255),
    duration_seconds INT DEFAULT 0,
    week_number INT DEFAULT 1,
    video_order INT DEFAULT 0,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    INDEX idx_course_week (course_id, week_number),
    INDEX idx_video_order (video_order)
);

-- User video progress table
CREATE TABLE IF NOT EXISTS user_video_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    watch_time_seconds INT DEFAULT 0,
    completed BOOLEAN DEFAULT FALSE,
    last_watched_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES course_videos(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_video (user_id, video_id)
);

-- User course enrollments
CREATE TABLE IF NOT EXISTS user_course_enrollments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_id INT NOT NULL,
    enrolled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    current_week INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES courses(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_course (user_id, course_id)
);

-- Calorie tracking tables
CREATE TABLE IF NOT EXISTS food_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    calories INT NOT NULL,
    protein DECIMAL(5,2) DEFAULT NULL,
    carbs DECIMAL(5,2) DEFAULT NULL,
    fat DECIMAL(5,2) DEFAULT NULL,
    serving_size VARCHAR(50) DEFAULT NULL,
    category VARCHAR(50) DEFAULT NULL,
    is_verified TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    KEY name (name),
    KEY category (category)
);

CREATE TABLE IF NOT EXISTS calorie_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    log_date DATE NOT NULL,
    meal_type ENUM('breakfast','lunch','dinner','snack') NOT NULL,
    food_item_id INT DEFAULT NULL,
    food_name VARCHAR(255) NOT NULL,
    calories INT NOT NULL,
    protein DECIMAL(5,2) DEFAULT NULL,
    carbs DECIMAL(5,2) DEFAULT NULL,
    fat DECIMAL(5,2) DEFAULT NULL,
    quantity DECIMAL(5,2) NOT NULL DEFAULT 1.00,
    serving_size VARCHAR(50) DEFAULT NULL,
    notes TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (food_item_id) REFERENCES food_items(id) ON DELETE SET NULL,
    KEY user_date (user_id, log_date)
);

CREATE TABLE IF NOT EXISTS calorie_goals (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    daily_calories INT NOT NULL,
    protein_goal DECIMAL(5,2) DEFAULT NULL,
    carbs_goal DECIMAL(5,2) DEFAULT NULL,
    fat_goal DECIMAL(5,2) DEFAULT NULL,
    start_date DATE NOT NULL,
    end_date DATE DEFAULT NULL,
    is_active TINYINT(1) NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    KEY user_id (user_id)
);

-- Insert default system settings
INSERT INTO settings (`key`, `value`, `group`, is_active) VALUES
('is_dev_mode', 'true', 'system', 1),
('app_version', '1.0.0', 'system', 1),
('maintenance_mode', 'false', 'system', 1)
ON DUPLICATE KEY UPDATE `value`=VALUES(`value`);

-- Insert sample motivational quotes
INSERT INTO motivational_quotes (quote, author, category, is_active) VALUES
('The only bad workout is the one that didn\'t happen.', 'Unknown', 'fitness', 1),
('Your body can do it. It\'s your mind you have to convince.', 'Unknown', 'motivation', 1),
('Fitness is not about being better than someone else. It\'s about being better than you used to be.', 'Khloe Kardashian', 'fitness', 1),
('The groundwork for all happiness is good health.', 'Leigh Hunt', 'health', 1),
('Take care of your body. It\'s the only place you have to live.', 'Jim Rohn', 'health', 1),
('Success isn\'t given. It\'s earned in the gym.', 'Unknown', 'motivation', 1),
('A healthy outside starts from the inside.', 'Robert Urich', 'mindfulness', 1),
('Exercise is a celebration of what your body can do, not a punishment for what you ate.', 'Unknown', 'mindfulness', 1)
ON DUPLICATE KEY UPDATE quote=VALUES(quote);

-- Insert sample courses
INSERT INTO courses (title, description, duration_weeks, difficulty, is_premium, is_active, sort_order) VALUES
('Beginner Fitness Journey', 'Perfect for those starting their fitness journey. Learn the basics of exercise and build healthy habits.', 4, 'beginner', FALSE, TRUE, 1),
('Intermediate Strength Training', 'Take your fitness to the next level with structured strength training routines.', 6, 'intermediate', TRUE, TRUE, 2),
('Advanced HIIT Workouts', 'High-intensity interval training for experienced fitness enthusiasts.', 8, 'advanced', TRUE, TRUE, 3),
('Yoga for Flexibility', 'Improve your flexibility and mindfulness with guided yoga sessions.', 4, 'beginner', FALSE, TRUE, 4)
ON DUPLICATE KEY UPDATE title=VALUES(title);

-- Insert sample food items
INSERT INTO food_items (name, calories, protein, carbs, fat, serving_size, category, is_verified) VALUES
('Chicken Breast (Grilled)', 165, 31.0, 0.0, 3.6, '100g', 'Protein', 1),
('Brown Rice (Cooked)', 111, 2.6, 23.0, 0.9, '100g', 'Carbohydrates', 1),
('Broccoli (Steamed)', 34, 2.8, 7.0, 0.4, '100g', 'Vegetables', 1),
('Banana', 89, 1.1, 23.0, 0.3, '1 medium', 'Fruits', 1),
('Almonds', 579, 21.0, 22.0, 50.0, '100g', 'Nuts', 1),
('Greek Yogurt (Plain)', 59, 10.0, 3.6, 0.4, '100g', 'Dairy', 1),
('Oatmeal (Cooked)', 68, 2.4, 12.0, 1.4, '100g', 'Grains', 1),
('Salmon (Grilled)', 206, 22.0, 0.0, 12.0, '100g', 'Protein', 1)
ON DUPLICATE KEY UPDATE name=VALUES(name);
