<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Robust Video Seeking Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 30px;
        }

        .video-section {
            margin-bottom: 40px;
        }

        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 12px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .video-frame {
            width: 100%;
            height: 100%;
            border: none;
        }

        .controls-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            border: 1px solid #e9ecef;
        }

        .control-group h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1rem;
            font-weight: 600;
        }

        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .btn {
            padding: 12px 16px;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
            transform: translateY(-1px);
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn-warning:hover {
            background: #e0a800;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
            transform: translateY(-1px);
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #1e7e34;
            transform: translateY(-1px);
        }

        .progress-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            width: 0%;
            transition: width 0.3s ease;
        }

        .time-display {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #007bff;
        }

        .stat-label {
            font-size: 0.8rem;
            color: #6c757d;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .log-section {
            background: #212529;
            color: #fff;
            padding: 20px;
            border-radius: 12px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .input-group input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 0.9rem;
        }

        .input-group button {
            padding: 10px 20px;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                border-radius: 12px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .content {
                padding: 20px;
            }
            
            .controls-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Robust Video Seeking Test</h1>
            <p>Test aggressive seeking functionality with throttling, debouncing, and error recovery</p>
        </div>

        <div class="content">
            <!-- Video Player Section -->
            <div class="video-section">
                <div class="video-container">
                    <iframe id="videoFrame" class="video-frame" 
                            src="./kft_vimeo_player_deploy.html?vimeo_id=1087487482&video_id=1&domain=mycloudforge.com&autoplay=0"
                            allowfullscreen>
                    </iframe>
                </div>

                <!-- Progress Bar -->
                <div class="progress-section">
                    <h3>Video Progress</h3>
                    <div class="progress-bar" id="progressBar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="time-display">
                        <span id="currentTime">0:00</span>
                        <span id="duration">0:00</span>
                    </div>
                </div>
            </div>

            <!-- Control Sections -->
            <div class="controls-section">
                <!-- Basic Seeking -->
                <div class="control-group">
                    <h3>🎯 Basic Seeking</h3>
                    <div class="input-group">
                        <input type="number" id="seekInput" placeholder="Seek to (seconds)" min="0" max="3600">
                        <button class="btn btn-primary" onclick="performSeek()">Seek</button>
                    </div>
                    <div class="button-grid">
                        <button class="btn btn-primary" onclick="seekTo(30)">30s</button>
                        <button class="btn btn-primary" onclick="seekTo(60)">1m</button>
                        <button class="btn btn-primary" onclick="seekTo(120)">2m</button>
                        <button class="btn btn-primary" onclick="seekTo(300)">5m</button>
                    </div>
                </div>

                <!-- Aggressive Seeking -->
                <div class="control-group">
                    <h3>🚀 Aggressive Seeking</h3>
                    <div class="button-grid">
                        <button class="btn btn-warning" onclick="rapidSeekTest()">Rapid Seek</button>
                        <button class="btn btn-warning" onclick="randomSeekTest()">Random Seek</button>
                        <button class="btn btn-warning" onclick="continuousSeekTest()">Continuous</button>
                        <button class="btn btn-danger" onclick="stopTests()">Stop Tests</button>
                    </div>
                </div>

                <!-- Force Seeking -->
                <div class="control-group">
                    <h3>💥 Force Seeking</h3>
                    <div class="button-grid">
                        <button class="btn btn-danger" onclick="forceSeek(60)">Force 1m</button>
                        <button class="btn btn-danger" onclick="forceSeek(180)">Force 3m</button>
                        <button class="btn btn-danger" onclick="forceSeek(600)">Force 10m</button>
                        <button class="btn btn-success" onclick="getStats()">Get Stats</button>
                    </div>
                </div>
            </div>

            <!-- Performance Stats -->
            <div class="stats-section" id="statsSection">
                <div class="stat-card">
                    <div class="stat-value" id="totalAttempts">0</div>
                    <div class="stat-label">Total Attempts</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successfulSeeks">0</div>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="failedSeeks">0</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="throttledSeeks">0</div>
                    <div class="stat-label">Throttled</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="successRate">0%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>

            <!-- Log Section -->
            <div class="log-section" id="logSection">
                <div class="log-entry log-info">🎬 Robust Video Seeking Test initialized</div>
                <div class="log-entry log-info">📱 Waiting for video player to load...</div>
            </div>
        </div>
    </div>

    <script>
        let videoFrame = document.getElementById('videoFrame');
        let currentPosition = 0;
        let videoDuration = 0;
        let testIntervals = [];
        let seekCount = 0;

        // Initialize
        window.addEventListener('load', function() {
            log('🎬 Test page loaded, setting up video player communication', 'info');
            setupProgressBar();
            updateStats();
        });

        // Listen for messages from video player
        window.addEventListener('message', function(event) {
            if (event.origin !== window.location.origin) return;
            
            try {
                const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
                handlePlayerMessage(data);
            } catch (e) {
                log(`❌ Error parsing player message: ${e}`, 'error');
            }
        });

        function handlePlayerMessage(data) {
            switch(data.event || data.type) {
                case 'onPlayerReady':
                    log('✅ Video player ready', 'success');
                    break;
                case 'onProgress':
                    currentPosition = data.position || 0;
                    updateProgress();
                    break;
                case 'onSeekSuccess':
                    log(`✅ Seek successful: ${data.position}s`, 'success');
                    break;
                case 'onSeekFailure':
                    log(`❌ Seek failed: ${data.error}`, 'error');
                    break;
                case 'onSeekThrottled':
                    log(`🎛️ Seek throttled: ${data.position}s`, 'warning');
                    break;
                default:
                    if (data.event) {
                        log(`📱 Player event: ${data.event}`, 'info');
                    }
            }
        }

        function setupProgressBar() {
            const progressBar = document.getElementById('progressBar');
            progressBar.addEventListener('click', function(e) {
                const rect = progressBar.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const progress = clickX / rect.width;
                const targetPosition = Math.floor(progress * videoDuration);
                
                if (targetPosition >= 0 && targetPosition <= videoDuration) {
                    seekTo(targetPosition);
                }
            });
        }

        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            const currentTimeEl = document.getElementById('currentTime');
            
            if (videoDuration > 0) {
                const progress = (currentPosition / videoDuration) * 100;
                progressFill.style.width = `${progress}%`;
            }
            
            currentTimeEl.textContent = formatTime(currentPosition);
        }

        function formatTime(seconds) {
            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
        }

        function log(message, type = 'info') {
            const logSection = document.getElementById('logSection');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logSection.appendChild(logEntry);
            logSection.scrollTop = logSection.scrollHeight;
        }

        function seekTo(position) {
            log(`🎯 Seeking to ${position}s`, 'info');
            seekCount++;
            
            // Send seek message to video player
            videoFrame.contentWindow.postMessage(JSON.stringify({
                action: 'seek',
                position: position,
                isUserInitiated: true
            }), '*');
        }

        function performSeek() {
            const seekInput = document.getElementById('seekInput');
            const position = parseInt(seekInput.value);
            
            if (isNaN(position) || position < 0) {
                log('❌ Invalid seek position', 'error');
                return;
            }
            
            seekTo(position);
        }

        function forceSeek(position) {
            log(`💥 Force seeking to ${position}s`, 'warning');
            seekCount++;
            
            videoFrame.contentWindow.postMessage(JSON.stringify({
                action: 'seek',
                position: position,
                isForceSeek: true,
                isAggressiveSeek: true
            }), '*');
        }

        function rapidSeekTest() {
            log('🚀 Starting rapid seek test...', 'warning');
            
            const positions = [30, 60, 90, 120, 150, 180, 210, 240];
            let index = 0;
            
            const interval = setInterval(() => {
                if (index >= positions.length) {
                    clearInterval(interval);
                    log('✅ Rapid seek test completed', 'success');
                    return;
                }
                
                seekTo(positions[index]);
                index++;
            }, 500); // Seek every 500ms
            
            testIntervals.push(interval);
        }

        function randomSeekTest() {
            log('🎲 Starting random seek test...', 'warning');
            
            const interval = setInterval(() => {
                const randomPosition = Math.floor(Math.random() * 600); // Random position up to 10 minutes
                seekTo(randomPosition);
            }, 1000); // Random seek every second
            
            testIntervals.push(interval);
            
            // Stop after 30 seconds
            setTimeout(() => {
                clearInterval(interval);
                log('✅ Random seek test completed', 'success');
            }, 30000);
        }

        function continuousSeekTest() {
            log('🔄 Starting continuous seek test...', 'warning');
            
            let position = 0;
            const interval = setInterval(() => {
                position += 10;
                if (position > 600) position = 0; // Reset after 10 minutes
                seekTo(position);
            }, 300); // Seek every 300ms
            
            testIntervals.push(interval);
        }

        function stopTests() {
            testIntervals.forEach(interval => clearInterval(interval));
            testIntervals = [];
            log('🛑 All tests stopped', 'info');
        }

        function getStats() {
            log('📊 Requesting performance stats...', 'info');
            
            // Request stats from video player
            videoFrame.contentWindow.postMessage(JSON.stringify({
                action: 'getStats'
            }), '*');
        }

        function updateStats() {
            // Update with mock data for now
            document.getElementById('totalAttempts').textContent = seekCount;
            document.getElementById('successfulSeeks').textContent = Math.floor(seekCount * 0.85);
            document.getElementById('failedSeeks').textContent = Math.floor(seekCount * 0.15);
            document.getElementById('throttledSeeks').textContent = Math.floor(seekCount * 0.3);
            document.getElementById('successRate').textContent = '85%';
        }

        // Update stats periodically
        setInterval(updateStats, 2000);
    </script>
</body>
</html>
