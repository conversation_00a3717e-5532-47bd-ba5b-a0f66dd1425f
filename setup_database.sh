#!/bin/bash

# KFT Fitness Database Setup Script
# This script sets up the complete database for the KFT Fitness application

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default database configuration
DB_HOST="localhost"
DB_USER="myclo4dz_new_kftdb"
DB_PASS="U.q.!)hDK+gR"
DB_NAME="myclo4dz_new_kftdb"

echo -e "${BLUE}=== KFT Fitness Database Setup ===${NC}"
echo "This script will set up the complete database for the KFT Fitness application."
echo ""

# Function to test database connection
test_connection() {
    echo -e "${BLUE}Testing database connection...${NC}"
    
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT 1;" 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Database connection successful!${NC}"
        return 0
    else
        echo -e "${RED}✗ Database connection failed!${NC}"
        return 1
    fi
}

# Function to import database schema
import_schema() {
    echo -e "${BLUE}Importing database schema...${NC}"
    
    # Check if the database file exists
    if [ ! -f "myclo4dz_databaseforge.sql" ]; then
        echo -e "${RED}✗ Database file not found: myclo4dz_databaseforge.sql${NC}"
        return 1
    fi
    
    # Create database if it doesn't exist
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" "$DB_NAME" < myclo4dz_databaseforge.sql
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Database schema imported successfully!${NC}"
        return 0
    else
        echo -e "${RED}✗ Failed to import database schema!${NC}"
        return 1
    fi
}

# Function to verify database setup
verify_setup() {
    echo -e "${BLUE}Verifying database setup...${NC}"
    
    # Check if key tables exist
    local tables=("users" "admin_users" "courses" "course_videos" "user_streaks" "motivational_quotes")
    local all_tables_exist=true
    
    for table in "${tables[@]}"; do
        result=$(mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -D "$DB_NAME" -e "SHOW TABLES LIKE '$table';" 2>/dev/null)
        
        if [ -z "$result" ]; then
            echo -e "${RED}✗ Table '$table' not found${NC}"
            all_tables_exist=false
        else
            echo -e "${GREEN}✓ Table '$table' exists${NC}"
        fi
    done
    
    if [ "$all_tables_exist" = true ]; then
        echo -e "${GREEN}✓ Database setup verification completed successfully!${NC}"
        return 0
    else
        echo -e "${RED}✗ Database setup verification failed!${NC}"
        return 1
    fi
}

# Function to update configuration files
update_config() {
    echo -e "${BLUE}Updating configuration files...${NC}"
    
    # Update admin config
    if [ -f "admin/includes/config.php" ]; then
        # Create backup of original config
        cp admin/includes/config.php admin/includes/config.php.backup
        
        # Update database configuration
        sed -i.bak "s/define('DB_HOST', '[^']*');/define('DB_HOST', '$DB_HOST');/" admin/includes/config.php
        sed -i.bak "s/define('DB_USER', '[^']*');/define('DB_USER', '$DB_USER');/" admin/includes/config.php
        sed -i.bak "s/define('DB_PASS', '[^']*');/define('DB_PASS', '$DB_PASS');/" admin/includes/config.php
        sed -i.bak "s/define('DB_NAME', '[^']*');/define('DB_NAME', '$DB_NAME');/" admin/includes/config.php
        
        echo -e "${GREEN}✓ Updated admin/includes/config.php${NC}"
    fi
    
    # Update API config
    if [ -f "admin/api/config.php" ]; then
        # Create backup of original config
        cp admin/api/config.php admin/api/config.php.backup
        
        # Update database configuration
        sed -i.bak "s/define('DB_HOST', '[^']*');/define('DB_HOST', '$DB_HOST');/" admin/api/config.php
        sed -i.bak "s/define('DB_USER', '[^']*');/define('DB_USER', '$DB_USER');/" admin/api/config.php
        sed -i.bak "s/define('DB_PASS', '[^']*');/define('DB_PASS', '$DB_PASS');/" admin/api/config.php
        sed -i.bak "s/define('DB_NAME', '[^']*');/define('DB_NAME', '$DB_NAME');/" admin/api/config.php
        
        echo -e "${GREEN}✓ Updated admin/api/config.php${NC}"
    fi
}

# Function to display summary
display_summary() {
    echo ""
    echo -e "${GREEN}=== Setup Complete ===${NC}"
    echo -e "${BLUE}Database Details:${NC}"
    echo "  Host: $DB_HOST"
    echo "  User: $DB_USER"
    echo "  Database: $DB_NAME"
    echo ""
    echo -e "${GREEN}Your KFT Fitness database is now ready to use!${NC}"
}

# Main execution
main() {
    # Check if MySQL is installed
    if ! command -v mysql &> /dev/null; then
        echo -e "${RED}✗ MySQL client not found. Please install MySQL first.${NC}"
        exit 1
    fi
    
    # Test connection
    if ! test_connection; then
        echo -e "${RED}Please check your credentials and try again.${NC}"
        exit 1
    fi
    
    # Import schema
    if ! import_schema; then
        echo -e "${RED}Database setup failed during schema import.${NC}"
        exit 1
    fi
    
    # Verify setup
    if ! verify_setup; then
        echo -e "${RED}Database setup verification failed.${NC}"
        exit 1
    fi
    
    # Update configuration files
    update_config
    
    # Display summary
    display_summary
}

# Run main function
main "$@"
