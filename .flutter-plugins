# This is a generated file; do not edit or check into version control.
android_intent_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/android_intent_plus-4.0.3/
app_settings=/Users/<USER>/.pub-cache/hosted/pub.dev/app_settings-5.2.0/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/
device_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/
file_selector_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/
file_selector_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/
file_selector_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/
firebase_analytics=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics-11.5.0/
firebase_analytics_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_analytics_web-0.5.10+13/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.14.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.23.0/
flutter_inappwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview-6.1.5/
flutter_inappwebview_android=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_android-1.1.3/
flutter_inappwebview_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_ios-1.1.2/
flutter_inappwebview_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_macos-1.1.2/
flutter_inappwebview_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_web-1.1.2/
flutter_inappwebview_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_inappwebview_windows-0.6.0/
flutter_plugin_android_lifecycle=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/
flutter_secure_storage=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/
flutter_secure_storage_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/
flutter_secure_storage_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/
flutter_secure_storage_web=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/
flutter_secure_storage_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/
image_picker=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/
image_picker_android=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/
image_picker_for_web=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/
image_picker_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/
image_picker_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/
image_picker_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/
image_picker_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.0+1/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
screen_protector=/Users/<USER>/.pub-cache/hosted/pub.dev/screen_protector-1.4.2+1/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-7.2.2/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/
video_player=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/
video_player_android=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/
video_player_avfoundation=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.7.1/
video_player_web=/Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.3.5/
wakelock_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.3.2/
webview_flutter=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/
webview_flutter_android=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/
webview_flutter_wkwebview=/Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/
